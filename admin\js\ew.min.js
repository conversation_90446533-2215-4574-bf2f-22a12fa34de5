/*!
 * JavaScript for PHPMaker v2021.0.15
 * Copyright (c) e.World Technology Limited. All rights reserved.
 */
!function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=t(jQuery),n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function i(e,t,a){return e(a={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&a.path)}},a.exports),a.exports}var o=i((function(e){var t=function(e){var t,a=Object.prototype,n=a.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function s(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,a){return e[t]=a}}function d(e,t,a,n){var r=t&&t.prototype instanceof m?t:m,i=Object.create(r.prototype),o=new I(n||[]);return i._invoke=function(e,t,a){var n=c;return function(r,i){if(n===p)throw new Error("Generator is already running");if(n===h){if("throw"===r)throw i;return P()}for(a.method=r,a.arg=i;;){var o=a.delegate;if(o){var l=O(o,a);if(l){if(l===g)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===c)throw n=h,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=p;var s=u(e,t,a);if("normal"===s.type){if(n=a.done?h:f,s.arg===g)continue;return{value:s.arg,done:a.done}}"throw"===s.type&&(n=h,a.method="throw",a.arg=s.arg)}}}(e,a,o),i}function u(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}e.wrap=d;var c="suspendedStart",f="suspendedYield",p="executing",h="completed",g={};function m(){}function w(){}function v(){}var b={};b[i]=function(){return this};var y=Object.getPrototypeOf,E=y&&y(y(C([])));E&&E!==a&&n.call(E,i)&&(b=E);var _=v.prototype=m.prototype=Object.create(b);function S(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function a(r,i,o,l){var s=u(e[r],e,i);if("throw"!==s.type){var d=s.arg,c=d.value;return c&&"object"==typeof c&&n.call(c,"__await")?t.resolve(c.__await).then((function(e){a("next",e,o,l)}),(function(e){a("throw",e,o,l)})):t.resolve(c).then((function(e){d.value=e,o(d)}),(function(e){return a("throw",e,o,l)}))}l(s.arg)}var r;this._invoke=function(e,n){function i(){return new t((function(t,r){a(e,n,t,r)}))}return r=r?r.then(i,i):i()}}function O(e,a){var n=e.iterator[a.method];if(n===t){if(a.delegate=null,"throw"===a.method){if(e.iterator.return&&(a.method="return",a.arg=t,O(e,a),"throw"===a.method))return g;a.method="throw",a.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var r=u(n,e.iterator,a.arg);if("throw"===r.type)return a.method="throw",a.arg=r.arg,a.delegate=null,g;var i=r.arg;return i?i.done?(a[e.resultName]=i.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,g):i:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function C(e){if(e){var a=e[i];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function a(){for(;++r<e.length;)if(n.call(e,r))return a.value=e[r],a.done=!1,a;return a.value=t,a.done=!0,a};return o.next=o}}return{next:P}}function P(){return{value:t,done:!0}}return w.prototype=_.constructor=v,v.constructor=w,w.displayName=s(v,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,s(e,l,"GeneratorFunction")),e.prototype=Object.create(_),e},e.awrap=function(e){return{__await:e}},S(x.prototype),x.prototype[o]=function(){return this},e.AsyncIterator=x,e.async=function(t,a,n,r,i){void 0===i&&(i=Promise);var o=new x(d(t,a,n,r),i);return e.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},S(_),s(_,l,"Generator"),_[i]=function(){return this},_.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var a in e)t.push(a);return t.reverse(),function a(){for(;t.length;){var n=t.pop();if(n in e)return a.value=n,a.done=!1,a}return a.done=!0,a}},e.values=C,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var a in this)"t"===a.charAt(0)&&n.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function r(n,r){return l.type="throw",l.arg=e,a.next=n,r&&(a.method="next",a.arg=t),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),d=n.call(o,"finallyLoc");if(s&&d){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!d)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),A(a),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var n=a.completion;if("throw"===n.type){var r=n.arg;A(a)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,a,n){return this.delegate={iterator:C(e),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}(e.exports);try{regeneratorRuntime=t}catch(e){Function("r","regeneratorRuntime = r")(t)}}));function l(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return(a=a.call(e)).next.bind(a);if(Array.isArray(e)||(a=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}if("undefined"!=typeof Blob&&("undefined"==typeof FormData||!FormData.prototype.keys)){var d=function(e){var t=e[0],a=e[1];return t instanceof Blob&&(t=new File([t],a,{type:t.type,lastModified:t.lastModified})),t},u=function(e,t){if(e.length<t)throw new TypeError(t+" argument required, but only "+e.length+" present.")},c=function(e,t,a){return t instanceof Blob?[String(e),t,void 0!==a?a+"":"string"==typeof t.name?t.name:"blob"]:[String(e),String(t)]},f=function(e,t){for(var a=0;a<e.length;a++)t(e[a])},p="object"==typeof window?window:"object"==typeof self?self:n,h=p.FormData,g=p.XMLHttpRequest&&p.XMLHttpRequest.prototype.send,m=p.Request&&p.fetch,w=p.navigator&&p.navigator.sendBeacon,v=p.Symbol&&Symbol.toStringTag;v&&(Blob.prototype[v]||(Blob.prototype[v]="Blob"),"File"in p&&!File.prototype[v]&&(File.prototype[v]="File"));try{new File([],"")}catch(e){p.File=function(e,t,a){var n=new Blob(e,a),r=a&&void 0!==a.lastModified?new Date(a.lastModified):new Date;return Object.defineProperties(n,{name:{value:t},lastModifiedDate:{value:r},lastModified:{value:+r},toString:{value:function(){return"[object File]"}}}),v&&Object.defineProperty(n,v,{value:"File"}),n}}var b=function(){function e(e){if(this._data=Object.create(null),!e)return this;var t=this;f(e.elements,(function(e){if(e.name&&!e.disabled&&"submit"!==e.type&&"button"!==e.type)if("file"===e.type){var a=e.files&&e.files.length?e.files:[new File([],"",{type:"application/octet-stream"})];f(a,(function(a){t.append(e.name,a)}))}else if("select-multiple"===e.type||"select-one"===e.type)f(e.options,(function(a){!a.disabled&&a.selected&&t.append(e.name,a.value)}));else if("checkbox"===e.type||"radio"===e.type)e.checked&&t.append(e.name,e.value);else{var n="textarea"===e.type?function(e){return e.replace(/\r\n/g,"\n").replace(/\n/g,"\r\n")}(e.value):e.value;t.append(e.name,n)}}))}var t=e.prototype;return t.append=function(e,t,a){u(arguments,2);var n=c.apply(null,arguments);e=n[0],t=n[1],a=n[2];var r=this._data;r[e]||(r[e]=[]),r[e].push([t,a])},t.delete=function(e){u(arguments,1),delete this._data[String(e)]},t.entries=o.mark((function e(){var t,a,n,r,i;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this._data,e.t0=o.keys(t);case 2:if((e.t1=e.t0()).done){e.next=13;break}a=e.t1.value,n=l(t[a]);case 5:if((r=n()).done){e.next=11;break}return i=r.value,e.next=9,[a,d(i)];case 9:e.next=5;break;case 11:e.next=2;break;case 13:case"end":return e.stop()}}),e,this)})),t.forEach=function(e,t){u(arguments,1);for(var a,n=l(this);!(a=n()).done;){var r=a.value,i=r[0],o=r[1];e.call(t,o,i,this)}},t.get=function(e){u(arguments,1);var t=this._data;return t[e=String(e)]?d(t[e][0]):null},t.getAll=function(e){return u(arguments,1),(this._data[String(e)]||[]).map(d)},t.has=function(e){return u(arguments,1),String(e)in this._data},t.keys=o.mark((function e(){var t,a,n,r;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=l(this);case 1:if((a=t()).done){e.next=7;break}return n=a.value,r=n[0],e.next=5,r;case 5:e.next=1;break;case 7:case"end":return e.stop()}}),e,this)})),t.set=function(e,t,a){u(arguments,2);var n=c.apply(null,arguments);this._data[n[0]]=[[n[1],n[2]]]},t.values=o.mark((function e(){var t,a,n,r;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=l(this);case 1:if((a=t()).done){e.next=7;break}return n=a.value,r=n[1],e.next=5,r;case 5:e.next=1;break;case 7:case"end":return e.stop()}}),e,this)})),t._asNative=function(){for(var e,t=new h,a=l(this);!(e=a()).done;){var n=e.value,r=n[0],i=n[1];t.append(r,i)}return t},t._blob=function(){for(var e,t="----formdata-polyfill-"+Math.random(),a=[],n=l(this);!(e=n()).done;){var r=e.value,i=r[0],o=r[1];a.push("--"+t+"\r\n"),o instanceof Blob?a.push('Content-Disposition: form-data; name="'+i+'"; filename="'+o.name+'"\r\n',"Content-Type: "+(o.type||"application/octet-stream")+"\r\n\r\n",o,"\r\n"):a.push('Content-Disposition: form-data; name="'+i+'"\r\n\r\n'+o+"\r\n")}return a.push("--"+t+"--"),new Blob(a,{type:"multipart/form-data; boundary="+t})},t[Symbol.iterator]=function(){return this.entries()},t.toString=function(){return"[object FormData]"},e}();if(v&&(b.prototype[v]="FormData"),g){var y=p.XMLHttpRequest.prototype.setRequestHeader;p.XMLHttpRequest.prototype.setRequestHeader=function(e,t){y.call(this,e,t),"content-type"===e.toLowerCase()&&(this._hasContentType=!0)},p.XMLHttpRequest.prototype.send=function(e){if(e instanceof b){var t=e._blob();this._hasContentType||this.setRequestHeader("Content-Type",t.type),g.call(this,t)}else g.call(this,e)}}if(m){var E=p.fetch;p.fetch=function(e,t){return t&&t.body&&t.body instanceof b&&(t.body=t.body._blob()),E.call(this,e,t)}}w&&(p.navigator.sendBeacon=function(e,t){return t instanceof b&&(t=t._asNative()),w.call(this,e,t)}),p.FormData=b}var _={__proto__:null,userLevelId:function(e){return e&&!ew.checkInteger(e.value)?{userLevelId:ew.language.phrase("UserLevelIDInteger")}:parseInt(e.value,10)<1&&{userLevelId:ew.language.phrase("UserLevelIDIncorrect")}},userLevelName:function(e){return function(t){var a=document.getElementById("x_"+e);if(a&&t){var n=t.value.trim(),r=parseInt(a.value.trim(),10);if(0===r&&!ew.sameText(n,"Default"))return{userLevelName:ew.language.phrase("UserLevelDefaultName")};if(-1===r&&!ew.sameText(n,"Administrator"))return{userLevelName:ew.language.phrase("UserLevelAdministratorName")};if(-2===r&&!ew.sameText(n,"Anonymous"))return{userLevelName:ew.language.phrase("UserLevelAnonymousName")};if(r>0&&["anonymous","administrator","default"].includes(n.toLowerCase()))return{userLevelName:ew.language.phrase("UserLevelNameIncorrect")}}return!1}},required:function(e){return function(t){var n,r=a.default(t),i=r.closest("#r_"+(null===(n=r.data("field"))||void 0===n?void 0:n.substr(2)));return i[0]||(i=r.closest("[id^=el]")),"none"!=i.css("display")&&(!(!t||ew.hasValue(t))&&{required:ew.language.phrase("EnterRequiredField").replace("%s",e)})}},fileRequired:function(e){return function(t){var a=document.getElementById("fn_"+t.id);return!(!a||ew.hasValue(a))&&{fileRequired:ew.language.phrase("EnterRequiredField").replace("%s",e)}}},mismatchPassword:function(e){var t;e.id.startsWith("c_")?t=e.id.replace(/^c_/,"x_"):"cpwd"==e.id&&(t="npwd");var a=document.getElementById(t);return e.value!==a.value&&{mismatchPassword:ew.language.phrase("MismatchPassword")}},between:function(e){var t,n;return e.id.startsWith("y_")&&(t=document.getElementById(e.id.replace(/^y_/,"x_")),n=document.getElementById(e.id.replace(/^y_/,"z_"))),!(!ew.hasValue(t)||"BETWEEN"!=a.default(n).val()||ew.hasValue(e))&&{between:ew.language.phrase("EnterValue2")}},passwordStrength:function(e){var t=a.default(e);return!(ew.isMaskedPassword(e)||!t.hasClass("ew-password-strength")||t.data("validated"))&&{passwordStrength:ew.language.phrase("PasswordTooSimple")}},username:function(e){return function(t){return!(e||!t.value.match(new RegExp("["+ew.escapeRegExChars(ew.INVALID_USERNAME_CHARACTERS)+"]")))&&{username:ew.language.phrase("InvalidUsernameChars")}}},password:function(e){return function(t){return!(e||ew.ENCRYPTED_PASSWORD||!t.value.match(new RegExp("["+ew.escapeRegExChars(ew.INVALID_PASSWORD_CHARACTERS)+"]")))&&{password:ew.language.phrase("InvalidPasswordChars")}}},email:function(e){var t=ew.getValue(e);return!ew.checkEmail(t)&&{email:ew.language.phrase("IncorrectEmail")}},emails:function(e,t){return function(a){var n=ew.getValue(a);return!ew.checkEmails(n,e)&&{email:t}}},datetime:function(e){return function(t){var a,n,r=ew.getValue(t),i=ew.DATE_SEPARATOR;return[12,15,115].includes(e)?(a=ew.checkShortDate,n="IncorrectShortDateYMD"):[5,9,109].includes(e)?(a=ew.checkDate,n="IncorrectDateYMD"):[14,17,117].includes(e)?(a=ew.checkShortEuroDate,n="IncorrectShortDateDMY"):[7,11,111].includes(e)?(a=ew.checkEuroDate,n="IncorrectDateDMY"):[13,16,116].includes(e)?(a=ew.checkShortUSDate,n="IncorrectShortDateMDY"):[6,10,110].includes(e)?(a=ew.checkUSDate,n="IncorrectDateMDY"):(a=ew.checkDateDef,n="IncorrectDate",i=ew.DATE_FORMAT),!(!a||a(r))&&{datetime:ew.language.phrase(n).replace(/%s/g,i)}}},time:function(e){var t=ew.getValue(e);return!ew.checkTime(t)&&{time:ew.language.phrase("IncorrectTime")}},float:function(e){var t=ew.getValue(e);return!ew.checkNumber(t)&&{time:ew.language.phrase("IncorrectFloat")}},range:function(e,t){return function(a){var n=ew.getValue(a);return!ew.checkRange(n,e,t)&&{range:ew.language.phrase("IncorrectRange").replace("%1",e).replace("%2",t)}}},integer:function(e){var t=ew.getValue(e);return!ew.checkInteger(t)&&{integer:ew.language.phrase("IncorrectInteger")}},phone:function(e){var t=ew.getValue(e);return!ew.checkPhone(t)&&{phone:ew.language.phrase("IncorrectPhone")}},zip:function(e){var t=ew.getValue(e);return!ew.checkZip(t)&&{zip:ew.language.phrase("IncorrectZip")}},creditCard:function(e){var t=ew.getValue(e);return!ew.checkCreditCard(t)&&{creditCard:ew.language.phrase("IncorrectCreditCard")}},ssn:function(e){var t=ew.getValue(e);return!ew.checkSsn(t)&&{ssn:ew.language.phrase("IncorrectSSN")}},guid:function(e){var t=ew.getValue(e);return!ew.checkGuid(t)&&{guid:ew.language.phrase("IncorrectGUID")}},regex:function(e){return function(t){var a=ew.getValue(t);return!ew.checkByRegEx(a,e)&&{regex:ew.language.phrase("IncorrectField")}}},custom:function(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];return function(t){if("function"==typeof e){var n=ew.getValue(t);if(e.apply(void 0,[n].concat(a)))return{custom:ew.language.phrase("IncorrectField")}}return!1}},captcha:function(e){return!(!e||ew.hasValue(e))&&{captcha:ew.language.phrase("EnterValidateCode")}},recaptcha:function(e){var t;return!(!e||ew.hasValue(e)||""!==(null===(t=grecaptcha)||void 0===t?void 0:t.getResponse(e.dataset.id)))&&{recaptcha:ew.language.phrase("ClickReCaptcha")}}},S=r(i((function(e){function t(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}e.exports=function(e,a,n){return a&&t(e.prototype,a),n&&t(e,n),e},e.exports.default=e.exports,e.exports.__esModule=!0}))),x=r(i((function(e){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.default=e.exports,e.exports.__esModule=!0}))),O=i((function(e){function t(a,n){return e.exports=t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},e.exports.default=e.exports,e.exports.__esModule=!0,t(a,n)}e.exports=t,e.exports.default=e.exports,e.exports.__esModule=!0}));r(O);var T=r(i((function(e){e.exports=function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,O(e,t)},e.exports.default=e.exports,e.exports.__esModule=!0}))),A=r(i((function(e){e.exports=function(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e},e.exports.default=e.exports,e.exports.__esModule=!0}))),I=r(i((function(e){function t(e,t,a,n,r,i,o){try{var l=e[i](o),s=l.value}catch(e){return void a(e)}l.done?t(s):Promise.resolve(s).then(n,r)}e.exports=function(e){return function(){var a=this,n=arguments;return new Promise((function(r,i){var o=e.apply(a,n);function l(e){t(o,r,i,l,s,"next",e)}function s(e){t(o,r,i,l,s,"throw",e)}l(void 0)}))}},e.exports.default=e.exports,e.exports.__esModule=!0})));function C(e,t){var n=this;this._initiated=!1,this.id=e,this.pageId=t,this.$element=null,this.htmlForm=null,this.initSearchPanel=!1,this.modified=!1,this.validateRequired=!0,this.validate=null,this.emptyRow=null,this.multiPage=null,this.autoSuggests={},this.lists={},this.disableForm=function(){var e=this.getForm();a.default(e).find(":submit:not(.dropdown-toggle)").prop("disabled",!0).addClass("disabled")},this.enableForm=function(){var e=this.getForm(),t=a.default(e);t.find(".ew-disabled-element").removeClass("ew-disabled-element").prop("disabled",!1),t.find(".ew-enabled-element").removeClass("ew-enabled-element").prop("disabled",!0),t.find(":submit:not(.dropdown-toggle)").prop("disabled",!1).removeClass("disabled")},this.appendHidden=function(e){var t=this.getForm(),n=a.default(t),r=a.default(e).closest(".ew-form"),i=r.attr("id")+"$"+e.name;if(!n.find("input:hidden[name='"+i+"']")[0]){var o=r.find('[name="'+e.name+'"]').serializeArray();o.length?o.forEach((function(e,t){a.default('<input type="hidden" name="'+i+'">').val(e.value).appendTo(n)})):a.default('<input type="hidden" name="'+i+'">').val("").appendTo(n)}},this.canSubmit=function(){var e=this.getForm(),t=a.default(e);if(this.disableForm(),this.updateTextArea(),!this.validate||this.validate()&&!t.find(".is-invalid")[0]){t.find("input[name^=sv_], input[name^=p_], .ew-template input").prop("disabled",!0).addClass("ew-disabled-element"),t.find("[data-readonly=1][disabled]").prop("disabled",!1).addClass("ew-enabled-element");var r=a.default(t.find("input[name='detailpage']").map((function(e,a){return t.find("#"+a.value).get()})));return r.length>1&&r.each((function(e,t){a.default(t).find(":input").each((function(e,a){if(/^(fn_)?(x|o)\d*_/.test(a.name)){var i=r.not(t).find(":input[name='"+a.name+"']");i.length&&(n.appendHidden(a),i.each((function(){n.appendHidden(this)})))}}))})),!0}return this.enableForm(),!1},this.submit=function(){var e=I(o.mark((function e(t,n){var r,i,l;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.getForm(),!this.canSubmit()){e.next=11;break}return n&&(r.action=n),i={f:this,form:r,result:!0},a.default(r).trigger("beforesubmit",[i]),e.next=7,i.result;case 7:l=e.sent,(a.default.isBoolean(l)&&l||a.default.isObject(l)&&l.value)&&r.submit(),e.next=12;break;case 11:this.enableForm();case 12:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}(),this.getList=function(e){return e=e.replace(/^(sv_)?[xy](\d*|\$rowindex\$)_|\[\]$/g,""),this.lists[e]},this.compileTemplates=function(){for(var e=0,t=Object.values(this.lists);e<t.length;e++){var n=t[e];n.template&&a.default.isString(n.template)&&(n.template=a.default.templates(n.template))}},this.getForm=function(){return this.htmlForm||(this.$element=a.default("#"+this.id),this.$element.is("form")?this.htmlForm=this.$element[0]:this.$element.is("div")&&(this.htmlForm=this.$element.closest("form")[0])),this.htmlForm},this.getElement=function(e){return this.$element||(this.$element=a.default("#"+this.id)),e?ew.getElement(e,this.$element):this.$element[0]},this.getElements=function(e){this.$element||(this.$element=a.default("#"+this.id));var t="[name='"+e+"']";t="input"+t+",select"+t+",textarea"+t+",button"+t;var n=this.$element.find(t);return 0==n.length?null:1==n.length&&n.is(":not([type=checkbox]):not([type=radio])")?n[0]:n.get()},this.updateLists=function(e){if(null!==e&&(a.default.isNumber(e)||"grid"!=this.pageId)){var t=this.getForm(),r=t.querySelector("input#confirm");if(r&&"confirm"==r.value)ew.removeSpinner();else{var i=function(t,n){var r="",i=e,o=t.split(" ");o.length>1&&(r=o[0],i="",t=o[1]);var l=a.default.isNumber(i)?"x"+i+"_":"x_";return t=t.startsWith("x_")?t.replace(/^x_/,l):l+t,n&&!t.endsWith("[]")&&(t+="[]"),r?r+" "+t:t},o=Object.entries(this.lists).map((function(e){var t=e[0],a=e[1];return"[name='"+i(t,a.multiple)+"']"})).join();if(o&&t.querySelector(o)){var l=[],s=[];this.compileTemplates();for(var d=0,u=Object.entries(this.lists);d<u.length;d++){var c=u[d],f=c[0],p=c[1],h=p.parentFields.slice().map((function(e){return i(e)})),g=p.ajax;if(f=i(f,p.multiple),a.default.isBoolean(g)){var m=h.map((function(e){return ew.getOptionValues(e,t)}));l.push([f,m,g,!1])}else ew.updateOptions.call(this,f,h,null,!1)}for(var w=0;w<l.length;w++)s.push(new Promise((function(e,t){setTimeout((function(){e(ew.updateOptions.apply(n,l.shift()))}),ew.AJAX_DELAY*w)})));Promise.all(s).then((function(){a.default(document).trigger("updatedone",[{source:n,target:t}])})).catch((function(e){console.log(e)}))}else ew.removeSpinner()}}},this.createAutoSuggest=function(e){var t=Object.assign({limit:ew.AUTO_SUGGEST_MAX_ENTRIES,form:this},ew.autoSuggestSettings,e);n.autoSuggests[e.id]=new ew.AutoSuggest(t)},this.initEditors=function(){var e=this.getForm();a.default(e.elements).filter("textarea.editor").each((function(e,t){var n=a.default(t).data("editor");!n||n.active||n.name.includes("$rowindex$")||n.create()}))},this.updateTextArea=function(e){var t=this.getForm();a.default(t.elements).filter("textarea.editor").each((function(t,n){var r=a.default(n).data("editor");return!(r&&(!e||r.name==e))||(r.save(),!e&&void 0)}))},this.destroyEditor=function(e){var t=this.getForm();a.default(t.elements).filter("textarea.editor").each((function(t,n){var r=a.default(n).data("editor");return!(r&&(!e||r.name==e))||(r.destroy(),!e&&void 0)}))},this.onError=function(e,t){return ew.onError(this,e,t)},this.initUpload=function(){var e=this.getForm();a.default(e.elements).filter("input:file:not([name*='$rowindex$'],[id='importfiles'])").each((function(e){a.default.later(ew.AJAX_DELAY*e,null,ew.upload,this)}))},this.setupFilters=function(e,t){var r=this.id,i=this.filterList?this.filterList.data:null,o=a.default(".ew-save-filter[data-form="+r+"]").toggleClass("disabled",!i),l=a.default(".ew-delete-filter[data-form="+r+"]").toggleClass("disabled",!t.length).toggleClass("dropdown-toggle",!!t.length),s=l.parent("li").toggleClass("dropdown-submenu dropdown-hover",!!t.length).toggleClass("disabled",!t.length),d=o.parent("li").toggleClass("disabled",!i);a.default(e.target);var u=function(e,t){if("Client"==ew.SEARCH_FILTER_OPTION)window.localStorage.setItem(e+"_filters",JSON.stringify(t));else if("Server"==ew.SEARCH_FILTER_OPTION){var r=a.default("body");r.css("cursor","wait"),a.default.ajax(ew.currentPage(),{type:"POST",dataType:"json",data:{ajax:"savefilters",filters:JSON.stringify(t)}}).done((function(e){e[0]&&e[0].success&&(n.filterList.filters=t)})).always((function(){r.css("cursor","default")}))}};if(d.off("click.ew").on("click.ew",(function(e){if(d.hasClass("disabled"))return!1;ew.prompt(ew.language.phrase("EnterFilterName"),(function(e){(e=ew.sanitize(e))&&(t.push([e,i]),u(r,t))}),!0)})).prevAll().remove(),l.next("ul.dropdown-menu").remove(),t.length){var c=a.default("<ul class='dropdown-menu'></ul>");for(var f in t)Array.isArray(t[f])&&(a.default('<li><a class="dropdown-item" data-index="'+f+'" href="#" onclick="return false;">'+t[f][0]+"</a></li>").on("click",(function(e){var n=a.default(this).find("a[data-index]").data("index");ew.prompt(ew.language.phrase("DeleteFilterConfirm").replace("%s",t[n][0]),(function(e){e&&(t.splice(n,1),u(r,t))}))})).appendTo(c),a.default('<li><a class="dropdown-item ew-filter-list" data-index="'+f+'" href="#" onclick="return false;">'+t[f][0]+"</a></li>").insertBefore(d).on("click",(function(e){var n=a.default(this).find("a[data-index]").data("index");a.default("<form>").attr({method:"post",action:ew.currentPage()}).append(a.default("<input type='hidden'>").attr({name:"cmd",value:"resetfilter"}),a.default("<input type='hidden'>").attr({name:ew.TOKEN_NAME_KEY,value:ew.TOKEN_NAME}),a.default("<input type='hidden'>").attr({name:ew.ANTIFORGERY_TOKEN_KEY,value:ew.ANTIFORGERY_TOKEN}),a.default("<input type='hidden'>").attr({name:"filter",value:JSON.stringify(t[n][1])})).appendTo("body").trigger("submit")})));a.default("<li class='dropdown-divider'></li>").insertBefore(d),s.append(c)}},this.init=function(){if(!this._initiated){"Client"==ew.SEARCH_FILTER_OPTION&&window.localStorage||"Server"==ew.SEARCH_FILTER_OPTION&&ew.IS_LOGGEDIN&&!ew.IS_SYS_ADMIN&&""!=ew.CURRENT_USER_NAME?(a.default(".ew-filter-option."+this.id+" .ew-btn-dropdown").on("show.bs.dropdown",(function(e){var t=[];if("Client"==ew.SEARCH_FILTER_OPTION){var r=window.localStorage.getItem(n.id+"_filters");r&&(t=ew.parseJson(r)||[])}else"Server"==ew.SEARCH_FILTER_OPTION&&(t=n.filterList&&n.filterList.filters?n.filterList.filters:[]);var i=a.default.grep(t,(function(e){if(Array.isArray(e)&&2==e.length)return e}));n.setupFilters(e,i)})),a.default(".ew-filter-option").show()):a.default(".ew-filter-option").hide();var e=this.getForm(),t=a.default(e);if(e){this.compileTemplates();var r=/s(ea)?rch$/.test(this.id);r&&this.initSearchPanel&&!ew.hasFormData(e)&&a.default("#"+this.id+"-search-panel").removeClass("show"),a.default(".ew-search-toggle[data-form="+this.id+"]").on("click.bs.button",(function(){a.default("#"+a.default(this).data("form")+"-search-panel").collapse("toggle")})),a.default(".ew-table .ew-search-operator").text().trim()||a.default(".ew-table .ew-search-operator").parent("td").hide(),r&&a.default(".ew-highlight[data-form="+this.id+"]").on("click.bs.button",(function(){a.default("span."+a.default(this).data("name")).toggleClass("ew-highlight-search")})),r&&t.find("select[id^=z_]").each((function(){"BETWEEN"!=a.default(this).trigger("change").val()&&t.find("#w_"+this.id.substr(2)).trigger("change")})),this.multiPage&&this.multiPage.render(),loadjs.ready(["editor"],this.initEditors.bind(this)),this.updateLists(),this.initUpload(),this.$element.is("form")&&(this.$element.find(".ew-detail-pages .ew-nav-tabs a[data-toggle=tab]").on("shown.bs.tab",(function(e){var t=a.default(e.target.getAttribute("href")),n=t.find(".table-responsive.ew-grid-middle-panel"),r=t.closest(".container-fluid");n.width()>=r.width()?n.width(r.width()+"px"):n.width("auto")})),t.on("submit",(function(e){return n.submit(e),!1})),t.find("[data-field], .ew-priv").on("change",(function(){ew.CONFIRM_CANCEL&&(n.modified=!0)})),t.find("#btn-cancel[data-href]").on("click",(function(){n.updateTextArea();var r=a.default(this).data("href");n.modified&&ew.hasFormData(e)?ew.prompt(ew.language.phrase("ConfirmCancel"),(function(e){e&&(t.find("#btn-action").prop("disabled",!0),window.location=r)})):(t.find("#btn-action").prop("disabled",!0),window.location=r)}))),this._initiated=!0,this.$element.data("form",this)}}},ew.forms.add(this)}function P(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function k(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?P(Object(a),!0).forEach((function(t){A(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):P(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function N(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return(a=a.call(e)).next.bind(a);if(Array.isArray(e)||(a=function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return R(e,t)}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}var L=function(){function e(e,t,a){if(A(this,"name",""),A(this,"validators",[]),A(this,"_validate",!0),this.name=e,Array.isArray(t))for(var n,r=N(t);!(n=r()).done;){var i=n.value;this.addValidator(i)}else"function"==typeof t&&this.addValidator(t);this.invalid=a}var t=e.prototype;return t.addValidator=function(e){"function"==typeof e&&this.validators.push(e)},t.addError=function(e){if(e){var t,a=null!==(t=this._error)&&void 0!==t?t:{};this._error=k(k({},a),e),this.invalid=!0}},t.clearErrors=function(){this._error=null,this.invalid=!1},t.clearValidators=function(){this.validators=[]},t.validate=function(){var e=!0;if(this.clearErrors(),this._element&&this.shouldValidate&&Array.isArray(this.validators)){for(var t,a=N(this.validators);!(t=a()).done;){var n=(0,t.value)(this._element);!1!==n&&(this.addError(n),e=!1)}this.updateFeedback()}return e},t.updateFeedback=function(){var e=this.errorMessage;this._element&&e&&(jQuery(this._element).closest("[id^=el_], .form-group").find(".invalid-feedback").html(e),ew.setInvalid(this._element))},t.focus=function(){this._element&&ew.setFocus(this._element)},S(e,[{key:"error",get:function(){return this._error}},{key:"errorMessage",get:function(){return this._error?Array.from(Object.values(this._error)).join("<br>"):""}},{key:"shouldValidate",get:function(){return!this._checkbox||this._checkbox.checked}},{key:"element",get:function(){return this._element},set:function(e){var t,a;this._element=e,this._checkbox=null!==(t=this._element)&&void 0!==t&&null!==(a=t.id)&&void 0!==a&&a.match(/^[xy]_/)?document.getElementById(this._element.id.replace(/^[xy]_/,"u_")):null}},{key:"value",get:function(){return this._element?ew.getValue(this._element):""}}]),e}();function D(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function M(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?D(Object(a),!0).forEach((function(t){A(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):D(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function j(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return(a=a.call(e)).next.bind(a);if(Array.isArray(e)||(a=function(e,t){if(!e)return;if("string"==typeof e)return F(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return F(e,t)}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}var U=function(e){function t(t,a){var n;return n=e.call(this,t,a)||this,A(x(n),"row",{}),A(x(n),"fields",{}),n}T(t,e);var a=t.prototype;return a.addField=function(e,t,a){e in this.fields||(this.fields[e]=new L(e,t,a))},a.getField=function(e){return this.fields[e]},a.addFields=function(e){if(Array.isArray(e))for(var t,a=j(e);!(t=a()).done;){var n=t.value;Array.isArray(n)&&this.addField.apply(this,n)}},a.addError=function(e,t){var a;t&&(this._error=null!==(a=this._error)&&void 0!==a?a:{},this._error[e]=t)},a.addCustomError=function(e,t){if(e in this.fields){var a=this.fields[e],n={custom:t};a.addError(n),a.updateFeedback(),this.addError(e,n)}return!1},a.focus=function(){for(var e=0,t=Object.entries(this.fields);e<t.length;e++){var a=t[e],n=a[0],r=a[1];if(r.invalid||this._error&&this._error[n]){r.focus(),this.makeVisible(r.element);break}}},a.makeVisible=function(e){if(this.multiPage)this.multiPage.gotoPageByElement(e);else if(this.$element.is("div")){var t=this.$element.closest(".tab-pane");t[0]&&!t.hasClass("active")&&t.closest(".tabbable, .ew-nav-tabs").find("a[data-toggle=tab][href='#"+t.attr("id")+"']").click()}},a.validateFields=function(e){this.rowIndex=null!=e?e:"",this.row={},this._error=null;for(var t=!0,a=0,n=Object.values(this.fields);a<n.length;a++){var r=n[a];r.element=this.getElements("x"+this.rowIndex+"_"+r.name),r.element||(r.element=this.getElements("x"+this.rowIndex+"_"+r.name+"[]")),r.element||(r.element=this.getElements(r.name)),this.row[r.name]=r.value,r.element&&!r.validate()&&(this.addError(r.name,r.error),t=!1)}if(this.value){Array.isArray(this.value)||(this.value=[this.value]);var i=parseInt(e,10)||0;i=i>1?i-1:0,this.value[i]=M({},this.row)}else this.value=M({},this.row);return this.focus(),t},a.setInvalid=function(e){this.rowIndex=null!=e?e:"";for(var t=0,a=Object.values(this.fields);t<a.length;t++){var n=a[t];n.invalid&&(n.element=this.getElements("x"+this.rowIndex+"_"+n.name),n.element||(n.element=this.getElements("x"+this.rowIndex+"_"+n.name+"[]")),n.element||(n.element=this.getElements(n.name)),ew.setInvalid(n.element))}},S(t,[{key:"error",get:function(){return this._error}}]),t}(C),$=function(){function e(e){A(this,"_isAutoSuggest",null),this.elementId=e.id,this.form=e.form,a.default.isString(this.form)&&(this.form=ew.forms.get(this.form)),this.element=this.form.getElement(this.elementId),this.formElement=this.form.getElement(),this.list=this.form.getList(this.elementId);var t=this.elementId.match(/^[xy](\d*|\$rowindex\$)_/),n=t?t[1]:"";this.parentFields=this.list.parentFields.slice().map((function(e){return 1==e.split(" ").length?e.replace(/^x_/,"x"+n+"_"):e})),this.limit=e.limit,this.debounce=e.debounce,this.data=e.data,this.recordCount=0}var t=e.prototype;return t.formatResult=function(e){return this.list.template&&!this.isAutoSuggest?this.list.template.render(e,ew.jsRenderHelpers):ew.displayValue(e,this.element)||e[0]},t.generateRequest=function(){var e=this,t=Object.assign({},this.data,{name:this.element.name,page:this.list.page,field:this.list.field,ajax:"autosuggest",language:ew.LANGUAGE_ID},ew.getUserParams("#p_"+this.elementId,this.formElement));return this.parentFields.length>0&&this.parentFields.forEach((function(a,n){var r=ew.getOptionValues(a,e.formElement);t["v"+(n+1)]=r.join(ew.MULTIPLE_OPTION_SEPARATOR)})),t},t.getUrl=function(e,t){var n=new URLSearchParams({q:e,n:this.limit,rnd:ew.random(),start:a.default.isNumber(t)?t:-1});return ew.getApiUrl(ew.API_LOOKUP_ACTION,n.toString())},t.prepare=function(e,t){return{url:this.getUrl(e,t),type:"POST",dataType:"json",data:this.generateRequest()}},t.transform=function(e){var t=[];return e&&"OK"==e.result&&(this.recordCount=e.totalRecordCount,t=e.records),t},S(e,[{key:"isAutoSuggest",get:function(){return null===this._isAutoSuggest&&(this._isAutoSuggest=ew.isAutoSuggest(this.element)),this._isAutoSuggest}}]),e}(),B=function(e,t,a){this.value=String(e),this.text=String(t),this.selected=!!a};function H(e){$.call(this,e);var t={typeahead:{}};if(this.elementId.includes("$rowindex$"))return t;if(this.input=this.form.getElement("sv_"+this.elementId),!this.input)return t;var n,r=this,i=a.default(this.input),o=a.default(this.element);this.minWidth=e.minWidth,this.maxHeight=e.maxHeight,this.highlight=e.highlight,this.hint=e.hint,this.minLength=e.minLength,this.templates=Object.assign({},e.templates),this.trigger=e.trigger,this.delay=e.delay,this.debounce=e.debounce,this.display=e.display||"text",this.forceSelection=e.forceSelect,this.$input=i,this.$element=o,i.val()&&o.val()&&this.element.add(o.val(),i.val(),!0),this.setValue=function(e){e=e||i.val();var t=this.element.options.findIndex((function(t){return t.text==e}));if(t<0){if(this.forceSelection&&e)return i.typeahead("val","").attr("placeholder",ew.language.phrase("ValueNotExist")).addClass("is-invalid"),void o.val("").trigger("change")}else this.element.options[t].selected=!0,/s(ea)?rch$/.test(this.formElement.id)&&!this.forceSelection||(e=this.element.options[t].value);e!==o.attr("value")&&o.attr("value",e).trigger("change")},this.transform=function(e){var t=$.prototype.transform.call(this,e);return this.element.options=t.map((function(e){return new B(e.lf||e[0],r.formatResult(e))})),this.element.options},this.source=function(e,t,i){n&&n.cancel(),n=a.default.later(r.debounce,null,(function(){r.recordCount=0;var t=r.prepare(e);a.default.ajax(t).done((function(e){i(r.transform(e))}))}))},this.count=function(){return r.typeahead.menu.$node.find(".tt-suggestion.tt-selectable").length},this.more=function(){var e=a.default("body");e.css("cursor","wait");var t=r.typeahead,n=t.menu.query,i=t.menu.datasets[0],o=r.count(),l=r.prepare(n,o);a.default.ajax(l).done((function(e){e=r.transform(e),i._append(n,e),t.menu.$node.find(".tt-dataset").scrollTop(i.$lastSuggestion.outerHeight()*o)})).always((function(){e.css("cursor","default")}))},i.on("typeahead:select",(function(e,t){r.setValue(t[r.display])})).on("change",(function(e){var t=i.data("tt-typeahead");if(t&&t.isOpen()&&!t.menu.empty()){var a=t.menu.getActiveSelectable();if(a){var n=a.index(),o=r.element.options[n].text;i.typeahead("val",o)}}r.setValue()})).on("blur",(function(e){var t=i.data("tt-typeahead");t&&t.isOpen()&&t.menu.close()})).on("focus",(function(e){i.attr("placeholder",i.data("placeholder")).removeClass("is-invalid")}));var l=r.list.template||r.templates.suggestion;l&&a.default.isString(l)&&(l=a.default.templates(l)),l&&(r.templates.suggestion=l.render.bind(l)),o.data("autosuggest",this),a.default((function(){var e={highlight:r.highlight,minLength:r.minLength,hint:r.hint,trigger:r.trigger,delay:r.delay},t={name:r.form.id+"-"+r.elementId,source:r.source,templates:r.templates,display:r.display,limit:r.limit,async:!0},a=[e,t];o.trigger("typeahead",[a]),r.limit=t.limit,i.typeahead.apply(i,a),i.on("typeahead:rendered",(function(){var t=r.typeahead.menu.$node,a=t.find(".tt-more").html(ew.language.phrase("More"));arguments.length>1&&r.recordCount>r.count()?a.one(e.trigger,(function(t){setTimeout((function(){r.more()}),e.delay),t.preventDefault(),t.stopPropagation()})):a.hide()})),i.off("blur.tt"),r.typeahead=i.data("tt-typeahead");var n=r.typeahead.menu.$node.css("z-index",1e3);r.minWidth&&n.css("min-width",r.minWidth);var l=n.find(".tt-dataset"),s=r.maxHeight||(parseInt(l.css("line-height"),10)+6)*(t.limit+1);l.css({"max-height":s,"overflow-y":"auto"})}))}H.prototype=Object.create($.prototype);var Y=function(){function e(){A(this,"_forms",{})}var t=e.prototype;return t.get=function(e){var t=a.default.isString(e)?e:ew.getForm(e).id;return this._forms[t]},t.add=function(e){this._forms[e.id]=e},t.ids=function(){return Object.keys(this._forms)},e}();function K(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return(a=a.call(e)).next.bind(a);if(Array.isArray(e)||(a=function(e,t){if(!e)return;if("string"==typeof e)return V(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return V(e,t)}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function G(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function z(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?G(Object(a),!0).forEach((function(t){A(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):G(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var W,q=new URL(window.location),J=new Y,X=a.default(document),Q=a.default("body"),Z=ew.removeSpinner;function ee(e){var t=e&&e.target?e.target:document;a.default(t).find(".ew-icon").closest("a, button").add(".ew-tooltip").tooltip({container:"body",trigger:ew.IS_MOBILE?"manual":"hover",placement:"bottom",sanitizeFn:ew.sanitizeFn})}function te(e){var t=e&&e.target?e.target:document;a.default.fn.pStrength&&"undefined"!=typeof ew.MIN_PASSWORD_STRENGTH&&a.default(t).find(".ew-password-strength").each((function(){var e=a.default(this);e.data("pStrength")||e.pStrength({changeBackground:!1,backgrounds:[],passwordValidFrom:ew.MIN_PASSWORD_STRENGTH,onPasswordStrengthChanged:function(e,t){var n=a.default(this),r=a.default("[id='"+n.attr("data-password-strength")+"']"),i=r.find(".progress-bar");if(r.width(n.outerWidth()),n.val()&&!ew.isMaskedPassword(this)){var o=t+"%";t<25?i.addClass("bg-danger").removeClass("bg-warning bg-info bg-success"):t<50?i.addClass("bg-warning").removeClass("bg-danger bg-info bg-success"):t<75?i.addClass("bg-info").removeClass("bg-danger bg-warning bg-success"):i.addClass("bg-success").removeClass("bg-danger bg-warning bg-info"),i.css("width",o),t>25&&(o=ew.language.phrase("PasswordStrength").replace("%p",o)),i.html(o),r.removeClass("d-none").show(),n.data("validated",t>=ew.MIN_PASSWORD_STRENGTH)}else r.addClass("d-none").hide(),n.data("validated",null)}})})),a.default.fn.pGenerator&&a.default(t).find(".ew-password-generator").each((function(){var e=a.default(this);e.data("pGenerator")||e.pGenerator({passwordLength:ew.GENERATE_PASSWORD_LENGTH,uppercase:ew.GENERATE_PASSWORD_UPPERCASE,lowercase:ew.GENERATE_PASSWORD_LOWERCASE,numbers:ew.GENERATE_PASSWORD_NUMBER,specialChars:ew.GENERATE_PASSWORD_SPECIALCHARS,onPasswordGenerated:function(e){var t=a.default(this);a.default("#"+t.attr("data-password-field")).val(e).trigger("change").trigger("focus").triggerHandler("click"),a.default("#"+t.attr("data-password-confirm")).val(e),a.default("#"+t.attr("data-password-strength")).addClass("d-none").hide()}})}))}function ae(e,t){var n=ew.PATH_BASE+ew.API_URL,r=new URLSearchParams(t).toString();if(a.default.isString(e))n+=e||"";else if(Array.isArray(e)){var i=e.map((function(e){return encodeURIComponent(e)})).join("/");n+=i||""}return n+(r?"?"+r:"")}function ne(e){var t=e.split("?"),a=t[1];if(a){var n=new URLSearchParams(a);n.forEach((function(e,t){e=decodeURIComponent(e),["<>","<=",">=",">","<"].includes(e)?n.set(t,e):n.set(t,ew.sanitize(e))})),a=n.toString()}return t[0]+(a?"?"+a:"")}function re(e){var t=e&&e.target?e.target:document;a.default(t).find(".ew-export-link[href]:not(.ew-email):not(.ew-print):not(.ew-xml)").on("click",(function(e){var t=a.default(this).attr("href");t&&"#"!=t&&oe(t),e.preventDefault()}))}function ie(e){var t=e&&e.target?e.target:document,n=a.default(t),r=n.find("input[type=checkbox].ew-multi-select"),i=function(e){var t=r.filter("[name^='"+e+"_']"),a=t.length,n=t.filter(":checked").length;t.closest("form").find("input[type=checkbox]#"+e).prop("checked",n==a).prop("indeterminate",n!=a&&0!=n)};r.on("click",(function(e){i(this.name.split("_")[0])})),n.find("input[type=checkbox].ew-priv:not(.ew-multi-select)").each((function(){i(this.id)}))}function oe(e,t){var n=e.includes("export=html");return Swal.fire(z(z({},ew.sweetAlertSettings),{},{showConfirmButton:!1,html:"<p>"+ew.language.phrase("Exporting")+"</p>",allowOutsideClick:!1,allowEscapeKey:!1,willOpen:function(){Swal.showLoading(),a.default.ajax({url:e,type:t?"POST":"GET",cache:!1,data:t||null,xhrFields:{responseType:n?"text":"blob"}}).done((function(t,a,r){var i=URL.createObjectURL(n?new Blob([t],{type:"text/html"}):t),o=document.createElement("a"),l=r.getResponseHeader("Content-Disposition").match(/\bfilename=((['"])(.+)\2|([^;]+))/i);o.style.display="none",o.href=i,l&&(o.download=l[3]||l[4]),document.body.appendChild(o),o.click(),X.trigger("export",[{type:"done",url:e,objectUrl:i}]),URL.revokeObjectURL(i),Swal.close()})).fail((function(t,a,n){var r;Swal.hideLoading(),Swal.update({showConfirmButton:!0}),null===(r=Swal.getActions())||void 0===r||r.classList.add("d-flex"),Swal.showValidationMessage("<div class='text-danger'>"+(n||ew.language.phrase("FailedToExport"))+"</div>"),X.trigger("export",[{type:"fail",url:e}])})).always((function(){X.trigger("export",[{type:"always",url:e}])}))}}))}function le(e){if(ew.LAZY_LOAD){var t=e&&e.target?e.target:document;a.default(t).find("img.ew-lazy").each((function(){this.src=this.dataset.src})),X.trigger("lazyload")}}function se(e){if(ew.USE_COLORBOX){var t=e&&e.target?e.target:document,n=Object.assign({},ew.lightboxSettings,{title:ew.language.phrase("LightboxTitle"),current:ew.language.phrase("LightboxCurrent"),previous:ew.language.phrase("LightboxPrevious"),next:ew.language.phrase("LightboxNext"),close:ew.language.phrase("LightboxClose"),xhrError:ew.language.phrase("LightboxXhrError"),imgError:ew.language.phrase("LightboxImgError")});a.default(t).find(".ew-lightbox").each((function(){var e=a.default(this);e.colorbox(Object.assign({rel:e.data("rel")},n))}))}}function de(e){if(ew.EMBED_PDF){var t=e&&e.target?e.target:document,n=Object.assign({},ew.PDFObjectOptions);a.default(t).find(".ew-pdfobject").not(":has(.pdfobject)").each((function(){var e=a.default(this),t=e.data("url"),r=e.html();t&&PDFObject.embed(t,this,Object.assign(n,{fallbackLink:r}))}))}}function ue(e){var t=e&&e.target?e.target:document,n=a.default(t);n.find("input[data-toggle=tooltip],textarea[data-toggle=tooltip],select[data-toggle=tooltip]").each((function(){var e=a.default(this);e.tooltip(Object.assign({html:!0,placement:"bottom",sanitizeFn:ew.sanitizeFn},e.data()))})),n.find("a.ew-tooltip-link").each(it),n.find(".ew-tooltip").tooltip({placement:"bottom",sanitizeFn:ew.sanitizeFn}),n.find(".ew-popover").popover({sanitizeFn:ew.sanitizeFn})}function ce(e){if(a.default.isString(e))try{return JSON.parse(e)}catch(e){return}return e}function fe(e){return"function"==typeof e}function pe(e,t,a,n){return a?Swal.fire(z(z({},ew.sweetAlertSettings),{},{html:e,input:"text",confirmButtonText:ew.language.phrase("OKBtn"),showCancelButton:fe(t),cancelButtonText:ew.language.phrase("CancelBtn"),inputValidator:n||function(e){if(!e)return ew.language.phrase("EnterValue")}})).then((function(e){fe(t)&&t(e.value)})):Swal.fire(z(z({},ew.sweetAlertSettings),{},{html:"<div>"+e+"</div>",confirmButtonText:ew.language.phrase("OKBtn"),showCancelButton:fe(t),cancelButtonText:ew.language.phrase("CancelBtn")})).then((function(e){fe(t)&&t(e.value)}))}function he(e){e=Object.assign({},ew.toastOptions,e),X.Toasts("create",e);var t=e.position;return a.default("#toastsContainer"+t[0].toUpperCase()+t.substring(1)).children().first()}function ge(e,t){return he({class:"ew-toast bg-"+(t=t||"danger"),title:ew.language.phrase(t),body:e,autohide:"success"==t&&ew.autoHideSuccessMessage,delay:"success"==t?ew.autoHideSuccessMessageDelay:500})}function me(e){if(e instanceof U)return e.$element[0];var t=a.default(e),n=t.closest(".ew-form");return n[0]||(n=t.closest(".ew-grid, .ew-multi-column-grid").find(".ew-form").not(".ew-pager-form")),n[0]}function we(e,t,n,r){var i=this.$element?this.$element[0]:this.form?this.form:null;if(i&&((this.htmlForm?this:J.get(i.id))&&(this.form&&a.default.isUndefined(e)?e=J.get(this).getList(this.name).childFields.slice():a.default.isString(e)&&(e=De(e,i)),e&&(!Array.isArray(e)||0!=e.length)))){var o=this,l=Promise.resolve();if(Array.isArray(e)&&a.default.isString(e[0])){for(var s=[],d=0,u=e.length;d<u;d++){var c=e[d].split(" ");if(1==c.length&&o.form){var f=Ye(o,!1).match(/^([xy]\d*_)/);f&&(e[d]=e[d].replace(/^([xy]\d*_)/,f[1]))}var p=De(e[d],i),h=[];if(s.push(p),2==c.length&&Array.isArray(p)){var g=a.default(p);g.each((function(){if(!h.includes(this.name)){h.push(this.name);var e=g.filter("[name='"+this.name+"']"),a=e.attr("type"),i=["radio","checkbox"].includes(a)?e.get():e[0];l=l.then(ve.bind(o,i,t,n,r))}}))}else l=l.then(ve.bind(o,p,t,n,r))}e=s;var m=J.get(o).getList(o.name);m&&Array.isArray(m.autoFillTargetFields)&&m.autoFillTargetFields[0]&&(l=l.then(rt.bind(null,o)))}else l=l.then(ve.bind(o,e,t,n,r));return l.then((function(){X.trigger("updatedone",[{source:o,target:e}])}))}}function ve(e,t,n,r){var i=Ye(e,!1);if(i){var o=me(e);if(o&&o.id){var l=J.get(o.id);if(l){var s=this,d=Array.from(arguments),u=$e(e),c=i.match(/^([xy])(\d*)_/),f=c?c[1]:"",p=c?c[2]:"",h=[],g=l.getList(i),m=a.default(e).data("updating",!0);if(!m.data("hidden")){if(a.default.isUndefined(t)&&(t=g.parentFields.slice(),""!=p))for(var w=0,v=t.length;w<v;w++){var b=t[w].split(" ");1==b.length&&(t[w]=t[w].replace(/^x_/,"x"+p+"_"))}if(Array.isArray(t)&&t.length>0)if(Array.isArray(t[0]))h=t;else if(a.default.isString(t[0]))for(w=0,v=t.length;w<v;w++)h.push($e(t[w],o));xe(e)||He(e);var y=function(t){var a=Ye(e);t.forEach((function(t){var n={data:t,parents:h,valid:!0,name:a,form:o};X.trigger("addoption",[n]),n.valid&&We(e,t,o)})),e.list&&e.render(),qe(e,u),!1!==r&&(!e.options&&e.length?m.first().triggerHandler("click"):m.first().trigger("change"))};a.default.isUndefined(n)&&(n=g.ajax);var E=function(){/(s(ea)?rch|summary|crosstab)$/.test(o.id)&&"x"==f&&(d[0]=i.replace(/^x_/,"y_"),we.apply(s,d))};if(!a.default.isBoolean(n)||Array.isArray(g.lookupOptions)&&g.lookupOptions.length>0){var _=g.lookupOptions;return y(_),E(),_}var S=Ye(e),x=Object.assign({page:g.page,field:g.field,ajax:"updateoption",language:ew.LANGUAGE_ID,name:S},be("#p_"+i,o));xe(e)&&s.htmlForm?x.v0=u[0]||St():(e.options&&!["select-one","select-multiple"].includes(e.type)||Se(e))&&(x.v0=u[0]?e.multiple?u.join(ew.MULTIPLE_OPTION_SEPARATOR):u[0]:St());w=0;for(var O=h.length;w<O;w++)x["v"+(w+1)]=h[w].join(ew.MULTIPLE_OPTION_SEPARATOR);return a.default.ajax(ae(ew.API_LOOKUP_ACTION),{type:"POST",dataType:"json",data:x,async:n}).done((function(t){var a=t.records||[];return y(a),E(),m.first().trigger("updated",[Object.assign({},t,{target:e})]),a})).always((function(){return m.data("updating",!1)}))}}}}}function be(e,t){e=e.replace(/\[\]$/,"");var n={},r=((t=a.default.isString(t)?/^#/.test(t)?t:"#"+t:t)?a.default(t).find(e):a.default(e)).val();r&&new URLSearchParams(r).forEach((function(e,t){n[t]=e}));return n}function ye(e){return e&&["1","y","t","true"].includes(e.toLowerCase())}function Ee(e,t,n,r,i,o){if(!e)return!1;var l=a.default(e),s=l.attr("target"),d=l.attr("action"),u=i&&l.find("input[type=checkbox][name='key_m[]']")[0];if(u&&!Ae(e))return Oe(ew.language.phrase("NoRecordSelected")),!1;if(r)if(a.default("iframe.ew-export").remove(),"email"==n&&(t+=("&"+a.default(o).serialize()).replace(/&export=email/i,"")),u){a.default("<iframe>").attr("name","ew-export-frame").addClass("ew-export d-none").appendTo(Q);try{l.append(a.default("<input type='hidden'>").attr({name:"custom",value:"1"})).attr({action:t,target:"ew-export-frame"}).find("input[name=exporttype]").val(n).end().trigger("submit")}finally{l.attr({target:s||"",action:d}).find("input[name=custom]").remove()}}else a.default("<iframe>").attr({name:"ew-export-frame",src:t}).addClass("ew-export d-none").appendTo(Q);else l.find("input[name=exporttype]").val(n),["xml","print"].includes(n)?l.trigger("submit"):oe(d,l.serialize());return!1}function _e(e){var t=a.default(e);return t.is(":hidden")&&t.data("editor")}function Se(e){var t=a.default(e);return t.is(":hidden")&&t.data("lookup")}function xe(e){var t=a.default(e);return t.is(":hidden")&&t.data("autosuggest")}function Oe(e,t,a){return Swal.fire(z(z({},ew.sweetAlertSettings),{},{html:'<p class="text-'+(a||"danger")+'">'+e+"</p>",confirmButtonText:ew.language.phrase("OKBtn")})).then((function(e){fe(t)&&t(e.value)}))}function Te(e){if(e){var t=a.default(e);if(!Fe(t)){if(_e(e))return t.data("editor").focus();if(Se(e))return t.parent().find(".ew-lookup-text").trigger("focus");!e.options&&e.length?e=t[0]:xe(e)&&(e=e.input),a.default(e).trigger("focus")}}}function Ae(e){return a.default(e).find("input[type=checkbox][name='key_m[]']:checked",e).length>0}function Ie(e){e&&e.form&&a.default(e.form.elements).filter("input[type=checkbox][name^="+e.name+"_], [type=checkbox][name="+e.name+"]").not(e).not(":disabled").prop("checked",e.checked)}function Ce(e,t){var n=a.default(t),r=n.closest(".ew-table");r[0]&&(n.data("selected")?n.removeClass(r.data("rowhighlightclass")||"ew-table-highlight-row").removeClass(r.data("roweditclass")||"ew-table-edit-row").addClass(r.data("rowselectclass")||"ew-table-select-row"):[ew.ROWTYPE_ADD,ew.ROWTYPE_EDIT].includes(n.data("rowtype"))?n.removeClass(r.data("rowselectclass")||"ew-table-select-row").removeClass(r.data("rowhighlightclass")||"ew-table-highlight-row").addClass(r.data("roweditclass")||"ew-table-edit-row"):n.removeClass(r.data("rowselectclass")||"ew-table-select-row").removeClass(r.data("roweditclass")||"ew-table-edit-row").removeClass(r.data("rowhighlightclass")||"ew-table-highlight-row"))}function Pe(e){a.default(e.rows).each((function(e,t){var n=a.default(t);!n.data("checked")&&n.data("selected")&&(n.data("selected",!1),Ce(0,t))}))}function ke(e){var t=a.default(e);if(t.closest(".ew-table")[0]){var n=t.closest(".ew-table > tbody > tr");n.siblings("[data-rowindex='"+n.data("rowindex")+"']").addBack().each((function(e,t){var n=a.default(t);n.data("selected",n.data("checked"))}))}}function Ne(e){var t=a.default(e),n=t.closest(".ew-table")[0];if(n){Pe(n);var r=t.closest(".ew-table > tbody > tr");r.siblings("[data-rowindex='"+r.data("rowindex")+"']").addBack().each((function(e,t){a.default(t).data("selected",!0),Ce(0,t)}))}}function Re(e,t,n){var r=a.default(t),i=a.default(t.rows);if(t&&t.rows&&(n||!r.data("isset"))&&0!=t.tBodies.length){var o=function(e){var t=a.default(this);if(!t.data("selected")&&![ew.ROWTYPE_ADD,ew.ROWTYPE_EDIT].includes(t.data("rowtype"))){var n=t.closest(".ew-table");if(!n[0])return;t.siblings("[data-rowindex='"+t.data("rowindex")+"']").addBack().each((function(e,t){a.default(t).addClass(n.data("rowhighlightclass")||"ew-table-highlight-row")}))}},l=function(e){var t=a.default(this);t.data("selected")||[ew.ROWTYPE_ADD,ew.ROWTYPE_EDIT].includes(t.data("rowtype"))||t.siblings("[data-rowindex='"+t.data("rowindex")+"']").addBack().each(Ce)},s=function(e){var t=a.default(this),n=t.closest(".ew-table")[0],r=a.default(e.target);if(n&&!r.hasClass("btn")&&!r.hasClass("ew-preview-row-btn")&&!r.is(":input")&&!t.data("checked")){var i=t.data("selected");Pe(n),t.siblings("[data-rowindex='"+t.data("rowindex")+"']").addBack().each((function(e,t){a.default(t).data("selected",!i),Ce(0,t)}))}},d=i.filter("[data-rowindex=1]").length||i.filter("[data-rowindex=0]").length||1,u=i.filter(":not(.ew-template)").each((function(){a.default(this.cells).removeClass("ew-table-last-row").last().addClass("ew-table-last-col")})).get(),c=r.parentsUntil(".ew-grid","."+ew.RESPONSIVE_TABLE_CLASS)[0];if(u.length)for(var f=1;f<=d;f++){var p=u[u.length-f];a.default(p.cells).each((function(){this.rowSpan==f&&a.default(this).addClass("ew-table-last-row").toggleClass("ew-table-border-bottom",!!c&&c.clientHeight>t.offsetHeight)}))}var h=r.closest("form")[0],g=h&&a.default(h.elements).filter("input#action:not([value^=grid])").length>0;a.default(t.tBodies[t.tBodies.length-1].rows).filter(":not(.ew-template):not(.ew-table-preview-row)").each((function(e){var t=a.default(this);g&&!t.data("isset")&&([ew.ROWTYPE_ADD,ew.ROWTYPE_EDIT].includes(t.data("rowtype"))&&t.on("mouseover",(function(){this.edit=!0})).addClass("ew-table-edit-row"),t.on("mouseover",o).on("mouseout",l).on("click",s),t.data("isset",!0));var n=e%(2*d)<d;t.toggleClass("ew-table-row",n).toggleClass("ew-table-alt-row",!n)})),Le(e,r.closest(".ew-grid")[0],n),r.data("isset",!0)}}function Le(e,t,n){var r=a.default(t);!t||!n&&r.data("isset")||(0!=(r.find("table.ew-multi-column-table").length?r.find("td[data-rowindex]").length:r.find("table.ew-table > tbody").first().children("tr:not(.ew-table-preview-row, .ew-template)").length)||r.find(".ew-grid-upper-panel, .ew-grid-lower-panel")[0]||r.hide(),r.find(".ew-grid-middle-panel:visible").hasClass(ew.RESPONSIVE_TABLE_CLASS)&&r.width()>a.default(".content").width()&&(r.addClass("d-flex"),r.closest(".ew-detail-pages").addClass("d-block"),r.closest(".ew-form").addClass("w-100"),ew.USE_OVERLAY_SCROLLBARS&&r.find(".ew-grid-middle-panel:not(.ew-preview-middle-panel)").overlayScrollbars(ew.overlayScrollbarsOptions)),r.data("isset",!0))}function De(e,t){var n;if(a.default.isObject(e)&&e.dataset)n="[data-table='"+e.dataset.table+"'][data-field='"+e.dataset.field+"']:not([name^=o]):not([name^='x$'])";else if(a.default.isString(e)){n="[name='"+e+"']";var r=e.split(" ");2==r.length&&(n="[data-table='"+r[0]+"'][data-field='"+Ye(r[1])+"']:not([name^=o]):not([name^='x$'])")}n="input"+n+",select"+n+",textarea"+n+",button"+n;var i=(t=a.default.isString(t)?/^#/.test(t)?t:"#"+t:t)?a.default(t).find(n):a.default(n);return 1==i.length&&i.is(":not([type=checkbox]):not([type=radio])")?i[0]:i.get()}function Me(e,t){t=a.default.isString(t)?"#"+t:t;var n="#"+e.replace(/([\$\[\]])/g,"\\$1")+",[name='"+e+"']";return t?a.default(t).find(n)[0]:a.default(n).first()[0]}function je(e,t){for(;e=e.parentNode;)if(e&&1==e.nodeType&&(!t||t(e)))return e;return null}function Fe(e){var t=a.default(e);return"none"==t.css("display")&&!t.closest(".dropdown-menu")[0]&&!Se(e)&&!xe(e)&&!_e(e)||null!=je(e,(function(e){return"none"==e.style.display&&!e.classList.contains("tab-pane")&&!e.classList.contains("collapse")}))}function Ue(e,t){return String(e).toLowerCase()==String(t).toLowerCase()}function $e(e,t){var n;a.default.isString(e)?n=2==e.split(" ").length?De(e):De(e,t):n="radio"==e.type||"checkbox"==e.type?De(e):e;if(n.options)return n.list?n.values:Array.prototype.filter.call(n.options,(function(e){return e.selected&&""!==e.value})).map((function(e){return e.value}));if(a.default.isNumber(n.length))return a.default(n).filter(":checked").map((function(){return this.value})).get();if(ew.isHiddenTextArea(n))return a.default(n).data("editor").save(),[n.value];var r=a.default(n).data();return r.lookup&&r.multiple?n.value.split(ew.MULTIPLE_OPTION_SEPARATOR):[n.value]}function Be(e,t){var n;a.default.isString(e)?n=2==e.split(" ").length?De(e):De(e,t):n=e;return xe(n)?[n.input.value]:Se(n)?a.default(n).parent().find(".ew-lookup-text .ew-option").map((function(){return a.default(this).text().trim()})).get():n.options?Array.prototype.filter.call(n.options,(function(e){return e.selected&&""!==e.value})).map((function(e){return e.text})):a.default.isNumber(n.length)?a.default(n).filter(":checked").map((function(){return a.default(this).parent().text()})).get():ew.isHiddenTextArea(n)?(a.default(n).data("editor").save(),[n.value]):[n.value]}function He(e){if(e.options){var t="select-multiple"==e.type||e.hasAttribute("data-dropdown")||!1===ye(e.getAttribute("data-pleaseselect"))||e.length>0&&""!=e.options[0].value?0:1;if(e.list)e.removeAll();else for(var a=e.length-1;a>=t;a--)e.remove(a);xe(e)&&(e.input.value="",e.value="")}}function Ye(e,t){var n=a.default.isString(e)?e:a.default(e).attr("name")||a.default(e).attr("id");return!1!==t?n.replace(/\[\]$/,""):n}function Ke(e,t){var n=a.default(t).data("value-separator");return Array.isArray(n)?n[e-1]:n||", "}function Ve(e,t){for(var n=e.df,r=2;r<=4;r++)if(e["df"+r]&&""!=e["df"+r]){var i=Ke(r-1,t);if(a.default.isUndefined(i))break;a.default.isValue(n)&&(n+=i),n+=e["df"+r]}return n}function Ge(e){return ew.OPTION_HTML_TEMPLATE.replace(/\{value\}/g,e)}function ze(e,t){if(e.length>(t||ew.MAX_OPTION_COUNT))return ew.language.phrase("CountSelected").replace("%s",e.length);if(e.length){for(var a="",n=0;n<e.length;n++)a+=Ge(e[n]);return a}return ew.language.phrase("PleaseSelect")}function We(e,t,a){var n,r=J.get(a.id),i=Ye(e),o=r.getList(i),l=t.lf,s={lf:t.lf,df1:t.df,df2:t.df2,df3:t.df3,df4:t.df4};n=o.template&&!xe(e)?o.template.render(s,ew.jsRenderHelpers):Ve(t,e)||l;var d,u={data:s,name:i,form:a.$element,value:l,text:n};e.options&&(e.list?d=new B(u.value,u.text):((d=document.createElement("option")).value=u.value,d.innerHTML=u.text),u=z(z({},u),{},{option:d}),X.trigger("newoption",[u]),e.list?e.add(u.option.value,u.option.text):e.add(u.option));return u.text}function qe(e,t){if(e&&t){var n=a.default(e);if(Array.isArray(t))if(e.options){if(e.list?e.value=t:(n.val(t),"select-one"==e.type&&-1==e.selectedIndex&&(e.selectedIndex=0)),xe(e)&&1==t.length)for(var r,i=K(e.options||[]);!(r=i()).done;){var o=r.value;if(o.value==t[0]){e.value=o.value,e.input.value=o.text;break}}else if(Se(e)){for(var l,s=[],d=[],u=e.options||[],c=K(t);!(l=c()).done;)for(var f,p=l.value,h=K(u);!(f=h()).done;){var g=f.value;if(p==g.value){s.push(g.value),d.push(Ge(g.text));break}}n.val(s.join(ew.MULTIPLE_OPTION_SEPARATOR)),n.parent().find(".ew-lookup-text").html(ze(d,n.data("maxcount")))}}else e.type&&(e.value=t.join(ew.MULTIPLE_OPTION_SEPARATOR));if(function(e){if(!a.default(e).data("autoselect"))return!1;var t=me(e);if(t){if(/s(ea)?rch$/.test(t.id))return!1;var n=J.get(t.id).getList(e.id);return!n||0!=n.parentFields.length}return!1}(e)&&e.options&&(e.list||"select-one"!=e.type||2!=e.options.length||e.options[1].selected?1!=e.options.length||e.options[0].selected||(e.options[0].selected=!0):e.options[1].selected=!0,e.list&&e.render(),xe(e))){var m=e.options||[];1==m.length&&(e.value=m[0].value,e.input.value=m[0].text)}}}function Je(){a.default("form.ew-form.ew-wait").removeClass("ew-wait").each((function(){var e=J.get(this.id);e&&(e.multiPage&&e.multiPage.lastPageSubmit||e.enableForm())})),ew.removeSpinner(),X.data("_ajax",!1)}function Xe(e,t){var a=e.replace(/<head>[\s\S]*<\/head>/,"").matchAll(/<script([^>]*)>([\s\S]*?)<\/script\s*>/gi);Array.from(a).filter((function(e,a){var n=e[2];if(/(\s+type\s*=\s*['"]*text\/javascript['"]*)|^((?!\s+type\s*=).)*$/i.test(e[1])&&n)return!e[1].includes('class="ew-apply-template"')||(Ze(n,"scr_"+t+"_0"),!1)})).forEach((function(e,a){return Ze(e[2],"scr_"+t+"_"+(a+1))}))}function Qe(e){for(var t,a=K(e.matchAll(/<script([^>]*)>([\s\S]*?)<\/script\s*>/gi));!(t=a()).done;){var n=t.value,r=n[0];/(\s+type\s*=\s*['"]*text\/javascript['"]*)|^((?!\s+type\s*=).)*$/i.test(n[1])&&(e=e.replace(r,""))}return e}function Ze(e,t){var a=document.createElement("SCRIPT");return t&&(a.id=t),a.text=e,document.body.appendChild(a)}function et(e){e&&a.default("script[id^='scr_"+e+"_']").remove()}function tt(e){var t=Qe(e).match(/<body[\s\S]*>[\s\S]*<\/body>/i);return t?a.default(t[0]).not("div[id^=ew].modal, #ew-tooltip, #ew-drilldown-panel, #cookie-consent, #template-upload, #template-download"):a.default()}function at(e){return e.options?Array.prototype.map.call(e.options,(function(e){return[e.value,e.text]})):[]}function nt(e){var t=a.default(this),n=t.data("args");et("ModalDialog");var r=t.removeData("args").find(".modal-body form").data("form");r&&r.destroyEditor();var i=t.find(".modal-body").html("");i.ewjtable&&i.ewjtable("instance")&&i.ewjtable("destroy"),t.find(".modal-footer .btn-primary").off(),t.find(".modal-dialog").removeClass((function(e,t){var a=t.match(/table\-\w+/);return a?a[0]:""})),t.data("showing",!1),t.data("url",null),n&&n.reload&&window.location.reload()}function rt(e){var t=J.get(e).$element[0];if(t){var n=$e(e),r=Ye(e),i=r.match(/^([xy])(\d*)_/),o=i?i[2]:"",l=J.get(e).getList(r),s=l.autoFillTargetFields,d=function(n){for(var i=n&&n.records||"",l=i?i[0]:[],d=0;d<s.length;d++){var u=De(s[d].replace(/^x_/,"x"+o+"_"),t);if(u){var c=a.default.isValue(l["af"+d])?String(l["af"+d]):"",f={results:i,result:l,data:c,form:t,name:r,target:s[d],cancel:!1,trigger:!0};if(a.default(e).trigger("autofill",[f]),f.cancel)continue;c=f.data,u.options?(qe(u,c.split(",")),xe(u)?(u.input.value=c,we.call(J.get(t.id),u)):Se(u)&&we.call(J.get(t.id),u)):_e(u)?(u.value=c,a.default(u).data("editor").set()):u.value=c,f.trigger&&a.default(u).trigger("change")}}return l};if(n.length>0&&""!=n[0]){var u=Object.assign({page:l.page,field:l.field,ajax:"autofill",v0:n[0],language:ew.LANGUAGE_ID},be("#p_"+r,t)),c=l.parentFields.slice();if(""!=o)for(var f=0,p=c.length;f<p;f++){1==(n=c[f].split(" ")).length&&(c[f]=c[f].replace(/^x_/,"x"+o+"_"))}for(var h=c.map((function(e){return $e(e,t)})),g=(f=0,h.length);f<g;f++)u["v"+(f+1)]=h[f].join(ew.MULTIPLE_OPTION_SEPARATOR);return a.default.post(ae(ew.API_LOOKUP_ACTION),u,d,"json")}return d()}}function it(e,t){var n=a.default(t),r=a.default("#"+n.data("tooltip-id")),i=n.data("trigger")||"hover",o=n.data("placement")||(ew.CSS_FLIP?"left":"right");r[0]&&(""!=r.text().trim()||r.find("img[src!='']")[0])&&(n.data("bs.popover")||n.popover({html:!0,placement:o,trigger:i,delay:100,container:a.default("#ew-tooltip")[0],content:r.html(),sanitizeFn:ew.sanitizeFn}).on("show.bs.popover",(function(e){var t,r=a.default(n.data("bs.popover").getTipElement()).css("z-index",9999);(t=n.data("tooltip-width"))&&r.css("max-width",parseInt(t,10)+"px")})))}function ot(){return location.href.split("#")[0].split("?")[0]}function lt(e){return pt(e,"us",ew.DATE_SEPARATOR)}function st(e){return pt(e,"usshort",ew.DATE_SEPARATOR)}function dt(e){return pt(e,"std",ew.DATE_SEPARATOR)}function ut(e){return pt(e,"stdshort",ew.DATE_SEPARATOR)}function ct(e){return pt(e,"euro",ew.DATE_SEPARATOR)}function ft(e){return pt(e,"euroshort",ew.DATE_SEPARATOR)}function pt(e,t,a){if(!e||""==e.length)return!0;var n=(e=e.replace(/ +/g," ").trim()).split(" ");if(n.length>0){var r,i,o,l,s;if(i=(r=/^(\d{4})-([0][1-9]|[1][0-2])-([0][1-9]|[1|2]\d|[3][0|1])$/).exec(n[0]))o=i[1],l=i[2],s=i[3];else{var d=vt(a);switch(t){case"std":r=new RegExp("^(\\d{4})"+d+"([0]?[1-9]|[1][0-2])"+d+"([0]?[1-9]|[1|2]\\d|[3][0|1])$");break;case"stdshort":r=new RegExp("^(\\d{2})"+d+"([0]?[1-9]|[1][0-2])"+d+"([0]?[1-9]|[1|2]\\d|[3][0|1])$");break;case"us":r=new RegExp("^([0]?[1-9]|[1][0-2])"+d+"([0]?[1-9]|[1|2]\\d|[3][0|1])"+d+"(\\d{4})$");break;case"usshort":r=new RegExp("^([0]?[1-9]|[1][0-2])"+d+"([0]?[1-9]|[1|2]\\d|[3][0|1])"+d+"(\\d{2})$");break;case"euro":r=new RegExp("^([0]?[1-9]|[1|2]\\d|[3][0|1])"+d+"([0]?[1-9]|[1][0-2])"+d+"(\\d{4})$");break;case"euroshort":r=new RegExp("^([0]?[1-9]|[1|2]\\d|[3][0|1])"+d+"([0]?[1-9]|[1][0-2])"+d+"(\\d{2})$")}if(!r.test(n[0]))return!1;var u=n[0].split(a);switch(t){case"std":case"stdshort":o=ht(u[0]),l=u[1],s=u[2];break;case"us":case"usshort":o=ht(u[2]),l=u[0],s=u[1];break;case"euro":case"euroshort":o=ht(u[2]),l=u[1],s=u[0]}}if(!gt(o,l,s))return!1}return!(n.length>1&&!yt(n[1]))}function ht(e){return 2==e.length?e>ew.UNFORMAT_YEAR?"19"+e:"20"+e:e}function gt(e,t,a){e=parseInt(e,10),t=parseInt(t,10),a=parseInt(a,10);var n=[4,6,9,11].includes(t)?30:31;return 2==t&&(n=e%4>0||e%100==0&&e%400>0?28:29),bt(a,1,n)}function mt(e){if(!(e=String(e))||0==e.length)return!0;e=e.trim();var t=vt(ew.THOUSANDS_SEP),a=vt(ew.DECIMAL_POINT);return new RegExp("^[+-]?(\\d{1,3}("+(t?t+"?":"")+"\\d{3})*("+a+"\\d+)?|"+a+"\\d+)$").test(e)}function wt(e){if(e=String(e),""!=ew.THOUSANDS_SEP){var t=new RegExp(vt(ew.THOUSANDS_SEP),"g");e=e.replace(t,"")}return""!=ew.DECIMAL_POINT&&(e=e.replace(ew.DECIMAL_POINT,".")),parseFloat(e)}function vt(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}function bt(e,t,n){return!e||0==e.length||((a.default.isNumber(t)||a.default.isNumber(n))&&mt(e)&&(e=wt(e)),!(!a.default.isNull(t)&&e<t)&&!(!a.default.isNull(n)&&e>n))}function yt(e){return!e||0==e.length||(e=e.trim(),new RegExp("^(0\\d|1\\d|2[0-3])"+vt(ew.TIME_SEPARATOR)+"[0-5]\\d(( ("+vt(ew.language.phrase("AM"))+"|"+vt(ew.language.phrase("PM"))+"))|("+vt(ew.TIME_SEPARATOR)+"[0-5]\\d(\\.\\d+|[+-][\\d:]+)?)?)$","i").test(e))}function Et(e){return!e||0==e.length||/^[\w.%+-]+@[\w.-]+\.[A-Z]{2,18}$/i.test(e.trim())}function _t(e){var t,n,r;try{var i=window.parent;n=i.document,r=i.Swal}catch(e){n=window.document,r=window.Swal}var o=null!==(t=null==e?void 0:e.target)&&void 0!==t?t:n,l=a.default(o).find("div.ew-message-dialog:hidden").first(),s=l.length?l.text():"";if(a.default.isString(e)&&(s=a.default("<div>"+e.trim()+"</div>").text()),""!=s.trim())return l.length&&["success","info","warning","danger"].forEach((function(e,t){var a=l.find(".alert-"+e).toggleClass("alert-"+e),n=a.find(".alert-heading").detach(),r=a.children(":not(.icon)");if(a.find(".icon").remove(),a[0]){var i=parseInt(r.css("width"),10);i>0&&r.first().css("width","auto");var o=he({class:"ew-toast bg-"+e,title:n[0]?n.html():ew.language.phrase(e),body:a.html(),autohide:"success"==e&&ew.autoHideSuccessMessage,delay:"success"==e?ew.autoHideSuccessMessageDelay:500});i>0&&o.css("max-width",i)}else;})),a.default.isString(e)?r.fire(z(z({},ew.sweetAlertSettings),{},{html:e})):void 0}function St(){return Math.floor(100001*Math.random())+1e5}function xt(e,t,n){var r=new URL(e),i=r.searchParams;if(i.set(ew.TOKEN_NAME_KEY,ew.TOKEN_NAME),i.set(ew.ANTIFORGERY_TOKEN_KEY,ew.ANTIFORGERY_TOKEN),Ue(n,"post")){var o=t?a.default(t):a.default("<form></form>").appendTo("body");o.attr({action:ar[0],method:"post"}),i.forEach((function(e,t){a.default('<input type="hidden">').attr({name:t,value:ew.sanitize(e)}).appendTo(o)})),o.trigger("submit")}else window.location=ne(r.toString())}X.ajaxSend((function(e,t,a){var n=a.url;n.match(/\/(\w+preview|session)\?/i)&&Z();var r=ae(),i=n.startsWith(r),o=i||n.startsWith(ot());!o&&n.match(/^http/i)&&(o=new URL(n).hostname==q.hostname);if(o)if(i&&ew.API_JWT_TOKEN&&!ew.IS_WINDOWS_AUTHENTICATION&&t.setRequestHeader(ew.API_JWT_AUTHORIZATION_HEADER,"Bearer "+ew.API_JWT_TOKEN),"GET"==a.type){var l=a.url.split("?");(s=new URLSearchParams(l[1])).set(ew.TOKEN_NAME_KEY,ew.TOKEN_NAME),s.set(ew.ANTIFORGERY_TOKEN_KEY,ew.ANTIFORGERY_TOKEN),l[1]=s.toString(),a.url=l[0]+(l[1]?"?"+l[1]:"")}else{var s;if(a.data instanceof FormData)a.data.set(ew.TOKEN_NAME_KEY,ew.TOKEN_NAME),a.data.set(ew.ANTIFORGERY_TOKEN_KEY,ew.ANTIFORGERY_TOKEN);else(s=new URLSearchParams(a.data)).set(ew.TOKEN_NAME_KEY,ew.TOKEN_NAME),s.set(ew.ANTIFORGERY_TOKEN_KEY,ew.ANTIFORGERY_TOKEN),a.data=s.toString()}})),X.ajaxStart((function(){X.data("_ajax",!0),ew.addSpinner(),a.default("form.ew-form").addClass("ew-wait").each((function(){var e=J.get(this.id);e&&(e.multiPage&&e.multiPage.lastPageSubmit||e.disableForm())}))})),X.ajaxStop(Je).ajaxError(Je);var Ot={__proto__:null,currentUrl:q,forms:J,AjaxLookup:$,AutoSuggest:H,Form:U,SelectionListOption:B,removeSpinner:function(){var e=Q.data("_spinner");e&&e.cancel(),e=a.default.later(500,null,(function(){!0!==X.data("_ajax")&&(Z(),a.default("form.ew-form").each((function(){var e=J.get(this.id);if(e)return e.focus(),!1})))})),Q.data("_spinner",e)},createSelect:function(e){var t;e.selectId.includes("$rowindex$")||a.default.fn.select2.amd.require(["select2/utils","select2/results","select2/dropdown/infiniteScroll","select2/dropdown/hidePlaceholder","select2/dropdown/selectOnClose"],(t=e,function(e,n,r,i,o){var l=Object.assign({},ew.selectOptions,t);if(null==l.resultsAdapter&&(l.resultsAdapter=n,l.dropdown&&l.columns&&l.customOption&&(l.resultsAdapter=e.Decorate(l.resultsAdapter,function(e){return function(){function t(){}var n=t.prototype;return n.render=function(e){var t=a.default('<div class="select2-results__options '+this.options.get("containerClass")+'" role="listbox"></div>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t,t},n.displayMessage=function(e,t){var n=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var r=a.default('<div role="alert" aria-live="assertive" class="select2-results__option"></div>');if(t.message.includes("<")&&t.message.includes(">"))r.append(t.message);else{var i=this.options.get("translations").get(t.message);r.append(n(i(t.args)))}r[0].className+=" select2-results__message",this.$results.append(r)},n.append=function(e,t){var n=this;if(this.hideLoading(),null!=t.results&&0!==t.results.length){t.results=this.sort(t.results);for(var r=this.options.get("columns"),i=t.results.length,o=this.$results.find("."+this.options.get("rowClass")).last(),l=0;l<t.results.length;l++){var s=t.results[l],d=this.option(s);if(o.length&&o.children().length!=r||(o=a.default('<div class="'+this.options.get("rowClass")+'"></div>'),this.$results.append(o)),o.append(d),l==i-1)for(var u=r-o.children().length,c=0;c<u;c++)o.append('<div class="'+this.options.get("colClass")+'"></div>')}}else 0===this.$results.children().length&&(this.$element.data("updating")?(this.trigger("results:message",{message:'<div class="spinner-border spinner-border-sm text-primary ew-select-spinner" role="status"><span class="sr-only">'+ew.language.phrase("Loading")+"</span></div> "+ew.language.phrase("Loading")}),this.$element.one("updatedone",(function(){return n.$element.select2("close").select2("open")}))):this.trigger("results:message",{message:"noResults"}))},n.option=function(t,a){var n=document.createElement("div");n.className="select2-results__option "+this.options.get("cellClass");var r={role:"option","aria-selected":"false"},i=window.Element.prototype.matches||window.Element.prototype.msMatchesSelector||window.Element.prototype.webkitMatchesSelector;for(var o in(null!=a.element&&i.call(a.element,":disabled")||null==a.element&&a.disabled)&&(delete r["aria-selected"],r["aria-disabled"]="true"),null==a.id&&delete r["aria-selected"],null!=a._resultId&&(n.id=a._resultId),a.title&&(n.title=a.title),r){var l=r[o];n.setAttribute(o,l)}return this.template(a,n),e.StoreData(n,"data",a),n},t}()}(e)),l.iconClass&&l.multiple&&!l.templateResult&&(l.templateResult=function(e){return'<label class="'+l.iconClass+' ew-dropdown-label">'+e.text+"</label>"})),l.templateResult||(l.templateResult=function(e){return(e.element instanceof HTMLOptionElement?e.element.innerHTML:"")||e.text}),null!=l.ajax&&(l.resultsAdapter=e.Decorate(l.resultsAdapter,r)),null!=l.placeholder&&(l.resultsAdapter=e.Decorate(l.resultsAdapter,i)),l.selectOnClose&&(l.resultsAdapter=e.Decorate(l.resultsAdapter,o))),a.default.isObject(l.ajax)){var s=new ew.AjaxLookup(l.ajax);l.ajax={url:function(e){var t=e.page?(e.page-1)*(settings.limit||ew.AUTO_SUGGEST_MAX_ENTRIES):-1;return s.getUrl(e.term,t)},type:"POST",dataType:"json",data:s.generateRequest.bind(s),delay:l.debounce,processResults:function(e){return{results:s.transform(e).map((function(e){return{id:e.lf,text:s.formatResult({lf:e.lf,df:e.df,df2:e.df2,df3:e.df3,df4:e.df4})}})),pagination:{more:e.length<s.recordCount}}}}}var d=a.default("select[data-select2-id='"+l.selectId+"']").select2(l);d.on("select2:open",(function(){var e;null===(e=a.default(".select2-container--open .select2-search__field")[0])||void 0===e||e.focus()})),l.multiple&&l.minimumResultsForSearch===1/0&&d.on("select2:opening select2:closing",(function(){a.default(".select2-container--open .select2-search__field").prop("disabled",!0)}))}))},initIcons:ee,initPasswordOptions:te,getApiUrl:ae,sanitizeUrl:ne,setSessionTimer:function(){var e,t,n,r,i=ew.SESSION_KEEP_ALIVE_INTERVAL>0||ew.IS_LOGGEDIN&&ew.IS_AUTOLOGIN,o=function(){a.default.get(ae(ew.API_SESSION_ACTION),{rnd:St()},(function(e){e&&a.default.isObject(e)&&(ew.TOKEN_NAME=e[ew.TOKEN_NAME_KEY],ew.ANTIFORGERY_TOKEN=e[ew.ANTIFORGERY_TOKEN_KEY],e.JWT&&(ew.API_JWT_TOKEN=e.JWT))}))},l=function(){r=ew.SESSION_TIMEOUT_COUNTDOWN,(e=ew.SESSION_TIMEOUT-ew.SESSION_TIMEOUT_COUNTDOWN)<0&&(e=0,r=ew.SESSION_TIMEOUT),t&&t.cancel()},s=function(){if(n&&n.cancel(),r>0){var e,t='<p class="text-danger">'+ew.language.phrase("SessionWillExpire")+"</p>";t.includes("%m")&&t.includes("%s")?t=(t=t.replace("%m",'<span class="ew-session-counter-minute">'+Math.floor(r/60)+"</span>")).replace("%s",'<span class="ew-session-counter-second">'+r%60+"</span>"):t.includes("%s")&&(t=t.replace("%s",'<span class="ew-session-counter-second">'+r+"</span>")),Swal.fire(z(z({},ew.sweetAlertSettings),{},{html:t,showConfirmButton:!0,confirmButtonText:ew.language.phrase("OKBtn"),timer:1e3*r,timerProgressBar:!0,allowOutsideClick:!1,allowEscapeKey:!1,willOpen:function(){e=setInterval((function(){var e=Swal.getContent(),t=e.querySelector(".ew-session-counter-minute"),a=e.querySelector(".ew-session-counter-second"),n=Math.round(Swal.getTimerLeft()/1e3);t&&a?(t.textContent=Math.floor(n/60),a.textContent=n%60):a&&(a.textContent=n)}),1e3)},onClose:function(){clearInterval(e)}})).then((function(e){e.value?(o(),!i&&ew.SESSION_TIMEOUT>0&&d()):e.dismiss===Swal.DismissReason.timer&&(l(),window.location=ne(ew.TIMEOUT_URL+"?expired=1"))}))}},d=function(){l(),t=a.default.later(1e3*e,null,s)};if(i){var u=ew.SESSION_KEEP_ALIVE_INTERVAL>0?ew.SESSION_KEEP_ALIVE_INTERVAL:ew.SESSION_TIMEOUT-ew.SESSION_TIMEOUT_COUNTDOWN;u<=0&&(u=60),n=a.default.later(1e3*u,null,o,null,!0)}else ew.SESSION_TIMEOUT>0&&d()},initExportLinks:re,initMultiSelectCheckboxes:ie,fileDownload:oe,lazyLoad:le,updateDropdownPosition:function(){var e=a.default(".select2-container--open").prev(".ew-select").data("select2");e&&(e.dropdown._positionDropdown(),e.dropdown._resizeDropdown())},initLightboxes:se,initPdfObjects:de,initTooltips:ue,parseJson:ce,searchOperatorChanged:function(e){var t=a.default(e).closest("[id^=r_], [id^=xsc_]"),n=e.id.substr(2),r=t.find(".ew-search-field"),i=t.find(".ew-search-field2"),o=i.find("[name='y_"+n+"'], [name='y_"+n+"[]']").length,l=t.find(".ew-search-cond"),s=l.length,d=t.find(".ew-search-and"),u=t.find(".ew-search-operator").find("[name='z_"+n+"']").val(),c=t.find(".ew-search-operator2"),f=c.find("[name='w_"+n+"']").val(),p="BETWEEN"==u,h=["IS NULL","IS NOT NULL"].includes(u),g=["IS NULL","IS NOT NULL"].includes(f),m=!o||p,w=h,v=!p&&(!s||g);l.toggleClass("d-none",m).find(":input").prop("disabled",m),d.toggleClass("d-none",!p),c.toggleClass("d-none",m).find(":input").prop("disabled",m),r.toggleClass("d-none",w).find(":input").prop("disabled",w),i.toggleClass("d-none",v).find(":input").prop("disabled",v)},isFunction:fe,prompt:pe,toast:he,showToast:ge,getForm:me,hasFormData:function(e){for(var t=a.default(e).find("[name^=x_],[name^=y_],[name^=z_],[name^=w_],[name=psearch]").filter(":enabled").get(),n=0,r=t.length;n<r;n++){var i=t[n];if(/^(z|w)_/.test(i.name)){if(/^IS/.test(a.default(i).val()))return!0}else if("checkbox"==i.type||"radio"==i.type){if(i.checked)return!0}else if("select-one"==i.type||"select-multiple"==i.type){if(a.default(i).val())return!0}else if(("text"==i.type||"hidden"==i.type||"textarea"==i.type)&&i.value)return!0}return!1},setSearchType:function(e,t){var n=a.default(e),r=n.closest("form"),i="";return r.find("input[name=psearchtype]").val(t||""),i="="==t?ew.language.phrase("QuickSearchExactShort"):"AND"==t?ew.language.phrase("QuickSearchAllShort"):"OR"==t?ew.language.phrase("QuickSearchAnyShort"):ew.language.phrase("QuickSearchAutoShort"),r.find("#searchtype").html(i+(i?"&nbsp;":"")),n.closest("ul").find("li").removeClass("active"),n.closest("li").addClass("active"),!1},updateOptions:we,getUserParams:be,applyTemplate:function(e,t,n,r,i){var o={data:i||{},id:e,template:t,class:n,export:r,enabled:!0};if(X.trigger("rendertemplate",[o]),o.enabled)if(document.body.replaceWith){var l,s=null===(l=document.getElementById(t))||void 0===l?void 0:l.content;if(!s)return;if(s.querySelectorAll(".ew-slot").forEach((function(e){var t=document.getElementById(e.name||e.id);t&&t.content?(e.dataset.rowspan>1&&Array.prototype.slice.call(t.content.childNodes).forEach((function(t){return t.rowSpan=e.dataset.rowspan})),e.replaceWith(t.content)):e.remove()})),a.default.views){var d=s.textContent,u=d.includes("{{")&&d.includes("}}");if(!u){var c=ew.jsRenderAttributes.map((function(e){return"["+e+"*='{{']["+e+"*='}}']"})).join(",");u=s.querySelector(c)}if(u){var f=Array.prototype.slice.call(s.querySelectorAll("script"));f.forEach((function(e){return e.remove()}));var p=document.createElement("div");p.appendChild(s);var h=p.innerHTML,g=a.default.templates(h);document.getElementById(e).innerHTML=g.render(o.data,ew.jsRenderHelpers),f.forEach((function(e){return document.body.appendChild(e)}))}else document.getElementById(e).appendChild(s)}else document.getElementById(e).appendChild(s)}else Oe(ew.language.phrase("UnsupportedBrowser")||"Your browser is not supported by this page.");r&&"print"!=r&&a.default((function(){var t=a.default("meta[http-equiv='Content-Type']"),n="<html><head>",i=a.default("#"+e);i.children(0).is("div[id^=ct_]")&&(i=i.children(0)),t[0]&&(n+="<meta http-equiv='Content-Type' content='"+t.attr("content")+"'>"),n+="pdf"==r?"<link rel='stylesheet' href='"+ew.PDF_STYLESHEET_FILENAME+"'>":"<style>"+a.default.ajax({async:!1,type:"GET",url:ew.PROJECT_STYLESHEET_FILENAME}).responseText+"</style>",n+="</head><body>",a.default(".ew-chart-top").each((function(){n+=a.default(this).html()})),n+=i.html(),a.default(".ew-chart-bottom").each((function(){n+=a.default(this).html()})),n+="</body></html>";var l=ot(),s={customexport:r,data:n,filename:o.class};if(s[ew.TOKEN_NAME]=ew.ANTIFORGERY_TOKEN,"email"==r){var d=q.searchParams.toString()+"&"+a.default.param(s);a.default.post(l,d,(function(e){_t(e)}))}else oe(l,s);window.parent.jQuery("body").css("cursor","default")}))},toggleGroup:function(e){for(var t,n=a.default(e),r=n.closest("tr"),i="tr",o=1;o<=6;o++){var l=1==o?"":"-"+o,s=r.data("group"+l);a.default.isValue(s)&&(t=o,""!=s&&(i+="[data-group"+l+"='"+String(s).replace(/'/g,"\\'")+"']"))}n.hasClass("icon-collapse")?(a.default(i).slice(1).addClass("ew-rpt-grp-hide-"+t),n.toggleClass("icon-expand icon-collapse")):(a.default(i).slice(1).removeClass("ew-rpt-grp-hide-"+t),n.toggleClass("icon-expand icon-collapse"))},convertToBool:ye,valueChanged:function(e,t,a,n){var r=De("x"+t+"_"+a,e),i=Me("o"+t+"_"+a,e),o=Me("fn_x"+t+"_"+a,e);if("hidden"==(null==r?void 0:r.type)&&!i)return!1;if(!i&&(!r||Array.isArray(r)&&0==r.length))return!1;var l=function(e){return $e(e).join()};if(i&&r)if(n){if(ye(l(i))===ye(l(r)))return!1}else if(l(i)==l(o||r))return!1;return!0},setLanguage:function(e){var t=a.default(e),n=t.val()||t.data("language");n&&(q.searchParams.set("language",n),window.location=ne(q.toString()))},submitAction:function(e,t){var n=e.target||e.srcElement,r=a.default(n),i=t.f||r.closest("form")[0]||currentForm,o=a.default(i),l=t.key,s=t.action,d=t.url||ot(),u=t.msg,c=t.data,f=t.success,p=!t.method||Ue(t.method[0],"p"),h=!t.select&&!t.key||t.select&&Ue(t.select[0],"m");if(h&&!o[0])return!1;if(h&&!Ae(o[0]))return pe('<p class="text-danger">'+ew.language.phrase("NoRecordSelected")+"</p>"),!1;var g=function(e){_t(e)},m=function(){if(p){if(s&&a.default("<input>").attr({type:"hidden",name:"useraction",value:s}).appendTo(o),a.default.isObject(c))for(var e in c){var t=o.find("input[type=hidden][name='"+e+"']");t[0]?t.val(c[e]):a.default("<input>").attr({type:"hidden",name:e,value:c[e]}).appendTo(o)}if(!h&&a.default.isObject(l))for(var e in l)a.default("<input>").attr({type:"hidden",name:e,value:l[e]}).appendTo(o);o.prop("action",d).trigger("submit")}else c=a.default.isObject(c)?a.default.param(c):a.default.isString(c)?c:"",s&&(c+="&useraction="+s+"&ajax="+s),h?c+="&"+o.find("input[name='key_m[]']:checked").serialize():l&&(c+="&"+(a.default.isObject(l)?a.default.param(l):l)),f&&a.default.isString(f)&&(f=window[f]),fe(f)?a.default.post(d,c,f):a.default.isObject(f)?(f.data=c,f.method=f.method||"POST",f.success=f.success||g,a.default.ajax(d,f)):a.default.post(d,c,g)};return u?pe(u,(function(e){e&&m()})):m(),!1},export:Ee,removeSpaces:function(e){return/^(<(p|br)\/?>(&nbsp;)?(<\/p>)?)?$/i.test(e.replace(/\s/g,""))?"":e},isHiddenTextArea:_e,isModalLookup:Se,isAutoSuggest:xe,alert:Oe,clearError:function(e){if(e.jquery){var t=e.attr("type");e="checkbox"==t||"radio"==t?e.get():e[0]}a.default(e).closest("[id^=el_], .form-group").find(".invalid-feedback").html("")},onError:function(e,t,n,r){if(t.jquery){var i=t.attr("type");t="checkbox"==i||"radio"==i?t.get():t[0]}else t instanceof L&&(t=t.element);return a.default(t).closest("[id^=el_], .form-group").find(".invalid-feedback").append("<p>"+n+"</p>"),r&&Te(t),null==e||e.makeVisible(t),!1},setFocus:Te,setInvalid:function(e){if(e){var t=a.default(e);if(!Fe(t)){!e.options&&e.length&&(e=t[0]);var n=t.closest(".form-group, [id^='el']");xe(e)?n.find(".ew-auto-suggest").addClass("is-invalid").one("click keydown",(function(){n.find(".is-invalid").removeClass("is-invalid")})):Se(e)?n.find(".input-group").addClass("is-invalid").one("click keydown",(function(){n.find(".is-invalid").removeClass("is-invalid")})):"checkbox"==e.type||"radio"==e.type?t.addClass("is-invalid").one("click keydown",(function(){n.find(".is-invalid").removeClass("is-invalid")})):(t.addClass("is-invalid").parent().one("click keydown",(function(){n.find(".is-invalid").removeClass("is-invalid")})),t.closest(".input-group").addClass("is-invalid"))}}},hasValue:function(e){return""!=$e(e).join("")},isMaskedPassword:function(e){var t=a.default(e).val();return t&&t.match(/^\*+$/)},sort:function(e,t,a){return e.shiftKey&&!e.ctrlKey?t=t.split("?")[0]+"?cmd=resetsort":2==a&&e.ctrlKey&&(t+="&ctrl=1"),window.location=ne(t),!0},confirmDelete:function(e){return Ne(e),pe(ew.language.phrase("DeleteConfirmMsg"),(function(t){t&&e.href?window.location=ne(e.href):ke(e)})),!1},keySelected:Ae,selectAllKey:function(e){Ie(e);var t=a.default(e).closest(".ew-table")[0];t&&a.default(t.tBodies).each((function(){a.default(this.rows).each((function(t,n){var r=a.default(n);r.is(":not(.ew-template):not(.ew-table-preview-row)")&&(r.data({selected:e.checked,checked:e.checked}),Ce(t,n))}))}))},selectAll:Ie,updateSelected:function(e){return a.default(e).find("input[type=checkbox][name^=u_]:checked,input:hidden[name^=u_][value=1]").length>0},setColor:Ce,clearSelected:Pe,clearDelete:ke,clickDelete:Ne,clickMultiCheckbox:function(e){var t=e.target||e.srcElement,n=a.default(t),r=n.closest(".ew-table")[0];if(r){Pe(r);var i=n.closest(".ew-table > tbody > tr");i.siblings("[data-rowindex='"+i.data("rowindex")+"']").addBack().each((function(e,n){a.default(n).data("checked",t.checked).data("selected",t.checked).find("input[type=checkbox][name='key_m[]']").each((function(){this!=t&&(this.checked=t.checked)})),Ce(0,n)})),e.stopPropagation()}},setupTable:Re,setupGrid:Le,addGridRow:function(e){var t=a.default(e).closest(".ew-grid"),n=t.find("table.ew-table").last(),r=n.parent("div"),i=n.find("tr.ew-template");if(!(e&&t[0]&&n[0]&&i[0]))return!1;var o=a.default(n[0].rows).last();n.find("td.ew-table-last-row").removeClass("ew-table-last-row");var l=i.clone(!0,!0).removeClass("ew-template"),s=t.find("div.ew-form[id^=f][id$=grid]");s[0]||(s=t.find("form.ew-form[id^=f][id$=list]"));var d=s.is("div")?"_"+s.attr("id"):"",u=s.find("#key_count"+d),c=parseInt(u.val(),10)+1;l.attr({id:"r"+c+l.attr("id").substring(2),"data-rowindex":c});var f=i.find("script:contains('$rowindex$')");l.children("td").each((function(){a.default(this).find("*").each((function(){a.default.each(this.attributes,(function(e,t){t.value=t.value.replace(/\$rowindex\$/g,c)}))}))})),l.find(".ew-icon").closest("a, button").removeData("bs.tooltip").tooltip({container:"body",placement:"bottom",trigger:"hover",sanitizeFn:ew.sanitizeFn}),u.val(c).after(a.default("<input>").attr({type:"hidden",id:"k"+c+"_action"+d,name:"k"+c+"_action"+d,value:"insert"})),o.after(l),f.each((function(){Ze(this.text.replace(/\$rowindex\$/g,c))}));var p=s.data("form");return p&&(p.initEditors(),p.initUpload()),Re(-1,n[0],!0),r.scrollTop(r[0].scrollHeight),!1},deleteGridRow:function(e,t){var n=a.default(e).tooltip("hide").removeData("bs.tooltip"),r=n.closest(".ew-grid, .ew-multi-column-grid"),i=n.closest("tr, div[data-rowindex]"),o=i.closest(".ew-table");if(!e||!r[0]||!i[0])return!1;var l=parseInt(i.data("rowindex"),10),s=r.find("div.ew-form[id^=f][id$=grid]");s[0]||(s=r.find("form.ew-form[id^=f][id$=list]"));var d=s.data("form");if(!s[0]||!d)return!1;var u=s.is("div")?"_"+s.attr("id"):"",c="#key_count"+u,f=function(){if(i.remove(),r.is(".ew-grid")&&Re(-1,o[0],!0),l>0){var e=s.find("#k"+l+"_action"+u);e[0]?e.val("insert"==e.val()?"insertdelete":"delete"):s.find(c).after(a.default("<input>").attr({type:"hidden",id:"k"+l+"_action"+u,name:"k"+l+"_action"+u,value:"delete"}))}};return fe(d.emptyRow)&&d.emptyRow(t)?f():pe(ew.language.phrase("DeleteConfirmMsg"),(function(e){e&&f()})),!1},htmlEncode:function(e){return String(e).replace(/&/g,"&amp;").replace(/\"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},getElements:De,getElement:Me,getAncestorBy:je,isHidden:Fe,sameText:Ue,sameString:function(e,t){return String(e)==String(t)},getValue:function(e,t){if(!e)return"";var n;if((n=a.default.isString(e)?2==e.split(" ").length?De(e):De(e,t):"radio"==e.type||"checkbox"==e.type?De(e):e).options){if(n.list){var r=n.values;return n.multiple?r:r[0]||""}var i=Array.prototype.filter.call(n.options,(function(e){return e.selected&&""!==e.value})).map((function(e){return e.value}));return"select-multiple"==n.type?i:i[0]||""}if(a.default.isNumber(n.length)){var o=a.default(n).filter(":checked").map((function(){return this.value})).get();return 1==n.length?o[0]:o}if(ew.isHiddenTextArea(n))return a.default(n).data("editor").save(),n.value;var l=a.default(n).data();return l.lookup&&l.multiple?n.value.split(ew.MULTIPLE_OPTION_SEPARATOR):n.value},getOptionValues:$e,getOptionTexts:Be,clearOptions:He,getId:Ye,valueSeparator:Ke,displayValue:Ve,optionHtml:Ge,optionsHtml:ze,newOption:We,selectOption:qe,executeScript:Xe,stripScript:Qe,addScript:Ze,removeScript:et,getContent:tt,getOptions:at,addOptionDialogShow:function(e){var t=ew.addOptionDialog||a.default("#ew-add-opt-dialog").on("hidden.bs.modal",(function(){et(t.data("args").el);var e=t.removeData("args").find(".modal-body form").data("form");e&&e.destroyEditor(),t.find(".modal-body").html(""),t.find(".modal-footer .btn-primary").off(),t.data("showing",!1)}));if(t[0]){if(!t.data("showing")){t.data("showing",!0);var n=function(e){var n,r=e,i=t.data("args"),o=J.get(i.lnk),l=t.find(".modal-body form input[name='"+ew.API_OBJECT_NAME+"']").val(),s=i.el,d=/^x(\d+)_/,u=s.match(d),c=u?u[0]:"x_",f=u?u[1]:-1,p=s.replace(d,"x_"),h=o.getList(s);if(a.default.isString(e)&&(r=ce(e)),null!==(n=r)&&void 0!==n&&n.success&&r[l]){t.modal("hide");var g=r[l],m=o.$element[0],w=De(s,m);if(w){var v=h.linkField,b=h.displayFields.slice(),y=h.filterFields.slice(),E=h.parentFields.slice();E.forEach((function(e,t){1==e.split(" ").length&&(E[t]=E[t].replace(/^x_/,c))}));var _=""!=v?g[v]:"",S={lf:_};if(b.forEach((function(e,t){e in g&&(S["df"+(t||"")]=g[e])})),y.forEach((function(e,t){e in g&&(S["ff"+(t||"")]=g[e])})),_&&b.length>0&&S.df){null===h.ajax&&h.lookupOptions.push(S);var x=E.map((function(e){return $e(e,m)}));i={data:S,parents:x,valid:!0,name:Ye(w),form:m};if(X.trigger("addoption",[i]),i.valid){var O=at(w),T=[],A=We(w,S,m);if(w.options)if(w.options[w.options.length-1].selected=!0,w.list&&(w.render(),a.default(w.target).find("input").last().trigger("focus")),xe(w))a.default(w).val(_).trigger("change"),a.default(w.input).val(A).trigger("focus");else if(Se(w)){var I=a.default(w),C=a.default(Me("lu_"+i.name,m));if(w.multiple){var P=a.default(w).val(),k=(T=[],String(_));if(""!==P&&(T=P.split(ew.MULTIPLE_OPTION_SEPARATOR)),!T.includes(k)){T.push(k),I.val(T.join()).trigger("change");var N=C.html(),R=C.find(".ew-option").map((function(){return a.default(this).html()})).get();R.length?(R.push(A),C.html(ze(R,I.data("maxcount")))):N==ew.language.phrase("PleaseSelect")?C.html(Ge(A)):N&&C.html(ew.language.phrase("CountSelected").replace("%s",T.length))}}else I.val(_).trigger("change"),C.html(A)}else a.default(w).trigger("change").trigger("focus");var L=a.default(m),D=L.is("div")?"_"+L.attr("id"):"",M=L.find("#key_count"+D).val();if(M>0)for(var j=1;j<=M;j++)if(j!=f){var F=De(p.replace(/^x/,"x"+j),m),U=at(F);T=[];JSON.stringify(O)==JSON.stringify(U)&&(We(F,S,m),F.options&&w.list&&F.render())}}}}}else{var $;if(null!==($=r)&&void 0!==$&&$.error){var B;a.default.isString(r.error)?ge(r.error):a.default.isString(null===(B=r.error)||void 0===B?void 0:B.description)&&ge(r.error.description)}else{var H,Y,K=a.default("<div></div>").html(e).find("div.ew-message-dialog");if(K[0])H=K.html();else(H=(null===(Y=r)||void 0===Y?void 0:Y.failureMessage)||e)&&""!=String(H).trim()||(H=ew.language.phrase("InsertFailed"));ge(H)}}},r=function(e){t.modal("hide"),Oe("Server Error "+e.status+": "+e.statusText)},i=function(e){var t=ew.addOptionDialog.find(".modal-body form")[0],i=J.get(t.id),o=e?e.target:null,l=a.default(o);return i.canSubmit()&&(l.prop("disabled",!1).removeClass("disabled"),Q.css("cursor","wait"),a.default.post(ae([ew.API_ADD_ACTION,t.elements[ew.API_OBJECT_NAME].value]),a.default(t).serialize(),n).fail(r).always((function(){i.enableForm(),l.prop("disabled",!1).removeClass("disabled"),Q.css("cursor","default")}))),!1};t.modal("hide"),t.data("args",e);a.default.get(e.url,(function(n){var r=J.get(e.lnk),o="x_",l=e.el.match(/^(x\d+_)/);l&&(o=l[1]);var s=r.getList(e.el),d=s.parentFields.slice().map((function(e){return 1==e.split(" ").length?e.replace(/^x_/,o):e})),u=r.htmlForm,c=d.map((function(e){return $e(e,u)})),f=d.map((function(e){return Be(e,u)})),p=s.filterFieldVars.slice();t.find(".modal-title").html(a.default(e.lnk).closest(".ew-add-opt-btn").data("title")),t.find(".modal-body").html(Qe(n)),(u=t.find(".modal-body form")[0])&&(a.default(u).on("keydown",(function(e){if("Enter"==e.key&&"TEXTAREA"!=e.target.nodeName)return i()})),c.forEach((function(e,t){var n;(n=De(p[t],u))&&(n.options||n.length?a.default(n).first().one("updated",(function(){return qe(n,e)})):qe(n,e))}))),ew.addOptionDialog=t.modal("show"),t.find(".modal-footer .btn-primary").click(i).focus(),Xe(n,e.el),u&&c.forEach((function(e,t){var a=De(p[t],u);a&&(xe(a)?(a.value=e[0],a.input.value=f[t][0],a.add(e[0],f[t][0],!0)):Se(a)?(a.value=e[0],we.call(J.get(u.id),a)):a.options||a.length||(a.value=e[0]))})),t.trigger("load.ew")})).fail(r)}}else Oe("DIV #ew-add-opt-dialog not found.")},modalDialogHide:nt,modalDialogShow:function(e){a.default(e.lnk).tooltip("hide");var t=e.f;if(t&&!Ae(t))return pe('<p class="text-danger">'+ew.language.phrase("NoRecordSelected")+"</p>"),!1;var n=ew.modalDialog||a.default("#ew-modal-dialog").on("hidden.bs.modal",nt);if(n.data("showing")&&n.data("url")==e.url)return!1;n.data({showing:!0,url:e.url}),e.reload=!1,"modal-sm"===e.size?n.find(".modal-dialog").toggleClass("modal-sm",!0).toggleClass("modal-lg modal-xl",!1):""===e.size?n.find(".modal-dialog").toggleClass("modal-sm modal-lg modal-xl",!1):"modal-lg"===e.size?n.find(".modal-dialog").toggleClass("modal-lg",!0).toggleClass("modal-sm modal-xl",!1):n.find(".modal-dialog").toggleClass("modal-xl",!0).toggleClass("modal-sm modal-lg",!1);var r=function(){var e=n.data("args"),t=a.default(e.lnk);return e.caption||t.data("caption")||t.data("original-title")||""},i=function(){var e=n.data("args");return a.default.isNull(e.btn)?"":e.btn&&""!=e.btn?ew.language.phrase(e.btn):r()},o=function(e){n.modal("hide"),e.status&&Oe("Server Error "+e.status+": "+e.statusText)},l=function(e){Q.css("cursor","default")},s=function(e){var t=null,r=e.url,i=e.reload;if((r||i)&&(t=function(){if(r)if(e.modal&&!function(e){var t=a.default("<a>",{href:e})[0];return window.location.pathname.endsWith(t.pathname)}(r)){var t=n.data("args");t.reload=!0,e.caption&&(t.caption=e.caption),t.btn=e.view?null:"",n.data("args",t),r+=(r.split("?").length>1?"&":"?")+"modal=1&rnd="+St(),Q.css("cursor","wait"),a.default.get(r).done(c).fail(o).always(l)}else n.modal("hide"),window.location=ne(r);else i&&(n.modal("hide"),window.location.reload())}),a.default.isString(e.failureMessage))Oe(e.failureMessage);else if(a.default.isString(e.warningMessage))Oe(e.warningMessage,t,"warning");else if(a.default.isString(e.message))Oe(e.message,t,"body");else if(a.default.isString(e.successMessage))Oe(e.successMessage,t,"success");else if(e.error){var s,d;a.default.isString(e.error)?Oe(e.error):a.default.isString(null===(s=e.error)||void 0===s?void 0:s.message)?Oe(e.error.message):a.default.isString(null===(d=e.error)||void 0===d?void 0:d.description)&&Oe(e.error.description)}else t&&t()},d=function(e){var t=ce(e);if(a.default.isObject(t))s(t);else{var r=tt(e);if(r.length){var o=n.find(".modal-body").html(r),l="",d=o.find("#confirm"),c=o.find("#conflict");if(c&&"1"==c.val())l+='<button type="button" id="btn-overwrite" class="btn btn-primary ew-btn">'+ew.language.phrase("OverwriteBtn")+"</button>",l+='<button type="button" id="btn-reload" class="btn btn-default ew-btn">'+ew.language.phrase("ReloadBtn")+"</button>",l+='<button type="button" class="btn btn-default ew-btn" data-dismiss="modal">'+ew.language.phrase("CancelBtn")+"</button>",n.find(".modal-footer").html(l),n.find(".modal-footer #btn-overwrite").on("click",{action:"overwrite"},u),n.find(".modal-footer #btn-reload").on("click",{action:"show"},u);else if(d&&"confirm"==d.val())l+='<button type="button" class="btn btn-primary ew-btn">'+ew.language.phrase("ConfirmBtn")+"</button>",l+='<button type="button" class="btn btn-default ew-btn">'+ew.language.phrase("CancelBtn")+"</button>",n.find(".modal-footer").html(l),n.find(".modal-footer .btn-primary").click(u).focus(),n.find(".modal-footer .btn-default").on("click",{action:"cancel"},u);else{var f=i();f&&(l+='<button type="button" class="btn btn-primary ew-btn">'+f+"</button>"),l+='<button type="button" class="btn btn-default ew-btn" data-dismiss="modal">'+ew.language.phrase("CancelBtn")+"</button>",n.find(".modal-footer").html(l),n.find(".modal-footer .btn-primary").addClass("ew-submit").click(u).focus()}Xe(e,"ModalDialog"),n.trigger("load.ew")}else e&&(n.modal("hide"),ew.alert(e))}},u=function(e){var t=n.find(".modal-body form")[0],r=a.default(t),i=J.get(t.id),s=e&&e.data?e.data.action:null,u=e?e.target:null;if(u){if(u.classList.contains("disabled"))return!1;i.enableForm=function(){a.default(u).prop("disabled",!1).removeClass("disabled")},i.disableForm=function(){a.default(u).prop("disabled",!0).addClass("disabled")}}var f=t.elements.action;return s&&f&&(f.value=s),"cancel"==s?a.default.post(r.attr("action"),r.serialize(),c).fail(o).always(l):i.canSubmit()&&(Q.css("cursor","wait"),a.default.post(r.attr("action"),r.serialize(),d).fail(o).always((function(){i.enableForm(),l()}))),!1};n.modal("hide"),n.data("args",e);var c=function(e){var t=ce(e);if(a.default.isObject(t))s(t);else{var o=n.data("args"),l=a.default(o.lnk);n.find(".modal-title").html(r());var d="",c=i();c&&(d+='<button type="button" class="btn btn-primary ew-btn">'+c+"</button>"),""!=d?d+='<button type="button" class="btn btn-default ew-btn" data-dismiss="modal">'+ew.language.phrase("CancelBtn")+"</button>":d='<button type="button" class="btn btn-default ew-btn" data-dismiss="modal">'+ew.language.phrase("CloseBtn")+"</button>",n.find(".modal-footer").html(d);var f=tt(e);n.find(".modal-body").html(f);var p=l.data("table");p&&n.find(".modal-dialog").addClass("table-"+p);var h=n.find(".modal-footer .btn-primary").addClass("ew-submit").click(u);n.find(".modal-body form").on("keydown",(function(e){if("Enter"==e.key&&"TEXTAREA"!=e.target.nodeName)return h.click(),!1})),ew.modalDialog=n.modal("show"),Xe(e,"ModalDialog"),n.trigger("load.ew"),h.focus()}};Q.css("cursor","wait");var f=e.url;if(t){var p=a.default(t);t.elements.modal||a.default("<input>").attr({type:"hidden",name:"modal",value:"1"}).appendTo(p),a.default.post(f,p.serialize(),c).fail(o).always(l)}else f+=(f.split("?").length>1?"&":"?")+"modal=1&rnd="+St(),a.default.get(f,c).fail(o).always(l);return!1},modalLookupShow:function(e){var t=e.el,n=me(e.lnk);if(n&&t){var r=ew.modalLookupDialog||a.default("#ew-modal-lookup-dialog").on("hidden.bs.modal",nt);if(r[0]){if(!r.data("showing")){r.data("showing",!0);var i,o,l=a.default(n).find("[id='"+t+"']"),s=r.find(".modal-body"),d=a.default(e.lnk),u=d.closest(".ew-lookup-list").find(".ew-lookup-text").trigger("focus"),c=Ye(t,!1),f=c.match(/^([xy])(\d*)_/),p=f?f[2]:"",h=J.get(n.id).getList(t),g=function(e){return"OK"==e.result&&Array.isArray(e.records)&&e.records.forEach((function(t,n){var r;Array.isArray(t)?r={lf:t[0],df1:t[1],df2:t[2],df3:t[3],df4:t[4]}:a.default.isObject(t)&&(r={lf:t.lf,df1:t.df,df2:t.df2,df3:t.df3,df4:t.df4});var i=Ve(t,l);h.template?r.df=h.template.render(r,ew.jsRenderHelpers):r.df=i,r.txt=i,e.records[n]=r})),e},m=function(e,t){if(xe(l)){var a=l[0];a.add(e,t,!0),a.input.value=t}},w=function(){return function(t){var r=[],i=[],o=[],d=[],f=!e.m&&e.srch;if(s.ewjtable("selectedRows").each((function(){var e=a.default(this).data("record");r.push(e.lf),i.push(e.df),o.push(e.df),d.push(e.txt)})),t.sort().join()===r.sort().join())u.html(ze(o,l.data("maxcount"))),m(r.join(),d.join(", ")),l.val(f?i.join(", "):r.join()).trigger("change");else{var p=Object.assign({page:h.page,field:h.field,ajax:"modal",keys:t},be("#p_"+c,n));Q.css("cursor","wait"),a.default.ajax(ae(ew.API_LOOKUP_ACTION),{type:"POST",dataType:"json",data:p}).done(g).then((function(e){if("OK"==e.result&&Array.isArray(e.records)){for(var t,a=[],n=[],r=[],i=[],o=K(e.records);!(t=o()).done;){var s=t.value;a.push(s.lf),n.push(s.df),r.push(s.df),i.push(s.txt)}u.html(ze(r,l.data("maxcount"))),m(a.join(),i.join(", ")),l.val(f?n.join(", "):a.join()).trigger("change")}})).always((function(){Q.css("cursor","default")}))}}(y),r.modal("hide"),!1};r.modal("hide"),r.data("args",e);var v=[],b=l.val(),y=""!==b?b.split(ew.MULTIPLE_OPTION_SEPARATOR):[],E=Object.assign({page:h.page,field:h.field},be("#p_"+c,n)),_=h.parentFields.slice();if(""!=p)for(var S=0,x=_.length;S<x;S++){1==_[S].split(" ").length&&(_[S]=_[S].replace(/^x_/,"x"+p+"_"))}if(_.length>0)for(S=0,x=_.length;S<x;S++)v.push($e(_[S],n));S=0;for(var O=v.length;S<O;S++)E["v"+(S+1)]=v[S].join(ew.MULTIPLE_OPTION_SEPARATOR);Q.css("cursor","wait"),s.ewjtable({paging:!0,pageSize:e.n,pageSizes:[],pageSizeChangeArea:!1,pageList:"minimal",selecting:!0,selectingCheckboxes:!0,multiselect:!!e.m,actions:{listAction:function(e,t){var n=Object.assign({},E,{ajax:"modal",start:t.start,recperpage:t.recperpage});return a.default.isObject(e)&&Object.assign(n,e),a.default.ajax(ae(ew.API_LOOKUP_ACTION),{type:"POST",dataType:"json",data:n}).done(g).always((function(){Q.css("cursor","default")}))}},messages:{serverCommunicationError:ew.language.phrase("ServerCommunicationError"),loadingMessage:'<div class="'+ew.spinnerClass+' m-3 ew-loading" role="status"><span class="sr-only">'+ew.language.phrase("Loading")+"</span></div>",noDataAvailable:ew.language.phrase("NoRecord"),close:ew.language.phrase("CloseBtn"),pagingInfo:ew.language.phrase("Record")+" {0}-{1} "+ew.language.phrase("Of")+" {2}",pageSizeChangeLabel:ew.language.phrase("RecordsPerPage"),gotoPageLabel:ew.language.phrase("Page")},fields:{lf:{key:!0,list:!1},df:{}},recordsLoaded:function(e,t){var n=a.default(e.target).find(".ewjtable-data-row").filter((function(){return y.includes(String(a.default(this).data("record-key")))}));a.default(e.target).ewjtable("selectRows",n)},selectionChanged:function(t,n){var r=n.rows;r&&(e.m||(y=[]),r.each((function(){var e=a.default(this),t=String(e.data("record-key")),n=y.indexOf(t),r=e.hasClass("ewjtable-row-selected");r&&-1==n?y.push(t):!r&&n>-1&&y.splice(n,1)})))}}).ewjtable("load",null,(function(t){"OK"==t.result?(r.find(".modal-title").html(d.attr("title")||d.data("original-title")),r.find(".modal-body .ewjtable thead").toggle(!!e.m),r.find(".modal-footer").html('<button type="button" id="btn-select" class="btn btn-primary ew-btn">'+ew.language.phrase("SelectBtn")+'</button><button type="button" class="btn btn-default ew-btn" data-dismiss="modal">'+ew.language.phrase("CancelBtn")+"</button>"),o=r.find(".modal-header .modal-tools input[name=sv]").off("keyup.ew").on("keyup.ew",(function(e){i&&i.cancel(),i=a.default.later(ew.LOOKUP_DELAY,null,(function(){s.ewjtable("load",{sv:o.val()})}))})),r.find(".modal-footer #btn-select").click(w),ew.modalLookupDialog=r.modal("show"),o.focus()):Oe(t.message)}))}}else Oe("DIV #ew-modal-lookup-dialog not found.")}},importDialogShow:function(e){a.default(e.lnk).tooltip("hide");var t=ew.importDialog||a.default("#ew-import-dialog");if(!t[0])return Oe("DIV #ew-import-dialog not found."),!1;var n,r=t.find("#importfiles"),i=t.find(".modal-body"),o=i.find(":input[id!=importfiles]"),l=i.find(".message"),s=i.find(".progress"),d=function(e,n){var r=a.default("<div>"+e+"</div>");n&&r.addClass(n),l.removeClass("d-none").html(r),"text-danger"==n&&t.find(".modal-footer .btn").prop("disabled",!1)},u=function(){l.addClass("d-none").html("")},c=function(e,t){s.removeClass("d-none").find(".progress-bar").removeClass("bg-success bg-info").addClass(t||"bg-success").attr("aria-valuenow",e).css("width",e+"%").html(e+"%")},f=function(e){try{var a=parseInt(e.count),n=parseInt(e.totalCount),r=e.file;if(n>0&&t.find(".modal-footer .ew-close-btn").data("import-progress")){var i=parseInt(100*a/n);c(i),d(ew.language.phrase("ImportMessageProgress").replace("%t",n).replace("%c",a).replace("%f",r),"text-info")}}catch(e){}},p=function(){var e=ae(ew.API_PROGRESS_ACTION),t={rnd:St()};t[ew.API_FILE_TOKEN_NAME]=r.data(ew.API_FILE_TOKEN_NAME),a.default.get(e,t,f,"json")},h=function(e){var a="";c(100);var n=e.files;if(t.find(".modal-footer .ew-close-btn").data("import-progress",!1),Array.isArray(n))for(var r=0,i=n.length;r<i;r++){var o=n[r],l=o.totalCount||0,u=o.count||0,f=o.successCount||0,p=o.failCount||0;if(""!=a&&(a+="<br>"),o.success)a+=ew.language.phrase("ImportMessageSuccess").replace("%t",l).replace("%c",u).replace("%f",o.file);else{a+=ew.language.phrase("ImportMessageError1").replace("%t",l).replace("%c",u).replace("%f",o.file).replace("%s",f).replace("%e",p),o.error&&(a+=ew.language.phrase("ImportMessageError2").replace("%e",o.error));var h=!0;if(o.failList){var g=0;for(r=1;r<=u&&(o.failList["row"+r]&&(g+=1,a+="<br>"+ew.language.phrase("ImportMessageError3").replace("%i",r).replace("%d",o.failList["row"+r])),!(g>=5));r++);p>5?a+="<br>"+ew.language.phrase("ImportMessageMore").replace("%s",p-5):h=!1}o.log&&h&&(a+="<br>"+ew.language.phrase("ImportMessageError4").replace("%l",o.log)),d(a,"text-danger")}}e.success?(d(a,"text-success"),t.find(".modal-footer .ew-close-btn").data("imported",!0)):(e.error&&(a=e.error),d(a,"text-danger")),s.addClass("d-none").find(".progress-bar").attr("aria-valuenow",0).css("width","0%").html("0%")},g=function(e){t.find(".modal-footer .ew-close-btn").data("import-progress",!1),d(ew.language.phrase("ImportMessageServerError").replace("%s",e.status).replace("%t",e.statusText),"text-danger")},m={session:ew.SESSION_ID};m[ew.TOKEN_NAME_KEY]=ew.TOKEN_NAME,m[ew.ANTIFORGERY_TOKEN_KEY]=ew.ANTIFORGERY_TOKEN;var w=ew.importUploadOptions;return w.acceptFileTypes||(w.acceptFileTypes=new RegExp("\\.("+ew.IMPORT_FILE_ALLOWED_EXT.replace(/,/g,"|")+")$","i")),r.data("blueimpFileupload")||r.fileupload(Object.assign({url:ae(ew.API_UPLOAD_ACTION),dataType:"json",autoUpload:!0,formData:m,singleFileUploads:!1,messages:{acceptFileTypes:ew.language.phrase("UploadErrMsgAcceptFileTypes"),maxFileSize:ew.language.phrase("UploadErrMsgMaxFileSize"),maxNumberOfFiles:ew.language.phrase("UploadErrMsgMaxNumberOfFiles"),minFileSize:ew.language.phrase("UploadErrMsgMinFileSize")},beforeSend:function(e,t){ew.API_JWT_TOKEN&&!ew.IS_WINDOWS_AUTHENTICATION&&e.setRequestHeader(ew.API_JWT_AUTHORIZATION_HEADER,"Bearer "+ew.API_JWT_TOKEN)},done:function(e,i){if(i.result&&i.result.files&&Array.isArray(i.result.files.importfiles)){var l=!0;i.result.files.importfiles.forEach((function(e,t){e.error&&(d(ew.language.phrase("ImportMessageUploadError").replace("%f",e.name).replace("%s",e.error),"text-danger"),l=!1)})),l&&function(e){Q.css("cursor","wait"),r.data(ew.API_FILE_TOKEN_NAME,e),t.find(".modal-footer .ew-close-btn").data("import-progress",!0);var i=ew.API_ACTION_NAME+"=import&"+ew.API_FILE_TOKEN_NAME+"="+encodeURIComponent(e);o.length&&(i+="&"+o.serialize()),a.default.ajax(ot(),{data:i,method:"POST",dataType:"json",beforeSend:function(e,t){n=a.default.later(100,null,p,null,!0)}}).done(h).fail(g).always((function(){Q.css("cursor","default"),n&&n.cancel()}))}(i.result[ew.API_FILE_TOKEN_NAME])}},change:function(e,t){u()},processfail:function(e,t){t.files.forEach((function(e,t){e.error&&d(ew.language.phrase("ImportMessageUploadError").replace("%f",e.name).replace("%s",e.error),"text-danger")}))},fail:function(e,t){d(ew.language.phrase("ImportMessageServerError").replace("%s",t.textStatus).replace("%t",t.errorThrown),"text-danger")},progressall:function(e,t){!function(e){var t=parseInt(100*e.loaded/e.total);c(t,"bg-info"),d(100===t?ew.language.phrase("ImportMessageUploadComplete"):ew.language.phrase("ImportMessageUploadProgress").replace("%p",t),"text-info")}(t)}},w)),t.modal("hide").find(".modal-title").html(e.hdr),t.find(".modal-footer .ew-close-btn").off("click.ew").on("click.ew",(function(){var e=a.default(this);e.data("imported")&&(e.data("imported",!1),window.location.reload())})),u(),ew.importDialog=t.modal("show"),!1},autoFill:rt,tooltip:it,emailDialogShow:function(e){var t=ew.emailDialog||a.default("#ew-email-dialog");if(!t[0])return Oe("DIV #ew-email-dialog not found."),!1;if(e.sel&&!Ae(e.f))return Oe(ew.language.phrase("NoRecordSelected")),!1;var n=t.find(".modal-body form"),r=n.data("form");return r||((r=new U(n.attr("id"))).addFields([["sender",[ew.Validators.required(ew.language.phrase("Sender")),ew.Validators.email]],["recipient",[ew.Validators.required(ew.language.phrase("Recipient")),ew.Validators.emails(ew.MAX_EMAIL_RECIPIENT,ew.language.phrase("EnterProperRecipientEmail"))]],["cc",ew.Validators.emails(ew.MAX_EMAIL_RECIPIENT,ew.language.phrase("EnterProperCcEmail"))],["bcc",ew.Validators.emails(ew.MAX_EMAIL_RECIPIENT,ew.language.phrase("EnterProperBccEmail"))],["subject",ew.Validators.required(ew.language.phrase("Subject"))]]),r.validate=function(){return this.validateFields()},r.submit=function(r){if(!this.validate())return!1;var i=n.serialize(),o="";e.f&&e.sel&&(o=a.default(e.f).find("input[type=checkbox][name='key_m[]']:checked").serialize()),e.key&&(i+="&"+a.default.param(e.key));var l=this.getForm();return e.url?(t.modal("hide"),e.exportid?ew.exportWithCharts(e.el,e.url,e.exportid,l):Ee(e.f,e.url,"email",!0,e.sel,l)):a.default.post(a.default(e.f).attr("action"),i+"&"+o,(function(e){_t(e)})),!0},n.data("form",r)),t.modal("hide").find(".modal-title").html(e.hdr),t.find(".modal-footer .btn-primary").off().click((function(e){e.preventDefault(),r.submit(e)&&t.modal("hide")})),ew.emailDialog=t.modal("show"),!1},showDrillDown:function(e,t,n,r,i){if(e&&e.ctrlKey){var o=n.split("?"),l=new URLSearchParams(o[1]);return l.set("d","2"),xt(o[0]+"?"+l.toString()),!1}var s=a.default.isString(t)?a.default("#"+t):a.default(t),d=s.data("drilldown-placement")||(s.hasClass("ew-chart-canvas")?ew.CSS_FLIP?"left":"right":"bottom");s.tooltip("hide");var u={obj:s[0],id:r,url:n,hdr:i,placement:d};X.trigger("drilldown",[u]);var c=u.url.split("?");u.file=c[0]||"",u.data=c[1]||"",s.data("bs.popover")||s.popover({html:!0,placement:u.placement,trigger:"manual",template:'<div class="popover"><h3 class="popover-header d-none" style="cursor: move;"></h3><div class="popover-body"></div></div>',content:'<div class="'+ew.spinnerClass+' m-3 ew-loading" role="status"><span class="sr-only">'+ew.language.phrase("Loading")+"</span></div>",container:a.default("#ew-drilldown-panel"),sanitizeFn:ew.sanitizeFn,boundary:"viewport"}).on("show.bs.popover",(function(e){s.attr("data-original-title","")})).on("shown.bs.popover",(function(e){if(s.data("args")){var t=s.data("args").data;t+=(t?"&":"")+ew.TOKEN_NAME_KEY+"="+ew.TOKEN_NAME,t+=(t?"&":"")+ew.ANTIFORGERY_TOKEN_KEY+"="+ew.ANTIFORGERY_TOKEN,a.default.ajax({cache:!1,dataType:"html",type:"POST",data:t,url:s.data("args").file,success:function(e){var t=a.default(s.data("bs.popover").getTipElement()).on("mousedown",(function(e){var t=a.default(this).addClass("drag"),n=t.outerHeight(),r=t.outerWidth(),i=t.offset().top+n-e.pageY,o=t.offset().left+r-e.pageX;Q.on("mousemove",(function(e){var a=e.pageY+i-n,l=e.pageX+o-r;t.hasClass("drag")&&t.offset({top:a,left:l})})).on("mouseup",(function(e){t.removeClass("drag")}))}));u.hdr&&t.find(".popover-header").empty().removeClass("d-none").append('<button type="button" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></button>'+u.hdr).find(".close").on("click",(function(){s.popover("hide")}));var n=e.match(/<body[^>]*>([\s\S]*?)<\/body\s*>/i);e=n?n[0]:e;var i=ew.stripScript(e);t.find(".popover-body").html(a.default("<div></div>").html(i).find("#ew-report")).find(".ew-table").each(ew.setupTable),ew.executeScript(e,r),s.popover("update")},error:function(e){e.responseText&&a.default($el.data("bs.popover").getTipElement()).find(".popover-body").empty().append('<p class="text-danger">'+e.responseText+"</p>")}})}})).on("hidden.bs.popover",(function(e){a.default.each(ew.drillDownCharts,(function(e,t){t.dispose()})),ew.drillDownCharts={},ew.removeScript(r)})),s.data("args",u).popover("show")},ajax:function(e,t){if(a.default.isObject(e)&&(e.url||e.action)){var n;e.url?e.url.startsWith(ae())?n=e.url.replace(ae(),"").split("/")[0]:e.url.startsWith(ew.API_URL)&&(n=e.url.replace(ew.API_URL,"").split("/")[0]):(n=e.action,delete e.action);var r=Object.assign({},e),i=function(e){if(a.default.isObject(e)&&"OK"==e.result){var t=e.records;return Array.isArray(t)&&1==t.length?(t=t[0],Array.isArray(t)&&1==t.length?t[0]:t):t}return e},o=r.url||ae(n),l=r.type||([ew.API_LIST_ACTION,ew.API_VIEW_ACTION,ew.API_DELETE_ACTION].includes(n)?"GET":"POST");if(delete r.url,delete r.type,r.dataType="json",!fe(t)){var s=a.default.ajax({url:o,async:!1,type:l,data:r});return i(s.responseJSON)}a.default.ajax({url:o,type:l,data:r,success:function(e){t(i(e))}})}},currentPage:ot,toggleSearchOperator:function(e,t){var a=this.form.elements[e];a&&(a.value=a.value!=t?t:"=")},checkUSDate:lt,checkShortUSDate:st,checkDate:dt,checkShortDate:ut,checkEuroDate:ct,checkShortEuroDate:ft,checkDateDef:function(e){return ew.DATE_FORMAT.match(/^yyyy/)?dt(e):ew.DATE_FORMAT.match(/^yy/)?ut(e):ew.DATE_FORMAT.match(/^m/)&&ew.DATE_FORMAT.match(/yyyy$/)?lt(e):ew.DATE_FORMAT.match(/^m/)&&ew.DATE_FORMAT.match(/yy$/)?st(e):ew.DATE_FORMAT.match(/^d/)&&ew.DATE_FORMAT.match(/yyyy$/)?ct(e):!(!ew.DATE_FORMAT.match(/^d/)||!ew.DATE_FORMAT.match(/yy$/))&&ft(e)},checkDateEx:pt,unformatYear:ht,checkDay:gt,checkInteger:function(e){return!e||0==e.length||!e.includes(ew.DECIMAL_POINT)&&mt(e)},checkNumber:mt,stringToFloat:wt,stringToDate:function(e){var t=e.replace(/^(\d{4})-([0][1-9]|[1][0-2])-([0][1-9]|[1|2]\d|[3][0|1]) (?:(0\d|1\d|2[0-3]):([0-5]\d):([0-5]\d))?$/,"$1 $2 $3 $4 $5 $6").split(" ");return new Date(t[0],t[1]-1,t[2],t[3],t[4],t[5])},escapeRegExChars:vt,checkRange:bt,checkTime:yt,checkPhone:function(e){return!e||0==e.length||/^\(\d{3}\) ?\d{3}( |-)?\d{4}|^\d{3}( |-)?\d{3}( |-)?\d{4}$/.test(e.trim())},checkZip:function(e){return!e||0==e.length||/^\d{5}$|^\d{5}-\d{4}$/.test(e.trim())},checkCreditCard:function(e){if(!e||0==e.length)return!0;var t=e.replace(/\D/g,"");if(0==t.length)return!1;for(var a,n=t.length%2!=1,r=0,i=0,o=t.length;i<o;i++)a=parseInt(t.charAt(i),10),n?(r+=(a*=2)%10,a/10>=1&&r++,n=!1):(r+=a,n=!0);return r%10==0},checkSsn:function(e){return!e||0==e.length||/^(?!000)([0-6]\d{2}|7([0-6]\d|7[012]))([ -]?)(?!00)\d\d\3(?!0000)\d{4}$/.test(e.trim())},checkEmails:function(e,t){if(!e||0==e.length)return!0;for(var a=e.replace(/,/g,";").split(";"),n=0,r=a.length;n<r;n++){if(t>0&&r>t)return!1;if(!Et(a[n]))return!1}return!0},checkEmail:Et,checkGuid:function(e){return!e||0==e.length||/^(\{\w{8}-\w{4}-\w{4}-\w{4}-\w{12}\}|\w{8}-\w{4}-\w{4}-\w{4}-\w{12})$/.test(e.trim())},checkByRegEx:function(e,t){return!e||0==e.length||!!e.match(t)},showMessage:_t,random:St,upload:function(e){var t=a.default(e);if(!t.data("blueimpFileupload")){var n=t.attr("name"),r=n.replace(/\$/g,"\\$"),i=t.data("table"),o=t.is("[multiple]"),l=t.closest(".form-group, [id^='el']"),s=t.prop("disabled")||"confirm"==t.closest("form").find("#confirm").val(),d=l.find("#ft_"+r),u=l.find("#fn_"+r),c=l.find("#fa_"+r),f=l.find("#fs_"+r),p=l.find("#fx_"+r),h=l.find("#fm_"+r),g=l.find("#fc_"+r),m=l.find(".custom-file-label"),w=m.html(),v=function(e){!o&&u.val()&&(confirm(ew.language.phrase("UploadOverwrite"))||(e.preventDefault(),e.stopPropagation()))},b=a.default.templates("#template-download"),y=a.default.templates("#template-upload"),E=me(e),_=a.default(E);(s="confirm"==_.find("#confirm").val()||t.attr("readonly"))&&t.prop("disabled",!0);var S=parseInt(g.val(),10),x=ae(ew.API_JQUERY_UPLOAD_ACTION),O={id:n,table:i,session:ew.SESSION_ID,replace:o?"0":"1",exts:p.val(),maxsize:h.val(),maxfilecount:g.val()};t.fileupload({url:x,type:"POST",multipart:!0,autoUpload:!0,loadImageFileTypes:/^image\/(gif|jpe?g|png)$/i,loadVideoFileTypes:/^video\/mp4$/i,loadAudioFileTypes:/^audio\/(mpeg|mp3)$/i,acceptFileTypes:p.val()?new RegExp("\\.("+p.val().replace(/,/g,"|")+")$","i"):null,maxFileSize:parseInt(h.val(),10),maxNumberOfFiles:S>1?S:null,filesContainer:d,formData:O,uploadTemplateId:null,downloadTemplateId:null,uploadTemplate:y.render.bind(y),downloadTemplate:b.render.bind(b),previewMaxWidth:ew.UPLOAD_THUMBNAIL_WIDTH,previewMaxHeight:ew.UPLOAD_THUMBNAIL_HEIGHT,dropZone:l,pasteZone:l,messages:{acceptFileTypes:ew.language.phrase("UploadErrMsgAcceptFileTypes"),maxFileSize:ew.language.phrase("UploadErrMsgMaxFileSize"),maxNumberOfFiles:ew.language.phrase("UploadErrMsgMaxNumberOfFiles"),minFileSize:ew.language.phrase("UploadErrMsgMinFileSize")},readonly:s}).on("fileuploaddone",(function(e,t){if(!t.result.files[0].error){var a=t.result.files[0].name,n=o&&u.val()?u.val().split(ew.MULTIPLE_UPLOAD_SEPARATOR):[];n.push(a),u.val(n.join(ew.MULTIPLE_UPLOAD_SEPARATOR)),c.val("0"),o||d.find("tbody > tr:not(:last-child)").remove()}})).on("fileuploaddestroy",(function(e,t){var n=a.default(e.originalEvent.target).data("url"),r=new URLSearchParams(n.split("?")[1]),i=r.get("id"),o=r.get(i);if(o){var l=u.val()?u.val().split(ew.MULTIPLE_UPLOAD_SEPARATOR):[],s=l.indexOf(o);s>-1&&l.splice(s,1),u.val(l.join(ew.MULTIPLE_UPLOAD_SEPARATOR)),c.val("0")}})).on("fileuploadchange",(function(e,t){var n;d.toggleClass("ew-has-rows",(null===(n=t.files)||void 0===n?void 0:n.length)>0);for(var r=u.val()?u.val().split(ew.MULTIPLE_UPLOAD_SEPARATOR):[],i=0;i<t.files.length;i++)r.push(t.files[i].name);var o=parseInt(g.val(),10);if(a.default.isNumber(o)&&o>0&&r.length>o)return Oe(ew.language.phrase("UploadErrMsgMaxNumberOfFiles")),!1;var l=parseInt(f.val(),10);return a.default.isNumber(l)&&l>0&&r.join(ew.MULTIPLE_UPLOAD_SEPARATOR).length>l?(Oe(ew.language.phrase("UploadErrMsgMaxFileLength")),!1):void 0})).on("fileuploadadded fileuploadfinished fileuploaddestroyed",(function(e,t){var a,n,r;d.toggleClass("ew-has-rows",(null===(a=t.files)||void 0===a?void 0:a.length)>0||(null===(n=t.result)||void 0===n||null===(r=n.files)||void 0===r?void 0:r.length)>0);var i=u.val()?u.val().split(ew.MULTIPLE_UPLOAD_SEPARATOR):[];m.html(i.join(", ")||w)})).on("fileuploadadded",(function(e,t){var a;d.toggleClass("ew-has-rows",(null===(a=t.files)||void 0===a?void 0:a.length)>0),t.context.find(".start").click(v)})).on("fileuploadcompleted",(function(e,t){se(e={target:t.context}),de(e),ew.updateDropdownPosition(),t.context.find("img").on("load",ew.updateDropdownPosition)})),u.val()&&a.default.ajax({url:x,data:{id:n,table:i,session:ew.SESSION_ID},dataType:"json",context:this,success:function(r){if(r&&r[n]){var i=t.fileupload("option","done");i&&i.call(e,a.default.Event(),{result:{files:r[n]}})}s&&d.find("td.delete").hide()}})}},parseNumber:function(e,t){if(a.default.isString(e)){var n,r=[],i=(t=t||{thousandsSeparator:ew.THOUSANDS_SEP,decimalSeparator:ew.DECIMAL_POINT}).thousandsSeparator,o=t.decimalSeparator;i&&r.push(vt(i)+"(?=\\d)"),n=new RegExp("(?:"+r.join("|")+")","g"),"."===o&&(o=null),e=e.replace(n,""),e=o?e.replace(o,"."):e}return a.default.isString(e)&&""!==e.trim()&&(e=+e),a.default.isNumber&&isFinite(e)||(e=null),e},formatNumber:function(e,t){if(a.default.isNumber(e)){var n,r,i,o,l=e<0,s=e+"",d=(t=t||{thousandsSeparator:ew.THOUSANDS_SEP,decimalSeparator:ew.DECIMAL_POINT}).decimalPlaces,u=t.decimalSeparator||".",c=t.thousandsSeparator;if(a.default.isNumber(d)&&d>=0&&d<=20&&(s=e.toFixed(d)),"."!==u&&(s=s.replace(".",u)),c){for(n=(n=s.lastIndexOf(u))>-1?n:s.length,r=s.substring(n),i=0,o=n;o>0;o--)i%3==0&&o!==n&&(!l||o>1)&&(r=c+r),r=s.charAt(o-1)+r,i++;s=r}return s}return a.default.isValue(e)&&e.toString?e.toString():""},parseDate:function(e,t){var n=a.default.makeArray(arguments);if(a.default.isNumber(t)&&t>=0&&t<=17){var r,i=ew.DATE_FORMAT.toUpperCase(),o=ew.DATE_SEPARATOR,l=ew.TIME_SEPARATOR;switch(t){case 0:case 1:case 2:case 8:r=i+" HH"+l+"mm"+l+"ss";break;case 3:r="hh:mm:ss A";break;case 4:r="HH:mm:ss";break;case 5:r="YYYY"+o+"MM"+o+"DD";break;case 6:r="MM"+o+"DD"+o+"YYYY";break;case 7:r="DD"+o+"MM"+o+"YYYY";break;case 9:r="YYYY"+o+"MM"+o+"DD HH"+l+"mm"+l+"ss";break;case 10:r="MM"+o+"DD"+o+"YYYY HH"+l+"mm"+l+"ss";break;case 11:r="DD"+o+"MM"+o+"YYYY HH"+l+"mm"+l+"ss";break;case 12:r="YY"+o+"MM"+o+"DD";break;case 13:r="MM"+o+"DD"+o+"YY";break;case 14:r="DD"+o+"MM"+o+"YY";break;case 15:r="YY"+o+"MM"+o+"DD HH"+l+"mm"+l+"ss";break;case 16:r="MM"+o+"DD"+o+"YY HH"+l+"mm"+l+"ss";break;case 17:r="DD"+o+"MM"+o+"YY HH"+l+"mm"+l+"ss"}n[1]=[r,"YYYY-MM-DD HH"+l+"mm"+l+"ss"]}return moment.apply(this,n)},formatDate:function(e,t){return moment(e).format(t||ew.DATE_FORMAT.toUpperCase())},initPage:function(e){var t=e&&e.target?e.target:document,n=a.default(t),r=n.find("table.ew-table:not(.ew-export-table)");Array.prototype.forEach.call(t.querySelectorAll(".ew-grid-upper-panel, .ew-grid-lower-panel"),ew.initGridPanel),ew.renderJsTemplates(e),le(e),function(e){for(var t,n=e&&e.target?e.target:document,r=a.default(n),i=K(ew.forms.ids());!(t=i()).done;){var o=t.value;r.find("#"+o)&&J.get(o).init()}}(e),ue(e),te(e),ee(e),se(e),de(e),n.find("[data-widget='treeview']").each((function(){adminlte.Treeview._jQueryInterface.call(a.default(this),"init")})),r.each(Re),n.find(".ew-btn-dropdown").on("shown.bs.dropdown",(function(){var e=a.default(this).removeClass("dropup"),t=a.default(window),n=e.find("> .dropdown-menu");e.toggleClass("dropup",n.offset().top+n.height()>t.scrollTop()+t.height())})),n.find("input[name=pageno]").on("keydown",(function(e){if("Enter"==e.key)return q.searchParams.set(this.name,parseInt(this.value)),window.location=ne(q.toString()),!1})),ew.IS_SCREEN_SM_MIN||n.find("."+ew.RESPONSIVE_TABLE_CLASS+" [data-toggle='dropdown']").parent().on("shown.bs.dropdown",(function(){var e=a.default(this),t=e.find(".dropdown-menu"),n=e.closest("."+ew.RESPONSIVE_TABLE_CLASS)[0];if(n.scrollHeight-n.clientHeight){var r=t.offset().top+t.outerHeight()-a.default(n).offset().top-n.clientHeight;r>0&&t.css(ew.CSS_FLIP?"right":"left","100%").css("top",parseFloat(t.css("top"))-r)}})),re(e),ie(e);var i=n.find(".ew-report");i[0]&&(i.find(".card").on("collapsed.lte.widget",(function(){var e=a.default(this).closest("[class^='col-']"),t=e.css("min-height");t&&e.data("min-height",t),e.css("min-height",0)})).on("expanded.lte.widget",(function(){var e=a.default(this).closest("[class^='col-']"),t=e.css("min-height");t&&e.css("min-height",t)})),i.find("span.ew-group-toggle").on("click",(function(){ew.toggleGroup(this)}))),"undefined"!=typeof ew.USE_JAVASCRIPT_MESSAGE&&ew.USE_JAVASCRIPT_MESSAGE&&_t(e)},redirect:xt,togglePassword:function(e){var t=a.default(e.currentTarget),n=t.closest(".input-group").find("input"),r=t.find("i");"text"==n.attr("type")?(n.attr("type","password"),r.toggleClass("fa-eye-slash fa-eye")):"password"==n.attr("type")&&(n.attr("type","text"),r.toggleClass("fa-eye-slash fa-eye"))},exportWithCharts:function(e,t,n,r){var i=e.target,o=new URL(window.location.href),l=t.split("?"),s=a.default(i),d=r?"post":"get";n+="_"+Date.now(),o.pathname=l[0],o.search=l[1],o.searchParams.set("exportid",n),s.is(".dropdown-menu a")&&(s=s.closest(".btn-group"));var u=function(){var e=o.searchParams,t="1"==e.get("custom");if(r&&!t){var n=a.default(r).serialize();a.default.post(o,n,(function(e){_t(e)}))}else{var i=e.get("export");t&&["word","excel","pdf","email"].includes(i)?("email"==i&&(e.delete("export"),o.search=e.toString()+"&"+a.default(r).serialize()),a.default("iframe.ew-export").remove(),a.default("<iframe></iframe>").addClass("ew-export d-none").attr("src",o.toString()).appendTo(Q.css("cursor","wait")),setTimeout((function(){Q.css("cursor","default")}),5e3)):"print"==i?xt(o.toString(),r,d):oe(o.toString(),null)}return!1},c=Object.keys(window.exportCharts);if(0==c.length)return u();Q.css("cursor","wait");for(var f=[],p=0;p<c.length;p++){var h=c[p],g=window.exportCharts[h],m="exportfilename="+n+"_"+h+".png|exportformat=png|exportaction=download|exportparameters=undefined";g&&g.toBase64Image&&f.push({chart_engine:"Chart.js",stream_type:"base64",stream:g.toBase64Image(),parameters:m})}return a.default.ajax({url:ae(ew.API_EXPORT_CHART_ACTION),data:{charts:JSON.stringify(f)},cache:!1,type:"POST"}).done((function(e){a.default.isString(e)&&(e=ce(e)),e.success?u():ew.alert(e.error)})).fail((function(e,t,a){ew.alert(a+": "+e.responseText)})).always((function(){Q.css("cursor","default")})),!1},fixLayoutHeight:function(){W&&W.cancel(),W=a.default.later(50,null,(function(){var e=Q.data("lte.layout");e&&e.fixLayoutHeight()}))},addEventHandlers:function(e){var t=ew.events[e];if(t)for(var n=0,r=Object.entries(t);n<r.length;n++){var i=r[n],o=i[0],l=i[1];a.default("[data-table="+e+"][data-field="+o+"]").on(l)}}};ew.IS_SCREEN_SM_MIN=window.matchMedia("(min-width: 768px)").matches,ew.MOBILE_DETECT=new MobileDetect(window.navigator.userAgent),ew.IS_MOBILE=!!ew.MOBILE_DETECT.mobile(),ew.IS_IE=ew.MOBILE_DETECT.version("MSIE")>0,window.exportCharts={},window.drillDownCharts={},ew.addSpinner(),Object.assign(ew,{MultiPage:function(e){var t=this;this.$form=null,this.formID=e,this.pageIndex=1,this.maxPageIndex=0,this.minPageIndex=0,this.pageIndexes=[],this.$pages=null,this.$collapses=null,this.isTab=!1,this.isCollapse=!1,this.lastPageSubmit=!1,this.hideDisabledButton=!1,this.hideInactivePages=!1,this.lockTabs=!1,this.hideTabs=!1,this.showPagerTop=!1,this.showPagerBottom=!1,this.pagerTemplate='<nav><ul class="pagination"><li class="page-item previous ew-prev"><a href="#" class="page-link"><span class="icon-prev"></span> {Prev}</a></li><li class="page-item next ew-next"><a href="#" class="page-link">{Next} <span class="icon-next"></span></a></li></ul></nav>';var n=function(e){e.preventDefault()},r=["lastPageSubmit","hideDisabledButton","hideInactivePages","lockTabs","hideTabs","showPagerTop","showPagerBottom","pagerTemplate"];this.set=function(){if(1==arguments.length&&a.default.isObject(arguments[0])){var e=arguments[0];for(var t in e){var n=t[0].toLowerCase()+t.substr(1);r.includes(n)&&(this[n]=e[t])}}},this.init=function(){var e=this.pagerTemplate.replace(/\{prev\}/i,ew.language.phrase("Prev")).replace(/\{next\}/i,ew.language.phrase("Next"));this.isTab?(this.showPagerTop&&this.$pages.closest(".tabbable, .ew-nav-tabs").before(e),this.showPagerBottom&&this.$pages.closest(".tabbable, .ew-nav-tabs").after(e),this.$form.find(".ew-prev").click((function(e){return t.$pages.off("show.bs.tab",n).filter(".active").parent().prev(":has([data-toggle=tab]:not(.ew-hidden):not(.ew-disabled))").find("[data-toggle=tab]").toggleClass("disabled d-none",!1).click(),!1})),this.$form.find(".ew-next").click((function(e){return t.$pages.off("show.bs.tab",n).filter(".active").parent().next(":has([data-toggle=tab]:not(.ew-hidden):not(.ew-disabled))").find("[data-toggle=tab]").toggleClass("disabled d-none",!1).click(),!1})),this.hideTabs&&this.$form.find(".ew-multi-page > .tabbable > .nav-tabs, .ew-multi-page > .ew-nav-tabs > .nav-tabs").hide()):this.isCollapse&&(this.showPagerTop&&this.$collapses.closest(".ew-accordion").before(e),this.showPagerBottom&&this.$collapses.closest(".ew-accordion").after(e),this.$form.find(".ew-prev").click((function(e){return t.$pages.closest(".card").filter(":has(.collapse.show)").prev(":has([data-toggle=collapse]:not(.ew-hidden):not(.ew-disabled))").toggleClass("disabled d-none",!1).find("[data-toggle=collapse]").click(),!1})),this.$form.find(".ew-next").click((function(e){return t.$pages.closest(".card").filter(":has(.collapse.show)").next(":has([data-toggle=collapse]:not(.ew-hidden):not(.ew-disabled))").toggleClass("disabled d-none",!1).find("[data-toggle=collapse]").click(),!1}))),this.pageShow()},this.pageShow=function(){this.isTab?(this.lockTabs&&this.$pages.on("show.bs.tab",n),this.$pages.each((function(){var e=a.default(this);t.hideInactivePages&&e.toggleClass("d-none",!e.hasClass("active")),t.lockTabs&&e.toggleClass("disabled",!e.hasClass("active"))}))):this.isCollapse&&this.$pages.closest(".card").each((function(){var e=a.default(this);t.hideInactivePages&&e.toggleClass("d-none",!e.find(".collapse.show")[0])}));var e=this.lastPageSubmit&&this.pageIndex!=this.maxPageIndex,r=this.$form.closest(".content, .modal-content").find("#btn-action, button.ew-submit").prop("disabled",e).toggle(!this.hideDisabledButton||!e);a.default(".ew-captcha").toggle(r.is(":visible:not(:disabled)")),e=this.pageIndex<=this.minPageIndex,this.$form.find(".ew-prev").toggleClass("disabled",e),e=this.pageIndex>=this.maxPageIndex,this.$form.find(".ew-next").toggleClass("disabled",e)},this.gotoPage=function(e){if(!(e<=0||e<this.minPageIndex||e>this.maxPageIndex)&&this.pageIndex!=e){var t=this.$pages.eq(e-1);if(this.isTab)t.is(":not(.d-none):not(.disabled)")?t.click():t.parent().next(":has([data-toggle=tab]):not(.d-none):not(.disabled)").find("[data-toggle=tab]").toggleClass("disabled",!1).click();else if(this.isCollapse){var a=t.closest(".card");a.is(":not(.d-none)")?t.click():a.next(":has([data-toggle=collapse]):not(.d-none)").find("[data-toggle=collapse]").click()}this.pageIndex=e}},this.gotoPageByIndex=this.gotoPage,this.gotoPageByElement=function(e){this.gotoPage(parseInt(a.default(e).data("page"),10)||-1)},this.gotoPageByElementId=function(e){var t=this.$form.find("[data-page]").filter("[id='"+e+"'],[name='"+e+"'],[data-field='"+e+"']");this.gotoPageByElement(t)},this.togglePage=function(e,t){this.isTab?this.$pages.eq(e-1).toggleClass("d-none",!t):this.isCollapse&&this.$pages.eq(e-1).closest(".card").toggle("d-none",!t)},this.render=function(){this.$form=a.default("#"+e),this.pageIndexes=this.$form.find("[data-page]").map((function(){var e=parseInt(a.default(this).data("page"),10);return e>0?e:null})).get(),this.pageIndexes.sort((function(e,t){return e-t})),this.minPageIndex=this.pageIndexes[0],this.maxPageIndex=this.pageIndexes[this.pageIndexes.length-1];var n=this.$form.find("[data-toggle=tab]");if(n[0])this.$pages=n,this.isTab=!0,n.on("shown.bs.tab",(function(e){t.pageIndex=n.index(e.target)+1,t.pageShow(),a.default(a.default(this).attr("href")).find(".ew-google-map").each((function(){var e=ew.googleMaps[this.id];e&&e.map&&(google.maps.event.trigger(e.map,"resize"),e.map.setCenter(e.latlng))}))})),this.pageIndex=n.index(n.parent(".active"))+1;else if(this.$collapses=this.$form.find("[data-toggle=collapse]"),this.$collapses[0]){this.$pages=this.$collapses,this.isCollapse=!0;var r=this.$collapses.closest(".card-header").next();r.on("shown.bs.collapse",(function(e){t.pageIndex=r.index(e.target)+1,t.pageShow(),a.default(this).find(".ew-google-map").each((function(){var e=ew.googleMaps[this.id];e&&e.map&&(google.maps.event.trigger(e.map,"resize"),e.map.setCenter(e.latlng))}))})),this.pageIndex=r.index(r.hasClass("show"))+1}a.default((function(){t.init()}))}},Form:U,Validators:_},Ot);var Tt=a.default(document);loadjs.ready("load",(function(){a.default.views.settings.debugMode(ew.DEBUG),ew.setSessionTimer(),ew.initPage(),a.default("#ew-modal-dialog").on("load.ew",ew.initPage),a.default("#ew-add-opt-dialog").on("load.ew",ew.initPage);var e=ew.currentUrl.searchParams.get("hash");e&&a.default("html, body").animate({scrollTop:a.default("#"+e).offset().top},800),ew.removeSpinner(),Tt.trigger("load")})),Tt.on("addoption",(function(e,t){for(var n=t.data,r=t.parents,i=0,o=r.length;i<o;i++){var l=r[i];if(!l.length)return t.valid=!1;var s=n["ff"+(i>0?i+1:"")];if(!a.default.isUndefined(s)&&!l.includes(String(s)))return t.valid=!1}})),Tt.on("show.bs.modal",".modal",(function(){var e=1050+a.default(".modal:visible").length;a.default(this).css("z-index",e),setTimeout((function(){a.default(".modal-backdrop").not(".modal-stack").css("z-index",e-1).addClass("modal-stack")}),0)})),Tt.on("hidden.bs.modal",".modal",(function(){a.default(".modal:visible").length&&a.default("body").addClass("modal-open")})),a.default.extend({isBoolean:function(e){return"boolean"==typeof e},isNull:function(e){return null===e},isNumber:function(e){return"number"==typeof e&&isFinite(e)},isObject:function(e){return e&&("object"==typeof e||this.isFunction(e))||!1},isString:function(e){return"string"==typeof e},isUndefined:function(e){return"undefined"==typeof e},isValue:function(e){return this.isObject(e)||this.isString(e)||this.isNumber(e)||this.isBoolean(e)},isDate:function(e){return"date"===this.type(e)&&"Invalid Date"!==e.toString()&&!isNaN(e)},later:function(e,t,a,n,r){e=e||0,t=t||{};var i,o,l=a,s=n;if(this.isString(a)&&(l=t[a]),l)return this.isUndefined(n)||this.isArray(s)||(s=[n]),i=function(){l.apply(t,s||[])},o=r?setInterval(i,e):setTimeout(i,e),{interval:r,cancel:function(){this.interval?clearInterval(o):clearTimeout(o)}}}}),a.default.fn.fields=function(e){var t,n,r,i={},o=this.attr("id"),l=this[0],s=o.match(/^[xy](\d*)_/);s?(t=ew.getForm(l),n=this.data("table"),r=s[1]):l&&l.htmlForm&&(t=l.$element,n=l.id.replace(new RegExp("^f|"+l.pageId+"$","g"),""),r=a.default(l.htmlForm).data("rowindex"));var d="[data-table"+(n?"="+n:"")+"][data-field"+(e?"=x_"+e:"")+"]";return a.default.isValue(r)&&(d+="[name^=x"+r+"_]"),t&&d&&a.default(t).find(d).each((function(){var e=this.getAttribute("data-field").substr(2),t=this.getAttribute("name");e=/^y_/.test(t)?"y_"+e:e,i[e]=i[e]?i[e].add(this):a.default(this)})),e?i[e]:i},a.default.fn.extend({row:function(){var e,t=this.closest("#r_"+(null===(e=this.data("field"))||void 0===e?void 0:e.substr(2)));return t[0]||(t=this.closest(".ew-table > tbody > tr")),t},visible:function(e){var t,a=this.closest("#r_"+(null===(t=this.data("field"))||void 0===t?void 0:t.substr(2)));return a[0]||(a=this.closest("[id^=el]")),"undefined"!=typeof e?(a.toggle(e),this):$el.is(":visible")},readonly:function(e){return"undefined"!=typeof e?(this.prop("readOnly",e),this):this.prop("readOnly")},disabled:function(e){return"undefined"!=typeof e?(this.prop("disabled",e),this):this.prop("disabled")},value:function(e){var t=this.attr("type");if("undefined"!=typeof e){Array.isArray(e)||(e=[e]);var a="radio"==(t=this.attr("type"))||"checkbox"==t?this.get():this[0];return ew.isHiddenTextArea(this)?this.val(e).data("editor").save():(ew.selectOption(a,e),this.hasClass("select2-hidden-accessible")&&this.trigger("change")),this}if("checkbox"==t){var n=ew.getOptionValues(this.get());return 1==this.length?n.join():n}return"radio"==t?ew.getOptionValues(this.get()).join():ew.isHiddenTextArea(this)?(this.data("editor").save(),this.val()):this.val()},toNumber:function(){return ew.parseNumber(this.value())},toDate:function(){return ew.parseDate(this.value(),this.data("format"))},toJsDate:function(){return ew.parseDate(this.value(),this.data("format")).toDate()}}),a.default(window).off("load.lte.treeview");var At=adminlte.Treeview;At.prototype._toggle=At.prototype.toggle,At.prototype.toggle=function(e){var t=a.default(e.currentTarget),n=t.next(),r=t.attr("href"),i="P"==e.target.tagName;!n.is(".nav-treeview")||i&&r&&"#"!=r&&"javascript:void(0);"!=r||(this._toggle(e),e.stopImmediatePropagation())},a.default("ul.dropdown-menu [data-toggle=dropdown]").on("click",(function(e){var t=a.default(this).attr("href");t&&"#"!=t&&"SPAN"==e.target.nodeName&&(window.location=t)}))}();
//# sourceMappingURL=ew.min.js.map