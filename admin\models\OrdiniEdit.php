<?php

namespace PHPMaker2021\aladin2022;

use Doctrine\DBAL\ParameterType;

/**
 * Page class
 */
class OrdiniEdit extends Ordini
{
    use MessagesTrait;

    // Page ID
    public $PageID = "edit";

    // Project ID
    public $ProjectID = PROJECT_ID;

    // Table name
    public $TableName = 'ordini';

    // Page object name
    public $PageObjName = "OrdiniEdit";

    // Rendering View
    public $RenderingView = false;

    // Page headings
    public $Heading = "";
    public $Subheading = "";
    public $PageHeader;
    public $PageFooter;

    // Page terminated
    private $terminated = false;

    // Page heading
    public function pageHeading()
    {
        global $Language;
        if ($this->Heading != "") {
            return $this->Heading;
        }
        if (method_exists($this, "tableCaption")) {
            return $this->tableCaption();
        }
        return "";
    }

    // Page subheading
    public function pageSubheading()
    {
        global $Language;
        if ($this->Subheading != "") {
            return $this->Subheading;
        }
        if ($this->TableName) {
            return $Language->phrase($this->PageID);
        }
        return "";
    }

    // Page name
    public function pageName()
    {
        return CurrentPageName();
    }

    // Page URL
    public function pageUrl()
    {
        $url = ScriptName() . "?";
        if ($this->UseTokenInUrl) {
            $url .= "t=" . $this->TableVar . "&"; // Add page token
        }
        return $url;
    }

    // Show Page Header
    public function showPageHeader()
    {
        $header = $this->PageHeader;
        $this->pageDataRendering($header);
        if ($header != "") { // Header exists, display
            echo '<p id="ew-page-header">' . $header . '</p>';
        }
    }

    // Show Page Footer
    public function showPageFooter()
    {
        $footer = $this->PageFooter;
        $this->pageDataRendered($footer);
        if ($footer != "") { // Footer exists, display
            echo '<p id="ew-page-footer">' . $footer . '</p>';
        }
    }

    // Validate page request
    protected function isPageRequest()
    {
        global $CurrentForm;
        if ($this->UseTokenInUrl) {
            if ($CurrentForm) {
                return ($this->TableVar == $CurrentForm->getValue("t"));
            }
            if (Get("t") !== null) {
                return ($this->TableVar == Get("t"));
            }
        }
        return true;
    }

    // Constructor
    public function __construct()
    {
        global $Language, $DashboardReport, $DebugTimer;

        // Initialize
        $GLOBALS["Page"] = &$this;

        // Language object
        $Language = Container("language");

        // Parent constuctor
        parent::__construct();

        // Table object (ordini)
        if (!isset($GLOBALS["ordini"]) || get_class($GLOBALS["ordini"]) == PROJECT_NAMESPACE . "ordini") {
            $GLOBALS["ordini"] = &$this;
        }

        // Page URL
        $pageUrl = $this->pageUrl();

        // Table name (for backward compatibility only)
        if (!defined(PROJECT_NAMESPACE . "TABLE_NAME")) {
            define(PROJECT_NAMESPACE . "TABLE_NAME", 'ordini');
        }

        // Start timer
        $DebugTimer = Container("timer");

        // Debug message
        LoadDebugMessage();

        // Open connection
        $GLOBALS["Conn"] = $GLOBALS["Conn"] ?? $this->getConnection();
    }

    // Get content from stream
    public function getContents($stream = null): string
    {
        global $Response;
        return is_object($Response) ? $Response->getBody() : ob_get_clean();
    }

    // Is lookup
    public function isLookup()
    {
        return SameText(Route(0), Config("API_LOOKUP_ACTION"));
    }

    // Is AutoFill
    public function isAutoFill()
    {
        return $this->isLookup() && SameText(Post("ajax"), "autofill");
    }

    // Is AutoSuggest
    public function isAutoSuggest()
    {
        return $this->isLookup() && SameText(Post("ajax"), "autosuggest");
    }

    // Is modal lookup
    public function isModalLookup()
    {
        return $this->isLookup() && SameText(Post("ajax"), "modal");
    }

    // Is terminated
    public function isTerminated()
    {
        return $this->terminated;
    }

    /**
     * Terminate page
     *
     * @param string $url URL for direction
     * @return void
     */
    public function terminate($url = "")
    {
        if ($this->terminated) {
            return;
        }
        global $ExportFileName, $TempImages, $DashboardReport, $Response;

        // Page is terminated
        $this->terminated = true;

         // Page Unload event
        if (method_exists($this, "pageUnload")) {
            $this->pageUnload();
        }

        // Global Page Unloaded event (in userfn*.php)
        Page_Unloaded();

        // Export
        if ($this->CustomExport && $this->CustomExport == $this->Export && array_key_exists($this->CustomExport, Config("EXPORT_CLASSES"))) {
            $content = $this->getContents();
            if ($ExportFileName == "") {
                $ExportFileName = $this->TableVar;
            }
            $class = PROJECT_NAMESPACE . Config("EXPORT_CLASSES." . $this->CustomExport);
            if (class_exists($class)) {
                $doc = new $class(Container("ordini"));
                $doc->Text = @$content;
                if ($this->isExport("email")) {
                    echo $this->exportEmail($doc->Text);
                } else {
                    $doc->export();
                }
                DeleteTempImages(); // Delete temp images
                return;
            }
        }
        if (!IsApi() && method_exists($this, "pageRedirecting")) {
            $this->pageRedirecting($url);
        }

        // Close connection
        CloseConnections();

        // Return for API
        if (IsApi()) {
            $res = $url === true;
            if (!$res) { // Show error
                WriteJson(array_merge(["success" => false], $this->getMessages()));
            }
            return;
        } else { // Check if response is JSON
            if (StartsString("application/json", $Response->getHeaderLine("Content-type")) && $Response->getBody()->getSize()) { // With JSON response
                $this->clearMessages();
                return;
            }
        }

        // Go to URL if specified
        if ($url != "") {
            if (!Config("DEBUG") && ob_get_length()) {
                ob_end_clean();
            }

            // Handle modal response
            if ($this->IsModal) { // Show as modal
                $row = ["url" => GetUrl($url), "modal" => "1"];
                $pageName = GetPageName($url);
                if ($pageName != $this->getListUrl()) { // Not List page
                    $row["caption"] = $this->getModalCaption($pageName);
                    if ($pageName == "OrdiniView") {
                        $row["view"] = "1";
                    }
                } else { // List page should not be shown as modal => error
                    $row["error"] = $this->getFailureMessage();
                    $this->clearFailureMessage();
                }
                WriteJson($row);
            } else {
                SaveDebugMessage();
                Redirect(GetUrl($url));
            }
        }
        return; // Return to controller
    }

    // Get records from recordset
    protected function getRecordsFromRecordset($rs, $current = false)
    {
        $rows = [];
        if (is_object($rs)) { // Recordset
            while ($rs && !$rs->EOF) {
                $this->loadRowValues($rs); // Set up DbValue/CurrentValue
                $row = $this->getRecordFromArray($rs->fields);
                if ($current) {
                    return $row;
                } else {
                    $rows[] = $row;
                }
                $rs->moveNext();
            }
        } elseif (is_array($rs)) {
            foreach ($rs as $ar) {
                $row = $this->getRecordFromArray($ar);
                if ($current) {
                    return $row;
                } else {
                    $rows[] = $row;
                }
            }
        }
        return $rows;
    }

    // Get record from array
    protected function getRecordFromArray($ar)
    {
        $row = [];
        if (is_array($ar)) {
            foreach ($ar as $fldname => $val) {
                if (array_key_exists($fldname, $this->Fields) && ($this->Fields[$fldname]->Visible || $this->Fields[$fldname]->IsPrimaryKey)) { // Primary key or Visible
                    $fld = &$this->Fields[$fldname];
                    if ($fld->HtmlTag == "FILE") { // Upload field
                        if (EmptyValue($val)) {
                            $row[$fldname] = null;
                        } else {
                            if ($fld->DataType == DATATYPE_BLOB) {
                                $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                    "/" . $fld->TableVar . "/" . $fld->Param . "/" . rawurlencode($this->getRecordKeyValue($ar))));
                                $row[$fldname] = ["type" => ContentType($val), "url" => $url, "name" => $fld->Param . ContentExtension($val)];
                            } elseif (!$fld->UploadMultiple || !ContainsString($val, Config("MULTIPLE_UPLOAD_SEPARATOR"))) { // Single file
                                $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                    "/" . $fld->TableVar . "/" . Encrypt($fld->physicalUploadPath() . $val)));
                                $row[$fldname] = ["type" => MimeContentType($val), "url" => $url, "name" => $val];
                            } else { // Multiple files
                                $files = explode(Config("MULTIPLE_UPLOAD_SEPARATOR"), $val);
                                $ar = [];
                                foreach ($files as $file) {
                                    $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                        "/" . $fld->TableVar . "/" . Encrypt($fld->physicalUploadPath() . $file)));
                                    if (!EmptyValue($file)) {
                                        $ar[] = ["type" => MimeContentType($file), "url" => $url, "name" => $file];
                                    }
                                }
                                $row[$fldname] = $ar;
                            }
                        }
                    } else {
                        $row[$fldname] = $val;
                    }
                }
            }
        }
        return $row;
    }

    // Get record key value from array
    protected function getRecordKeyValue($ar)
    {
        $key = "";
        if (is_array($ar)) {
            $key .= @$ar['id'];
        }
        return $key;
    }

    /**
     * Hide fields for add/edit
     *
     * @return void
     */
    protected function hideFieldsForAddEdit()
    {
        if ($this->isAdd() || $this->isCopy() || $this->isGridAdd()) {
            $this->id->Visible = false;
        }
    }

    // Lookup data
    public function lookup()
    {
        global $Language, $Security;

        // Get lookup object
        $fieldName = Post("field");
        $lookup = $this->Fields[$fieldName]->Lookup;

        // Get lookup parameters
        $lookupType = Post("ajax", "unknown");
        $pageSize = -1;
        $offset = -1;
        $searchValue = "";
        if (SameText($lookupType, "modal")) {
            $searchValue = Post("sv", "");
            $pageSize = Post("recperpage", 10);
            $offset = Post("start", 0);
        } elseif (SameText($lookupType, "autosuggest")) {
            $searchValue = Param("q", "");
            $pageSize = Param("n", -1);
            $pageSize = is_numeric($pageSize) ? (int)$pageSize : -1;
            if ($pageSize <= 0) {
                $pageSize = Config("AUTO_SUGGEST_MAX_ENTRIES");
            }
            $start = Param("start", -1);
            $start = is_numeric($start) ? (int)$start : -1;
            $page = Param("page", -1);
            $page = is_numeric($page) ? (int)$page : -1;
            $offset = $start >= 0 ? $start : ($page > 0 && $pageSize > 0 ? ($page - 1) * $pageSize : 0);
        }
        $userSelect = Decrypt(Post("s", ""));
        $userFilter = Decrypt(Post("f", ""));
        $userOrderBy = Decrypt(Post("o", ""));
        $keys = Post("keys");
        $lookup->LookupType = $lookupType; // Lookup type
        if ($keys !== null) { // Selected records from modal
            if (is_array($keys)) {
                $keys = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $keys);
            }
            $lookup->FilterFields = []; // Skip parent fields if any
            $lookup->FilterValues[] = $keys; // Lookup values
            $pageSize = -1; // Show all records
        } else { // Lookup values
            $lookup->FilterValues[] = Post("v0", Post("lookupValue", ""));
        }
        $cnt = is_array($lookup->FilterFields) ? count($lookup->FilterFields) : 0;
        for ($i = 1; $i <= $cnt; $i++) {
            $lookup->FilterValues[] = Post("v" . $i, "");
        }
        $lookup->SearchValue = $searchValue;
        $lookup->PageSize = $pageSize;
        $lookup->Offset = $offset;
        if ($userSelect != "") {
            $lookup->UserSelect = $userSelect;
        }
        if ($userFilter != "") {
            $lookup->UserFilter = $userFilter;
        }
        if ($userOrderBy != "") {
            $lookup->UserOrderBy = $userOrderBy;
        }
        $lookup->toJson($this); // Use settings from current page
    }
    public $FormClassName = "ew-horizontal ew-form ew-edit-form";
    public $IsModal = false;
    public $IsMobileOrModal = false;
    public $DbMasterFilter;
    public $DbDetailFilter;
    public $HashValue; // Hash Value
    public $DisplayRecords = 1;
    public $StartRecord;
    public $StopRecord;
    public $TotalRecords = 0;
    public $RecordRange = 10;
    public $RecordCount;

    /**
     * Page run
     *
     * @return void
     */
    public function run()
    {
        global $ExportType, $CustomExportType, $ExportFileName, $UserProfile, $Language, $Security, $CurrentForm,
            $SkipHeaderFooter;

        // Is modal
        $this->IsModal = Param("modal") == "1";

        // Create form object
        $CurrentForm = new HttpForm();
        $this->CurrentAction = Param("action"); // Set up current action
        $this->id->setVisibility();
        $this->_token->Visible = false;
        $this->data->setVisibility();
        $this->status->setVisibility();
        $this->progress->setVisibility();
        $this->persone->setVisibility();
        $this->data_arrivo->setVisibility();
        $this->data_partenza->setVisibility();
        $this->piazzola_id->setVisibility();
        $this->piazzola->setVisibility();
        $this->nome->setVisibility();
        $this->cognome->setVisibility();
        $this->importo->setVisibility();
        $this->_email->setVisibility();
        $this->prefisso->setVisibility();
        $this->telefono->setVisibility();
        $this->codice_fiscale->setVisibility();
        $this->targa->setVisibility();
        $this->country->setVisibility();
        $this->invio_whatsapp->setVisibility();
        $this->presenza_disabili->setVisibility();
        $this->note->setVisibility();
        $this->wa_inviato->setVisibility();
        $this->debug->Visible = false;
        $this->hideFieldsForAddEdit();

        // Do not use lookup cache
        $this->setUseLookupCache(false);

        // Global Page Loading event (in userfn*.php)
        Page_Loading();

        // Page Load event
        if (method_exists($this, "pageLoad")) {
            $this->pageLoad();
        }

        // Set up lookup cache
        $this->setupLookupOptions($this->piazzola_id);

        // Check modal
        if ($this->IsModal) {
            $SkipHeaderFooter = true;
        }
        $this->IsMobileOrModal = IsMobile() || $this->IsModal;
        $this->FormClassName = "ew-form ew-edit-form ew-horizontal";
        $loaded = false;
        $postBack = false;

        // Set up current action and primary key
        if (IsApi()) {
            // Load key values
            $loaded = true;
            if (($keyValue = Get("id") ?? Key(0) ?? Route(2)) !== null) {
                $this->id->setQueryStringValue($keyValue);
                $this->id->setOldValue($this->id->QueryStringValue);
            } elseif (Post("id") !== null) {
                $this->id->setFormValue(Post("id"));
                $this->id->setOldValue($this->id->FormValue);
            } else {
                $loaded = false; // Unable to load key
            }

            // Load record
            if ($loaded) {
                $loaded = $this->loadRow();
            }
            if (!$loaded) {
                $this->setFailureMessage($Language->phrase("NoRecord")); // Set no record message
                $this->terminate();
                return;
            }
            $this->CurrentAction = "update"; // Update record directly
            $this->OldKey = $this->getKey(true); // Get from CurrentValue
            $postBack = true;
        } else {
            if (Post("action") !== null) {
                $this->CurrentAction = Post("action"); // Get action code
                if (!$this->isShow()) { // Not reload record, handle as postback
                    $postBack = true;
                }

                // Get key from Form
                $this->setKey(Post($this->OldKeyName), $this->isShow());
            } else {
                $this->CurrentAction = "show"; // Default action is display

                // Load key from QueryString
                $loadByQuery = false;
                if (($keyValue = Get("id") ?? Route("id")) !== null) {
                    $this->id->setQueryStringValue($keyValue);
                    $loadByQuery = true;
                } else {
                    $this->id->CurrentValue = null;
                }
            }

            // Load recordset
            if ($this->isShow()) {
                // Load current record
                $loaded = $this->loadRow();
                $this->OldKey = $loaded ? $this->getKey(true) : ""; // Get from CurrentValue
            }
        }

        // Process form if post back
        if ($postBack) {
            $this->loadFormValues(); // Get form values
        }

        // Validate form if post back
        if ($postBack) {
            if (!$this->validateForm()) {
                $this->EventCancelled = true; // Event cancelled
                $this->restoreFormValues();
                if (IsApi()) {
                    $this->terminate();
                    return;
                } else {
                    $this->CurrentAction = ""; // Form error, reset action
                }
            }
        }

        // Perform current action
        switch ($this->CurrentAction) {
            case "show": // Get a record to display
                if (!$loaded) { // Load record based on key
                    if ($this->getFailureMessage() == "") {
                        $this->setFailureMessage($Language->phrase("NoRecord")); // No record found
                    }
                    $this->terminate("OrdiniList"); // No matching record, return to list
                    return;
                }
                break;
            case "update": // Update
                $returnUrl = $this->getReturnUrl();
                if (GetPageName($returnUrl) == "OrdiniList") {
                    $returnUrl = $this->addMasterUrl($returnUrl); // List page, return to List page with correct master key if necessary
                }
                $this->SendEmail = true; // Send email on update success
                if ($this->editRow()) { // Update record based on key
                    if ($this->getSuccessMessage() == "") {
                        $this->setSuccessMessage($Language->phrase("UpdateSuccess")); // Update success
                    }
                    if (IsApi()) {
                        $this->terminate(true);
                        return;
                    } else {
                        $this->terminate($returnUrl); // Return to caller
                        return;
                    }
                } elseif (IsApi()) { // API request, return
                    $this->terminate();
                    return;
                } elseif ($this->getFailureMessage() == $Language->phrase("NoRecord")) {
                    $this->terminate($returnUrl); // Return to caller
                    return;
                } else {
                    $this->EventCancelled = true; // Event cancelled
                    $this->restoreFormValues(); // Restore form values if update failed
                }
        }

        // Set up Breadcrumb
        $this->setupBreadcrumb();

        // Render the record
        $this->RowType = ROWTYPE_EDIT; // Render as Edit
        $this->resetAttributes();
        $this->renderRow();

        // Set LoginStatus / Page_Rendering / Page_Render
        if (!IsApi() && !$this->isTerminated()) {
            // Pass table and field properties to client side
            $this->toClientVar(["tableCaption"], ["caption", "Visible", "Required", "IsInvalid", "Raw"]);

            // Setup login status
            SetupLoginStatus();

            // Pass login status to client side
            SetClientVar("login", LoginStatus());

            // Global Page Rendering event (in userfn*.php)
            Page_Rendering();

            // Page Render event
            if (method_exists($this, "pageRender")) {
                $this->pageRender();
            }
        }
    }

    // Get upload files
    protected function getUploadFiles()
    {
        global $CurrentForm, $Language;
    }

    // Load form values
    protected function loadFormValues()
    {
        // Load from form
        global $CurrentForm;

        // Check field name 'id' first before field var 'x_id'
        $val = $CurrentForm->hasValue("id") ? $CurrentForm->getValue("id") : $CurrentForm->getValue("x_id");
        if (!$this->id->IsDetailKey) {
            $this->id->setFormValue($val);
        }

        // Check field name 'data' first before field var 'x_data'
        $val = $CurrentForm->hasValue("data") ? $CurrentForm->getValue("data") : $CurrentForm->getValue("x_data");
        if (!$this->data->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->data->Visible = false; // Disable update for API request
            } else {
                $this->data->setFormValue($val);
            }
            $this->data->CurrentValue = UnFormatDateTime($this->data->CurrentValue, 0);
        }

        // Check field name 'status' first before field var 'x_status'
        $val = $CurrentForm->hasValue("status") ? $CurrentForm->getValue("status") : $CurrentForm->getValue("x_status");
        if (!$this->status->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->status->Visible = false; // Disable update for API request
            } else {
                $this->status->setFormValue($val);
            }
        }

        // Check field name 'progress' first before field var 'x_progress'
        $val = $CurrentForm->hasValue("progress") ? $CurrentForm->getValue("progress") : $CurrentForm->getValue("x_progress");
        if (!$this->progress->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->progress->Visible = false; // Disable update for API request
            } else {
                $this->progress->setFormValue($val);
            }
        }

        // Check field name 'persone' first before field var 'x_persone'
        $val = $CurrentForm->hasValue("persone") ? $CurrentForm->getValue("persone") : $CurrentForm->getValue("x_persone");
        if (!$this->persone->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->persone->Visible = false; // Disable update for API request
            } else {
                $this->persone->setFormValue($val);
            }
        }

        // Check field name 'data_arrivo' first before field var 'x_data_arrivo'
        $val = $CurrentForm->hasValue("data_arrivo") ? $CurrentForm->getValue("data_arrivo") : $CurrentForm->getValue("x_data_arrivo");
        if (!$this->data_arrivo->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->data_arrivo->Visible = false; // Disable update for API request
            } else {
                $this->data_arrivo->setFormValue($val);
            }
            $this->data_arrivo->CurrentValue = UnFormatDateTime($this->data_arrivo->CurrentValue, 1);
        }

        // Check field name 'data_partenza' first before field var 'x_data_partenza'
        $val = $CurrentForm->hasValue("data_partenza") ? $CurrentForm->getValue("data_partenza") : $CurrentForm->getValue("x_data_partenza");
        if (!$this->data_partenza->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->data_partenza->Visible = false; // Disable update for API request
            } else {
                $this->data_partenza->setFormValue($val);
            }
            $this->data_partenza->CurrentValue = UnFormatDateTime($this->data_partenza->CurrentValue, 1);
        }

        // Check field name 'piazzola_id' first before field var 'x_piazzola_id'
        $val = $CurrentForm->hasValue("piazzola_id") ? $CurrentForm->getValue("piazzola_id") : $CurrentForm->getValue("x_piazzola_id");
        if (!$this->piazzola_id->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->piazzola_id->Visible = false; // Disable update for API request
            } else {
                $this->piazzola_id->setFormValue($val);
            }
        }

        // Check field name 'piazzola' first before field var 'x_piazzola'
        $val = $CurrentForm->hasValue("piazzola") ? $CurrentForm->getValue("piazzola") : $CurrentForm->getValue("x_piazzola");
        if (!$this->piazzola->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->piazzola->Visible = false; // Disable update for API request
            } else {
                $this->piazzola->setFormValue($val);
            }
        }

        // Check field name 'nome' first before field var 'x_nome'
        $val = $CurrentForm->hasValue("nome") ? $CurrentForm->getValue("nome") : $CurrentForm->getValue("x_nome");
        if (!$this->nome->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->nome->Visible = false; // Disable update for API request
            } else {
                $this->nome->setFormValue($val);
            }
        }

        // Check field name 'cognome' first before field var 'x_cognome'
        $val = $CurrentForm->hasValue("cognome") ? $CurrentForm->getValue("cognome") : $CurrentForm->getValue("x_cognome");
        if (!$this->cognome->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->cognome->Visible = false; // Disable update for API request
            } else {
                $this->cognome->setFormValue($val);
            }
        }

        // Check field name 'importo' first before field var 'x_importo'
        $val = $CurrentForm->hasValue("importo") ? $CurrentForm->getValue("importo") : $CurrentForm->getValue("x_importo");
        if (!$this->importo->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->importo->Visible = false; // Disable update for API request
            } else {
                $this->importo->setFormValue($val);
            }
        }

        // Check field name 'email' first before field var 'x__email'
        $val = $CurrentForm->hasValue("email") ? $CurrentForm->getValue("email") : $CurrentForm->getValue("x__email");
        if (!$this->_email->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->_email->Visible = false; // Disable update for API request
            } else {
                $this->_email->setFormValue($val);
            }
        }

        // Check field name 'prefisso' first before field var 'x_prefisso'
        $val = $CurrentForm->hasValue("prefisso") ? $CurrentForm->getValue("prefisso") : $CurrentForm->getValue("x_prefisso");
        if (!$this->prefisso->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->prefisso->Visible = false; // Disable update for API request
            } else {
                $this->prefisso->setFormValue($val);
            }
        }

        // Check field name 'telefono' first before field var 'x_telefono'
        $val = $CurrentForm->hasValue("telefono") ? $CurrentForm->getValue("telefono") : $CurrentForm->getValue("x_telefono");
        if (!$this->telefono->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->telefono->Visible = false; // Disable update for API request
            } else {
                $this->telefono->setFormValue($val);
            }
        }

        // Check field name 'codice_fiscale' first before field var 'x_codice_fiscale'
        $val = $CurrentForm->hasValue("codice_fiscale") ? $CurrentForm->getValue("codice_fiscale") : $CurrentForm->getValue("x_codice_fiscale");
        if (!$this->codice_fiscale->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->codice_fiscale->Visible = false; // Disable update for API request
            } else {
                $this->codice_fiscale->setFormValue($val);
            }
        }

        // Check field name 'targa' first before field var 'x_targa'
        $val = $CurrentForm->hasValue("targa") ? $CurrentForm->getValue("targa") : $CurrentForm->getValue("x_targa");
        if (!$this->targa->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->targa->Visible = false; // Disable update for API request
            } else {
                $this->targa->setFormValue($val);
            }
        }

        // Check field name 'country' first before field var 'x_country'
        $val = $CurrentForm->hasValue("country") ? $CurrentForm->getValue("country") : $CurrentForm->getValue("x_country");
        if (!$this->country->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->country->Visible = false; // Disable update for API request
            } else {
                $this->country->setFormValue($val);
            }
        }

        // Check field name 'invio_whatsapp' first before field var 'x_invio_whatsapp'
        $val = $CurrentForm->hasValue("invio_whatsapp") ? $CurrentForm->getValue("invio_whatsapp") : $CurrentForm->getValue("x_invio_whatsapp");
        if (!$this->invio_whatsapp->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->invio_whatsapp->Visible = false; // Disable update for API request
            } else {
                $this->invio_whatsapp->setFormValue($val);
            }
        }

        // Check field name 'presenza_disabili' first before field var 'x_presenza_disabili'
        $val = $CurrentForm->hasValue("presenza_disabili") ? $CurrentForm->getValue("presenza_disabili") : $CurrentForm->getValue("x_presenza_disabili");
        if (!$this->presenza_disabili->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->presenza_disabili->Visible = false; // Disable update for API request
            } else {
                $this->presenza_disabili->setFormValue($val);
            }
        }

        // Check field name 'note' first before field var 'x_note'
        $val = $CurrentForm->hasValue("note") ? $CurrentForm->getValue("note") : $CurrentForm->getValue("x_note");
        if (!$this->note->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->note->Visible = false; // Disable update for API request
            } else {
                $this->note->setFormValue($val);
            }
        }

        // Check field name 'wa_inviato' first before field var 'x_wa_inviato'
        $val = $CurrentForm->hasValue("wa_inviato") ? $CurrentForm->getValue("wa_inviato") : $CurrentForm->getValue("x_wa_inviato");
        if (!$this->wa_inviato->IsDetailKey) {
            if (IsApi() && $val === null) {
                $this->wa_inviato->Visible = false; // Disable update for API request
            } else {
                $this->wa_inviato->setFormValue($val);
            }
        }
    }

    // Restore form values
    public function restoreFormValues()
    {
        global $CurrentForm;
        $this->id->CurrentValue = $this->id->FormValue;
        $this->data->CurrentValue = $this->data->FormValue;
        $this->data->CurrentValue = UnFormatDateTime($this->data->CurrentValue, 0);
        $this->status->CurrentValue = $this->status->FormValue;
        $this->progress->CurrentValue = $this->progress->FormValue;
        $this->persone->CurrentValue = $this->persone->FormValue;
        $this->data_arrivo->CurrentValue = $this->data_arrivo->FormValue;
        $this->data_arrivo->CurrentValue = UnFormatDateTime($this->data_arrivo->CurrentValue, 1);
        $this->data_partenza->CurrentValue = $this->data_partenza->FormValue;
        $this->data_partenza->CurrentValue = UnFormatDateTime($this->data_partenza->CurrentValue, 1);
        $this->piazzola_id->CurrentValue = $this->piazzola_id->FormValue;
        $this->piazzola->CurrentValue = $this->piazzola->FormValue;
        $this->nome->CurrentValue = $this->nome->FormValue;
        $this->cognome->CurrentValue = $this->cognome->FormValue;
        $this->importo->CurrentValue = $this->importo->FormValue;
        $this->_email->CurrentValue = $this->_email->FormValue;
        $this->prefisso->CurrentValue = $this->prefisso->FormValue;
        $this->telefono->CurrentValue = $this->telefono->FormValue;
        $this->codice_fiscale->CurrentValue = $this->codice_fiscale->FormValue;
        $this->targa->CurrentValue = $this->targa->FormValue;
        $this->country->CurrentValue = $this->country->FormValue;
        $this->invio_whatsapp->CurrentValue = $this->invio_whatsapp->FormValue;
        $this->presenza_disabili->CurrentValue = $this->presenza_disabili->FormValue;
        $this->note->CurrentValue = $this->note->FormValue;
        $this->wa_inviato->CurrentValue = $this->wa_inviato->FormValue;
    }

    /**
     * Load row based on key values
     *
     * @return void
     */
    public function loadRow()
    {
        global $Security, $Language;
        $filter = $this->getRecordFilter();

        // Call Row Selecting event
        $this->rowSelecting($filter);

        // Load SQL based on filter
        $this->CurrentFilter = $filter;
        $sql = $this->getCurrentSql();
        $conn = $this->getConnection();
        $res = false;
        $row = $conn->fetchAssoc($sql);
        if ($row) {
            $res = true;
            $this->loadRowValues($row); // Load row values
        }
        return $res;
    }

    /**
     * Load row values from recordset or record
     *
     * @param Recordset|array $rs Record
     * @return void
     */
    public function loadRowValues($rs = null)
    {
        if (is_array($rs)) {
            $row = $rs;
        } elseif ($rs && property_exists($rs, "fields")) { // Recordset
            $row = $rs->fields;
        } else {
            $row = $this->newRow();
        }

        // Call Row Selected event
        $this->rowSelected($row);
        if (!$rs) {
            return;
        }
        $this->id->setDbValue($row['id']);
        $this->_token->setDbValue($row['token']);
        $this->data->setDbValue($row['data']);
        $this->status->setDbValue($row['status']);
        $this->progress->setDbValue($row['progress']);
        $this->persone->setDbValue($row['persone']);
        $this->data_arrivo->setDbValue($row['data_arrivo']);
        $this->data_partenza->setDbValue($row['data_partenza']);
        $this->piazzola_id->setDbValue($row['piazzola_id']);
        $this->piazzola->setDbValue($row['piazzola']);
        $this->nome->setDbValue($row['nome']);
        $this->cognome->setDbValue($row['cognome']);
        $this->importo->setDbValue($row['importo']);
        $this->_email->setDbValue($row['email']);
        $this->prefisso->setDbValue($row['prefisso']);
        $this->telefono->setDbValue($row['telefono']);
        $this->codice_fiscale->setDbValue($row['codice_fiscale']);
        $this->targa->setDbValue($row['targa']);
        $this->country->setDbValue($row['country']);
        $this->invio_whatsapp->setDbValue($row['invio_whatsapp']);
        $this->presenza_disabili->setDbValue($row['presenza_disabili']);
        $this->note->setDbValue($row['note']);
        $this->wa_inviato->setDbValue($row['wa_inviato']);
        $this->debug->setDbValue($row['debug']);
    }

    // Return a row with default values
    protected function newRow()
    {
        $row = [];
        $row['id'] = null;
        $row['token'] = null;
        $row['data'] = null;
        $row['status'] = null;
        $row['progress'] = null;
        $row['persone'] = null;
        $row['data_arrivo'] = null;
        $row['data_partenza'] = null;
        $row['piazzola_id'] = null;
        $row['piazzola'] = null;
        $row['nome'] = null;
        $row['cognome'] = null;
        $row['importo'] = null;
        $row['email'] = null;
        $row['prefisso'] = null;
        $row['telefono'] = null;
        $row['codice_fiscale'] = null;
        $row['targa'] = null;
        $row['country'] = null;
        $row['invio_whatsapp'] = null;
        $row['presenza_disabili'] = null;
        $row['note'] = null;
        $row['wa_inviato'] = null;
        $row['debug'] = null;
        return $row;
    }

    // Load old record
    protected function loadOldRecord()
    {
        // Load old record
        $this->OldRecordset = null;
        $validKey = $this->OldKey != "";
        if ($validKey) {
            $this->CurrentFilter = $this->getRecordFilter();
            $sql = $this->getCurrentSql();
            $conn = $this->getConnection();
            $this->OldRecordset = LoadRecordset($sql, $conn);
        }
        $this->loadRowValues($this->OldRecordset); // Load row values
        return $validKey;
    }

    // Render row values based on field settings
    public function renderRow()
    {
        global $Security, $Language, $CurrentLanguage;

        // Initialize URLs

        // Convert decimal values if posted back
        if ($this->importo->FormValue == $this->importo->CurrentValue && is_numeric(ConvertToFloatString($this->importo->CurrentValue))) {
            $this->importo->CurrentValue = ConvertToFloatString($this->importo->CurrentValue);
        }

        // Call Row_Rendering event
        $this->rowRendering();

        // Common render codes for all row types

        // id

        // token

        // data

        // status

        // progress

        // persone

        // data_arrivo

        // data_partenza

        // piazzola_id

        // piazzola

        // nome

        // cognome

        // importo

        // email

        // prefisso

        // telefono

        // codice_fiscale

        // targa

        // country

        // invio_whatsapp

        // presenza_disabili

        // note

        // wa_inviato

        // debug
        if ($this->RowType == ROWTYPE_VIEW) {
            // id
            $this->id->ViewValue = $this->id->CurrentValue;
            $this->id->ViewCustomAttributes = "";

            // data
            $this->data->ViewValue = $this->data->CurrentValue;
            $this->data->ViewValue = FormatDateTime($this->data->ViewValue, 0);
            $this->data->ViewCustomAttributes = "";

            // status
            if (strval($this->status->CurrentValue) != "") {
                $this->status->ViewValue = $this->status->optionCaption($this->status->CurrentValue);
            } else {
                $this->status->ViewValue = null;
            }
            $this->status->ViewCustomAttributes = "";

            // progress
            if (strval($this->progress->CurrentValue) != "") {
                $this->progress->ViewValue = $this->progress->optionCaption($this->progress->CurrentValue);
            } else {
                $this->progress->ViewValue = null;
            }
            $this->progress->ViewCustomAttributes = "";

            // persone
            $this->persone->ViewValue = $this->persone->CurrentValue;
            $this->persone->ViewValue = FormatNumber($this->persone->ViewValue, 0, -2, -2, -2);
            $this->persone->ViewCustomAttributes = "";

            // data_arrivo
            $this->data_arrivo->ViewValue = $this->data_arrivo->CurrentValue;
            $this->data_arrivo->ViewValue = FormatDateTime($this->data_arrivo->ViewValue, 1);
            $this->data_arrivo->ViewCustomAttributes = "";

            // data_partenza
            $this->data_partenza->ViewValue = $this->data_partenza->CurrentValue;
            $this->data_partenza->ViewValue = FormatDateTime($this->data_partenza->ViewValue, 1);
            $this->data_partenza->ViewCustomAttributes = "";

            // piazzola_id
            $curVal = trim(strval($this->piazzola_id->CurrentValue));
            if ($curVal != "") {
                $this->piazzola_id->ViewValue = $this->piazzola_id->lookupCacheOption($curVal);
                if ($this->piazzola_id->ViewValue === null) { // Lookup from database
                    $filterWrk = "`id`" . SearchString("=", $curVal, DATATYPE_NUMBER, "");
                    $sqlWrk = $this->piazzola_id->Lookup->getSql(false, $filterWrk, '', $this, true, true);
                    $rswrk = Conn()->executeQuery($sqlWrk)->fetchAll(\PDO::FETCH_BOTH);
                    $ari = count($rswrk);
                    if ($ari > 0) { // Lookup values found
                        $arwrk = $this->piazzola_id->Lookup->renderViewRow($rswrk[0]);
                        $this->piazzola_id->ViewValue = $this->piazzola_id->displayValue($arwrk);
                    } else {
                        $this->piazzola_id->ViewValue = $this->piazzola_id->CurrentValue;
                    }
                }
            } else {
                $this->piazzola_id->ViewValue = null;
            }
            $this->piazzola_id->ViewCustomAttributes = "";

            // piazzola
            $this->piazzola->ViewValue = $this->piazzola->CurrentValue;
            $this->piazzola->ViewCustomAttributes = "";

            // nome
            $this->nome->ViewValue = $this->nome->CurrentValue;
            $this->nome->ViewCustomAttributes = "";

            // cognome
            $this->cognome->ViewValue = $this->cognome->CurrentValue;
            $this->cognome->ViewCustomAttributes = "";

            // importo
            $this->importo->ViewValue = $this->importo->CurrentValue;
            $this->importo->ViewValue = FormatCurrency($this->importo->ViewValue, 2, -1, -2, -2);
            $this->importo->ViewCustomAttributes = "";

            // email
            $this->_email->ViewValue = $this->_email->CurrentValue;
            $this->_email->ViewCustomAttributes = "";

            // prefisso
            $this->prefisso->ViewValue = $this->prefisso->CurrentValue;
            $this->prefisso->ViewCustomAttributes = "";

            // telefono
            $this->telefono->ViewValue = $this->telefono->CurrentValue;
            $this->telefono->ViewCustomAttributes = "";

            // codice_fiscale
            $this->codice_fiscale->ViewValue = $this->codice_fiscale->CurrentValue;
            $this->codice_fiscale->ViewCustomAttributes = "";

            // targa
            $this->targa->ViewValue = $this->targa->CurrentValue;
            $this->targa->ViewCustomAttributes = "";

            // country
            $this->country->ViewValue = $this->country->CurrentValue;
            $this->country->ViewCustomAttributes = "";

            // invio_whatsapp
            if (ConvertToBool($this->invio_whatsapp->CurrentValue)) {
                $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(2) != "" ? $this->invio_whatsapp->tagCaption(2) : "si";
            } else {
                $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(1) != "" ? $this->invio_whatsapp->tagCaption(1) : "no";
            }
            $this->invio_whatsapp->ViewCustomAttributes = "";

            // presenza_disabili
            if (ConvertToBool($this->presenza_disabili->CurrentValue)) {
                $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(2) != "" ? $this->presenza_disabili->tagCaption(2) : "si";
            } else {
                $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(1) != "" ? $this->presenza_disabili->tagCaption(1) : "no";
            }
            $this->presenza_disabili->ViewCustomAttributes = "";

            // note
            $this->note->ViewValue = $this->note->CurrentValue;
            $this->note->ViewCustomAttributes = "";

            // wa_inviato
            if (ConvertToBool($this->wa_inviato->CurrentValue)) {
                $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(2) != "" ? $this->wa_inviato->tagCaption(2) : "1";
            } else {
                $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(1) != "" ? $this->wa_inviato->tagCaption(1) : "0";
            }
            $this->wa_inviato->ViewCustomAttributes = "";

            // id
            $this->id->LinkCustomAttributes = "";
            $this->id->HrefValue = "";
            $this->id->TooltipValue = "";

            // data
            $this->data->LinkCustomAttributes = "";
            $this->data->HrefValue = "";
            $this->data->TooltipValue = "";

            // status
            $this->status->LinkCustomAttributes = "";
            $this->status->HrefValue = "";
            $this->status->TooltipValue = "";

            // progress
            $this->progress->LinkCustomAttributes = "";
            $this->progress->HrefValue = "";
            $this->progress->TooltipValue = "";

            // persone
            $this->persone->LinkCustomAttributes = "";
            $this->persone->HrefValue = "";
            $this->persone->TooltipValue = "";

            // data_arrivo
            $this->data_arrivo->LinkCustomAttributes = "";
            $this->data_arrivo->HrefValue = "";
            $this->data_arrivo->TooltipValue = "";

            // data_partenza
            $this->data_partenza->LinkCustomAttributes = "";
            $this->data_partenza->HrefValue = "";
            $this->data_partenza->TooltipValue = "";

            // piazzola_id
            $this->piazzola_id->LinkCustomAttributes = "";
            $this->piazzola_id->HrefValue = "";
            $this->piazzola_id->TooltipValue = "";

            // piazzola
            $this->piazzola->LinkCustomAttributes = "";
            $this->piazzola->HrefValue = "";
            $this->piazzola->TooltipValue = "";

            // nome
            $this->nome->LinkCustomAttributes = "";
            $this->nome->HrefValue = "";
            $this->nome->TooltipValue = "";

            // cognome
            $this->cognome->LinkCustomAttributes = "";
            $this->cognome->HrefValue = "";
            $this->cognome->TooltipValue = "";

            // importo
            $this->importo->LinkCustomAttributes = "";
            $this->importo->HrefValue = "";
            $this->importo->TooltipValue = "";

            // email
            $this->_email->LinkCustomAttributes = "";
            $this->_email->HrefValue = "";
            $this->_email->TooltipValue = "";

            // prefisso
            $this->prefisso->LinkCustomAttributes = "";
            $this->prefisso->HrefValue = "";
            $this->prefisso->TooltipValue = "";

            // telefono
            $this->telefono->LinkCustomAttributes = "";
            $this->telefono->HrefValue = "";
            $this->telefono->TooltipValue = "";

            // codice_fiscale
            $this->codice_fiscale->LinkCustomAttributes = "";
            $this->codice_fiscale->HrefValue = "";
            $this->codice_fiscale->TooltipValue = "";

            // targa
            $this->targa->LinkCustomAttributes = "";
            $this->targa->HrefValue = "";
            $this->targa->TooltipValue = "";

            // country
            $this->country->LinkCustomAttributes = "";
            $this->country->HrefValue = "";
            $this->country->TooltipValue = "";

            // invio_whatsapp
            $this->invio_whatsapp->LinkCustomAttributes = "";
            $this->invio_whatsapp->HrefValue = "";
            $this->invio_whatsapp->TooltipValue = "";

            // presenza_disabili
            $this->presenza_disabili->LinkCustomAttributes = "";
            $this->presenza_disabili->HrefValue = "";
            $this->presenza_disabili->TooltipValue = "";

            // note
            $this->note->LinkCustomAttributes = "";
            $this->note->HrefValue = "";
            $this->note->TooltipValue = "";

            // wa_inviato
            $this->wa_inviato->LinkCustomAttributes = "";
            $this->wa_inviato->HrefValue = "";
            $this->wa_inviato->TooltipValue = "";
        } elseif ($this->RowType == ROWTYPE_EDIT) {
            // id
            $this->id->EditAttrs["class"] = "form-control";
            $this->id->EditCustomAttributes = "";
            $this->id->EditValue = $this->id->CurrentValue;
            $this->id->ViewCustomAttributes = "";

            // data
            $this->data->EditAttrs["class"] = "form-control";
            $this->data->EditCustomAttributes = "";
            $this->data->EditValue = HtmlEncode(FormatDateTime($this->data->CurrentValue, 8));
            $this->data->PlaceHolder = RemoveHtml($this->data->caption());

            // status
            $this->status->EditCustomAttributes = "";
            $this->status->EditValue = $this->status->options(false);
            $this->status->PlaceHolder = RemoveHtml($this->status->caption());

            // progress
            $this->progress->EditCustomAttributes = "";
            $this->progress->EditValue = $this->progress->options(false);
            $this->progress->PlaceHolder = RemoveHtml($this->progress->caption());

            // persone
            $this->persone->EditAttrs["class"] = "form-control";
            $this->persone->EditCustomAttributes = "";
            $this->persone->EditValue = HtmlEncode($this->persone->CurrentValue);
            $this->persone->PlaceHolder = RemoveHtml($this->persone->caption());

            // data_arrivo
            $this->data_arrivo->EditAttrs["class"] = "form-control";
            $this->data_arrivo->EditCustomAttributes = "";
            $this->data_arrivo->EditValue = HtmlEncode(FormatDateTime($this->data_arrivo->CurrentValue, 8));
            $this->data_arrivo->PlaceHolder = RemoveHtml($this->data_arrivo->caption());

            // data_partenza
            $this->data_partenza->EditAttrs["class"] = "form-control";
            $this->data_partenza->EditCustomAttributes = "";
            $this->data_partenza->EditValue = HtmlEncode(FormatDateTime($this->data_partenza->CurrentValue, 8));
            $this->data_partenza->PlaceHolder = RemoveHtml($this->data_partenza->caption());

            // piazzola_id
            $this->piazzola_id->EditAttrs["class"] = "form-control";
            $this->piazzola_id->EditCustomAttributes = "";
            $curVal = trim(strval($this->piazzola_id->CurrentValue));
            if ($curVal != "") {
                $this->piazzola_id->ViewValue = $this->piazzola_id->lookupCacheOption($curVal);
            } else {
                $this->piazzola_id->ViewValue = $this->piazzola_id->Lookup !== null && is_array($this->piazzola_id->Lookup->Options) ? $curVal : null;
            }
            if ($this->piazzola_id->ViewValue !== null) { // Load from cache
                $this->piazzola_id->EditValue = array_values($this->piazzola_id->Lookup->Options);
            } else { // Lookup from database
                if ($curVal == "") {
                    $filterWrk = "0=1";
                } else {
                    $filterWrk = "`id`" . SearchString("=", $this->piazzola_id->CurrentValue, DATATYPE_NUMBER, "");
                }
                $sqlWrk = $this->piazzola_id->Lookup->getSql(true, $filterWrk, '', $this, false, true);
                $rswrk = Conn()->executeQuery($sqlWrk)->fetchAll(\PDO::FETCH_BOTH);
                $ari = count($rswrk);
                $arwrk = $rswrk;
                $this->piazzola_id->EditValue = $arwrk;
            }
            $this->piazzola_id->PlaceHolder = RemoveHtml($this->piazzola_id->caption());

            // piazzola
            $this->piazzola->EditAttrs["class"] = "form-control";
            $this->piazzola->EditCustomAttributes = "";
            if (!$this->piazzola->Raw) {
                $this->piazzola->CurrentValue = HtmlDecode($this->piazzola->CurrentValue);
            }
            $this->piazzola->EditValue = HtmlEncode($this->piazzola->CurrentValue);
            $this->piazzola->PlaceHolder = RemoveHtml($this->piazzola->caption());

            // nome
            $this->nome->EditAttrs["class"] = "form-control";
            $this->nome->EditCustomAttributes = "";
            if (!$this->nome->Raw) {
                $this->nome->CurrentValue = HtmlDecode($this->nome->CurrentValue);
            }
            $this->nome->EditValue = HtmlEncode($this->nome->CurrentValue);
            $this->nome->PlaceHolder = RemoveHtml($this->nome->caption());

            // cognome
            $this->cognome->EditAttrs["class"] = "form-control";
            $this->cognome->EditCustomAttributes = "";
            if (!$this->cognome->Raw) {
                $this->cognome->CurrentValue = HtmlDecode($this->cognome->CurrentValue);
            }
            $this->cognome->EditValue = HtmlEncode($this->cognome->CurrentValue);
            $this->cognome->PlaceHolder = RemoveHtml($this->cognome->caption());

            // importo
            $this->importo->EditAttrs["class"] = "form-control";
            $this->importo->EditCustomAttributes = "";
            $this->importo->EditValue = HtmlEncode($this->importo->CurrentValue);
            $this->importo->PlaceHolder = RemoveHtml($this->importo->caption());
            if (strval($this->importo->EditValue) != "" && is_numeric($this->importo->EditValue)) {
                $this->importo->EditValue = FormatNumber($this->importo->EditValue, -2, -1, -2, -2);
            }

            // email
            $this->_email->EditAttrs["class"] = "form-control";
            $this->_email->EditCustomAttributes = "";
            if (!$this->_email->Raw) {
                $this->_email->CurrentValue = HtmlDecode($this->_email->CurrentValue);
            }
            $this->_email->EditValue = HtmlEncode($this->_email->CurrentValue);
            $this->_email->PlaceHolder = RemoveHtml($this->_email->caption());

            // prefisso
            $this->prefisso->EditAttrs["class"] = "form-control";
            $this->prefisso->EditCustomAttributes = "";
            if (!$this->prefisso->Raw) {
                $this->prefisso->CurrentValue = HtmlDecode($this->prefisso->CurrentValue);
            }
            $this->prefisso->EditValue = HtmlEncode($this->prefisso->CurrentValue);
            $this->prefisso->PlaceHolder = RemoveHtml($this->prefisso->caption());

            // telefono
            $this->telefono->EditAttrs["class"] = "form-control";
            $this->telefono->EditCustomAttributes = "";
            if (!$this->telefono->Raw) {
                $this->telefono->CurrentValue = HtmlDecode($this->telefono->CurrentValue);
            }
            $this->telefono->EditValue = HtmlEncode($this->telefono->CurrentValue);
            $this->telefono->PlaceHolder = RemoveHtml($this->telefono->caption());

            // codice_fiscale
            $this->codice_fiscale->EditAttrs["class"] = "form-control";
            $this->codice_fiscale->EditCustomAttributes = "";
            if (!$this->codice_fiscale->Raw) {
                $this->codice_fiscale->CurrentValue = HtmlDecode($this->codice_fiscale->CurrentValue);
            }
            $this->codice_fiscale->EditValue = HtmlEncode($this->codice_fiscale->CurrentValue);
            $this->codice_fiscale->PlaceHolder = RemoveHtml($this->codice_fiscale->caption());

            // targa
            $this->targa->EditAttrs["class"] = "form-control";
            $this->targa->EditCustomAttributes = "";
            if (!$this->targa->Raw) {
                $this->targa->CurrentValue = HtmlDecode($this->targa->CurrentValue);
            }
            $this->targa->EditValue = HtmlEncode($this->targa->CurrentValue);
            $this->targa->PlaceHolder = RemoveHtml($this->targa->caption());

            // country
            $this->country->EditAttrs["class"] = "form-control";
            $this->country->EditCustomAttributes = "";
            if (!$this->country->Raw) {
                $this->country->CurrentValue = HtmlDecode($this->country->CurrentValue);
            }
            $this->country->EditValue = HtmlEncode($this->country->CurrentValue);
            $this->country->PlaceHolder = RemoveHtml($this->country->caption());

            // invio_whatsapp
            $this->invio_whatsapp->EditCustomAttributes = "";
            $this->invio_whatsapp->EditValue = $this->invio_whatsapp->options(false);
            $this->invio_whatsapp->PlaceHolder = RemoveHtml($this->invio_whatsapp->caption());

            // presenza_disabili
            $this->presenza_disabili->EditCustomAttributes = "";
            $this->presenza_disabili->EditValue = $this->presenza_disabili->options(false);
            $this->presenza_disabili->PlaceHolder = RemoveHtml($this->presenza_disabili->caption());

            // note
            $this->note->EditAttrs["class"] = "form-control";
            $this->note->EditCustomAttributes = "";
            $this->note->EditValue = HtmlEncode($this->note->CurrentValue);
            $this->note->PlaceHolder = RemoveHtml($this->note->caption());

            // wa_inviato
            $this->wa_inviato->EditCustomAttributes = "";
            $this->wa_inviato->EditValue = $this->wa_inviato->options(false);
            $this->wa_inviato->PlaceHolder = RemoveHtml($this->wa_inviato->caption());

            // Edit refer script

            // id
            $this->id->LinkCustomAttributes = "";
            $this->id->HrefValue = "";

            // data
            $this->data->LinkCustomAttributes = "";
            $this->data->HrefValue = "";

            // status
            $this->status->LinkCustomAttributes = "";
            $this->status->HrefValue = "";

            // progress
            $this->progress->LinkCustomAttributes = "";
            $this->progress->HrefValue = "";

            // persone
            $this->persone->LinkCustomAttributes = "";
            $this->persone->HrefValue = "";

            // data_arrivo
            $this->data_arrivo->LinkCustomAttributes = "";
            $this->data_arrivo->HrefValue = "";

            // data_partenza
            $this->data_partenza->LinkCustomAttributes = "";
            $this->data_partenza->HrefValue = "";

            // piazzola_id
            $this->piazzola_id->LinkCustomAttributes = "";
            $this->piazzola_id->HrefValue = "";

            // piazzola
            $this->piazzola->LinkCustomAttributes = "";
            $this->piazzola->HrefValue = "";

            // nome
            $this->nome->LinkCustomAttributes = "";
            $this->nome->HrefValue = "";

            // cognome
            $this->cognome->LinkCustomAttributes = "";
            $this->cognome->HrefValue = "";

            // importo
            $this->importo->LinkCustomAttributes = "";
            $this->importo->HrefValue = "";

            // email
            $this->_email->LinkCustomAttributes = "";
            $this->_email->HrefValue = "";

            // prefisso
            $this->prefisso->LinkCustomAttributes = "";
            $this->prefisso->HrefValue = "";

            // telefono
            $this->telefono->LinkCustomAttributes = "";
            $this->telefono->HrefValue = "";

            // codice_fiscale
            $this->codice_fiscale->LinkCustomAttributes = "";
            $this->codice_fiscale->HrefValue = "";

            // targa
            $this->targa->LinkCustomAttributes = "";
            $this->targa->HrefValue = "";

            // country
            $this->country->LinkCustomAttributes = "";
            $this->country->HrefValue = "";

            // invio_whatsapp
            $this->invio_whatsapp->LinkCustomAttributes = "";
            $this->invio_whatsapp->HrefValue = "";

            // presenza_disabili
            $this->presenza_disabili->LinkCustomAttributes = "";
            $this->presenza_disabili->HrefValue = "";

            // note
            $this->note->LinkCustomAttributes = "";
            $this->note->HrefValue = "";

            // wa_inviato
            $this->wa_inviato->LinkCustomAttributes = "";
            $this->wa_inviato->HrefValue = "";
        }
        if ($this->RowType == ROWTYPE_ADD || $this->RowType == ROWTYPE_EDIT || $this->RowType == ROWTYPE_SEARCH) { // Add/Edit/Search row
            $this->setupFieldTitles();
        }

        // Call Row Rendered event
        if ($this->RowType != ROWTYPE_AGGREGATEINIT) {
            $this->rowRendered();
        }
    }

    // Validate form
    protected function validateForm()
    {
        global $Language;

        // Check if validation required
        if (!Config("SERVER_VALIDATE")) {
            return true;
        }
        if ($this->id->Required) {
            if (!$this->id->IsDetailKey && EmptyValue($this->id->FormValue)) {
                $this->id->addErrorMessage(str_replace("%s", $this->id->caption(), $this->id->RequiredErrorMessage));
            }
        }
        if ($this->data->Required) {
            if (!$this->data->IsDetailKey && EmptyValue($this->data->FormValue)) {
                $this->data->addErrorMessage(str_replace("%s", $this->data->caption(), $this->data->RequiredErrorMessage));
            }
        }
        if (!CheckDate($this->data->FormValue)) {
            $this->data->addErrorMessage($this->data->getErrorMessage(false));
        }
        if ($this->status->Required) {
            if ($this->status->FormValue == "") {
                $this->status->addErrorMessage(str_replace("%s", $this->status->caption(), $this->status->RequiredErrorMessage));
            }
        }
        if ($this->progress->Required) {
            if ($this->progress->FormValue == "") {
                $this->progress->addErrorMessage(str_replace("%s", $this->progress->caption(), $this->progress->RequiredErrorMessage));
            }
        }
        if ($this->persone->Required) {
            if (!$this->persone->IsDetailKey && EmptyValue($this->persone->FormValue)) {
                $this->persone->addErrorMessage(str_replace("%s", $this->persone->caption(), $this->persone->RequiredErrorMessage));
            }
        }
        if (!CheckInteger($this->persone->FormValue)) {
            $this->persone->addErrorMessage($this->persone->getErrorMessage(false));
        }
        if ($this->data_arrivo->Required) {
            if (!$this->data_arrivo->IsDetailKey && EmptyValue($this->data_arrivo->FormValue)) {
                $this->data_arrivo->addErrorMessage(str_replace("%s", $this->data_arrivo->caption(), $this->data_arrivo->RequiredErrorMessage));
            }
        }
        if ($this->data_partenza->Required) {
            if (!$this->data_partenza->IsDetailKey && EmptyValue($this->data_partenza->FormValue)) {
                $this->data_partenza->addErrorMessage(str_replace("%s", $this->data_partenza->caption(), $this->data_partenza->RequiredErrorMessage));
            }
        }
        if ($this->piazzola_id->Required) {
            if (!$this->piazzola_id->IsDetailKey && EmptyValue($this->piazzola_id->FormValue)) {
                $this->piazzola_id->addErrorMessage(str_replace("%s", $this->piazzola_id->caption(), $this->piazzola_id->RequiredErrorMessage));
            }
        }
        if ($this->piazzola->Required) {
            if (!$this->piazzola->IsDetailKey && EmptyValue($this->piazzola->FormValue)) {
                $this->piazzola->addErrorMessage(str_replace("%s", $this->piazzola->caption(), $this->piazzola->RequiredErrorMessage));
            }
        }
        if ($this->nome->Required) {
            if (!$this->nome->IsDetailKey && EmptyValue($this->nome->FormValue)) {
                $this->nome->addErrorMessage(str_replace("%s", $this->nome->caption(), $this->nome->RequiredErrorMessage));
            }
        }
        if ($this->cognome->Required) {
            if (!$this->cognome->IsDetailKey && EmptyValue($this->cognome->FormValue)) {
                $this->cognome->addErrorMessage(str_replace("%s", $this->cognome->caption(), $this->cognome->RequiredErrorMessage));
            }
        }
        if ($this->importo->Required) {
            if (!$this->importo->IsDetailKey && EmptyValue($this->importo->FormValue)) {
                $this->importo->addErrorMessage(str_replace("%s", $this->importo->caption(), $this->importo->RequiredErrorMessage));
            }
        }
        if (!CheckNumber($this->importo->FormValue)) {
            $this->importo->addErrorMessage($this->importo->getErrorMessage(false));
        }
        if ($this->_email->Required) {
            if (!$this->_email->IsDetailKey && EmptyValue($this->_email->FormValue)) {
                $this->_email->addErrorMessage(str_replace("%s", $this->_email->caption(), $this->_email->RequiredErrorMessage));
            }
        }
        if ($this->prefisso->Required) {
            if (!$this->prefisso->IsDetailKey && EmptyValue($this->prefisso->FormValue)) {
                $this->prefisso->addErrorMessage(str_replace("%s", $this->prefisso->caption(), $this->prefisso->RequiredErrorMessage));
            }
        }
        if ($this->telefono->Required) {
            if (!$this->telefono->IsDetailKey && EmptyValue($this->telefono->FormValue)) {
                $this->telefono->addErrorMessage(str_replace("%s", $this->telefono->caption(), $this->telefono->RequiredErrorMessage));
            }
        }
        if ($this->codice_fiscale->Required) {
            if (!$this->codice_fiscale->IsDetailKey && EmptyValue($this->codice_fiscale->FormValue)) {
                $this->codice_fiscale->addErrorMessage(str_replace("%s", $this->codice_fiscale->caption(), $this->codice_fiscale->RequiredErrorMessage));
            }
        }
        if ($this->targa->Required) {
            if (!$this->targa->IsDetailKey && EmptyValue($this->targa->FormValue)) {
                $this->targa->addErrorMessage(str_replace("%s", $this->targa->caption(), $this->targa->RequiredErrorMessage));
            }
        }
        if ($this->country->Required) {
            if (!$this->country->IsDetailKey && EmptyValue($this->country->FormValue)) {
                $this->country->addErrorMessage(str_replace("%s", $this->country->caption(), $this->country->RequiredErrorMessage));
            }
        }
        if ($this->invio_whatsapp->Required) {
            if ($this->invio_whatsapp->FormValue == "") {
                $this->invio_whatsapp->addErrorMessage(str_replace("%s", $this->invio_whatsapp->caption(), $this->invio_whatsapp->RequiredErrorMessage));
            }
        }
        if ($this->presenza_disabili->Required) {
            if ($this->presenza_disabili->FormValue == "") {
                $this->presenza_disabili->addErrorMessage(str_replace("%s", $this->presenza_disabili->caption(), $this->presenza_disabili->RequiredErrorMessage));
            }
        }
        if ($this->note->Required) {
            if (!$this->note->IsDetailKey && EmptyValue($this->note->FormValue)) {
                $this->note->addErrorMessage(str_replace("%s", $this->note->caption(), $this->note->RequiredErrorMessage));
            }
        }
        if ($this->wa_inviato->Required) {
            if ($this->wa_inviato->FormValue == "") {
                $this->wa_inviato->addErrorMessage(str_replace("%s", $this->wa_inviato->caption(), $this->wa_inviato->RequiredErrorMessage));
            }
        }

        // Return validate result
        $validateForm = !$this->hasInvalidFields();

        // Call Form_CustomValidate event
        $formCustomError = "";
        $validateForm = $validateForm && $this->formCustomValidate($formCustomError);
        if ($formCustomError != "") {
            $this->setFailureMessage($formCustomError);
        }
        return $validateForm;
    }

    // Update record based on key values
    protected function editRow()
    {
        global $Security, $Language;
        $oldKeyFilter = $this->getRecordFilter();
        $filter = $this->applyUserIDFilters($oldKeyFilter);
        $conn = $this->getConnection();
        $this->CurrentFilter = $filter;
        $sql = $this->getCurrentSql();
        $rsold = $conn->fetchAssoc($sql);
        $editRow = false;
        if (!$rsold) {
            $this->setFailureMessage($Language->phrase("NoRecord")); // Set no record message
            $editRow = false; // Update Failed
        } else {
            // Save old values
            $this->loadDbValues($rsold);
            $rsnew = [];

            // data
            $this->data->setDbValueDef($rsnew, UnFormatDateTime($this->data->CurrentValue, 0), null, $this->data->ReadOnly);

            // status
            $this->status->setDbValueDef($rsnew, $this->status->CurrentValue, null, $this->status->ReadOnly);

            // progress
            $this->progress->setDbValueDef($rsnew, $this->progress->CurrentValue, null, $this->progress->ReadOnly);

            // persone
            $this->persone->setDbValueDef($rsnew, $this->persone->CurrentValue, null, $this->persone->ReadOnly);

            // data_arrivo
            $this->data_arrivo->setDbValueDef($rsnew, UnFormatDateTime($this->data_arrivo->CurrentValue, 1), null, $this->data_arrivo->ReadOnly);

            // data_partenza
            $this->data_partenza->setDbValueDef($rsnew, UnFormatDateTime($this->data_partenza->CurrentValue, 1), null, $this->data_partenza->ReadOnly);

            // piazzola_id
            $this->piazzola_id->setDbValueDef($rsnew, $this->piazzola_id->CurrentValue, null, $this->piazzola_id->ReadOnly);

            // piazzola
            $this->piazzola->setDbValueDef($rsnew, $this->piazzola->CurrentValue, null, $this->piazzola->ReadOnly);

            // nome
            $this->nome->setDbValueDef($rsnew, $this->nome->CurrentValue, null, $this->nome->ReadOnly);

            // cognome
            $this->cognome->setDbValueDef($rsnew, $this->cognome->CurrentValue, null, $this->cognome->ReadOnly);

            // importo
            $this->importo->setDbValueDef($rsnew, $this->importo->CurrentValue, null, $this->importo->ReadOnly);

            // email
            $this->_email->setDbValueDef($rsnew, $this->_email->CurrentValue, null, $this->_email->ReadOnly);

            // prefisso
            $this->prefisso->setDbValueDef($rsnew, $this->prefisso->CurrentValue, null, $this->prefisso->ReadOnly);

            // telefono
            $this->telefono->setDbValueDef($rsnew, $this->telefono->CurrentValue, null, $this->telefono->ReadOnly);

            // codice_fiscale
            $this->codice_fiscale->setDbValueDef($rsnew, $this->codice_fiscale->CurrentValue, null, $this->codice_fiscale->ReadOnly);

            // targa
            $this->targa->setDbValueDef($rsnew, $this->targa->CurrentValue, null, $this->targa->ReadOnly);

            // country
            $this->country->setDbValueDef($rsnew, $this->country->CurrentValue, null, $this->country->ReadOnly);

            // invio_whatsapp
            $tmpBool = $this->invio_whatsapp->CurrentValue;
            if ($tmpBool != "1" && $tmpBool != "0") {
                $tmpBool = !empty($tmpBool) ? "1" : "0";
            }
            $this->invio_whatsapp->setDbValueDef($rsnew, $tmpBool, null, $this->invio_whatsapp->ReadOnly);

            // presenza_disabili
            $tmpBool = $this->presenza_disabili->CurrentValue;
            if ($tmpBool != "1" && $tmpBool != "0") {
                $tmpBool = !empty($tmpBool) ? "1" : "0";
            }
            $this->presenza_disabili->setDbValueDef($rsnew, $tmpBool, null, $this->presenza_disabili->ReadOnly);

            // note
            $this->note->setDbValueDef($rsnew, $this->note->CurrentValue, null, $this->note->ReadOnly);

            // wa_inviato
            $tmpBool = $this->wa_inviato->CurrentValue;
            if ($tmpBool != "1" && $tmpBool != "0") {
                $tmpBool = !empty($tmpBool) ? "1" : "0";
            }
            $this->wa_inviato->setDbValueDef($rsnew, $tmpBool, null, $this->wa_inviato->ReadOnly);

            // Call Row Updating event
            $updateRow = $this->rowUpdating($rsold, $rsnew);
            if ($updateRow) {
                if (count($rsnew) > 0) {
                    try {
                        $editRow = $this->update($rsnew, "", $rsold);
                    } catch (\Exception $e) {
                        $this->setFailureMessage($e->getMessage());
                    }
                } else {
                    $editRow = true; // No field to update
                }
                if ($editRow) {
                }
            } else {
                if ($this->getSuccessMessage() != "" || $this->getFailureMessage() != "") {
                    // Use the message, do nothing
                } elseif ($this->CancelMessage != "") {
                    $this->setFailureMessage($this->CancelMessage);
                    $this->CancelMessage = "";
                } else {
                    $this->setFailureMessage($Language->phrase("UpdateCancelled"));
                }
                $editRow = false;
            }
        }

        // Call Row_Updated event
        if ($editRow) {
            $this->rowUpdated($rsold, $rsnew);
        }

        // Clean upload path if any
        if ($editRow) {
        }

        // Write JSON for API request
        if (IsApi() && $editRow) {
            $row = $this->getRecordsFromRecordset([$rsnew], true);
            WriteJson(["success" => true, $this->TableVar => $row]);
        }
        return $editRow;
    }

    // Set up Breadcrumb
    protected function setupBreadcrumb()
    {
        global $Breadcrumb, $Language;
        $Breadcrumb = new Breadcrumb("index");
        $url = CurrentUrl();
        $Breadcrumb->add("list", $this->TableVar, $this->addMasterUrl("OrdiniList"), "", $this->TableVar, true);
        $pageId = "edit";
        $Breadcrumb->add("edit", $pageId, $url);
    }

    // Setup lookup options
    public function setupLookupOptions($fld)
    {
        if ($fld->Lookup !== null && $fld->Lookup->Options === null) {
            // Get default connection and filter
            $conn = $this->getConnection();
            $lookupFilter = "";

            // No need to check any more
            $fld->Lookup->Options = [];

            // Set up lookup SQL and connection
            switch ($fld->FieldVar) {
                case "x_status":
                    break;
                case "x_progress":
                    break;
                case "x_piazzola_id":
                    break;
                case "x_invio_whatsapp":
                    break;
                case "x_presenza_disabili":
                    break;
                case "x_wa_inviato":
                    break;
                default:
                    $lookupFilter = "";
                    break;
            }

            // Always call to Lookup->getSql so that user can setup Lookup->Options in Lookup_Selecting server event
            $sql = $fld->Lookup->getSql(false, "", $lookupFilter, $this);

            // Set up lookup cache
            if ($fld->UseLookupCache && $sql != "" && count($fld->Lookup->Options) == 0) {
                $totalCnt = $this->getRecordCount($sql, $conn);
                if ($totalCnt > $fld->LookupCacheCount) { // Total count > cache count, do not cache
                    return;
                }
                $rows = $conn->executeQuery($sql)->fetchAll(\PDO::FETCH_BOTH);
                $ar = [];
                foreach ($rows as $row) {
                    $row = $fld->Lookup->renderViewRow($row);
                    $ar[strval($row[0])] = $row;
                }
                $fld->Lookup->Options = $ar;
            }
        }
    }

    // Set up starting record parameters
    public function setupStartRecord()
    {
        if ($this->DisplayRecords == 0) {
            return;
        }
        if ($this->isPageRequest()) { // Validate request
            $startRec = Get(Config("TABLE_START_REC"));
            $pageNo = Get(Config("TABLE_PAGE_NO"));
            if ($pageNo !== null) { // Check for "pageno" parameter first
                if (is_numeric($pageNo)) {
                    $this->StartRecord = ($pageNo - 1) * $this->DisplayRecords + 1;
                    if ($this->StartRecord <= 0) {
                        $this->StartRecord = 1;
                    } elseif ($this->StartRecord >= (int)(($this->TotalRecords - 1) / $this->DisplayRecords) * $this->DisplayRecords + 1) {
                        $this->StartRecord = (int)(($this->TotalRecords - 1) / $this->DisplayRecords) * $this->DisplayRecords + 1;
                    }
                    $this->setStartRecordNumber($this->StartRecord);
                }
            } elseif ($startRec !== null) { // Check for "start" parameter
                $this->StartRecord = $startRec;
                $this->setStartRecordNumber($this->StartRecord);
            }
        }
        $this->StartRecord = $this->getStartRecordNumber();

        // Check if correct start record counter
        if (!is_numeric($this->StartRecord) || $this->StartRecord == "") { // Avoid invalid start record counter
            $this->StartRecord = 1; // Reset start record counter
            $this->setStartRecordNumber($this->StartRecord);
        } elseif ($this->StartRecord > $this->TotalRecords) { // Avoid starting record > total records
            $this->StartRecord = (int)(($this->TotalRecords - 1) / $this->DisplayRecords) * $this->DisplayRecords + 1; // Point to last page first record
            $this->setStartRecordNumber($this->StartRecord);
        } elseif (($this->StartRecord - 1) % $this->DisplayRecords != 0) {
            $this->StartRecord = (int)(($this->StartRecord - 1) / $this->DisplayRecords) * $this->DisplayRecords + 1; // Point to page boundary
            $this->setStartRecordNumber($this->StartRecord);
        }
    }

    // Page Load event
    public function pageLoad()
    {
        //Log("Page Load");
    }

    // Page Unload event
    public function pageUnload()
    {
        //Log("Page Unload");
    }

    // Page Redirecting event
    public function pageRedirecting(&$url)
    {
        // Example:
        //$url = "your URL";
    }

    // Message Showing event
    // $type = ''|'success'|'failure'|'warning'
    public function messageShowing(&$msg, $type)
    {
        if ($type == 'success') {
            //$msg = "your success message";
        } elseif ($type == 'failure') {
            //$msg = "your failure message";
        } elseif ($type == 'warning') {
            //$msg = "your warning message";
        } else {
            //$msg = "your message";
        }
    }

    // Page Render event
    public function pageRender()
    {
        //Log("Page Render");
    }

    // Page Data Rendering event
    public function pageDataRendering(&$header)
    {
        // Example:
        //$header = "your header";
    }

    // Page Data Rendered event
    public function pageDataRendered(&$footer)
    {
        // Example:
        //$footer = "your footer";
    }

    // Form Custom Validate event
    public function formCustomValidate(&$customError)
    {
        // Return error message in CustomError
        return true;
    }
}
