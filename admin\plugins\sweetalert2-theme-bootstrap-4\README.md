# @sweetalert2/theme-bootstrap-4 - Bootstrap 4 Theme for [SweetAlert2](https://github.com/sweetalert2/sweetalert2)

[![npm version](https://img.shields.io/npm/v/@sweetalert2/theme-bootstrap-4.svg)](https://www.npmjs.com/package/@sweetalert2/theme-bootstrap-4)

Installation
------------

```sh
npm install --save sweetalert2 @sweetalert2/theme-bootstrap-4
```

Usage
-----

With CSS:

```html
<!-- Include the Bootstrap 4 theme -->
<link rel="stylesheet" href="@sweetalert2/theme-bootstrap-4/bootstrap-4.css">

<script src="sweetalert2/dist/sweetalert2.min.js"></script>
```

With SASS:

`your-app.js`:
```js
import Swal from 'sweetalert2/src/sweetalert2.js'
```

`your-app.scss`:
```scss
@import '~@sweetalert2/theme-bootstrap-4/bootstrap-4.scss';
```
