<?php

namespace PHPMaker2021\aladin2022;

use Doctrine\DBAL\ParameterType;

/**
 * Page class
 */
class OrdiniDelete extends Ordini
{
    use MessagesTrait;

    // Page ID
    public $PageID = "delete";

    // Project ID
    public $ProjectID = PROJECT_ID;

    // Table name
    public $TableName = 'ordini';

    // Page object name
    public $PageObjName = "OrdiniDelete";

    // Rendering View
    public $RenderingView = false;

    // Page headings
    public $Heading = "";
    public $Subheading = "";
    public $PageHeader;
    public $PageFooter;

    // Page terminated
    private $terminated = false;

    // Page heading
    public function pageHeading()
    {
        global $Language;
        if ($this->Heading != "") {
            return $this->Heading;
        }
        if (method_exists($this, "tableCaption")) {
            return $this->tableCaption();
        }
        return "";
    }

    // Page subheading
    public function pageSubheading()
    {
        global $Language;
        if ($this->Subheading != "") {
            return $this->Subheading;
        }
        if ($this->TableName) {
            return $Language->phrase($this->PageID);
        }
        return "";
    }

    // Page name
    public function pageName()
    {
        return CurrentPageName();
    }

    // Page URL
    public function pageUrl()
    {
        $url = ScriptName() . "?";
        if ($this->UseTokenInUrl) {
            $url .= "t=" . $this->TableVar . "&"; // Add page token
        }
        return $url;
    }

    // Show Page Header
    public function showPageHeader()
    {
        $header = $this->PageHeader;
        $this->pageDataRendering($header);
        if ($header != "") { // Header exists, display
            echo '<p id="ew-page-header">' . $header . '</p>';
        }
    }

    // Show Page Footer
    public function showPageFooter()
    {
        $footer = $this->PageFooter;
        $this->pageDataRendered($footer);
        if ($footer != "") { // Footer exists, display
            echo '<p id="ew-page-footer">' . $footer . '</p>';
        }
    }

    // Validate page request
    protected function isPageRequest()
    {
        global $CurrentForm;
        if ($this->UseTokenInUrl) {
            if ($CurrentForm) {
                return ($this->TableVar == $CurrentForm->getValue("t"));
            }
            if (Get("t") !== null) {
                return ($this->TableVar == Get("t"));
            }
        }
        return true;
    }

    // Constructor
    public function __construct()
    {
        global $Language, $DashboardReport, $DebugTimer;

        // Initialize
        $GLOBALS["Page"] = &$this;

        // Language object
        $Language = Container("language");

        // Parent constuctor
        parent::__construct();

        // Table object (ordini)
        if (!isset($GLOBALS["ordini"]) || get_class($GLOBALS["ordini"]) == PROJECT_NAMESPACE . "ordini") {
            $GLOBALS["ordini"] = &$this;
        }

        // Page URL
        $pageUrl = $this->pageUrl();

        // Table name (for backward compatibility only)
        if (!defined(PROJECT_NAMESPACE . "TABLE_NAME")) {
            define(PROJECT_NAMESPACE . "TABLE_NAME", 'ordini');
        }

        // Start timer
        $DebugTimer = Container("timer");

        // Debug message
        LoadDebugMessage();

        // Open connection
        $GLOBALS["Conn"] = $GLOBALS["Conn"] ?? $this->getConnection();
    }

    // Get content from stream
    public function getContents($stream = null): string
    {
        global $Response;
        return is_object($Response) ? $Response->getBody() : ob_get_clean();
    }

    // Is lookup
    public function isLookup()
    {
        return SameText(Route(0), Config("API_LOOKUP_ACTION"));
    }

    // Is AutoFill
    public function isAutoFill()
    {
        return $this->isLookup() && SameText(Post("ajax"), "autofill");
    }

    // Is AutoSuggest
    public function isAutoSuggest()
    {
        return $this->isLookup() && SameText(Post("ajax"), "autosuggest");
    }

    // Is modal lookup
    public function isModalLookup()
    {
        return $this->isLookup() && SameText(Post("ajax"), "modal");
    }

    // Is terminated
    public function isTerminated()
    {
        return $this->terminated;
    }

    /**
     * Terminate page
     *
     * @param string $url URL for direction
     * @return void
     */
    public function terminate($url = "")
    {
        if ($this->terminated) {
            return;
        }
        global $ExportFileName, $TempImages, $DashboardReport, $Response;

        // Page is terminated
        $this->terminated = true;

         // Page Unload event
        if (method_exists($this, "pageUnload")) {
            $this->pageUnload();
        }

        // Global Page Unloaded event (in userfn*.php)
        Page_Unloaded();

        // Export
        if ($this->CustomExport && $this->CustomExport == $this->Export && array_key_exists($this->CustomExport, Config("EXPORT_CLASSES"))) {
            $content = $this->getContents();
            if ($ExportFileName == "") {
                $ExportFileName = $this->TableVar;
            }
            $class = PROJECT_NAMESPACE . Config("EXPORT_CLASSES." . $this->CustomExport);
            if (class_exists($class)) {
                $doc = new $class(Container("ordini"));
                $doc->Text = @$content;
                if ($this->isExport("email")) {
                    echo $this->exportEmail($doc->Text);
                } else {
                    $doc->export();
                }
                DeleteTempImages(); // Delete temp images
                return;
            }
        }
        if (!IsApi() && method_exists($this, "pageRedirecting")) {
            $this->pageRedirecting($url);
        }

        // Close connection
        CloseConnections();

        // Return for API
        if (IsApi()) {
            $res = $url === true;
            if (!$res) { // Show error
                WriteJson(array_merge(["success" => false], $this->getMessages()));
            }
            return;
        } else { // Check if response is JSON
            if (StartsString("application/json", $Response->getHeaderLine("Content-type")) && $Response->getBody()->getSize()) { // With JSON response
                $this->clearMessages();
                return;
            }
        }

        // Go to URL if specified
        if ($url != "") {
            if (!Config("DEBUG") && ob_get_length()) {
                ob_end_clean();
            }
            SaveDebugMessage();
            Redirect(GetUrl($url));
        }
        return; // Return to controller
    }

    // Get records from recordset
    protected function getRecordsFromRecordset($rs, $current = false)
    {
        $rows = [];
        if (is_object($rs)) { // Recordset
            while ($rs && !$rs->EOF) {
                $this->loadRowValues($rs); // Set up DbValue/CurrentValue
                $row = $this->getRecordFromArray($rs->fields);
                if ($current) {
                    return $row;
                } else {
                    $rows[] = $row;
                }
                $rs->moveNext();
            }
        } elseif (is_array($rs)) {
            foreach ($rs as $ar) {
                $row = $this->getRecordFromArray($ar);
                if ($current) {
                    return $row;
                } else {
                    $rows[] = $row;
                }
            }
        }
        return $rows;
    }

    // Get record from array
    protected function getRecordFromArray($ar)
    {
        $row = [];
        if (is_array($ar)) {
            foreach ($ar as $fldname => $val) {
                if (array_key_exists($fldname, $this->Fields) && ($this->Fields[$fldname]->Visible || $this->Fields[$fldname]->IsPrimaryKey)) { // Primary key or Visible
                    $fld = &$this->Fields[$fldname];
                    if ($fld->HtmlTag == "FILE") { // Upload field
                        if (EmptyValue($val)) {
                            $row[$fldname] = null;
                        } else {
                            if ($fld->DataType == DATATYPE_BLOB) {
                                $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                    "/" . $fld->TableVar . "/" . $fld->Param . "/" . rawurlencode($this->getRecordKeyValue($ar))));
                                $row[$fldname] = ["type" => ContentType($val), "url" => $url, "name" => $fld->Param . ContentExtension($val)];
                            } elseif (!$fld->UploadMultiple || !ContainsString($val, Config("MULTIPLE_UPLOAD_SEPARATOR"))) { // Single file
                                $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                    "/" . $fld->TableVar . "/" . Encrypt($fld->physicalUploadPath() . $val)));
                                $row[$fldname] = ["type" => MimeContentType($val), "url" => $url, "name" => $val];
                            } else { // Multiple files
                                $files = explode(Config("MULTIPLE_UPLOAD_SEPARATOR"), $val);
                                $ar = [];
                                foreach ($files as $file) {
                                    $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                        "/" . $fld->TableVar . "/" . Encrypt($fld->physicalUploadPath() . $file)));
                                    if (!EmptyValue($file)) {
                                        $ar[] = ["type" => MimeContentType($file), "url" => $url, "name" => $file];
                                    }
                                }
                                $row[$fldname] = $ar;
                            }
                        }
                    } else {
                        $row[$fldname] = $val;
                    }
                }
            }
        }
        return $row;
    }

    // Get record key value from array
    protected function getRecordKeyValue($ar)
    {
        $key = "";
        if (is_array($ar)) {
            $key .= @$ar['id'];
        }
        return $key;
    }

    /**
     * Hide fields for add/edit
     *
     * @return void
     */
    protected function hideFieldsForAddEdit()
    {
        if ($this->isAdd() || $this->isCopy() || $this->isGridAdd()) {
            $this->id->Visible = false;
        }
    }
    public $DbMasterFilter = "";
    public $DbDetailFilter = "";
    public $StartRecord;
    public $TotalRecords = 0;
    public $RecordCount;
    public $RecKeys = [];
    public $StartRowCount = 1;
    public $RowCount = 0;

    /**
     * Page run
     *
     * @return void
     */
    public function run()
    {
        global $ExportType, $CustomExportType, $ExportFileName, $UserProfile, $Language, $Security, $CurrentForm;
        $this->CurrentAction = Param("action"); // Set up current action
        $this->id->setVisibility();
        $this->_token->Visible = false;
        $this->data->setVisibility();
        $this->status->Visible = false;
        $this->progress->setVisibility();
        $this->persone->setVisibility();
        $this->data_arrivo->setVisibility();
        $this->data_partenza->setVisibility();
        $this->piazzola_id->setVisibility();
        $this->piazzola->setVisibility();
        $this->nome->setVisibility();
        $this->cognome->setVisibility();
        $this->importo->setVisibility();
        $this->_email->setVisibility();
        $this->prefisso->setVisibility();
        $this->telefono->setVisibility();
        $this->codice_fiscale->setVisibility();
        $this->targa->setVisibility();
        $this->country->setVisibility();
        $this->invio_whatsapp->setVisibility();
        $this->presenza_disabili->setVisibility();
        $this->note->Visible = false;
        $this->wa_inviato->setVisibility();
        $this->debug->Visible = false;
        $this->hideFieldsForAddEdit();

        // Do not use lookup cache
        $this->setUseLookupCache(false);

        // Global Page Loading event (in userfn*.php)
        Page_Loading();

        // Page Load event
        if (method_exists($this, "pageLoad")) {
            $this->pageLoad();
        }

        // Set up lookup cache
        $this->setupLookupOptions($this->piazzola_id);

        // Set up Breadcrumb
        $this->setupBreadcrumb();

        // Load key parameters
        $this->RecKeys = $this->getRecordKeys(); // Load record keys
        $filter = $this->getFilterFromRecordKeys();
        if ($filter == "") {
            $this->terminate("OrdiniList"); // Prevent SQL injection, return to list
            return;
        }

        // Set up filter (WHERE Clause)
        $this->CurrentFilter = $filter;

        // Get action
        if (IsApi()) {
            $this->CurrentAction = "delete"; // Delete record directly
        } elseif (Post("action") !== null) {
            $this->CurrentAction = Post("action");
        } elseif (Get("action") == "1") {
            $this->CurrentAction = "delete"; // Delete record directly
        } else {
            $this->CurrentAction = "show"; // Display record
        }
        if ($this->isDelete()) {
            $this->SendEmail = true; // Send email on delete success
            if ($this->deleteRows()) { // Delete rows
                if ($this->getSuccessMessage() == "") {
                    $this->setSuccessMessage($Language->phrase("DeleteSuccess")); // Set up success message
                }
                if (IsApi()) {
                    $this->terminate(true);
                    return;
                } else {
                    $this->terminate($this->getReturnUrl()); // Return to caller
                    return;
                }
            } else { // Delete failed
                if (IsApi()) {
                    $this->terminate();
                    return;
                }
                $this->CurrentAction = "show"; // Display record
            }
        }
        if ($this->isShow()) { // Load records for display
            if ($this->Recordset = $this->loadRecordset()) {
                $this->TotalRecords = $this->Recordset->recordCount(); // Get record count
            }
            if ($this->TotalRecords <= 0) { // No record found, exit
                if ($this->Recordset) {
                    $this->Recordset->close();
                }
                $this->terminate("OrdiniList"); // Return to list
                return;
            }
        }

        // Set LoginStatus / Page_Rendering / Page_Render
        if (!IsApi() && !$this->isTerminated()) {
            // Pass table and field properties to client side
            $this->toClientVar(["tableCaption"], ["caption", "Visible", "Required", "IsInvalid", "Raw"]);

            // Setup login status
            SetupLoginStatus();

            // Pass login status to client side
            SetClientVar("login", LoginStatus());

            // Global Page Rendering event (in userfn*.php)
            Page_Rendering();

            // Page Render event
            if (method_exists($this, "pageRender")) {
                $this->pageRender();
            }
        }
    }

    // Load recordset
    public function loadRecordset($offset = -1, $rowcnt = -1)
    {
        // Load List page SQL (QueryBuilder)
        $sql = $this->getListSql();

        // Load recordset
        if ($offset > -1) {
            $sql->setFirstResult($offset);
        }
        if ($rowcnt > 0) {
            $sql->setMaxResults($rowcnt);
        }
        $stmt = $sql->execute();
        $rs = new Recordset($stmt, $sql);

        // Call Recordset Selected event
        $this->recordsetSelected($rs);
        return $rs;
    }

    /**
     * Load row based on key values
     *
     * @return void
     */
    public function loadRow()
    {
        global $Security, $Language;
        $filter = $this->getRecordFilter();

        // Call Row Selecting event
        $this->rowSelecting($filter);

        // Load SQL based on filter
        $this->CurrentFilter = $filter;
        $sql = $this->getCurrentSql();
        $conn = $this->getConnection();
        $res = false;
        $row = $conn->fetchAssoc($sql);
        if ($row) {
            $res = true;
            $this->loadRowValues($row); // Load row values
        }
        return $res;
    }

    /**
     * Load row values from recordset or record
     *
     * @param Recordset|array $rs Record
     * @return void
     */
    public function loadRowValues($rs = null)
    {
        if (is_array($rs)) {
            $row = $rs;
        } elseif ($rs && property_exists($rs, "fields")) { // Recordset
            $row = $rs->fields;
        } else {
            $row = $this->newRow();
        }

        // Call Row Selected event
        $this->rowSelected($row);
        if (!$rs) {
            return;
        }
        $this->id->setDbValue($row['id']);
        $this->_token->setDbValue($row['token']);
        $this->data->setDbValue($row['data']);
        $this->status->setDbValue($row['status']);
        $this->progress->setDbValue($row['progress']);
        $this->persone->setDbValue($row['persone']);
        $this->data_arrivo->setDbValue($row['data_arrivo']);
        $this->data_partenza->setDbValue($row['data_partenza']);
        $this->piazzola_id->setDbValue($row['piazzola_id']);
        $this->piazzola->setDbValue($row['piazzola']);
        $this->nome->setDbValue($row['nome']);
        $this->cognome->setDbValue($row['cognome']);
        $this->importo->setDbValue($row['importo']);
        $this->_email->setDbValue($row['email']);
        $this->prefisso->setDbValue($row['prefisso']);
        $this->telefono->setDbValue($row['telefono']);
        $this->codice_fiscale->setDbValue($row['codice_fiscale']);
        $this->targa->setDbValue($row['targa']);
        $this->country->setDbValue($row['country']);
        $this->invio_whatsapp->setDbValue($row['invio_whatsapp']);
        $this->presenza_disabili->setDbValue($row['presenza_disabili']);
        $this->note->setDbValue($row['note']);
        $this->wa_inviato->setDbValue($row['wa_inviato']);
        $this->debug->setDbValue($row['debug']);
    }

    // Return a row with default values
    protected function newRow()
    {
        $row = [];
        $row['id'] = null;
        $row['token'] = null;
        $row['data'] = null;
        $row['status'] = null;
        $row['progress'] = null;
        $row['persone'] = null;
        $row['data_arrivo'] = null;
        $row['data_partenza'] = null;
        $row['piazzola_id'] = null;
        $row['piazzola'] = null;
        $row['nome'] = null;
        $row['cognome'] = null;
        $row['importo'] = null;
        $row['email'] = null;
        $row['prefisso'] = null;
        $row['telefono'] = null;
        $row['codice_fiscale'] = null;
        $row['targa'] = null;
        $row['country'] = null;
        $row['invio_whatsapp'] = null;
        $row['presenza_disabili'] = null;
        $row['note'] = null;
        $row['wa_inviato'] = null;
        $row['debug'] = null;
        return $row;
    }

    // Render row values based on field settings
    public function renderRow()
    {
        global $Security, $Language, $CurrentLanguage;

        // Initialize URLs

        // Convert decimal values if posted back
        if ($this->importo->FormValue == $this->importo->CurrentValue && is_numeric(ConvertToFloatString($this->importo->CurrentValue))) {
            $this->importo->CurrentValue = ConvertToFloatString($this->importo->CurrentValue);
        }

        // Call Row_Rendering event
        $this->rowRendering();

        // Common render codes for all row types

        // id

        // token
        $this->_token->CellCssStyle = "white-space: nowrap;";

        // data

        // status
        $this->status->CellCssStyle = "white-space: nowrap;";

        // progress

        // persone

        // data_arrivo

        // data_partenza

        // piazzola_id

        // piazzola

        // nome

        // cognome

        // importo

        // email

        // prefisso

        // telefono

        // codice_fiscale

        // targa

        // country

        // invio_whatsapp

        // presenza_disabili

        // note

        // wa_inviato

        // debug
        $this->debug->CellCssStyle = "white-space: nowrap;";
        if ($this->RowType == ROWTYPE_VIEW) {
            // id
            $this->id->ViewValue = $this->id->CurrentValue;
            $this->id->ViewCustomAttributes = "";

            // data
            $this->data->ViewValue = $this->data->CurrentValue;
            $this->data->ViewValue = FormatDateTime($this->data->ViewValue, 0);
            $this->data->ViewCustomAttributes = "";

            // progress
            if (strval($this->progress->CurrentValue) != "") {
                $this->progress->ViewValue = $this->progress->optionCaption($this->progress->CurrentValue);
            } else {
                $this->progress->ViewValue = null;
            }
            $this->progress->ViewCustomAttributes = "";

            // persone
            $this->persone->ViewValue = $this->persone->CurrentValue;
            $this->persone->ViewValue = FormatNumber($this->persone->ViewValue, 0, -2, -2, -2);
            $this->persone->ViewCustomAttributes = "";

            // data_arrivo
            $this->data_arrivo->ViewValue = $this->data_arrivo->CurrentValue;
            $this->data_arrivo->ViewValue = FormatDateTime($this->data_arrivo->ViewValue, 1);
            $this->data_arrivo->ViewCustomAttributes = "";

            // data_partenza
            $this->data_partenza->ViewValue = $this->data_partenza->CurrentValue;
            $this->data_partenza->ViewValue = FormatDateTime($this->data_partenza->ViewValue, 1);
            $this->data_partenza->ViewCustomAttributes = "";

            // piazzola_id
            $curVal = trim(strval($this->piazzola_id->CurrentValue));
            if ($curVal != "") {
                $this->piazzola_id->ViewValue = $this->piazzola_id->lookupCacheOption($curVal);
                if ($this->piazzola_id->ViewValue === null) { // Lookup from database
                    $filterWrk = "`id`" . SearchString("=", $curVal, DATATYPE_NUMBER, "");
                    $sqlWrk = $this->piazzola_id->Lookup->getSql(false, $filterWrk, '', $this, true, true);
                    $rswrk = Conn()->executeQuery($sqlWrk)->fetchAll(\PDO::FETCH_BOTH);
                    $ari = count($rswrk);
                    if ($ari > 0) { // Lookup values found
                        $arwrk = $this->piazzola_id->Lookup->renderViewRow($rswrk[0]);
                        $this->piazzola_id->ViewValue = $this->piazzola_id->displayValue($arwrk);
                    } else {
                        $this->piazzola_id->ViewValue = $this->piazzola_id->CurrentValue;
                    }
                }
            } else {
                $this->piazzola_id->ViewValue = null;
            }
            $this->piazzola_id->ViewCustomAttributes = "";

            // piazzola
            $this->piazzola->ViewValue = $this->piazzola->CurrentValue;
            $this->piazzola->ViewCustomAttributes = "";

            // nome
            $this->nome->ViewValue = $this->nome->CurrentValue;
            $this->nome->ViewCustomAttributes = "";

            // cognome
            $this->cognome->ViewValue = $this->cognome->CurrentValue;
            $this->cognome->ViewCustomAttributes = "";

            // importo
            $this->importo->ViewValue = $this->importo->CurrentValue;
            $this->importo->ViewValue = FormatCurrency($this->importo->ViewValue, 2, -1, -2, -2);
            $this->importo->ViewCustomAttributes = "";

            // email
            $this->_email->ViewValue = $this->_email->CurrentValue;
            $this->_email->ViewCustomAttributes = "";

            // prefisso
            $this->prefisso->ViewValue = $this->prefisso->CurrentValue;
            $this->prefisso->ViewCustomAttributes = "";

            // telefono
            $this->telefono->ViewValue = $this->telefono->CurrentValue;
            $this->telefono->ViewCustomAttributes = "";

            // codice_fiscale
            $this->codice_fiscale->ViewValue = $this->codice_fiscale->CurrentValue;
            $this->codice_fiscale->ViewCustomAttributes = "";

            // targa
            $this->targa->ViewValue = $this->targa->CurrentValue;
            $this->targa->ViewCustomAttributes = "";

            // country
            $this->country->ViewValue = $this->country->CurrentValue;
            $this->country->ViewCustomAttributes = "";

            // invio_whatsapp
            if (ConvertToBool($this->invio_whatsapp->CurrentValue)) {
                $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(2) != "" ? $this->invio_whatsapp->tagCaption(2) : "si";
            } else {
                $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(1) != "" ? $this->invio_whatsapp->tagCaption(1) : "no";
            }
            $this->invio_whatsapp->ViewCustomAttributes = "";

            // presenza_disabili
            if (ConvertToBool($this->presenza_disabili->CurrentValue)) {
                $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(2) != "" ? $this->presenza_disabili->tagCaption(2) : "si";
            } else {
                $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(1) != "" ? $this->presenza_disabili->tagCaption(1) : "no";
            }
            $this->presenza_disabili->ViewCustomAttributes = "";

            // wa_inviato
            if (ConvertToBool($this->wa_inviato->CurrentValue)) {
                $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(2) != "" ? $this->wa_inviato->tagCaption(2) : "1";
            } else {
                $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(1) != "" ? $this->wa_inviato->tagCaption(1) : "0";
            }
            $this->wa_inviato->ViewCustomAttributes = "";

            // id
            $this->id->LinkCustomAttributes = "";
            $this->id->HrefValue = "";
            $this->id->TooltipValue = "";

            // data
            $this->data->LinkCustomAttributes = "";
            $this->data->HrefValue = "";
            $this->data->TooltipValue = "";

            // progress
            $this->progress->LinkCustomAttributes = "";
            $this->progress->HrefValue = "";
            $this->progress->TooltipValue = "";

            // persone
            $this->persone->LinkCustomAttributes = "";
            $this->persone->HrefValue = "";
            $this->persone->TooltipValue = "";

            // data_arrivo
            $this->data_arrivo->LinkCustomAttributes = "";
            $this->data_arrivo->HrefValue = "";
            $this->data_arrivo->TooltipValue = "";

            // data_partenza
            $this->data_partenza->LinkCustomAttributes = "";
            $this->data_partenza->HrefValue = "";
            $this->data_partenza->TooltipValue = "";

            // piazzola_id
            $this->piazzola_id->LinkCustomAttributes = "";
            $this->piazzola_id->HrefValue = "";
            $this->piazzola_id->TooltipValue = "";

            // piazzola
            $this->piazzola->LinkCustomAttributes = "";
            $this->piazzola->HrefValue = "";
            $this->piazzola->TooltipValue = "";

            // nome
            $this->nome->LinkCustomAttributes = "";
            $this->nome->HrefValue = "";
            $this->nome->TooltipValue = "";

            // cognome
            $this->cognome->LinkCustomAttributes = "";
            $this->cognome->HrefValue = "";
            $this->cognome->TooltipValue = "";

            // importo
            $this->importo->LinkCustomAttributes = "";
            $this->importo->HrefValue = "";
            $this->importo->TooltipValue = "";

            // email
            $this->_email->LinkCustomAttributes = "";
            $this->_email->HrefValue = "";
            $this->_email->TooltipValue = "";

            // prefisso
            $this->prefisso->LinkCustomAttributes = "";
            $this->prefisso->HrefValue = "";
            $this->prefisso->TooltipValue = "";

            // telefono
            $this->telefono->LinkCustomAttributes = "";
            $this->telefono->HrefValue = "";
            $this->telefono->TooltipValue = "";

            // codice_fiscale
            $this->codice_fiscale->LinkCustomAttributes = "";
            $this->codice_fiscale->HrefValue = "";
            $this->codice_fiscale->TooltipValue = "";

            // targa
            $this->targa->LinkCustomAttributes = "";
            $this->targa->HrefValue = "";
            $this->targa->TooltipValue = "";

            // country
            $this->country->LinkCustomAttributes = "";
            $this->country->HrefValue = "";
            $this->country->TooltipValue = "";

            // invio_whatsapp
            $this->invio_whatsapp->LinkCustomAttributes = "";
            $this->invio_whatsapp->HrefValue = "";
            $this->invio_whatsapp->TooltipValue = "";

            // presenza_disabili
            $this->presenza_disabili->LinkCustomAttributes = "";
            $this->presenza_disabili->HrefValue = "";
            $this->presenza_disabili->TooltipValue = "";

            // wa_inviato
            $this->wa_inviato->LinkCustomAttributes = "";
            $this->wa_inviato->HrefValue = "";
            $this->wa_inviato->TooltipValue = "";
        }

        // Call Row Rendered event
        if ($this->RowType != ROWTYPE_AGGREGATEINIT) {
            $this->rowRendered();
        }
    }

    // Delete records based on current filter
    protected function deleteRows()
    {
        global $Language, $Security;
        $deleteRows = true;
        $sql = $this->getCurrentSql();
        $conn = $this->getConnection();
        $rows = $conn->fetchAll($sql);
        if (count($rows) == 0) {
            $this->setFailureMessage($Language->phrase("NoRecord")); // No record found
            return false;
        }
        $conn->beginTransaction();

        // Clone old rows
        $rsold = $rows;

        // Call row deleting event
        if ($deleteRows) {
            foreach ($rsold as $row) {
                $deleteRows = $this->rowDeleting($row);
                if (!$deleteRows) {
                    break;
                }
            }
        }
        if ($deleteRows) {
            $key = "";
            foreach ($rsold as $row) {
                $thisKey = "";
                if ($thisKey != "") {
                    $thisKey .= Config("COMPOSITE_KEY_SEPARATOR");
                }
                $thisKey .= $row['id'];
                if (Config("DELETE_UPLOADED_FILES")) { // Delete old files
                    $this->deleteUploadedFiles($row);
                }
                $deleteRows = $this->delete($row); // Delete
                if ($deleteRows === false) {
                    break;
                }
                if ($key != "") {
                    $key .= ", ";
                }
                $key .= $thisKey;
            }
        }
        if (!$deleteRows) {
            // Set up error message
            if ($this->getSuccessMessage() != "" || $this->getFailureMessage() != "") {
                // Use the message, do nothing
            } elseif ($this->CancelMessage != "") {
                $this->setFailureMessage($this->CancelMessage);
                $this->CancelMessage = "";
            } else {
                $this->setFailureMessage($Language->phrase("DeleteCancelled"));
            }
        }
        if ($deleteRows) {
            $conn->commit(); // Commit the changes
        } else {
            $conn->rollback(); // Rollback changes
        }

        // Call Row Deleted event
        if ($deleteRows) {
            foreach ($rsold as $row) {
                $this->rowDeleted($row);
            }
        }

        // Write JSON for API request
        if (IsApi() && $deleteRows) {
            $row = $this->getRecordsFromRecordset($rsold);
            WriteJson(["success" => true, $this->TableVar => $row]);
        }
        return $deleteRows;
    }

    // Set up Breadcrumb
    protected function setupBreadcrumb()
    {
        global $Breadcrumb, $Language;
        $Breadcrumb = new Breadcrumb("index");
        $url = CurrentUrl();
        $Breadcrumb->add("list", $this->TableVar, $this->addMasterUrl("OrdiniList"), "", $this->TableVar, true);
        $pageId = "delete";
        $Breadcrumb->add("delete", $pageId, $url);
    }

    // Setup lookup options
    public function setupLookupOptions($fld)
    {
        if ($fld->Lookup !== null && $fld->Lookup->Options === null) {
            // Get default connection and filter
            $conn = $this->getConnection();
            $lookupFilter = "";

            // No need to check any more
            $fld->Lookup->Options = [];

            // Set up lookup SQL and connection
            switch ($fld->FieldVar) {
                case "x_status":
                    break;
                case "x_progress":
                    break;
                case "x_piazzola_id":
                    break;
                case "x_invio_whatsapp":
                    break;
                case "x_presenza_disabili":
                    break;
                case "x_wa_inviato":
                    break;
                default:
                    $lookupFilter = "";
                    break;
            }

            // Always call to Lookup->getSql so that user can setup Lookup->Options in Lookup_Selecting server event
            $sql = $fld->Lookup->getSql(false, "", $lookupFilter, $this);

            // Set up lookup cache
            if ($fld->UseLookupCache && $sql != "" && count($fld->Lookup->Options) == 0) {
                $totalCnt = $this->getRecordCount($sql, $conn);
                if ($totalCnt > $fld->LookupCacheCount) { // Total count > cache count, do not cache
                    return;
                }
                $rows = $conn->executeQuery($sql)->fetchAll(\PDO::FETCH_BOTH);
                $ar = [];
                foreach ($rows as $row) {
                    $row = $fld->Lookup->renderViewRow($row);
                    $ar[strval($row[0])] = $row;
                }
                $fld->Lookup->Options = $ar;
            }
        }
    }

    // Page Load event
    public function pageLoad()
    {
        //Log("Page Load");
    }

    // Page Unload event
    public function pageUnload()
    {
        //Log("Page Unload");
    }

    // Page Redirecting event
    public function pageRedirecting(&$url)
    {
        // Example:
        //$url = "your URL";
    }

    // Message Showing event
    // $type = ''|'success'|'failure'|'warning'
    public function messageShowing(&$msg, $type)
    {
        if ($type == 'success') {
            //$msg = "your success message";
        } elseif ($type == 'failure') {
            //$msg = "your failure message";
        } elseif ($type == 'warning') {
            //$msg = "your warning message";
        } else {
            //$msg = "your message";
        }
    }

    // Page Render event
    public function pageRender()
    {
        //Log("Page Render");
    }

    // Page Data Rendering event
    public function pageDataRendering(&$header)
    {
        // Example:
        //$header = "your header";
    }

    // Page Data Rendered event
    public function pageDataRendered(&$footer)
    {
        // Example:
        //$footer = "your footer";
    }
}
