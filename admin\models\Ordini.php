<?php

namespace PHPMaker2021\aladin2022;

use Doctrine\DBAL\ParameterType;

/**
 * Table class for ordini
 */
class Ordini extends DbTable
{
    protected $SqlFrom = "";
    protected $SqlSelect = null;
    protected $SqlSelectList = null;
    protected $SqlWhere = "";
    protected $SqlGroupBy = "";
    protected $SqlHaving = "";
    protected $SqlOrderBy = "";
    public $UseSessionForListSql = true;

    // Column CSS classes
    public $LeftColumnClass = "col-sm-2 col-form-label ew-label";
    public $RightColumnClass = "col-sm-10";
    public $OffsetColumnClass = "col-sm-10 offset-sm-2";
    public $TableLeftColumnClass = "w-col-2";

    // Export
    public $ExportDoc;

    // Fields
    public $id;
    public $_token;
    public $data;
    public $status;
    public $progress;
    public $persone;
    public $data_arrivo;
    public $data_partenza;
    public $piazzola_id;
    public $piazzola;
    public $nome;
    public $cognome;
    public $importo;
    public $_email;
    public $prefisso;
    public $telefono;
    public $codice_fiscale;
    public $targa;
    public $country;
    public $invio_whatsapp;
    public $presenza_disabili;
    public $note;
    public $wa_inviato;
    public $debug;

    // Page ID
    public $PageID = ""; // To be overridden by subclass

    // Constructor
    public function __construct()
    {
        global $Language, $CurrentLanguage;
        parent::__construct();

        // Language object
        $Language = Container("language");
        $this->TableVar = 'ordini';
        $this->TableName = 'ordini';
        $this->TableType = 'TABLE';

        // Update Table
        $this->UpdateTable = "`ordini`";
        $this->Dbid = 'DB';
        $this->ExportAll = true;
        $this->ExportPageBreakCount = 0; // Page break per every n record (PDF only)
        $this->ExportPageOrientation = "portrait"; // Page orientation (PDF only)
        $this->ExportPageSize = "a4"; // Page size (PDF only)
        $this->ExportExcelPageOrientation = ""; // Page orientation (PhpSpreadsheet only)
        $this->ExportExcelPageSize = ""; // Page size (PhpSpreadsheet only)
        $this->ExportWordPageOrientation = "portrait"; // Page orientation (PHPWord only)
        $this->ExportWordColumnWidth = null; // Cell width (PHPWord only)
        $this->DetailAdd = false; // Allow detail add
        $this->DetailEdit = false; // Allow detail edit
        $this->DetailView = false; // Allow detail view
        $this->ShowMultipleDetails = false; // Show multiple details
        $this->GridAddRowCount = 5;
        $this->AllowAddDeleteRow = true; // Allow add/delete row
        $this->UserIDAllowSecurity = Config("DEFAULT_USER_ID_ALLOW_SECURITY"); // Default User ID allowed permissions
        $this->BasicSearch = new BasicSearch($this->TableVar);

        // id
        $this->id = new DbField('ordini', 'ordini', 'x_id', 'id', '`id`', '`id`', 3, 11, -1, false, '`id`', false, false, false, 'FORMATTED TEXT', 'NO');
        $this->id->IsAutoIncrement = true; // Autoincrement field
        $this->id->IsPrimaryKey = true; // Primary key field
        $this->id->Sortable = true; // Allow sort
        $this->id->DefaultErrorMessage = $Language->phrase("IncorrectInteger");
        $this->id->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->id->Param, "CustomMsg");
        $this->Fields['id'] = &$this->id;

        // token
        $this->_token = new DbField('ordini', 'ordini', 'x__token', 'token', '`token`', '`token`', 200, 255, -1, false, '`token`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->_token->Sortable = false; // Allow sort
        $this->_token->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->_token->Param, "CustomMsg");
        $this->Fields['token'] = &$this->_token;

        // data
        $this->data = new DbField('ordini', 'ordini', 'x_data', 'data', '`data`', CastDateFieldForLike("`data`", 0, "DB"), 135, 19, 0, false, '`data`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->data->Sortable = true; // Allow sort
        $this->data->DefaultErrorMessage = str_replace("%s", $GLOBALS["DATE_FORMAT"], $Language->phrase("IncorrectDate"));
        $this->data->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->data->Param, "CustomMsg");
        $this->Fields['data'] = &$this->data;

        // status
        $this->status = new DbField('ordini', 'ordini', 'x_status', 'status', '`status`', '`status`', 202, 5, -1, false, '`status`', false, false, false, 'FORMATTED TEXT', 'RADIO');
        $this->status->Sortable = false; // Allow sort
        $this->status->Lookup = new Lookup('status', 'ordini', false, '', ["","","",""], [], [], [], [], [], [], '', '');
        $this->status->OptionCount = 2;
        $this->status->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->status->Param, "CustomMsg");
        $this->Fields['status'] = &$this->status;

        // progress
        $this->progress = new DbField('ordini', 'ordini', 'x_progress', 'progress', '`progress`', '`progress`', 202, 16, -1, false, '`progress`', false, false, false, 'FORMATTED TEXT', 'RADIO');
        $this->progress->Sortable = true; // Allow sort
        $this->progress->Lookup = new Lookup('progress', 'ordini', false, '', ["","","",""], [], [], [], [], [], [], '', '');
        $this->progress->OptionCount = 2;
        $this->progress->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->progress->Param, "CustomMsg");
        $this->Fields['progress'] = &$this->progress;

        // persone
        $this->persone = new DbField('ordini', 'ordini', 'x_persone', 'persone', '`persone`', '`persone`', 3, 11, -1, false, '`persone`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->persone->Sortable = true; // Allow sort
        $this->persone->DefaultErrorMessage = $Language->phrase("IncorrectInteger");
        $this->persone->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->persone->Param, "CustomMsg");
        $this->Fields['persone'] = &$this->persone;

        // data_arrivo
        $this->data_arrivo = new DbField('ordini', 'ordini', 'x_data_arrivo', 'data_arrivo', '`data_arrivo`', CastDateFieldForLike("`data_arrivo`", 1, "DB"), 135, 19, 1, false, '`data_arrivo`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->data_arrivo->Sortable = true; // Allow sort
        $this->data_arrivo->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->data_arrivo->Param, "CustomMsg");
        $this->Fields['data_arrivo'] = &$this->data_arrivo;

        // data_partenza
        $this->data_partenza = new DbField('ordini', 'ordini', 'x_data_partenza', 'data_partenza', '`data_partenza`', CastDateFieldForLike("`data_partenza`", 1, "DB"), 135, 19, 1, false, '`data_partenza`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->data_partenza->Sortable = true; // Allow sort
        $this->data_partenza->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->data_partenza->Param, "CustomMsg");
        $this->Fields['data_partenza'] = &$this->data_partenza;

        // piazzola_id
        $this->piazzola_id = new DbField('ordini', 'ordini', 'x_piazzola_id', 'piazzola_id', '`piazzola_id`', '`piazzola_id`', 3, 11, -1, false, '`piazzola_id`', false, false, false, 'FORMATTED TEXT', 'SELECT');
        $this->piazzola_id->Sortable = true; // Allow sort
        $this->piazzola_id->UsePleaseSelect = true; // Use PleaseSelect by default
        $this->piazzola_id->PleaseSelectText = $Language->phrase("PleaseSelect"); // "PleaseSelect" text
        $this->piazzola_id->Lookup = new Lookup('piazzola_id', 'piazzole', false, 'id', ["piazzola_it","","",""], [], [], [], [], [], [], '', '');
        $this->piazzola_id->DefaultErrorMessage = $Language->phrase("IncorrectInteger");
        $this->piazzola_id->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->piazzola_id->Param, "CustomMsg");
        $this->Fields['piazzola_id'] = &$this->piazzola_id;

        // piazzola
        $this->piazzola = new DbField('ordini', 'ordini', 'x_piazzola', 'piazzola', '`piazzola`', '`piazzola`', 200, 100, -1, false, '`piazzola`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->piazzola->Sortable = true; // Allow sort
        $this->piazzola->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->piazzola->Param, "CustomMsg");
        $this->Fields['piazzola'] = &$this->piazzola;

        // nome
        $this->nome = new DbField('ordini', 'ordini', 'x_nome', 'nome', '`nome`', '`nome`', 200, 255, -1, false, '`nome`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->nome->Sortable = true; // Allow sort
        $this->nome->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->nome->Param, "CustomMsg");
        $this->Fields['nome'] = &$this->nome;

        // cognome
        $this->cognome = new DbField('ordini', 'ordini', 'x_cognome', 'cognome', '`cognome`', '`cognome`', 200, 255, -1, false, '`cognome`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->cognome->Sortable = true; // Allow sort
        $this->cognome->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->cognome->Param, "CustomMsg");
        $this->Fields['cognome'] = &$this->cognome;

        // importo
        $this->importo = new DbField('ordini', 'ordini', 'x_importo', 'importo', '`importo`', '`importo`', 131, 10, -1, false, '`importo`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->importo->Sortable = true; // Allow sort
        $this->importo->DefaultDecimalPrecision = 2; // Default decimal precision
        $this->importo->DefaultErrorMessage = $Language->phrase("IncorrectFloat");
        $this->importo->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->importo->Param, "CustomMsg");
        $this->Fields['importo'] = &$this->importo;

        // email
        $this->_email = new DbField('ordini', 'ordini', 'x__email', 'email', '`email`', '`email`', 200, 255, -1, false, '`email`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->_email->Sortable = true; // Allow sort
        $this->_email->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->_email->Param, "CustomMsg");
        $this->Fields['email'] = &$this->_email;

        // prefisso
        $this->prefisso = new DbField('ordini', 'ordini', 'x_prefisso', 'prefisso', '`prefisso`', '`prefisso`', 200, 10, -1, false, '`prefisso`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->prefisso->Sortable = true; // Allow sort
        $this->prefisso->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->prefisso->Param, "CustomMsg");
        $this->Fields['prefisso'] = &$this->prefisso;

        // telefono
        $this->telefono = new DbField('ordini', 'ordini', 'x_telefono', 'telefono', '`telefono`', '`telefono`', 200, 255, -1, false, '`telefono`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->telefono->Sortable = true; // Allow sort
        $this->telefono->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->telefono->Param, "CustomMsg");
        $this->Fields['telefono'] = &$this->telefono;

        // codice_fiscale
        $this->codice_fiscale = new DbField('ordini', 'ordini', 'x_codice_fiscale', 'codice_fiscale', '`codice_fiscale`', '`codice_fiscale`', 200, 30, -1, false, '`codice_fiscale`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->codice_fiscale->Sortable = true; // Allow sort
        $this->codice_fiscale->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->codice_fiscale->Param, "CustomMsg");
        $this->Fields['codice_fiscale'] = &$this->codice_fiscale;

        // targa
        $this->targa = new DbField('ordini', 'ordini', 'x_targa', 'targa', '`targa`', '`targa`', 200, 50, -1, false, '`targa`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->targa->Sortable = true; // Allow sort
        $this->targa->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->targa->Param, "CustomMsg");
        $this->Fields['targa'] = &$this->targa;

        // country
        $this->country = new DbField('ordini', 'ordini', 'x_country', 'country', '`country`', '`country`', 200, 255, -1, false, '`country`', false, false, false, 'FORMATTED TEXT', 'TEXT');
        $this->country->Sortable = true; // Allow sort
        $this->country->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->country->Param, "CustomMsg");
        $this->Fields['country'] = &$this->country;

        // invio_whatsapp
        $this->invio_whatsapp = new DbField('ordini', 'ordini', 'x_invio_whatsapp', 'invio_whatsapp', '`invio_whatsapp`', '`invio_whatsapp`', 202, 1, -1, false, '`invio_whatsapp`', false, false, false, 'FORMATTED TEXT', 'CHECKBOX');
        $this->invio_whatsapp->Sortable = true; // Allow sort
        $this->invio_whatsapp->DataType = DATATYPE_BOOLEAN;
        $this->invio_whatsapp->Lookup = new Lookup('invio_whatsapp', 'ordini', false, '', ["","","",""], [], [], [], [], [], [], '', '');
        $this->invio_whatsapp->OptionCount = 2;
        $this->invio_whatsapp->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->invio_whatsapp->Param, "CustomMsg");
        $this->Fields['invio_whatsapp'] = &$this->invio_whatsapp;

        // presenza_disabili
        $this->presenza_disabili = new DbField('ordini', 'ordini', 'x_presenza_disabili', 'presenza_disabili', '`presenza_disabili`', '`presenza_disabili`', 202, 1, -1, false, '`presenza_disabili`', false, false, false, 'FORMATTED TEXT', 'CHECKBOX');
        $this->presenza_disabili->Sortable = true; // Allow sort
        $this->presenza_disabili->DataType = DATATYPE_BOOLEAN;
        $this->presenza_disabili->Lookup = new Lookup('presenza_disabili', 'ordini', false, '', ["","","",""], [], [], [], [], [], [], '', '');
        $this->presenza_disabili->OptionCount = 2;
        $this->presenza_disabili->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->presenza_disabili->Param, "CustomMsg");
        $this->Fields['presenza_disabili'] = &$this->presenza_disabili;

        // note
        $this->note = new DbField('ordini', 'ordini', 'x_note', 'note', '`note`', '`note`', 201, 65535, -1, false, '`note`', false, false, false, 'FORMATTED TEXT', 'TEXTAREA');
        $this->note->Sortable = true; // Allow sort
        $this->note->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->note->Param, "CustomMsg");
        $this->Fields['note'] = &$this->note;

        // wa_inviato
        $this->wa_inviato = new DbField('ordini', 'ordini', 'x_wa_inviato', 'wa_inviato', '`wa_inviato`', '`wa_inviato`', 202, 1, -1, false, '`wa_inviato`', false, false, false, 'FORMATTED TEXT', 'CHECKBOX');
        $this->wa_inviato->Sortable = true; // Allow sort
        $this->wa_inviato->DataType = DATATYPE_BOOLEAN;
        $this->wa_inviato->Lookup = new Lookup('wa_inviato', 'ordini', false, '', ["","","",""], [], [], [], [], [], [], '', '');
        $this->wa_inviato->OptionCount = 2;
        $this->wa_inviato->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->wa_inviato->Param, "CustomMsg");
        $this->Fields['wa_inviato'] = &$this->wa_inviato;

        // debug
        $this->debug = new DbField('ordini', 'ordini', 'x_debug', 'debug', '`debug`', '`debug`', 201, 65535, -1, false, '`debug`', false, false, false, 'FORMATTED TEXT', 'TEXTAREA');
        $this->debug->Sortable = false; // Allow sort
        $this->debug->CustomMsg = $Language->FieldPhrase($this->TableVar, $this->debug->Param, "CustomMsg");
        $this->Fields['debug'] = &$this->debug;
    }

    // Field Visibility
    public function getFieldVisibility($fldParm)
    {
        global $Security;
        return $this->$fldParm->Visible; // Returns original value
    }

    // Set left column class (must be predefined col-*-* classes of Bootstrap grid system)
    public function setLeftColumnClass($class)
    {
        if (preg_match('/^col\-(\w+)\-(\d+)$/', $class, $match)) {
            $this->LeftColumnClass = $class . " col-form-label ew-label";
            $this->RightColumnClass = "col-" . $match[1] . "-" . strval(12 - (int)$match[2]);
            $this->OffsetColumnClass = $this->RightColumnClass . " " . str_replace("col-", "offset-", $class);
            $this->TableLeftColumnClass = preg_replace('/^col-\w+-(\d+)$/', "w-col-$1", $class); // Change to w-col-*
        }
    }

    // Single column sort
    public function updateSort(&$fld)
    {
        if ($this->CurrentOrder == $fld->Name) {
            $sortField = $fld->Expression;
            $lastSort = $fld->getSort();
            if (in_array($this->CurrentOrderType, ["ASC", "DESC", "NO"])) {
                $curSort = $this->CurrentOrderType;
            } else {
                $curSort = $lastSort;
            }
            $fld->setSort($curSort);
            $orderBy = in_array($curSort, ["ASC", "DESC"]) ? $sortField . " " . $curSort : "";
            $this->setSessionOrderBy($orderBy); // Save to Session
        } else {
            $fld->setSort("");
        }
    }

    // Table level SQL
    public function getSqlFrom() // From
    {
        return ($this->SqlFrom != "") ? $this->SqlFrom : "`ordini`";
    }

    public function sqlFrom() // For backward compatibility
    {
        return $this->getSqlFrom();
    }

    public function setSqlFrom($v)
    {
        $this->SqlFrom = $v;
    }

    public function getSqlSelect() // Select
    {
        return $this->SqlSelect ?? $this->getQueryBuilder()->select("*");
    }

    public function sqlSelect() // For backward compatibility
    {
        return $this->getSqlSelect();
    }

    public function setSqlSelect($v)
    {
        $this->SqlSelect = $v;
    }

    public function getSqlWhere() // Where
    {
        $where = ($this->SqlWhere != "") ? $this->SqlWhere : "";
        $this->DefaultFilter = "`status`='close'";
        AddFilter($where, $this->DefaultFilter);
        return $where;
    }

    public function sqlWhere() // For backward compatibility
    {
        return $this->getSqlWhere();
    }

    public function setSqlWhere($v)
    {
        $this->SqlWhere = $v;
    }

    public function getSqlGroupBy() // Group By
    {
        return ($this->SqlGroupBy != "") ? $this->SqlGroupBy : "";
    }

    public function sqlGroupBy() // For backward compatibility
    {
        return $this->getSqlGroupBy();
    }

    public function setSqlGroupBy($v)
    {
        $this->SqlGroupBy = $v;
    }

    public function getSqlHaving() // Having
    {
        return ($this->SqlHaving != "") ? $this->SqlHaving : "";
    }

    public function sqlHaving() // For backward compatibility
    {
        return $this->getSqlHaving();
    }

    public function setSqlHaving($v)
    {
        $this->SqlHaving = $v;
    }

    public function getSqlOrderBy() // Order By
    {
        return ($this->SqlOrderBy != "") ? $this->SqlOrderBy : $this->DefaultSort;
    }

    public function sqlOrderBy() // For backward compatibility
    {
        return $this->getSqlOrderBy();
    }

    public function setSqlOrderBy($v)
    {
        $this->SqlOrderBy = $v;
    }

    // Apply User ID filters
    public function applyUserIDFilters($filter)
    {
        return $filter;
    }

    // Check if User ID security allows view all
    public function userIDAllow($id = "")
    {
        $allow = $this->UserIDAllowSecurity;
        switch ($id) {
            case "add":
            case "copy":
            case "gridadd":
            case "register":
            case "addopt":
                return (($allow & 1) == 1);
            case "edit":
            case "gridedit":
            case "update":
            case "changepassword":
            case "resetpassword":
                return (($allow & 4) == 4);
            case "delete":
                return (($allow & 2) == 2);
            case "view":
                return (($allow & 32) == 32);
            case "search":
                return (($allow & 64) == 64);
            default:
                return (($allow & 8) == 8);
        }
    }

    /**
     * Get record count
     *
     * @param string|QueryBuilder $sql SQL or QueryBuilder
     * @param mixed $c Connection
     * @return int
     */
    public function getRecordCount($sql, $c = null)
    {
        $cnt = -1;
        $rs = null;
        if ($sql instanceof \Doctrine\DBAL\Query\QueryBuilder) { // Query builder
            $sqlwrk = clone $sql;
            $sqlwrk = $sqlwrk->resetQueryPart("orderBy")->getSQL();
        } else {
            $sqlwrk = $sql;
        }
        $pattern = '/^SELECT\s([\s\S]+)\sFROM\s/i';
        // Skip Custom View / SubQuery / SELECT DISTINCT / ORDER BY
        if (
            ($this->TableType == 'TABLE' || $this->TableType == 'VIEW' || $this->TableType == 'LINKTABLE') &&
            preg_match($pattern, $sqlwrk) && !preg_match('/\(\s*(SELECT[^)]+)\)/i', $sqlwrk) &&
            !preg_match('/^\s*select\s+distinct\s+/i', $sqlwrk) && !preg_match('/\s+order\s+by\s+/i', $sqlwrk)
        ) {
            $sqlwrk = "SELECT COUNT(*) FROM " . preg_replace($pattern, "", $sqlwrk);
        } else {
            $sqlwrk = "SELECT COUNT(*) FROM (" . $sqlwrk . ") COUNT_TABLE";
        }
        $conn = $c ?? $this->getConnection();
        $rs = $conn->executeQuery($sqlwrk);
        $cnt = $rs->fetchColumn();
        if ($cnt !== false) {
            return (int)$cnt;
        }

        // Unable to get count by SELECT COUNT(*), execute the SQL to get record count directly
        return ExecuteRecordCount($sql, $conn);
    }

    // Get SQL
    public function getSql($where, $orderBy = "")
    {
        return $this->buildSelectSql(
            $this->getSqlSelect(),
            $this->getSqlFrom(),
            $this->getSqlWhere(),
            $this->getSqlGroupBy(),
            $this->getSqlHaving(),
            $this->getSqlOrderBy(),
            $where,
            $orderBy
        )->getSQL();
    }

    // Table SQL
    public function getCurrentSql()
    {
        $filter = $this->CurrentFilter;
        $filter = $this->applyUserIDFilters($filter);
        $sort = $this->getSessionOrderBy();
        return $this->getSql($filter, $sort);
    }

    /**
     * Table SQL with List page filter
     *
     * @return QueryBuilder
     */
    public function getListSql()
    {
        $filter = $this->UseSessionForListSql ? $this->getSessionWhere() : "";
        AddFilter($filter, $this->CurrentFilter);
        $filter = $this->applyUserIDFilters($filter);
        $this->recordsetSelecting($filter);
        $select = $this->getSqlSelect();
        $from = $this->getSqlFrom();
        $sort = $this->UseSessionForListSql ? $this->getSessionOrderBy() : "";
        $this->Sort = $sort;
        return $this->buildSelectSql(
            $select,
            $from,
            $this->getSqlWhere(),
            $this->getSqlGroupBy(),
            $this->getSqlHaving(),
            $this->getSqlOrderBy(),
            $filter,
            $sort
        );
    }

    // Get ORDER BY clause
    public function getOrderBy()
    {
        $orderBy = $this->getSqlOrderBy();
        $sort = $this->getSessionOrderBy();
        if ($orderBy != "" && $sort != "") {
            $orderBy .= ", " . $sort;
        } elseif ($sort != "") {
            $orderBy = $sort;
        }
        return $orderBy;
    }

    // Get record count based on filter (for detail record count in master table pages)
    public function loadRecordCount($filter)
    {
        $origFilter = $this->CurrentFilter;
        $this->CurrentFilter = $filter;
        $this->recordsetSelecting($this->CurrentFilter);
        $select = $this->TableType == 'CUSTOMVIEW' ? $this->getSqlSelect() : $this->getQueryBuilder()->select("*");
        $groupBy = $this->TableType == 'CUSTOMVIEW' ? $this->getSqlGroupBy() : "";
        $having = $this->TableType == 'CUSTOMVIEW' ? $this->getSqlHaving() : "";
        $sql = $this->buildSelectSql($select, $this->getSqlFrom(), $this->getSqlWhere(), $groupBy, $having, "", $this->CurrentFilter, "");
        $cnt = $this->getRecordCount($sql);
        $this->CurrentFilter = $origFilter;
        return $cnt;
    }

    // Get record count (for current List page)
    public function listRecordCount()
    {
        $filter = $this->getSessionWhere();
        AddFilter($filter, $this->CurrentFilter);
        $filter = $this->applyUserIDFilters($filter);
        $this->recordsetSelecting($filter);
        $select = $this->TableType == 'CUSTOMVIEW' ? $this->getSqlSelect() : $this->getQueryBuilder()->select("*");
        $groupBy = $this->TableType == 'CUSTOMVIEW' ? $this->getSqlGroupBy() : "";
        $having = $this->TableType == 'CUSTOMVIEW' ? $this->getSqlHaving() : "";
        $sql = $this->buildSelectSql($select, $this->getSqlFrom(), $this->getSqlWhere(), $groupBy, $having, "", $filter, "");
        $cnt = $this->getRecordCount($sql);
        return $cnt;
    }

    /**
     * INSERT statement
     *
     * @param mixed $rs
     * @return QueryBuilder
     */
    protected function insertSql(&$rs)
    {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->insert($this->UpdateTable);
        foreach ($rs as $name => $value) {
            if (!isset($this->Fields[$name]) || $this->Fields[$name]->IsCustom) {
                continue;
            }
            $type = GetParameterType($this->Fields[$name], $value, $this->Dbid);
            $queryBuilder->setValue($this->Fields[$name]->Expression, $queryBuilder->createPositionalParameter($value, $type));
        }
        return $queryBuilder;
    }

    // Insert
    public function insert(&$rs)
    {
        $conn = $this->getConnection();
        $success = $this->insertSql($rs)->execute();
        if ($success) {
            // Get insert id if necessary
            $this->id->setDbValue($conn->lastInsertId());
            $rs['id'] = $this->id->DbValue;
        }
        return $success;
    }

    /**
     * UPDATE statement
     *
     * @param array $rs Data to be updated
     * @param string|array $where WHERE clause
     * @param string $curfilter Filter
     * @return QueryBuilder
     */
    protected function updateSql(&$rs, $where = "", $curfilter = true)
    {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->update($this->UpdateTable);
        foreach ($rs as $name => $value) {
            if (!isset($this->Fields[$name]) || $this->Fields[$name]->IsCustom || $this->Fields[$name]->IsAutoIncrement) {
                continue;
            }
            $type = GetParameterType($this->Fields[$name], $value, $this->Dbid);
            $queryBuilder->set($this->Fields[$name]->Expression, $queryBuilder->createPositionalParameter($value, $type));
        }
        $filter = ($curfilter) ? $this->CurrentFilter : "";
        if (is_array($where)) {
            $where = $this->arrayToFilter($where);
        }
        AddFilter($filter, $where);
        if ($filter != "") {
            $queryBuilder->where($filter);
        }
        return $queryBuilder;
    }

    // Update
    public function update(&$rs, $where = "", $rsold = null, $curfilter = true)
    {
        // If no field is updated, execute may return 0. Treat as success
        $success = $this->updateSql($rs, $where, $curfilter)->execute();
        $success = ($success > 0) ? $success : true;
        return $success;
    }

    /**
     * DELETE statement
     *
     * @param array $rs Key values
     * @param string|array $where WHERE clause
     * @param string $curfilter Filter
     * @return QueryBuilder
     */
    protected function deleteSql(&$rs, $where = "", $curfilter = true)
    {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->delete($this->UpdateTable);
        if (is_array($where)) {
            $where = $this->arrayToFilter($where);
        }
        if ($rs) {
            if (array_key_exists('id', $rs)) {
                AddFilter($where, QuotedName('id', $this->Dbid) . '=' . QuotedValue($rs['id'], $this->id->DataType, $this->Dbid));
            }
        }
        $filter = ($curfilter) ? $this->CurrentFilter : "";
        AddFilter($filter, $where);
        return $queryBuilder->where($filter != "" ? $filter : "0=1");
    }

    // Delete
    public function delete(&$rs, $where = "", $curfilter = false)
    {
        $success = true;
        if ($success) {
            $success = $this->deleteSql($rs, $where, $curfilter)->execute();
        }
        return $success;
    }

    // Load DbValue from recordset or array
    protected function loadDbValues($row)
    {
        if (!is_array($row)) {
            return;
        }
        $this->id->DbValue = $row['id'];
        $this->_token->DbValue = $row['token'];
        $this->data->DbValue = $row['data'];
        $this->status->DbValue = $row['status'];
        $this->progress->DbValue = $row['progress'];
        $this->persone->DbValue = $row['persone'];
        $this->data_arrivo->DbValue = $row['data_arrivo'];
        $this->data_partenza->DbValue = $row['data_partenza'];
        $this->piazzola_id->DbValue = $row['piazzola_id'];
        $this->piazzola->DbValue = $row['piazzola'];
        $this->nome->DbValue = $row['nome'];
        $this->cognome->DbValue = $row['cognome'];
        $this->importo->DbValue = $row['importo'];
        $this->_email->DbValue = $row['email'];
        $this->prefisso->DbValue = $row['prefisso'];
        $this->telefono->DbValue = $row['telefono'];
        $this->codice_fiscale->DbValue = $row['codice_fiscale'];
        $this->targa->DbValue = $row['targa'];
        $this->country->DbValue = $row['country'];
        $this->invio_whatsapp->DbValue = $row['invio_whatsapp'];
        $this->presenza_disabili->DbValue = $row['presenza_disabili'];
        $this->note->DbValue = $row['note'];
        $this->wa_inviato->DbValue = $row['wa_inviato'];
        $this->debug->DbValue = $row['debug'];
    }

    // Delete uploaded files
    public function deleteUploadedFiles($row)
    {
        $this->loadDbValues($row);
    }

    // Record filter WHERE clause
    protected function sqlKeyFilter()
    {
        return "`id` = @id@";
    }

    // Get Key
    public function getKey($current = false)
    {
        $keys = [];
        $val = $current ? $this->id->CurrentValue : $this->id->OldValue;
        if (EmptyValue($val)) {
            return "";
        } else {
            $keys[] = $val;
        }
        return implode(Config("COMPOSITE_KEY_SEPARATOR"), $keys);
    }

    // Set Key
    public function setKey($key, $current = false)
    {
        $this->OldKey = strval($key);
        $keys = explode(Config("COMPOSITE_KEY_SEPARATOR"), $this->OldKey);
        if (count($keys) == 1) {
            if ($current) {
                $this->id->CurrentValue = $keys[0];
            } else {
                $this->id->OldValue = $keys[0];
            }
        }
    }

    // Get record filter
    public function getRecordFilter($row = null)
    {
        $keyFilter = $this->sqlKeyFilter();
        if (is_array($row)) {
            $val = array_key_exists('id', $row) ? $row['id'] : null;
        } else {
            $val = $this->id->OldValue !== null ? $this->id->OldValue : $this->id->CurrentValue;
        }
        if (!is_numeric($val)) {
            return "0=1"; // Invalid key
        }
        if ($val === null) {
            return "0=1"; // Invalid key
        } else {
            $keyFilter = str_replace("@id@", AdjustSql($val, $this->Dbid), $keyFilter); // Replace key value
        }
        return $keyFilter;
    }

    // Return page URL
    public function getReturnUrl()
    {
        $referUrl = ReferUrl();
        $referPageName = ReferPageName();
        $name = PROJECT_NAME . "_" . $this->TableVar . "_" . Config("TABLE_RETURN_URL");
        // Get referer URL automatically
        if ($referUrl != "" && $referPageName != CurrentPageName() && $referPageName != "login") { // Referer not same page or login page
            $_SESSION[$name] = $referUrl; // Save to Session
        }
        return $_SESSION[$name] ?? GetUrl("OrdiniList");
    }

    // Set return page URL
    public function setReturnUrl($v)
    {
        $_SESSION[PROJECT_NAME . "_" . $this->TableVar . "_" . Config("TABLE_RETURN_URL")] = $v;
    }

    // Get modal caption
    public function getModalCaption($pageName)
    {
        global $Language;
        if ($pageName == "OrdiniView") {
            return $Language->phrase("View");
        } elseif ($pageName == "OrdiniEdit") {
            return $Language->phrase("Edit");
        } elseif ($pageName == "OrdiniAdd") {
            return $Language->phrase("Add");
        } else {
            return "";
        }
    }

    // API page name
    public function getApiPageName($action)
    {
        switch (strtolower($action)) {
            case Config("API_VIEW_ACTION"):
                return "OrdiniView";
            case Config("API_ADD_ACTION"):
                return "OrdiniAdd";
            case Config("API_EDIT_ACTION"):
                return "OrdiniEdit";
            case Config("API_DELETE_ACTION"):
                return "OrdiniDelete";
            case Config("API_LIST_ACTION"):
                return "OrdiniList";
            default:
                return "";
        }
    }

    // List URL
    public function getListUrl()
    {
        return "OrdiniList";
    }

    // View URL
    public function getViewUrl($parm = "")
    {
        if ($parm != "") {
            $url = $this->keyUrl("OrdiniView", $this->getUrlParm($parm));
        } else {
            $url = $this->keyUrl("OrdiniView", $this->getUrlParm(Config("TABLE_SHOW_DETAIL") . "="));
        }
        return $this->addMasterUrl($url);
    }

    // Add URL
    public function getAddUrl($parm = "")
    {
        if ($parm != "") {
            $url = "OrdiniAdd?" . $this->getUrlParm($parm);
        } else {
            $url = "OrdiniAdd";
        }
        return $this->addMasterUrl($url);
    }

    // Edit URL
    public function getEditUrl($parm = "")
    {
        $url = $this->keyUrl("OrdiniEdit", $this->getUrlParm($parm));
        return $this->addMasterUrl($url);
    }

    // Inline edit URL
    public function getInlineEditUrl()
    {
        $url = $this->keyUrl(CurrentPageName(), $this->getUrlParm("action=edit"));
        return $this->addMasterUrl($url);
    }

    // Copy URL
    public function getCopyUrl($parm = "")
    {
        $url = $this->keyUrl("OrdiniAdd", $this->getUrlParm($parm));
        return $this->addMasterUrl($url);
    }

    // Inline copy URL
    public function getInlineCopyUrl()
    {
        $url = $this->keyUrl(CurrentPageName(), $this->getUrlParm("action=copy"));
        return $this->addMasterUrl($url);
    }

    // Delete URL
    public function getDeleteUrl()
    {
        return $this->keyUrl("OrdiniDelete", $this->getUrlParm());
    }

    // Add master url
    public function addMasterUrl($url)
    {
        return $url;
    }

    public function keyToJson($htmlEncode = false)
    {
        $json = "";
        $json .= "id:" . JsonEncode($this->id->CurrentValue, "number");
        $json = "{" . $json . "}";
        if ($htmlEncode) {
            $json = HtmlEncode($json);
        }
        return $json;
    }

    // Add key value to URL
    public function keyUrl($url, $parm = "")
    {
        if ($this->id->CurrentValue !== null) {
            $url .= "/" . rawurlencode($this->id->CurrentValue);
        } else {
            return "javascript:ew.alert(ew.language.phrase('InvalidRecord'));";
        }
        if ($parm != "") {
            $url .= "?" . $parm;
        }
        return $url;
    }

    // Render sort
    public function renderSort($fld)
    {
        $classId = $fld->TableVar . "_" . $fld->Param;
        $scriptId = str_replace("%id%", $classId, "tpc_%id%");
        $scriptStart = $this->UseCustomTemplate ? "<template id=\"" . $scriptId . "\">" : "";
        $scriptEnd = $this->UseCustomTemplate ? "</template>" : "";
        $jsSort = " class=\"ew-pointer\" onclick=\"ew.sort(event, '" . $this->sortUrl($fld) . "', 1);\"";
        if ($this->sortUrl($fld) == "") {
            $html = <<<NOSORTHTML
{$scriptStart}<div class="ew-table-header-caption">{$fld->caption()}</div>{$scriptEnd}
NOSORTHTML;
        } else {
            if ($fld->getSort() == "ASC") {
                $sortIcon = '<i class="fas fa-sort-up"></i>';
            } elseif ($fld->getSort() == "DESC") {
                $sortIcon = '<i class="fas fa-sort-down"></i>';
            } else {
                $sortIcon = '';
            }
            $html = <<<SORTHTML
{$scriptStart}<div{$jsSort}><div class="ew-table-header-btn"><span class="ew-table-header-caption">{$fld->caption()}</span><span class="ew-table-header-sort">{$sortIcon}</span></div></div>{$scriptEnd}
SORTHTML;
        }
        return $html;
    }

    // Sort URL
    public function sortUrl($fld)
    {
        if (
            $this->CurrentAction || $this->isExport() ||
            in_array($fld->Type, [128, 204, 205])
        ) { // Unsortable data type
                return "";
        } elseif ($fld->Sortable) {
            $urlParm = $this->getUrlParm("order=" . urlencode($fld->Name) . "&amp;ordertype=" . $fld->getNextSort());
            return $this->addMasterUrl(CurrentPageName() . "?" . $urlParm);
        } else {
            return "";
        }
    }

    // Get record keys from Post/Get/Session
    public function getRecordKeys()
    {
        $arKeys = [];
        $arKey = [];
        if (Param("key_m") !== null) {
            $arKeys = Param("key_m");
            $cnt = count($arKeys);
        } else {
            if (($keyValue = Param("id") ?? Route("id")) !== null) {
                $arKeys[] = $keyValue;
            } elseif (IsApi() && (($keyValue = Key(0) ?? Route(2)) !== null)) {
                $arKeys[] = $keyValue;
            } else {
                $arKeys = null; // Do not setup
            }

            //return $arKeys; // Do not return yet, so the values will also be checked by the following code
        }
        // Check keys
        $ar = [];
        if (is_array($arKeys)) {
            foreach ($arKeys as $key) {
                if (!is_numeric($key)) {
                    continue;
                }
                $ar[] = $key;
            }
        }
        return $ar;
    }

    // Get filter from record keys
    public function getFilterFromRecordKeys($setCurrent = true)
    {
        $arKeys = $this->getRecordKeys();
        $keyFilter = "";
        foreach ($arKeys as $key) {
            if ($keyFilter != "") {
                $keyFilter .= " OR ";
            }
            if ($setCurrent) {
                $this->id->CurrentValue = $key;
            } else {
                $this->id->OldValue = $key;
            }
            $keyFilter .= "(" . $this->getRecordFilter() . ")";
        }
        return $keyFilter;
    }

    // Load recordset based on filter
    public function &loadRs($filter)
    {
        $sql = $this->getSql($filter); // Set up filter (WHERE Clause)
        $conn = $this->getConnection();
        $stmt = $conn->executeQuery($sql);
        return $stmt;
    }

    // Load row values from record
    public function loadListRowValues(&$rs)
    {
        if (is_array($rs)) {
            $row = $rs;
        } elseif ($rs && property_exists($rs, "fields")) { // Recordset
            $row = $rs->fields;
        } else {
            return;
        }
        $this->id->setDbValue($row['id']);
        $this->_token->setDbValue($row['token']);
        $this->data->setDbValue($row['data']);
        $this->status->setDbValue($row['status']);
        $this->progress->setDbValue($row['progress']);
        $this->persone->setDbValue($row['persone']);
        $this->data_arrivo->setDbValue($row['data_arrivo']);
        $this->data_partenza->setDbValue($row['data_partenza']);
        $this->piazzola_id->setDbValue($row['piazzola_id']);
        $this->piazzola->setDbValue($row['piazzola']);
        $this->nome->setDbValue($row['nome']);
        $this->cognome->setDbValue($row['cognome']);
        $this->importo->setDbValue($row['importo']);
        $this->_email->setDbValue($row['email']);
        $this->prefisso->setDbValue($row['prefisso']);
        $this->telefono->setDbValue($row['telefono']);
        $this->codice_fiscale->setDbValue($row['codice_fiscale']);
        $this->targa->setDbValue($row['targa']);
        $this->country->setDbValue($row['country']);
        $this->invio_whatsapp->setDbValue($row['invio_whatsapp']);
        $this->presenza_disabili->setDbValue($row['presenza_disabili']);
        $this->note->setDbValue($row['note']);
        $this->wa_inviato->setDbValue($row['wa_inviato']);
        $this->debug->setDbValue($row['debug']);
    }

    // Render list row values
    public function renderListRow()
    {
        global $Security, $CurrentLanguage, $Language;

        // Call Row Rendering event
        $this->rowRendering();

        // Common render codes

        // id

        // token
        $this->_token->CellCssStyle = "white-space: nowrap;";

        // data

        // status
        $this->status->CellCssStyle = "white-space: nowrap;";

        // progress

        // persone

        // data_arrivo

        // data_partenza

        // piazzola_id

        // piazzola

        // nome

        // cognome

        // importo

        // email

        // prefisso

        // telefono

        // codice_fiscale

        // targa

        // country

        // invio_whatsapp

        // presenza_disabili

        // note

        // wa_inviato

        // debug
        $this->debug->CellCssStyle = "white-space: nowrap;";

        // id
        $this->id->ViewValue = $this->id->CurrentValue;
        $this->id->ViewCustomAttributes = "";

        // token
        $this->_token->ViewValue = $this->_token->CurrentValue;
        $this->_token->ViewCustomAttributes = "";

        // data
        $this->data->ViewValue = $this->data->CurrentValue;
        $this->data->ViewValue = FormatDateTime($this->data->ViewValue, 0);
        $this->data->ViewCustomAttributes = "";

        // status
        if (strval($this->status->CurrentValue) != "") {
            $this->status->ViewValue = $this->status->optionCaption($this->status->CurrentValue);
        } else {
            $this->status->ViewValue = null;
        }
        $this->status->ViewCustomAttributes = "";

        // progress
        if (strval($this->progress->CurrentValue) != "") {
            $this->progress->ViewValue = $this->progress->optionCaption($this->progress->CurrentValue);
        } else {
            $this->progress->ViewValue = null;
        }
        $this->progress->ViewCustomAttributes = "";

        // persone
        $this->persone->ViewValue = $this->persone->CurrentValue;
        $this->persone->ViewValue = FormatNumber($this->persone->ViewValue, 0, -2, -2, -2);
        $this->persone->ViewCustomAttributes = "";

        // data_arrivo
        $this->data_arrivo->ViewValue = $this->data_arrivo->CurrentValue;
        $this->data_arrivo->ViewValue = FormatDateTime($this->data_arrivo->ViewValue, 1);
        $this->data_arrivo->ViewCustomAttributes = "";

        // data_partenza
        $this->data_partenza->ViewValue = $this->data_partenza->CurrentValue;
        $this->data_partenza->ViewValue = FormatDateTime($this->data_partenza->ViewValue, 1);
        $this->data_partenza->ViewCustomAttributes = "";

        // piazzola_id
        $curVal = trim(strval($this->piazzola_id->CurrentValue));
        if ($curVal != "") {
            $this->piazzola_id->ViewValue = $this->piazzola_id->lookupCacheOption($curVal);
            if ($this->piazzola_id->ViewValue === null) { // Lookup from database
                $filterWrk = "`id`" . SearchString("=", $curVal, DATATYPE_NUMBER, "");
                $sqlWrk = $this->piazzola_id->Lookup->getSql(false, $filterWrk, '', $this, true, true);
                $rswrk = Conn()->executeQuery($sqlWrk)->fetchAll(\PDO::FETCH_BOTH);
                $ari = count($rswrk);
                if ($ari > 0) { // Lookup values found
                    $arwrk = $this->piazzola_id->Lookup->renderViewRow($rswrk[0]);
                    $this->piazzola_id->ViewValue = $this->piazzola_id->displayValue($arwrk);
                } else {
                    $this->piazzola_id->ViewValue = $this->piazzola_id->CurrentValue;
                }
            }
        } else {
            $this->piazzola_id->ViewValue = null;
        }
        $this->piazzola_id->ViewCustomAttributes = "";

        // piazzola
        $this->piazzola->ViewValue = $this->piazzola->CurrentValue;
        $this->piazzola->ViewCustomAttributes = "";

        // nome
        $this->nome->ViewValue = $this->nome->CurrentValue;
        $this->nome->ViewCustomAttributes = "";

        // cognome
        $this->cognome->ViewValue = $this->cognome->CurrentValue;
        $this->cognome->ViewCustomAttributes = "";

        // importo
        $this->importo->ViewValue = $this->importo->CurrentValue;
        $this->importo->ViewValue = FormatCurrency($this->importo->ViewValue, 2, -1, -2, -2);
        $this->importo->ViewCustomAttributes = "";

        // email
        $this->_email->ViewValue = $this->_email->CurrentValue;
        $this->_email->ViewCustomAttributes = "";

        // prefisso
        $this->prefisso->ViewValue = $this->prefisso->CurrentValue;
        $this->prefisso->ViewCustomAttributes = "";

        // telefono
        $this->telefono->ViewValue = $this->telefono->CurrentValue;
        $this->telefono->ViewCustomAttributes = "";

        // codice_fiscale
        $this->codice_fiscale->ViewValue = $this->codice_fiscale->CurrentValue;
        $this->codice_fiscale->ViewCustomAttributes = "";

        // targa
        $this->targa->ViewValue = $this->targa->CurrentValue;
        $this->targa->ViewCustomAttributes = "";

        // country
        $this->country->ViewValue = $this->country->CurrentValue;
        $this->country->ViewCustomAttributes = "";

        // invio_whatsapp
        if (ConvertToBool($this->invio_whatsapp->CurrentValue)) {
            $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(2) != "" ? $this->invio_whatsapp->tagCaption(2) : "si";
        } else {
            $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(1) != "" ? $this->invio_whatsapp->tagCaption(1) : "no";
        }
        $this->invio_whatsapp->ViewCustomAttributes = "";

        // presenza_disabili
        if (ConvertToBool($this->presenza_disabili->CurrentValue)) {
            $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(2) != "" ? $this->presenza_disabili->tagCaption(2) : "si";
        } else {
            $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(1) != "" ? $this->presenza_disabili->tagCaption(1) : "no";
        }
        $this->presenza_disabili->ViewCustomAttributes = "";

        // note
        $this->note->ViewValue = $this->note->CurrentValue;
        $this->note->ViewCustomAttributes = "";

        // wa_inviato
        if (ConvertToBool($this->wa_inviato->CurrentValue)) {
            $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(2) != "" ? $this->wa_inviato->tagCaption(2) : "1";
        } else {
            $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(1) != "" ? $this->wa_inviato->tagCaption(1) : "0";
        }
        $this->wa_inviato->ViewCustomAttributes = "";

        // debug
        $this->debug->ViewValue = $this->debug->CurrentValue;
        $this->debug->ViewCustomAttributes = "";

        // id
        $this->id->LinkCustomAttributes = "";
        $this->id->HrefValue = "";
        $this->id->TooltipValue = "";

        // token
        $this->_token->LinkCustomAttributes = "";
        $this->_token->HrefValue = "";
        $this->_token->TooltipValue = "";

        // data
        $this->data->LinkCustomAttributes = "";
        $this->data->HrefValue = "";
        $this->data->TooltipValue = "";

        // status
        $this->status->LinkCustomAttributes = "";
        $this->status->HrefValue = "";
        $this->status->TooltipValue = "";

        // progress
        $this->progress->LinkCustomAttributes = "";
        $this->progress->HrefValue = "";
        $this->progress->TooltipValue = "";

        // persone
        $this->persone->LinkCustomAttributes = "";
        $this->persone->HrefValue = "";
        $this->persone->TooltipValue = "";

        // data_arrivo
        $this->data_arrivo->LinkCustomAttributes = "";
        $this->data_arrivo->HrefValue = "";
        $this->data_arrivo->TooltipValue = "";

        // data_partenza
        $this->data_partenza->LinkCustomAttributes = "";
        $this->data_partenza->HrefValue = "";
        $this->data_partenza->TooltipValue = "";

        // piazzola_id
        $this->piazzola_id->LinkCustomAttributes = "";
        $this->piazzola_id->HrefValue = "";
        $this->piazzola_id->TooltipValue = "";

        // piazzola
        $this->piazzola->LinkCustomAttributes = "";
        $this->piazzola->HrefValue = "";
        $this->piazzola->TooltipValue = "";

        // nome
        $this->nome->LinkCustomAttributes = "";
        $this->nome->HrefValue = "";
        $this->nome->TooltipValue = "";

        // cognome
        $this->cognome->LinkCustomAttributes = "";
        $this->cognome->HrefValue = "";
        $this->cognome->TooltipValue = "";

        // importo
        $this->importo->LinkCustomAttributes = "";
        $this->importo->HrefValue = "";
        $this->importo->TooltipValue = "";

        // email
        $this->_email->LinkCustomAttributes = "";
        $this->_email->HrefValue = "";
        $this->_email->TooltipValue = "";

        // prefisso
        $this->prefisso->LinkCustomAttributes = "";
        $this->prefisso->HrefValue = "";
        $this->prefisso->TooltipValue = "";

        // telefono
        $this->telefono->LinkCustomAttributes = "";
        $this->telefono->HrefValue = "";
        $this->telefono->TooltipValue = "";

        // codice_fiscale
        $this->codice_fiscale->LinkCustomAttributes = "";
        $this->codice_fiscale->HrefValue = "";
        $this->codice_fiscale->TooltipValue = "";

        // targa
        $this->targa->LinkCustomAttributes = "";
        $this->targa->HrefValue = "";
        $this->targa->TooltipValue = "";

        // country
        $this->country->LinkCustomAttributes = "";
        $this->country->HrefValue = "";
        $this->country->TooltipValue = "";

        // invio_whatsapp
        $this->invio_whatsapp->LinkCustomAttributes = "";
        $this->invio_whatsapp->HrefValue = "";
        $this->invio_whatsapp->TooltipValue = "";

        // presenza_disabili
        $this->presenza_disabili->LinkCustomAttributes = "";
        $this->presenza_disabili->HrefValue = "";
        $this->presenza_disabili->TooltipValue = "";

        // note
        $this->note->LinkCustomAttributes = "";
        $this->note->HrefValue = "";
        $this->note->TooltipValue = "";

        // wa_inviato
        $this->wa_inviato->LinkCustomAttributes = "";
        $this->wa_inviato->HrefValue = "";
        $this->wa_inviato->TooltipValue = "";

        // debug
        $this->debug->LinkCustomAttributes = "";
        $this->debug->HrefValue = "";
        $this->debug->TooltipValue = "";

        // Call Row Rendered event
        $this->rowRendered();

        // Save data for Custom Template
        $this->Rows[] = $this->customTemplateFieldValues();
    }

    // Render edit row values
    public function renderEditRow()
    {
        global $Security, $CurrentLanguage, $Language;

        // Call Row Rendering event
        $this->rowRendering();

        // id
        $this->id->EditAttrs["class"] = "form-control";
        $this->id->EditCustomAttributes = "";
        $this->id->EditValue = $this->id->CurrentValue;
        $this->id->ViewCustomAttributes = "";

        // token
        $this->_token->EditAttrs["class"] = "form-control";
        $this->_token->EditCustomAttributes = "";
        if (!$this->_token->Raw) {
            $this->_token->CurrentValue = HtmlDecode($this->_token->CurrentValue);
        }
        $this->_token->EditValue = $this->_token->CurrentValue;
        $this->_token->PlaceHolder = RemoveHtml($this->_token->caption());

        // data
        $this->data->EditAttrs["class"] = "form-control";
        $this->data->EditCustomAttributes = "";
        $this->data->EditValue = FormatDateTime($this->data->CurrentValue, 8);
        $this->data->PlaceHolder = RemoveHtml($this->data->caption());

        // status
        $this->status->EditCustomAttributes = "";
        $this->status->EditValue = $this->status->options(false);
        $this->status->PlaceHolder = RemoveHtml($this->status->caption());

        // progress
        $this->progress->EditCustomAttributes = "";
        $this->progress->EditValue = $this->progress->options(false);
        $this->progress->PlaceHolder = RemoveHtml($this->progress->caption());

        // persone
        $this->persone->EditAttrs["class"] = "form-control";
        $this->persone->EditCustomAttributes = "";
        $this->persone->EditValue = $this->persone->CurrentValue;
        $this->persone->PlaceHolder = RemoveHtml($this->persone->caption());

        // data_arrivo
        $this->data_arrivo->EditAttrs["class"] = "form-control";
        $this->data_arrivo->EditCustomAttributes = "";
        $this->data_arrivo->EditValue = FormatDateTime($this->data_arrivo->CurrentValue, 8);
        $this->data_arrivo->PlaceHolder = RemoveHtml($this->data_arrivo->caption());

        // data_partenza
        $this->data_partenza->EditAttrs["class"] = "form-control";
        $this->data_partenza->EditCustomAttributes = "";
        $this->data_partenza->EditValue = FormatDateTime($this->data_partenza->CurrentValue, 8);
        $this->data_partenza->PlaceHolder = RemoveHtml($this->data_partenza->caption());

        // piazzola_id
        $this->piazzola_id->EditAttrs["class"] = "form-control";
        $this->piazzola_id->EditCustomAttributes = "";
        $this->piazzola_id->PlaceHolder = RemoveHtml($this->piazzola_id->caption());

        // piazzola
        $this->piazzola->EditAttrs["class"] = "form-control";
        $this->piazzola->EditCustomAttributes = "";
        if (!$this->piazzola->Raw) {
            $this->piazzola->CurrentValue = HtmlDecode($this->piazzola->CurrentValue);
        }
        $this->piazzola->EditValue = $this->piazzola->CurrentValue;
        $this->piazzola->PlaceHolder = RemoveHtml($this->piazzola->caption());

        // nome
        $this->nome->EditAttrs["class"] = "form-control";
        $this->nome->EditCustomAttributes = "";
        if (!$this->nome->Raw) {
            $this->nome->CurrentValue = HtmlDecode($this->nome->CurrentValue);
        }
        $this->nome->EditValue = $this->nome->CurrentValue;
        $this->nome->PlaceHolder = RemoveHtml($this->nome->caption());

        // cognome
        $this->cognome->EditAttrs["class"] = "form-control";
        $this->cognome->EditCustomAttributes = "";
        if (!$this->cognome->Raw) {
            $this->cognome->CurrentValue = HtmlDecode($this->cognome->CurrentValue);
        }
        $this->cognome->EditValue = $this->cognome->CurrentValue;
        $this->cognome->PlaceHolder = RemoveHtml($this->cognome->caption());

        // importo
        $this->importo->EditAttrs["class"] = "form-control";
        $this->importo->EditCustomAttributes = "";
        $this->importo->EditValue = $this->importo->CurrentValue;
        $this->importo->PlaceHolder = RemoveHtml($this->importo->caption());
        if (strval($this->importo->EditValue) != "" && is_numeric($this->importo->EditValue)) {
            $this->importo->EditValue = FormatNumber($this->importo->EditValue, -2, -1, -2, -2);
        }

        // email
        $this->_email->EditAttrs["class"] = "form-control";
        $this->_email->EditCustomAttributes = "";
        if (!$this->_email->Raw) {
            $this->_email->CurrentValue = HtmlDecode($this->_email->CurrentValue);
        }
        $this->_email->EditValue = $this->_email->CurrentValue;
        $this->_email->PlaceHolder = RemoveHtml($this->_email->caption());

        // prefisso
        $this->prefisso->EditAttrs["class"] = "form-control";
        $this->prefisso->EditCustomAttributes = "";
        if (!$this->prefisso->Raw) {
            $this->prefisso->CurrentValue = HtmlDecode($this->prefisso->CurrentValue);
        }
        $this->prefisso->EditValue = $this->prefisso->CurrentValue;
        $this->prefisso->PlaceHolder = RemoveHtml($this->prefisso->caption());

        // telefono
        $this->telefono->EditAttrs["class"] = "form-control";
        $this->telefono->EditCustomAttributes = "";
        if (!$this->telefono->Raw) {
            $this->telefono->CurrentValue = HtmlDecode($this->telefono->CurrentValue);
        }
        $this->telefono->EditValue = $this->telefono->CurrentValue;
        $this->telefono->PlaceHolder = RemoveHtml($this->telefono->caption());

        // codice_fiscale
        $this->codice_fiscale->EditAttrs["class"] = "form-control";
        $this->codice_fiscale->EditCustomAttributes = "";
        if (!$this->codice_fiscale->Raw) {
            $this->codice_fiscale->CurrentValue = HtmlDecode($this->codice_fiscale->CurrentValue);
        }
        $this->codice_fiscale->EditValue = $this->codice_fiscale->CurrentValue;
        $this->codice_fiscale->PlaceHolder = RemoveHtml($this->codice_fiscale->caption());

        // targa
        $this->targa->EditAttrs["class"] = "form-control";
        $this->targa->EditCustomAttributes = "";
        if (!$this->targa->Raw) {
            $this->targa->CurrentValue = HtmlDecode($this->targa->CurrentValue);
        }
        $this->targa->EditValue = $this->targa->CurrentValue;
        $this->targa->PlaceHolder = RemoveHtml($this->targa->caption());

        // country
        $this->country->EditAttrs["class"] = "form-control";
        $this->country->EditCustomAttributes = "";
        if (!$this->country->Raw) {
            $this->country->CurrentValue = HtmlDecode($this->country->CurrentValue);
        }
        $this->country->EditValue = $this->country->CurrentValue;
        $this->country->PlaceHolder = RemoveHtml($this->country->caption());

        // invio_whatsapp
        $this->invio_whatsapp->EditCustomAttributes = "";
        $this->invio_whatsapp->EditValue = $this->invio_whatsapp->options(false);
        $this->invio_whatsapp->PlaceHolder = RemoveHtml($this->invio_whatsapp->caption());

        // presenza_disabili
        $this->presenza_disabili->EditCustomAttributes = "";
        $this->presenza_disabili->EditValue = $this->presenza_disabili->options(false);
        $this->presenza_disabili->PlaceHolder = RemoveHtml($this->presenza_disabili->caption());

        // note
        $this->note->EditAttrs["class"] = "form-control";
        $this->note->EditCustomAttributes = "";
        $this->note->EditValue = $this->note->CurrentValue;
        $this->note->PlaceHolder = RemoveHtml($this->note->caption());

        // wa_inviato
        $this->wa_inviato->EditCustomAttributes = "";
        $this->wa_inviato->EditValue = $this->wa_inviato->options(false);
        $this->wa_inviato->PlaceHolder = RemoveHtml($this->wa_inviato->caption());

        // debug
        $this->debug->EditAttrs["class"] = "form-control";
        $this->debug->EditCustomAttributes = "";
        $this->debug->EditValue = $this->debug->CurrentValue;
        $this->debug->PlaceHolder = RemoveHtml($this->debug->caption());

        // Call Row Rendered event
        $this->rowRendered();
    }

    // Aggregate list row values
    public function aggregateListRowValues()
    {
    }

    // Aggregate list row (for rendering)
    public function aggregateListRow()
    {
        // Call Row Rendered event
        $this->rowRendered();
    }

    // Export data in HTML/CSV/Word/Excel/Email/PDF format
    public function exportDocument($doc, $recordset, $startRec = 1, $stopRec = 1, $exportPageType = "")
    {
        if (!$recordset || !$doc) {
            return;
        }
        if (!$doc->ExportCustom) {
            // Write header
            $doc->exportTableHeader();
            if ($doc->Horizontal) { // Horizontal format, write header
                $doc->beginExportRow();
                if ($exportPageType == "view") {
                    $doc->exportCaption($this->id);
                    $doc->exportCaption($this->data);
                    $doc->exportCaption($this->progress);
                    $doc->exportCaption($this->persone);
                    $doc->exportCaption($this->data_arrivo);
                    $doc->exportCaption($this->data_partenza);
                    $doc->exportCaption($this->piazzola_id);
                    $doc->exportCaption($this->piazzola);
                    $doc->exportCaption($this->nome);
                    $doc->exportCaption($this->cognome);
                    $doc->exportCaption($this->importo);
                    $doc->exportCaption($this->_email);
                    $doc->exportCaption($this->prefisso);
                    $doc->exportCaption($this->telefono);
                    $doc->exportCaption($this->codice_fiscale);
                    $doc->exportCaption($this->targa);
                    $doc->exportCaption($this->country);
                    $doc->exportCaption($this->invio_whatsapp);
                    $doc->exportCaption($this->presenza_disabili);
                    $doc->exportCaption($this->note);
                    $doc->exportCaption($this->wa_inviato);
                } else {
                    $doc->exportCaption($this->id);
                    $doc->exportCaption($this->data);
                    $doc->exportCaption($this->progress);
                    $doc->exportCaption($this->persone);
                    $doc->exportCaption($this->data_arrivo);
                    $doc->exportCaption($this->data_partenza);
                    $doc->exportCaption($this->piazzola_id);
                    $doc->exportCaption($this->piazzola);
                    $doc->exportCaption($this->nome);
                    $doc->exportCaption($this->cognome);
                    $doc->exportCaption($this->importo);
                    $doc->exportCaption($this->_email);
                    $doc->exportCaption($this->prefisso);
                    $doc->exportCaption($this->telefono);
                    $doc->exportCaption($this->codice_fiscale);
                    $doc->exportCaption($this->targa);
                    $doc->exportCaption($this->country);
                    $doc->exportCaption($this->invio_whatsapp);
                    $doc->exportCaption($this->presenza_disabili);
                    $doc->exportCaption($this->wa_inviato);
                }
                $doc->endExportRow();
            }
        }

        // Move to first record
        $recCnt = $startRec - 1;
        $stopRec = ($stopRec > 0) ? $stopRec : PHP_INT_MAX;
        while (!$recordset->EOF && $recCnt < $stopRec) {
            $row = $recordset->fields;
            $recCnt++;
            if ($recCnt >= $startRec) {
                $rowCnt = $recCnt - $startRec + 1;

                // Page break
                if ($this->ExportPageBreakCount > 0) {
                    if ($rowCnt > 1 && ($rowCnt - 1) % $this->ExportPageBreakCount == 0) {
                        $doc->exportPageBreak();
                    }
                }
                $this->loadListRowValues($row);

                // Render row
                $this->RowType = ROWTYPE_VIEW; // Render view
                $this->resetAttributes();
                $this->renderListRow();
                if (!$doc->ExportCustom) {
                    $doc->beginExportRow($rowCnt); // Allow CSS styles if enabled
                    if ($exportPageType == "view") {
                        $doc->exportField($this->id);
                        $doc->exportField($this->data);
                        $doc->exportField($this->progress);
                        $doc->exportField($this->persone);
                        $doc->exportField($this->data_arrivo);
                        $doc->exportField($this->data_partenza);
                        $doc->exportField($this->piazzola_id);
                        $doc->exportField($this->piazzola);
                        $doc->exportField($this->nome);
                        $doc->exportField($this->cognome);
                        $doc->exportField($this->importo);
                        $doc->exportField($this->_email);
                        $doc->exportField($this->prefisso);
                        $doc->exportField($this->telefono);
                        $doc->exportField($this->codice_fiscale);
                        $doc->exportField($this->targa);
                        $doc->exportField($this->country);
                        $doc->exportField($this->invio_whatsapp);
                        $doc->exportField($this->presenza_disabili);
                        $doc->exportField($this->note);
                        $doc->exportField($this->wa_inviato);
                    } else {
                        $doc->exportField($this->id);
                        $doc->exportField($this->data);
                        $doc->exportField($this->progress);
                        $doc->exportField($this->persone);
                        $doc->exportField($this->data_arrivo);
                        $doc->exportField($this->data_partenza);
                        $doc->exportField($this->piazzola_id);
                        $doc->exportField($this->piazzola);
                        $doc->exportField($this->nome);
                        $doc->exportField($this->cognome);
                        $doc->exportField($this->importo);
                        $doc->exportField($this->_email);
                        $doc->exportField($this->prefisso);
                        $doc->exportField($this->telefono);
                        $doc->exportField($this->codice_fiscale);
                        $doc->exportField($this->targa);
                        $doc->exportField($this->country);
                        $doc->exportField($this->invio_whatsapp);
                        $doc->exportField($this->presenza_disabili);
                        $doc->exportField($this->wa_inviato);
                    }
                    $doc->endExportRow($rowCnt);
                }
            }

            // Call Row Export server event
            if ($doc->ExportCustom) {
                $this->rowExport($row);
            }
            $recordset->moveNext();
        }
        if (!$doc->ExportCustom) {
            $doc->exportTableFooter();
        }
    }

    // Get file data
    public function getFileData($fldparm, $key, $resize, $width = 0, $height = 0, $plugins = [])
    {
        // No binary fields
        return false;
    }

    // Table level events

    // Recordset Selecting event
    public function recordsetSelecting(&$filter)
    {
        // Enter your code here
    }

    // Recordset Selected event
    public function recordsetSelected(&$rs)
    {
        //Log("Recordset Selected");
    }

    // Recordset Search Validated event
    public function recordsetSearchValidated()
    {
        // Example:
        //$this->MyField1->AdvancedSearch->SearchValue = "your search criteria"; // Search value
    }

    // Recordset Searching event
    public function recordsetSearching(&$filter)
    {
        // Enter your code here
    }

    // Row_Selecting event
    public function rowSelecting(&$filter)
    {
        // Enter your code here
    }

    // Row Selected event
    public function rowSelected(&$rs)
    {
        //Log("Row Selected");
    }

    // Row Inserting event
    public function rowInserting($rsold, &$rsnew)
    {
        // Enter your code here
        // To cancel, set return value to false
        return true;
    }

    // Row Inserted event
    public function rowInserted($rsold, &$rsnew)
    {
        //Log("Row Inserted");
    }

    // Row Updating event
    public function rowUpdating($rsold, &$rsnew)
    {
        // Enter your code here
        // To cancel, set return value to false
        return true;
    }

    // Row Updated event
    public function rowUpdated($rsold, &$rsnew)
    {
        //Log("Row Updated");
    }

    // Row Update Conflict event
    public function rowUpdateConflict($rsold, &$rsnew)
    {
        // Enter your code here
        // To ignore conflict, set return value to false
        return true;
    }

    // Grid Inserting event
    public function gridInserting()
    {
        // Enter your code here
        // To reject grid insert, set return value to false
        return true;
    }

    // Grid Inserted event
    public function gridInserted($rsnew)
    {
        //Log("Grid Inserted");
    }

    // Grid Updating event
    public function gridUpdating($rsold)
    {
        // Enter your code here
        // To reject grid update, set return value to false
        return true;
    }

    // Grid Updated event
    public function gridUpdated($rsold, $rsnew)
    {
        //Log("Grid Updated");
    }

    // Row Deleting event
    public function rowDeleting(&$rs)
    {
        // Enter your code here
        // To cancel, set return value to False
        return true;
    }

    // Row Deleted event
    public function rowDeleted(&$rs)
    {
        //Log("Row Deleted");
    }

    // Email Sending event
    public function emailSending($email, &$args)
    {
        //var_dump($email); var_dump($args); exit();
        return true;
    }

    // Lookup Selecting event
    public function lookupSelecting($fld, &$filter)
    {
        //var_dump($fld->Name, $fld->Lookup, $filter); // Uncomment to view the filter
        // Enter your code here
    }

    // Row Rendering event
    public function rowRendering()
    {
        // Enter your code here
    }

    // Row Rendered event
    public function rowRendered()
    {
        // To view properties of field class, use:
        //var_dump($this-><FieldName>);
    }

    // User ID Filtering event
    public function userIdFiltering(&$filter)
    {
        // Enter your code here
    }
}
