{"version": 3, "file": "purify.js", "sources": ["../src/utils.js", "../src/tags.js", "../src/attrs.js", "../src/regexp.js", "../src/purify.js"], "sourcesContent": ["const { hasOwnProperty, setPrototypeOf, isFrozen, keys: objectKeys } = Object;\n\nlet { freeze, seal } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayJoin = unapply(Array.prototype.join);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\nconst regExpCreate = unconstruct(RegExp);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array) {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = stringToLowerCase(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = {};\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property])) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayJoin,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  hasOwnProperty,\n  isFrozen,\n  objectKeys,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpCreate,\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n};\n", "import { freeze } from './utils';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'audio',\n  'canvas',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'video',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\s\\S]*|[\\s\\S]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\s\\S]*|[\\s\\S]*%>/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205f\\u3000]/g // eslint-disable-line no-control-regex\n);\n", "import * as TAGS from './tags';\nimport * as ATTRS from './attrs';\nimport * as EXPRESSIONS from './regexp';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  objectKeys,\n  arrayForEach,\n  arrayIndexOf,\n  arrayJoin,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  regExpCreate,\n  typeErrorCreate,\n} from './utils';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n  let removeTitle = false;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    Text,\n    Comment,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML =\n    trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML('')\n      : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    getElementsByTagName,\n    createDocumentFragment,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    implementation &&\n    typeof implementation.createHTMLDocument !== 'undefined' &&\n    document.documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Output should be safe for jQuery's $() factory? */\n  let SAFE_FOR_JQUERY = false;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* If `RETURN_DOM` or `RETURN_DOM_FRAGMENT` is enabled, decide if the returned DOM\n   * `Node` is imported into the current `Document`. If this flag is not enabled the\n   * `Node` will belong (its ownerDocument) to a fresh `HTMLDocument`, created by\n   * DOMPurify. */\n  let RETURN_DOM_IMPORT = false;\n\n  /* Try to return a Trusted Type object instead of a string, retrun a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks? */\n  let SANITIZE_DOM = true;\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  const FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR)\n        : DEFAULT_ALLOWED_ATTR;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), cfg.ADD_URI_SAFE_ATTR)\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(clone(DEFAULT_DATA_URI_TAGS), cfg.ADD_DATA_URI_TAGS)\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS) : {};\n    FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR) : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    SAFE_FOR_JQUERY = cfg.SAFE_FOR_JQUERY || false; // Default false\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_DOM_IMPORT = cfg.RETURN_DOM_IMPORT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      node.outerHTML = emptyHTML;\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /* Use the DOMParser API by default, fallback later if needs be */\n    try {\n      doc = new DOMParser().parseFromString(dirtyPayload, 'text/html');\n    } catch (_) {}\n\n    /* Remove title to fix a mXSS bug in older MS Edge */\n    if (removeTitle) {\n      addToSet(FORBID_TAGS, ['title']);\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createHTMLDocument('');\n      const { body } = doc;\n      body.parentNode.removeChild(body.parentNode.firstElementChild);\n      body.outerHTML = dirtyPayload;\n    }\n\n    if (dirty && leadingWhitespace) {\n      doc.body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        doc.body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n  };\n\n  /* Here we test for a broken feature in Edge that might cause mXSS */\n  if (DOMPurify.isSupported) {\n    (function () {\n      try {\n        const doc = _initDocument('<x/><title>&lt;/title&gt;&lt;img&gt;');\n        if (regExpTest(/<\\/title/, doc.querySelector('title').innerHTML)) {\n          removeTitle = true;\n        }\n      } catch (_) {}\n    })();\n  }\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT,\n      () => {\n        return NodeFilter.FILTER_ACCEPT;\n      },\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    if (elm instanceof Text || elm instanceof Comment) {\n      return false;\n    }\n\n    if (\n      typeof elm.nodeName !== 'string' ||\n      typeof elm.textContent !== 'string' ||\n      typeof elm.removeChild !== 'function' ||\n      !(elm.attributes instanceof NamedNodeMap) ||\n      typeof elm.removeAttribute !== 'function' ||\n      typeof elm.setAttribute !== 'function' ||\n      typeof elm.namespaceURI !== 'string'\n    ) {\n      return true;\n    }\n\n    return false;\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  // eslint-disable-next-line complexity\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = stringToLowerCase(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Take care of an mXSS pattern using p, br inside svg, math */\n    if (\n      (tagName === 'svg' || tagName === 'math') &&\n      currentNode.querySelectorAll('p, br').length !== 0\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Keep content except for bad-listed elements */\n      if (\n        KEEP_CONTENT &&\n        !FORBID_CONTENTS[tagName] &&\n        typeof currentNode.insertAdjacentHTML === 'function'\n      ) {\n        try {\n          const htmlToInsert = currentNode.innerHTML;\n          currentNode.insertAdjacentHTML(\n            'AfterEnd',\n            trustedTypesPolicy\n              ? trustedTypesPolicy.createHTML(htmlToInsert)\n              : htmlToInsert\n          );\n        } catch (_) {}\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove in case a noscript/noembed XSS is suspected */\n    if (\n      tagName === 'noscript' &&\n      regExpTest(/<\\/noscript/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    if (\n      tagName === 'noembed' &&\n      regExpTest(/<\\/noembed/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Convert markup to cover jQuery behavior */\n    if (\n      SAFE_FOR_JQUERY &&\n      !currentNode.firstElementChild &&\n      (!currentNode.content || !currentNode.content.firstElementChild) &&\n      regExpTest(/</g, currentNode.textContent)\n    ) {\n      arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n      if (currentNode.innerHTML) {\n        currentNode.innerHTML = stringReplace(\n          currentNode.innerHTML,\n          /</g,\n          '&lt;'\n        );\n      } else {\n        currentNode.innerHTML = stringReplace(\n          currentNode.textContent,\n          /</g,\n          '&lt;'\n        );\n      }\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && regExpTest(DATA_ATTR, lcName)) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      return false;\n\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n      // eslint-disable-next-line no-negated-condition\n    } else if (!value) {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    } else {\n      return false;\n    }\n\n    return true;\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  // eslint-disable-next-line complexity\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let idAttr;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    let { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = stringTrim(attr.value);\n      lcName = stringToLowerCase(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      // Safari (iOS + Mac), last tested v8.0.5, crashes if you try to\n      // remove a \"name\" attribute from an <img> tag that has an \"id\"\n      // attribute at the time.\n      if (\n        lcName === 'name' &&\n        currentNode.nodeName === 'IMG' &&\n        attributes.id\n      ) {\n        idAttr = attributes.id;\n        attributes = arraySlice(attributes, []);\n        _removeAttribute('id', currentNode);\n        _removeAttribute(name, currentNode);\n        if (arrayIndexOf(attributes, idAttr) > l) {\n          currentNode.setAttribute('id', idAttr.value);\n        }\n      } else if (\n        // This works around a bug in Safari, where input[type=file]\n        // cannot be dynamically set after type has been removed\n        currentNode.nodeName === 'INPUT' &&\n        lcName === 'type' &&\n        value === 'file' &&\n        hookEvent.keepAttr &&\n        (ALLOWED_ATTR[lcName] || !FORBID_ATTR[lcName])\n      ) {\n        continue;\n      } else {\n        // This avoids a crash in Safari v9.0 with double-ids.\n        // The trick is to first set the id to be empty and then to\n        // remove the attribute\n        if (name === 'id') {\n          currentNode.setAttribute(name, '');\n        }\n\n        _removeAttribute(name, currentNode);\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (SAFE_FOR_JQUERY && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Take care of an mXSS pattern using namespace switches */\n      if (\n        regExpTest(/svg|math/i, currentNode.namespaceURI) &&\n        regExpTest(\n          regExpCreate(\n            '</(' + arrayJoin(objectKeys(FORBID_CONTENTS), '|') + ')',\n            'i'\n          ),\n          value\n        )\n      ) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = currentNode.nodeName.toLowerCase();\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        arrayPop(DOMPurify.removed);\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    if (!dirty) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      // eslint-disable-next-line no-negated-condition\n      if (typeof dirty.toString !== 'function') {\n        throw typeErrorCreate('toString is not a function');\n      } else {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* No special handling necessary for in-place sanitization */\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!-->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : emptyHTML;\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (RETURN_DOM_IMPORT) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = stringToLowerCase(tag);\n    const lcName = stringToLowerCase(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["hasOwnProperty", "Object", "setPrototypeOf", "isFrozen", "objectKeys", "keys", "freeze", "seal", "Reflect", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayIndexOf", "indexOf", "arrayJoin", "join", "arrayPop", "pop", "arrayPush", "push", "arraySlice", "slice", "stringToLowerCase", "String", "toLowerCase", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "regExpCreate", "unconstruct", "typeErrorCreate", "TypeError", "func", "thisArg", "addToSet", "set", "array", "l", "length", "element", "lcElement", "clone", "object", "newObject", "property", "html", "svg", "svgFilters", "mathMl", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "createHTML", "_", "console", "warn", "createDOMPurify", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "removeTitle", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "Text", "Comment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "RETURN_TRUSTED_TYPE", "implementation", "createNodeIterator", "getElementsByTagName", "createDocumentFragment", "importNode", "hooks", "createHTMLDocument", "documentMode", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "SAFE_FOR_JQUERY", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_DOM_IMPORT", "SANITIZE_DOM", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "CONFIG", "formElement", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "body", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "insertBefore", "createTextNode", "childNodes", "call", "querySelector", "innerHTML", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "FILTER_ACCEPT", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "setAttribute", "namespaceURI", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "tagName", "allowedTags", "querySelectorAll", "insertAdjacentHTML", "htmlToInsert", "cloneNode", "_isValidAttribute", "lcTag", "lcName", "value", "_sanitizeAttributes", "attr", "idAttr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "undefined", "id", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toString", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "serializedHTML", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;;;;;;;;MAAQA,iBAA+DC,OAA/DD;MAAgBE,iBAA+CD,OAA/CC;MAAgBC,WAA+BF,OAA/BE;MAAgBC,aAAeH,OAArBI;MAE5CC,SAAiBL,OAAjBK;MAAQC,OAASN,OAATM;;aACa,OAAOC,OAAP,KAAmB,WAAnB,IAAkCA;MAAvDC,aAAAA;MAAOC,iBAAAA;;EAEb,IAAI,CAACD,KAAL,EAAY;EACVA,UAAQ,eAAUE,GAAV,EAAeC,SAAf,EAA0BC,IAA1B,EAAgC;EACtC,WAAOF,IAAIF,KAAJ,CAAUG,SAAV,EAAqBC,IAArB,CAAP;EACD,GAFD;EAGD;;EAED,IAAI,CAACP,MAAL,EAAa;EACXA,WAAS,gBAAUQ,CAAV,EAAa;EACpB,WAAOA,CAAP;EACD,GAFD;EAGD;;EAED,IAAI,CAACP,IAAL,EAAW;EACTA,SAAO,cAAUO,CAAV,EAAa;EAClB,WAAOA,CAAP;EACD,GAFD;EAGD;;EAED,IAAI,CAACJ,SAAL,EAAgB;EACdA,cAAY,mBAAUK,IAAV,EAAgBF,IAAhB,EAAsB;EAChC,8CAAWE,IAAX,mCAAmBF,IAAnB;EACD,GAFD;EAGD;;EAED,IAAMG,eAAeC,QAAQC,MAAMC,SAAN,CAAgBC,OAAxB,CAArB;EACA,IAAMC,eAAeJ,QAAQC,MAAMC,SAAN,CAAgBG,OAAxB,CAArB;EACA,IAAMC,YAAYN,QAAQC,MAAMC,SAAN,CAAgBK,IAAxB,CAAlB;EACA,IAAMC,WAAWR,QAAQC,MAAMC,SAAN,CAAgBO,GAAxB,CAAjB;EACA,IAAMC,YAAYV,QAAQC,MAAMC,SAAN,CAAgBS,IAAxB,CAAlB;EACA,IAAMC,aAAaZ,QAAQC,MAAMC,SAAN,CAAgBW,KAAxB,CAAnB;;EAEA,IAAMC,oBAAoBd,QAAQe,OAAOb,SAAP,CAAiBc,WAAzB,CAA1B;EACA,IAAMC,cAAcjB,QAAQe,OAAOb,SAAP,CAAiBgB,KAAzB,CAApB;EACA,IAAMC,gBAAgBnB,QAAQe,OAAOb,SAAP,CAAiBkB,OAAzB,CAAtB;EACA,IAAMC,gBAAgBrB,QAAQe,OAAOb,SAAP,CAAiBG,OAAzB,CAAtB;EACA,IAAMiB,aAAatB,QAAQe,OAAOb,SAAP,CAAiBqB,IAAzB,CAAnB;;EAEA,IAAMC,aAAaxB,QAAQyB,OAAOvB,SAAP,CAAiBwB,IAAzB,CAAnB;EACA,IAAMC,eAAeC,YAAYH,MAAZ,CAArB;;EAEA,IAAMI,kBAAkBD,YAAYE,SAAZ,CAAxB;;AAEA,EAAO,SAAS9B,OAAT,CAAiB+B,IAAjB,EAAuB;EAC5B,SAAO,UAACC,OAAD;EAAA,sCAAapC,IAAb;EAAaA,UAAb;EAAA;;EAAA,WAAsBJ,MAAMuC,IAAN,EAAYC,OAAZ,EAAqBpC,IAArB,CAAtB;EAAA,GAAP;EACD;;AAED,EAAO,SAASgC,WAAT,CAAqBG,IAArB,EAA2B;EAChC,SAAO;EAAA,uCAAInC,IAAJ;EAAIA,UAAJ;EAAA;;EAAA,WAAaH,UAAUsC,IAAV,EAAgBnC,IAAhB,CAAb;EAAA,GAAP;EACD;;EAED;AACA,EAAO,SAASqC,QAAT,CAAkBC,GAAlB,EAAuBC,KAAvB,EAA8B;EACnC,MAAIlD,cAAJ,EAAoB;EAClB;EACA;EACA;EACAA,mBAAeiD,GAAf,EAAoB,IAApB;EACD;;EAED,MAAIE,IAAID,MAAME,MAAd;EACA,SAAOD,GAAP,EAAY;EACV,QAAIE,UAAUH,MAAMC,CAAN,CAAd;EACA,QAAI,OAAOE,OAAP,KAAmB,QAAvB,EAAiC;EAC/B,UAAMC,YAAYzB,kBAAkBwB,OAAlB,CAAlB;EACA,UAAIC,cAAcD,OAAlB,EAA2B;EACzB;EACA,YAAI,CAACpD,SAASiD,KAAT,CAAL,EAAsB;EACpBA,gBAAMC,CAAN,IAAWG,SAAX;EACD;;EAEDD,kBAAUC,SAAV;EACD;EACF;;EAEDL,QAAII,OAAJ,IAAe,IAAf;EACD;;EAED,SAAOJ,GAAP;EACD;;EAED;AACA,EAAO,SAASM,KAAT,CAAeC,MAAf,EAAuB;EAC5B,MAAMC,YAAY,EAAlB;;EAEA,MAAIC,iBAAJ;EACA,OAAKA,QAAL,IAAiBF,MAAjB,EAAyB;EACvB,QAAIjD,MAAMT,cAAN,EAAsB0D,MAAtB,EAA8B,CAACE,QAAD,CAA9B,CAAJ,EAA+C;EAC7CD,gBAAUC,QAAV,IAAsBF,OAAOE,QAAP,CAAtB;EACD;EACF;;EAED,SAAOD,SAAP;EACD;;EC/FM,IAAME,OAAOvD,OAAO,CACzB,GADyB,EAEzB,MAFyB,EAGzB,SAHyB,EAIzB,SAJyB,EAKzB,MALyB,EAMzB,SANyB,EAOzB,OAPyB,EAQzB,OARyB,EASzB,GATyB,EAUzB,KAVyB,EAWzB,KAXyB,EAYzB,KAZyB,EAazB,OAbyB,EAczB,YAdyB,EAezB,MAfyB,EAgBzB,IAhByB,EAiBzB,QAjByB,EAkBzB,QAlByB,EAmBzB,SAnByB,EAoBzB,QApByB,EAqBzB,MArByB,EAsBzB,MAtByB,EAuBzB,KAvByB,EAwBzB,UAxByB,EAyBzB,SAzByB,EA0BzB,MA1ByB,EA2BzB,UA3ByB,EA4BzB,IA5ByB,EA6BzB,WA7ByB,EA8BzB,KA9ByB,EA+BzB,SA/ByB,EAgCzB,KAhCyB,EAiCzB,KAjCyB,EAkCzB,KAlCyB,EAmCzB,IAnCyB,EAoCzB,IApCyB,EAqCzB,SArCyB,EAsCzB,IAtCyB,EAuCzB,UAvCyB,EAwCzB,YAxCyB,EAyCzB,QAzCyB,EA0CzB,MA1CyB,EA2CzB,QA3CyB,EA4CzB,MA5CyB,EA6CzB,IA7CyB,EA8CzB,IA9CyB,EA+CzB,IA/CyB,EAgDzB,IAhDyB,EAiDzB,IAjDyB,EAkDzB,IAlDyB,EAmDzB,MAnDyB,EAoDzB,QApDyB,EAqDzB,QArDyB,EAsDzB,IAtDyB,EAuDzB,MAvDyB,EAwDzB,GAxDyB,EAyDzB,KAzDyB,EA0DzB,OA1DyB,EA2DzB,KA3DyB,EA4DzB,KA5DyB,EA6DzB,OA7DyB,EA8DzB,QA9DyB,EA+DzB,IA/DyB,EAgEzB,MAhEyB,EAiEzB,KAjEyB,EAkEzB,MAlEyB,EAmEzB,SAnEyB,EAoEzB,MApEyB,EAqEzB,UArEyB,EAsEzB,OAtEyB,EAuEzB,KAvEyB,EAwEzB,MAxEyB,EAyEzB,IAzEyB,EA0EzB,UA1EyB,EA2EzB,QA3EyB,EA4EzB,QA5EyB,EA6EzB,GA7EyB,EA8EzB,SA9EyB,EA+EzB,KA/EyB,EAgFzB,UAhFyB,EAiFzB,GAjFyB,EAkFzB,IAlFyB,EAmFzB,IAnFyB,EAoFzB,MApFyB,EAqFzB,GArFyB,EAsFzB,MAtFyB,EAuFzB,SAvFyB,EAwFzB,QAxFyB,EAyFzB,QAzFyB,EA0FzB,OA1FyB,EA2FzB,QA3FyB,EA4FzB,QA5FyB,EA6FzB,MA7FyB,EA8FzB,QA9FyB,EA+FzB,QA/FyB,EAgGzB,OAhGyB,EAiGzB,KAjGyB,EAkGzB,SAlGyB,EAmGzB,KAnGyB,EAoGzB,OApGyB,EAqGzB,OArGyB,EAsGzB,IAtGyB,EAuGzB,UAvGyB,EAwGzB,UAxGyB,EAyGzB,OAzGyB,EA0GzB,IA1GyB,EA2GzB,OA3GyB,EA4GzB,MA5GyB,EA6GzB,IA7GyB,EA8GzB,OA9GyB,EA+GzB,IA/GyB,EAgHzB,GAhHyB,EAiHzB,IAjHyB,EAkHzB,KAlHyB,EAmHzB,OAnHyB,EAoHzB,KApHyB,CAAP,CAAb;;EAuHP;AACA,EAAO,IAAMwD,MAAMxD,OAAO,CACxB,KADwB,EAExB,GAFwB,EAGxB,UAHwB,EAIxB,aAJwB,EAKxB,cALwB,EAMxB,cANwB,EAOxB,eAPwB,EAQxB,kBARwB,EASxB,OATwB,EAUxB,QAVwB,EAWxB,QAXwB,EAYxB,UAZwB,EAaxB,MAbwB,EAcxB,MAdwB,EAexB,SAfwB,EAgBxB,QAhBwB,EAiBxB,MAjBwB,EAkBxB,GAlBwB,EAmBxB,OAnBwB,EAoBxB,UApBwB,EAqBxB,OArBwB,EAsBxB,OAtBwB,EAuBxB,MAvBwB,EAwBxB,gBAxBwB,EAyBxB,QAzBwB,EA0BxB,MA1BwB,EA2BxB,UA3BwB,EA4BxB,OA5BwB,EA6BxB,MA7BwB,EA8BxB,SA9BwB,EA+BxB,SA/BwB,EAgCxB,UAhCwB,EAiCxB,gBAjCwB,EAkCxB,MAlCwB,EAmCxB,MAnCwB,EAoCxB,OApCwB,EAqCxB,QArCwB,EAsCxB,QAtCwB,EAuCxB,MAvCwB,EAwCxB,UAxCwB,EAyCxB,OAzCwB,EA0CxB,MA1CwB,EA2CxB,OA3CwB,EA4CxB,OA5CwB,EA6CxB,MA7CwB,EA8CxB,OA9CwB,CAAP,CAAZ;;AAiDP,EAAO,IAAMyD,aAAazD,OAAO,CAC/B,SAD+B,EAE/B,eAF+B,EAG/B,qBAH+B,EAI/B,aAJ+B,EAK/B,kBAL+B,EAM/B,mBAN+B,EAO/B,mBAP+B,EAQ/B,gBAR+B,EAS/B,SAT+B,EAU/B,SAV+B,EAW/B,SAX+B,EAY/B,SAZ+B,EAa/B,SAb+B,EAc/B,gBAd+B,EAe/B,SAf+B,EAgB/B,aAhB+B,EAiB/B,cAjB+B,EAkB/B,UAlB+B,EAmB/B,cAnB+B,EAoB/B,oBApB+B,EAqB/B,aArB+B,EAsB/B,QAtB+B,EAuB/B,cAvB+B,CAAP,CAAnB;;AA0BP,EAAO,IAAM0D,SAAS1D,OAAO,CAC3B,MAD2B,EAE3B,UAF2B,EAG3B,QAH2B,EAI3B,SAJ2B,EAK3B,OAL2B,EAM3B,QAN2B,EAO3B,IAP2B,EAQ3B,YAR2B,EAS3B,eAT2B,EAU3B,IAV2B,EAW3B,IAX2B,EAY3B,OAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,MAhB2B,EAiB3B,IAjB2B,EAkB3B,QAlB2B,EAmB3B,OAnB2B,EAoB3B,QApB2B,EAqB3B,MArB2B,EAsB3B,MAtB2B,EAuB3B,SAvB2B,EAwB3B,QAxB2B,EAyB3B,KAzB2B,EA0B3B,OA1B2B,EA2B3B,KA3B2B,EA4B3B,QA5B2B,EA6B3B,YA7B2B,CAAP,CAAf;;AAgCP,EAAO,IAAM2D,OAAO3D,OAAO,CAAC,OAAD,CAAP,CAAb;;ECnOA,IAAMuD,SAAOvD,OAAO,CACzB,QADyB,EAEzB,QAFyB,EAGzB,OAHyB,EAIzB,KAJyB,EAKzB,gBALyB,EAMzB,cANyB,EAOzB,sBAPyB,EAQzB,UARyB,EASzB,YATyB,EAUzB,SAVyB,EAWzB,QAXyB,EAYzB,SAZyB,EAazB,aAbyB,EAczB,aAdyB,EAezB,SAfyB,EAgBzB,MAhByB,EAiBzB,OAjByB,EAkBzB,OAlByB,EAmBzB,OAnByB,EAoBzB,MApByB,EAqBzB,SArByB,EAsBzB,UAtByB,EAuBzB,cAvByB,EAwBzB,QAxByB,EAyBzB,aAzByB,EA0BzB,UA1ByB,EA2BzB,UA3ByB,EA4BzB,SA5ByB,EA6BzB,KA7ByB,EA8BzB,UA9ByB,EA+BzB,yBA/ByB,EAgCzB,uBAhCyB,EAiCzB,UAjCyB,EAkCzB,WAlCyB,EAmCzB,SAnCyB,EAoCzB,cApCyB,EAqCzB,MArCyB,EAsCzB,KAtCyB,EAuCzB,SAvCyB,EAwCzB,QAxCyB,EAyCzB,QAzCyB,EA0CzB,MA1CyB,EA2CzB,MA3CyB,EA4CzB,UA5CyB,EA6CzB,IA7CyB,EA8CzB,WA9CyB,EA+CzB,WA/CyB,EAgDzB,OAhDyB,EAiDzB,MAjDyB,EAkDzB,OAlDyB,EAmDzB,MAnDyB,EAoDzB,MApDyB,EAqDzB,SArDyB,EAsDzB,MAtDyB,EAuDzB,KAvDyB,EAwDzB,KAxDyB,EAyDzB,WAzDyB,EA0DzB,OA1DyB,EA2DzB,QA3DyB,EA4DzB,KA5DyB,EA6DzB,WA7DyB,EA8DzB,UA9DyB,EA+DzB,OA/DyB,EAgEzB,MAhEyB,EAiEzB,SAjEyB,EAkEzB,YAlEyB,EAmEzB,QAnEyB,EAoEzB,MApEyB,EAqEzB,SArEyB,EAsEzB,SAtEyB,EAuEzB,aAvEyB,EAwEzB,aAxEyB,EAyEzB,QAzEyB,EA0EzB,SA1EyB,EA2EzB,SA3EyB,EA4EzB,YA5EyB,EA6EzB,UA7EyB,EA8EzB,KA9EyB,EA+EzB,UA/EyB,EAgFzB,KAhFyB,EAiFzB,UAjFyB,EAkFzB,MAlFyB,EAmFzB,MAnFyB,EAoFzB,SApFyB,EAqFzB,YArFyB,EAsFzB,OAtFyB,EAuFzB,UAvFyB,EAwFzB,OAxFyB,EAyFzB,MAzFyB,EA0FzB,OA1FyB,EA2FzB,MA3FyB,EA4FzB,SA5FyB,EA6FzB,OA7FyB,EA8FzB,KA9FyB,EA+FzB,QA/FyB,EAgGzB,MAhGyB,EAiGzB,OAjGyB,EAkGzB,SAlGyB,EAmGzB,UAnGyB,EAoGzB,OApGyB,EAqGzB,WArGyB,EAsGzB,MAtGyB,EAuGzB,QAvGyB,EAwGzB,QAxGyB,EAyGzB,OAzGyB,EA0GzB,OA1GyB,EA2GzB,OA3GyB,CAAP,CAAb;;AA8GP,EAAO,IAAMwD,QAAMxD,OAAO,CACxB,eADwB,EAExB,YAFwB,EAGxB,UAHwB,EAIxB,oBAJwB,EAKxB,QALwB,EAMxB,eANwB,EAOxB,eAPwB,EAQxB,SARwB,EASxB,eATwB,EAUxB,gBAVwB,EAWxB,OAXwB,EAYxB,MAZwB,EAaxB,IAbwB,EAcxB,OAdwB,EAexB,MAfwB,EAgBxB,WAhBwB,EAiBxB,WAjBwB,EAkBxB,OAlBwB,EAmBxB,qBAnBwB,EAoBxB,6BApBwB,EAqBxB,eArBwB,EAsBxB,iBAtBwB,EAuBxB,IAvBwB,EAwBxB,IAxBwB,EAyBxB,GAzBwB,EA0BxB,IA1BwB,EA2BxB,IA3BwB,EA4BxB,iBA5BwB,EA6BxB,WA7BwB,EA8BxB,SA9BwB,EA+BxB,SA/BwB,EAgCxB,KAhCwB,EAiCxB,UAjCwB,EAkCxB,WAlCwB,EAmCxB,KAnCwB,EAoCxB,MApCwB,EAqCxB,cArCwB,EAsCxB,WAtCwB,EAuCxB,QAvCwB,EAwCxB,aAxCwB,EAyCxB,aAzCwB,EA0CxB,eA1CwB,EA2CxB,aA3CwB,EA4CxB,WA5CwB,EA6CxB,kBA7CwB,EA8CxB,cA9CwB,EA+CxB,YA/CwB,EAgDxB,cAhDwB,EAiDxB,aAjDwB,EAkDxB,IAlDwB,EAmDxB,IAnDwB,EAoDxB,IApDwB,EAqDxB,IArDwB,EAsDxB,YAtDwB,EAuDxB,UAvDwB,EAwDxB,eAxDwB,EAyDxB,mBAzDwB,EA0DxB,QA1DwB,EA2DxB,MA3DwB,EA4DxB,IA5DwB,EA6DxB,iBA7DwB,EA8DxB,IA9DwB,EA+DxB,KA/DwB,EAgExB,GAhEwB,EAiExB,IAjEwB,EAkExB,IAlEwB,EAmExB,IAnEwB,EAoExB,IApEwB,EAqExB,SArEwB,EAsExB,WAtEwB,EAuExB,YAvEwB,EAwExB,UAxEwB,EAyExB,MAzEwB,EA0ExB,cA1EwB,EA2ExB,gBA3EwB,EA4ExB,cA5EwB,EA6ExB,kBA7EwB,EA8ExB,gBA9EwB,EA+ExB,OA/EwB,EAgFxB,YAhFwB,EAiFxB,YAjFwB,EAkFxB,cAlFwB,EAmFxB,cAnFwB,EAoFxB,aApFwB,EAqFxB,aArFwB,EAsFxB,kBAtFwB,EAuFxB,WAvFwB,EAwFxB,KAxFwB,EAyFxB,MAzFwB,EA0FxB,OA1FwB,EA2FxB,QA3FwB,EA4FxB,MA5FwB,EA6FxB,KA7FwB,EA8FxB,MA9FwB,EA+FxB,YA/FwB,EAgGxB,QAhGwB,EAiGxB,UAjGwB,EAkGxB,SAlGwB,EAmGxB,OAnGwB,EAoGxB,QApGwB,EAqGxB,aArGwB,EAsGxB,QAtGwB,EAuGxB,UAvGwB,EAwGxB,aAxGwB,EAyGxB,MAzGwB,EA0GxB,YA1GwB,EA2GxB,qBA3GwB,EA4GxB,kBA5GwB,EA6GxB,cA7GwB,EA8GxB,QA9GwB,EA+GxB,eA/GwB,EAgHxB,qBAhHwB,EAiHxB,gBAjHwB,EAkHxB,GAlHwB,EAmHxB,IAnHwB,EAoHxB,IApHwB,EAqHxB,QArHwB,EAsHxB,MAtHwB,EAuHxB,MAvHwB,EAwHxB,aAxHwB,EAyHxB,WAzHwB,EA0HxB,SA1HwB,EA2HxB,QA3HwB,EA4HxB,QA5HwB,EA6HxB,OA7HwB,EA8HxB,MA9HwB,EA+HxB,iBA/HwB,EAgIxB,kBAhIwB,EAiIxB,kBAjIwB,EAkIxB,cAlIwB,EAmIxB,aAnIwB,EAoIxB,cApIwB,EAqIxB,aArIwB,EAsIxB,YAtIwB,EAuIxB,cAvIwB,EAwIxB,kBAxIwB,EAyIxB,mBAzIwB,EA0IxB,gBA1IwB,EA2IxB,iBA3IwB,EA4IxB,mBA5IwB,EA6IxB,gBA7IwB,EA8IxB,QA9IwB,EA+IxB,cA/IwB,EAgJxB,OAhJwB,EAiJxB,cAjJwB,EAkJxB,UAlJwB,EAmJxB,SAnJwB,EAoJxB,SApJwB,EAqJxB,WArJwB,EAsJxB,aAtJwB,EAuJxB,iBAvJwB,EAwJxB,gBAxJwB,EAyJxB,YAzJwB,EA0JxB,MA1JwB,EA2JxB,IA3JwB,EA4JxB,IA5JwB,EA6JxB,SA7JwB,EA8JxB,QA9JwB,EA+JxB,SA/JwB,EAgKxB,YAhKwB,EAiKxB,SAjKwB,EAkKxB,YAlKwB,EAmKxB,eAnKwB,EAoKxB,eApKwB,EAqKxB,OArKwB,EAsKxB,cAtKwB,EAuKxB,MAvKwB,EAwKxB,cAxKwB,EAyKxB,kBAzKwB,EA0KxB,kBA1KwB,EA2KxB,GA3KwB,EA4KxB,IA5KwB,EA6KxB,IA7KwB,EA8KxB,OA9KwB,EA+KxB,GA/KwB,EAgLxB,IAhLwB,EAiLxB,IAjLwB,EAkLxB,GAlLwB,EAmLxB,YAnLwB,CAAP,CAAZ;;AAsLP,EAAO,IAAM0D,WAAS1D,OAAO,CAC3B,QAD2B,EAE3B,aAF2B,EAG3B,OAH2B,EAI3B,UAJ2B,EAK3B,OAL2B,EAM3B,cAN2B,EAO3B,aAP2B,EAQ3B,YAR2B,EAS3B,YAT2B,EAU3B,OAV2B,EAW3B,KAX2B,EAY3B,SAZ2B,EAa3B,cAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,OAhB2B,EAiB3B,QAjB2B,EAkB3B,MAlB2B,EAmB3B,IAnB2B,EAoB3B,SApB2B,EAqB3B,QArB2B,EAsB3B,eAtB2B,EAuB3B,QAvB2B,EAwB3B,QAxB2B,EAyB3B,gBAzB2B,EA0B3B,WA1B2B,EA2B3B,UA3B2B,EA4B3B,aA5B2B,EA6B3B,SA7B2B,EA8B3B,SA9B2B,EA+B3B,eA/B2B,EAgC3B,UAhC2B,EAiC3B,UAjC2B,EAkC3B,MAlC2B,EAmC3B,UAnC2B,EAoC3B,UApC2B,EAqC3B,YArC2B,EAsC3B,SAtC2B,EAuC3B,QAvC2B,EAwC3B,QAxC2B,EAyC3B,aAzC2B,EA0C3B,eA1C2B,EA2C3B,sBA3C2B,EA4C3B,WA5C2B,EA6C3B,WA7C2B,EA8C3B,YA9C2B,EA+C3B,UA/C2B,EAgD3B,gBAhD2B,EAiD3B,gBAjD2B,EAkD3B,WAlD2B,EAmD3B,SAnD2B,EAoD3B,OApD2B,EAqD3B,OArD2B,CAAP,CAAf;;AAwDP,EAAO,IAAM4D,MAAM5D,OAAO,CACxB,YADwB,EAExB,QAFwB,EAGxB,aAHwB,EAIxB,WAJwB,EAKxB,aALwB,CAAP,CAAZ;;EC5VP;AACA,EAAO,IAAM6D,gBAAgB5D,KAAK,2BAAL,CAAtB;AACP,EAAO,IAAM6D,WAAW7D,KAAK,uBAAL,CAAjB;AACP,EAAO,IAAM8D,YAAY9D,KAAK,4BAAL,CAAlB;AACP,EAAO,IAAM+D,YAAY/D,KAAK,gBAAL,CAAlB;AACP,EAAO,IAAMgE,iBAAiBhE,KAC5B,uFAD4B;EAAA,CAAvB;AAGP,EAAO,IAAMiE,oBAAoBjE,KAAK,uBAAL,CAA1B;AACP,EAAO,IAAMkE,kBAAkBlE,KAC7B,6DAD6B;EAAA,CAAxB;;;;;;ECaP,IAAMmE,YAAY,SAAZA,SAAY;EAAA,SAAO,OAAOC,MAAP,KAAkB,WAAlB,GAAgC,IAAhC,GAAuCA,MAA9C;EAAA,CAAlB;;EAEA;;;;;;;;EAQA,IAAMC,4BAA4B,SAA5BA,yBAA4B,CAAUC,YAAV,EAAwBC,QAAxB,EAAkC;EAClE,MACE,QAAOD,YAAP,yCAAOA,YAAP,OAAwB,QAAxB,IACA,OAAOA,aAAaE,YAApB,KAAqC,UAFvC,EAGE;EACA,WAAO,IAAP;EACD;;EAED;EACA;EACA;EACA,MAAIC,SAAS,IAAb;EACA,MAAMC,YAAY,uBAAlB;EACA,MACEH,SAASI,aAAT,IACAJ,SAASI,aAAT,CAAuBC,YAAvB,CAAoCF,SAApC,CAFF,EAGE;EACAD,aAASF,SAASI,aAAT,CAAuBE,YAAvB,CAAoCH,SAApC,CAAT;EACD;;EAED,MAAMI,aAAa,eAAeL,SAAS,MAAMA,MAAf,GAAwB,EAAvC,CAAnB;;EAEA,MAAI;EACF,WAAOH,aAAaE,YAAb,CAA0BM,UAA1B,EAAsC;EAC3CC,gBAD2C,sBAChCzB,OADgC,EAC1B;EACf,eAAOA,OAAP;EACD;EAH0C,KAAtC,CAAP;EAKD,GAND,CAME,OAAO0B,CAAP,EAAU;EACV;EACA;EACA;EACAC,YAAQC,IAAR,CACE,yBAAyBJ,UAAzB,GAAsC,wBADxC;EAGA,WAAO,IAAP;EACD;EACF,CArCD;;EAuCA,SAASK,eAAT,GAA+C;EAAA,MAAtBf,MAAsB,uEAAbD,WAAa;;EAC7C,MAAMiB,YAAY,SAAZA,SAAY,CAACC,IAAD;EAAA,WAAUF,gBAAgBE,IAAhB,CAAV;EAAA,GAAlB;;EAEA;;;;EAIAD,YAAUE,OAAV,GAAoBC,QAApB;;EAEA;;;;EAIAH,YAAUI,OAAV,GAAoB,EAApB;;EAEA,MAAI,CAACpB,MAAD,IAAW,CAACA,OAAOG,QAAnB,IAA+BH,OAAOG,QAAP,CAAgBkB,QAAhB,KAA6B,CAAhE,EAAmE;EACjE;EACA;EACAL,cAAUM,WAAV,GAAwB,KAAxB;;EAEA,WAAON,SAAP;EACD;;EAED,MAAMO,mBAAmBvB,OAAOG,QAAhC;EACA,MAAIqB,cAAc,KAAlB;;EAxB6C,MA0BvCrB,QA1BuC,GA0B1BH,MA1B0B,CA0BvCG,QA1BuC;EAAA,MA4B3CsB,gBA5B2C,GAqCzCzB,MArCyC,CA4B3CyB,gBA5B2C;EAAA,MA6B3CC,mBA7B2C,GAqCzC1B,MArCyC,CA6B3C0B,mBA7B2C;EAAA,MA8B3CC,IA9B2C,GAqCzC3B,MArCyC,CA8B3C2B,IA9B2C;EAAA,MA+B3CC,UA/B2C,GAqCzC5B,MArCyC,CA+B3C4B,UA/B2C;EAAA,6BAqCzC5B,MArCyC,CAgC3C6B,YAhC2C;EAAA,MAgC3CA,YAhC2C,wCAgC5B7B,OAAO6B,YAAP,IAAuB7B,OAAO8B,eAhCF;EAAA,MAiC3CC,IAjC2C,GAqCzC/B,MArCyC,CAiC3C+B,IAjC2C;EAAA,MAkC3CC,OAlC2C,GAqCzChC,MArCyC,CAkC3CgC,OAlC2C;EAAA,MAmC3CC,SAnC2C,GAqCzCjC,MArCyC,CAmC3CiC,SAnC2C;EAAA,MAoC3C/B,YApC2C,GAqCzCF,MArCyC,CAoC3CE,YApC2C;;EAuC7C;EACA;EACA;EACA;EACA;EACA;;EACA,MAAI,OAAOwB,mBAAP,KAA+B,UAAnC,EAA+C;EAC7C,QAAMQ,WAAW/B,SAASgC,aAAT,CAAuB,UAAvB,CAAjB;EACA,QAAID,SAASE,OAAT,IAAoBF,SAASE,OAAT,CAAiBC,aAAzC,EAAwD;EACtDlC,iBAAW+B,SAASE,OAAT,CAAiBC,aAA5B;EACD;EACF;;EAED,MAAMC,qBAAqBrC,0BACzBC,YADyB,EAEzBqB,gBAFyB,CAA3B;EAIA,MAAMgB,YACJD,sBAAsBE,mBAAtB,GACIF,mBAAmB3B,UAAnB,CAA8B,EAA9B,CADJ,GAEI,EAHN;;EAxD6C,kBAkEzCR,QAlEyC;EAAA,MA8D3CsC,cA9D2C,aA8D3CA,cA9D2C;EAAA,MA+D3CC,kBA/D2C,aA+D3CA,kBA/D2C;EAAA,MAgE3CC,oBAhE2C,aAgE3CA,oBAhE2C;EAAA,MAiE3CC,sBAjE2C,aAiE3CA,sBAjE2C;EAAA,MAmErCC,UAnEqC,GAmEtBtB,gBAnEsB,CAmErCsB,UAnEqC;;;EAqE7C,MAAIC,QAAQ,EAAZ;;EAEA;;;EAGA9B,YAAUM,WAAV,GACEmB,kBACA,OAAOA,eAAeM,kBAAtB,KAA6C,WAD7C,IAEA5C,SAAS6C,YAAT,KAA0B,CAH5B;;EA1E6C,MAgF3CxD,gBAhF2C,GAsFzCyD,aAtFyC;EAAA,MAiF3CxD,WAjF2C,GAsFzCwD,QAtFyC;EAAA,MAkF3CvD,YAlF2C,GAsFzCuD,SAtFyC;EAAA,MAmF3CtD,YAnF2C,GAsFzCsD,SAtFyC;EAAA,MAoF3CpD,oBApF2C,GAsFzCoD,iBAtFyC;EAAA,MAqF3CnD,kBArF2C,GAsFzCmD,eAtFyC;EAAA,MAwFvCrD,iBAxFuC,GAwFpBqD,cAxFoB;;EA0F7C;;;;;EAKA;;EACA,MAAIC,eAAe,IAAnB;EACA,MAAMC,uBAAuB5E,SAAS,EAAT,iCACxB6E,IADwB,wBAExBA,GAFwB,wBAGxBA,UAHwB,wBAIxBA,MAJwB,wBAKxBA,IALwB,GAA7B;;EAQA;EACA,MAAIC,eAAe,IAAnB;EACA,MAAMC,uBAAuB/E,SAAS,EAAT,iCACxBgF,MADwB,wBAExBA,KAFwB,wBAGxBA,QAHwB,wBAIxBA,GAJwB,GAA7B;;EAOA;EACA,MAAIC,cAAc,IAAlB;;EAEA;EACA,MAAIC,cAAc,IAAlB;;EAEA;EACA,MAAIC,kBAAkB,IAAtB;;EAEA;EACA,MAAIC,kBAAkB,IAAtB;;EAEA;EACA,MAAIC,0BAA0B,KAA9B;;EAEA;EACA,MAAIC,kBAAkB,KAAtB;;EAEA;;;EAGA,MAAIC,qBAAqB,KAAzB;;EAEA;EACA,MAAIC,iBAAiB,KAArB;;EAEA;EACA,MAAIC,aAAa,KAAjB;;EAEA;;EAEA,MAAIC,aAAa,KAAjB;;EAEA;;;;EAIA,MAAIC,aAAa,KAAjB;;EAEA;;EAEA,MAAIC,sBAAsB,KAA1B;;EAEA;;;;EAIA,MAAIC,oBAAoB,KAAxB;;EAEA;;EAEA,MAAI5B,sBAAsB,KAA1B;;EAEA;EACA,MAAI6B,eAAe,IAAnB;;EAEA;EACA,MAAIC,eAAe,IAAnB;;EAEA;;EAEA,MAAIC,WAAW,KAAf;;EAEA;EACA,MAAIC,eAAe,EAAnB;;EAEA;EACA,MAAMC,kBAAkBlG,SAAS,EAAT,EAAa,CACnC,gBADmC,EAEnC,OAFmC,EAGnC,UAHmC,EAInC,MAJmC,EAKnC,eALmC,EAMnC,MANmC,EAOnC,QAPmC,EAQnC,MARmC,EASnC,IATmC,EAUnC,IAVmC,EAWnC,IAXmC,EAYnC,IAZmC,EAanC,OAbmC,EAcnC,SAdmC,EAenC,UAfmC,EAgBnC,WAhBmC,EAiBnC,QAjBmC,EAkBnC,OAlBmC,EAmBnC,KAnBmC,EAoBnC,UApBmC,EAqBnC,OArBmC,EAsBnC,OAtBmC,EAuBnC,OAvBmC,EAwBnC,KAxBmC,CAAb,CAAxB;;EA2BA;EACA,MAAImG,gBAAgB,IAApB;EACA,MAAMC,wBAAwBpG,SAAS,EAAT,EAAa,CACzC,OADyC,EAEzC,OAFyC,EAGzC,KAHyC,EAIzC,QAJyC,EAKzC,OALyC,EAMzC,OANyC,CAAb,CAA9B;;EASA;EACA,MAAIqG,sBAAsB,IAA1B;EACA,MAAMC,8BAA8BtG,SAAS,EAAT,EAAa,CAC/C,KAD+C,EAE/C,OAF+C,EAG/C,KAH+C,EAI/C,IAJ+C,EAK/C,OAL+C,EAM/C,MAN+C,EAO/C,SAP+C,EAQ/C,aAR+C,EAS/C,SAT+C,EAU/C,OAV+C,EAW/C,OAX+C,EAY/C,OAZ+C,EAa/C,OAb+C,CAAb,CAApC;;EAgBA;EACA,MAAIuG,SAAS,IAAb;;EAEA;EACA;;EAEA,MAAMC,cAAc5E,SAASgC,aAAT,CAAuB,MAAvB,CAApB;;EAEA;;;;;EAKA;EACA,MAAM6C,eAAe,SAAfA,YAAe,CAAUC,GAAV,EAAe;EAClC,QAAIH,UAAUA,WAAWG,GAAzB,EAA8B;EAC5B;EACD;;EAED;EACA,QAAI,CAACA,GAAD,IAAQ,QAAOA,GAAP,yCAAOA,GAAP,OAAe,QAA3B,EAAqC;EACnCA,YAAM,EAAN;EACD;;EAED;EACA/B,mBACE,kBAAkB+B,GAAlB,GACI1G,SAAS,EAAT,EAAa0G,IAAI/B,YAAjB,CADJ,GAEIC,oBAHN;EAIAE,mBACE,kBAAkB4B,GAAlB,GACI1G,SAAS,EAAT,EAAa0G,IAAI5B,YAAjB,CADJ,GAEIC,oBAHN;EAIAsB,0BACE,uBAAuBK,GAAvB,GACI1G,SAASO,MAAM+F,2BAAN,CAAT,EAA6CI,IAAIC,iBAAjD,CADJ,GAEIL,2BAHN;EAIAH,oBACE,uBAAuBO,GAAvB,GACI1G,SAASO,MAAM6F,qBAAN,CAAT,EAAuCM,IAAIE,iBAA3C,CADJ,GAEIR,qBAHN;EAIAnB,kBAAc,iBAAiByB,GAAjB,GAAuB1G,SAAS,EAAT,EAAa0G,IAAIzB,WAAjB,CAAvB,GAAuD,EAArE;EACAC,kBAAc,iBAAiBwB,GAAjB,GAAuB1G,SAAS,EAAT,EAAa0G,IAAIxB,WAAjB,CAAvB,GAAuD,EAArE;EACAe,mBAAe,kBAAkBS,GAAlB,GAAwBA,IAAIT,YAA5B,GAA2C,KAA1D;EACAd,sBAAkBuB,IAAIvB,eAAJ,KAAwB,KAA1C,CA9BkC;EA+BlCC,sBAAkBsB,IAAItB,eAAJ,KAAwB,KAA1C,CA/BkC;EAgClCC,8BAA0BqB,IAAIrB,uBAAJ,IAA+B,KAAzD,CAhCkC;EAiClCC,sBAAkBoB,IAAIpB,eAAJ,IAAuB,KAAzC,CAjCkC;EAkClCC,yBAAqBmB,IAAInB,kBAAJ,IAA0B,KAA/C,CAlCkC;EAmClCC,qBAAiBkB,IAAIlB,cAAJ,IAAsB,KAAvC,CAnCkC;EAoClCG,iBAAae,IAAIf,UAAJ,IAAkB,KAA/B,CApCkC;EAqClCC,0BAAsBc,IAAId,mBAAJ,IAA2B,KAAjD,CArCkC;EAsClCC,wBAAoBa,IAAIb,iBAAJ,IAAyB,KAA7C,CAtCkC;EAuClC5B,0BAAsByC,IAAIzC,mBAAJ,IAA2B,KAAjD,CAvCkC;EAwClCyB,iBAAagB,IAAIhB,UAAJ,IAAkB,KAA/B,CAxCkC;EAyClCI,mBAAeY,IAAIZ,YAAJ,KAAqB,KAApC,CAzCkC;EA0ClCC,mBAAeW,IAAIX,YAAJ,KAAqB,KAApC,CA1CkC;EA2ClCC,eAAWU,IAAIV,QAAJ,IAAgB,KAA3B,CA3CkC;EA4ClC3E,wBAAiBqF,IAAIG,kBAAJ,IAA0BxF,iBAA3C;EACA,QAAIkE,kBAAJ,EAAwB;EACtBH,wBAAkB,KAAlB;EACD;;EAED,QAAIQ,mBAAJ,EAAyB;EACvBD,mBAAa,IAAb;EACD;;EAED;EACA,QAAIM,YAAJ,EAAkB;EAChBtB,qBAAe3E,SAAS,EAAT,iCAAiB6E,IAAjB,GAAf;EACAC,qBAAe,EAAf;EACA,UAAImB,aAAatF,IAAb,KAAsB,IAA1B,EAAgC;EAC9BX,iBAAS2E,YAAT,EAAuBE,IAAvB;EACA7E,iBAAS8E,YAAT,EAAuBE,MAAvB;EACD;;EAED,UAAIiB,aAAarF,GAAb,KAAqB,IAAzB,EAA+B;EAC7BZ,iBAAS2E,YAAT,EAAuBE,GAAvB;EACA7E,iBAAS8E,YAAT,EAAuBE,KAAvB;EACAhF,iBAAS8E,YAAT,EAAuBE,GAAvB;EACD;;EAED,UAAIiB,aAAapF,UAAb,KAA4B,IAAhC,EAAsC;EACpCb,iBAAS2E,YAAT,EAAuBE,UAAvB;EACA7E,iBAAS8E,YAAT,EAAuBE,KAAvB;EACAhF,iBAAS8E,YAAT,EAAuBE,GAAvB;EACD;;EAED,UAAIiB,aAAanF,MAAb,KAAwB,IAA5B,EAAkC;EAChCd,iBAAS2E,YAAT,EAAuBE,MAAvB;EACA7E,iBAAS8E,YAAT,EAAuBE,QAAvB;EACAhF,iBAAS8E,YAAT,EAAuBE,GAAvB;EACD;EACF;;EAED;EACA,QAAI0B,IAAII,QAAR,EAAkB;EAChB,UAAInC,iBAAiBC,oBAArB,EAA2C;EACzCD,uBAAepE,MAAMoE,YAAN,CAAf;EACD;;EAED3E,eAAS2E,YAAT,EAAuB+B,IAAII,QAA3B;EACD;;EAED,QAAIJ,IAAIK,QAAR,EAAkB;EAChB,UAAIjC,iBAAiBC,oBAArB,EAA2C;EACzCD,uBAAevE,MAAMuE,YAAN,CAAf;EACD;;EAED9E,eAAS8E,YAAT,EAAuB4B,IAAIK,QAA3B;EACD;;EAED,QAAIL,IAAIC,iBAAR,EAA2B;EACzB3G,eAASqG,mBAAT,EAA8BK,IAAIC,iBAAlC;EACD;;EAED;EACA,QAAIZ,YAAJ,EAAkB;EAChBpB,mBAAa,OAAb,IAAwB,IAAxB;EACD;;EAED;EACA,QAAIa,cAAJ,EAAoB;EAClBxF,eAAS2E,YAAT,EAAuB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAAvB;EACD;;EAED;EACA,QAAIA,aAAaqC,KAAjB,EAAwB;EACtBhH,eAAS2E,YAAT,EAAuB,CAAC,OAAD,CAAvB;EACA,aAAOM,YAAYgC,KAAnB;EACD;;EAED;EACA;EACA,QAAI7J,MAAJ,EAAY;EACVA,aAAOsJ,GAAP;EACD;;EAEDH,aAASG,GAAT;EACD,GA7HD;;EA+HA;;;;;EAKA,MAAMQ,eAAe,SAAfA,YAAe,CAAUC,IAAV,EAAgB;EACnC1I,cAAUgE,UAAUI,OAApB,EAA6B,EAAExC,SAAS8G,IAAX,EAA7B;EACA,QAAI;EACF;EACAA,WAAKC,UAAL,CAAgBC,WAAhB,CAA4BF,IAA5B;EACD,KAHD,CAGE,OAAO9E,CAAP,EAAU;EACV8E,WAAKG,SAAL,GAAiBtD,SAAjB;EACD;EACF,GARD;;EAUA;;;;;;EAMA,MAAMuD,mBAAmB,SAAnBA,gBAAmB,CAAUC,IAAV,EAAgBL,IAAhB,EAAsB;EAC7C,QAAI;EACF1I,gBAAUgE,UAAUI,OAApB,EAA6B;EAC3B4E,mBAAWN,KAAKO,gBAAL,CAAsBF,IAAtB,CADgB;EAE3BG,cAAMR;EAFqB,OAA7B;EAID,KALD,CAKE,OAAO9E,CAAP,EAAU;EACV5D,gBAAUgE,UAAUI,OAApB,EAA6B;EAC3B4E,mBAAW,IADgB;EAE3BE,cAAMR;EAFqB,OAA7B;EAID;;EAEDA,SAAKS,eAAL,CAAqBJ,IAArB;EACD,GAdD;;EAgBA;;;;;;EAMA,MAAMK,gBAAgB,SAAhBA,aAAgB,CAAUC,KAAV,EAAiB;EACrC;EACA,QAAIC,YAAJ;EACA,QAAIC,0BAAJ;;EAEA,QAAItC,UAAJ,EAAgB;EACdoC,cAAQ,sBAAsBA,KAA9B;EACD,KAFD,MAEO;EACL;EACA,UAAMG,UAAUjJ,YAAY8I,KAAZ,EAAmB,aAAnB,CAAhB;EACAE,0BAAoBC,WAAWA,QAAQ,CAAR,CAA/B;EACD;;EAED,QAAMC,eAAenE,qBACjBA,mBAAmB3B,UAAnB,CAA8B0F,KAA9B,CADiB,GAEjBA,KAFJ;EAGA;EACA,QAAI;EACFC,YAAM,IAAIrE,SAAJ,GAAgByE,eAAhB,CAAgCD,YAAhC,EAA8C,WAA9C,CAAN;EACD,KAFD,CAEE,OAAO7F,CAAP,EAAU;;EAEZ;EACA,QAAIY,WAAJ,EAAiB;EACfjD,eAASiF,WAAT,EAAsB,CAAC,OAAD,CAAtB;EACD;;EAED;EACA,QAAI,CAAC8C,GAAD,IAAQ,CAACA,IAAIK,eAAjB,EAAkC;EAChCL,YAAM7D,eAAeM,kBAAf,CAAkC,EAAlC,CAAN;EADgC,iBAEfuD,GAFe;EAAA,UAExBM,IAFwB,QAExBA,IAFwB;;EAGhCA,WAAKjB,UAAL,CAAgBC,WAAhB,CAA4BgB,KAAKjB,UAAL,CAAgBkB,iBAA5C;EACAD,WAAKf,SAAL,GAAiBY,YAAjB;EACD;;EAED,QAAIJ,SAASE,iBAAb,EAAgC;EAC9BD,UAAIM,IAAJ,CAASE,YAAT,CACE3G,SAAS4G,cAAT,CAAwBR,iBAAxB,CADF,EAEED,IAAIM,IAAJ,CAASI,UAAT,CAAoB,CAApB,KAA0B,IAF5B;EAID;;EAED;EACA,WAAOrE,qBAAqBsE,IAArB,CAA0BX,GAA1B,EAA+BvC,iBAAiB,MAAjB,GAA0B,MAAzD,EAAiE,CAAjE,CAAP;EACD,GA3CD;;EA6CA;EACA,MAAI/C,UAAUM,WAAd,EAA2B;EACzB,KAAC,YAAY;EACX,UAAI;EACF,YAAMgF,MAAMF,cAAc,sCAAd,CAAZ;EACA,YAAItI,WAAW,UAAX,EAAuBwI,IAAIY,aAAJ,CAAkB,OAAlB,EAA2BC,SAAlD,CAAJ,EAAkE;EAChE3F,wBAAc,IAAd;EACD;EACF,OALD,CAKE,OAAOZ,CAAP,EAAU;EACb,KAPD;EAQD;;EAED;;;;;;EAMA,MAAMwG,kBAAkB,SAAlBA,eAAkB,CAAUnG,IAAV,EAAgB;EACtC,WAAOyB,mBAAmBuE,IAAnB,CACLhG,KAAKoB,aAAL,IAAsBpB,IADjB,EAELA,IAFK,EAGLW,WAAWyF,YAAX,GAA0BzF,WAAW0F,YAArC,GAAoD1F,WAAW2F,SAH1D,EAIL,YAAM;EACJ,aAAO3F,WAAW4F,aAAlB;EACD,KANI,EAOL,KAPK,CAAP;EASD,GAVD;;EAYA;;;;;;EAMA,MAAMC,eAAe,SAAfA,YAAe,CAAUC,GAAV,EAAe;EAClC,QAAIA,eAAe3F,IAAf,IAAuB2F,eAAe1F,OAA1C,EAAmD;EACjD,aAAO,KAAP;EACD;;EAED,QACE,OAAO0F,IAAIC,QAAX,KAAwB,QAAxB,IACA,OAAOD,IAAIE,WAAX,KAA2B,QAD3B,IAEA,OAAOF,IAAI9B,WAAX,KAA2B,UAF3B,IAGA,EAAE8B,IAAIG,UAAJ,YAA0BhG,YAA5B,CAHA,IAIA,OAAO6F,IAAIvB,eAAX,KAA+B,UAJ/B,IAKA,OAAOuB,IAAII,YAAX,KAA4B,UAL5B,IAMA,OAAOJ,IAAIK,YAAX,KAA4B,QAP9B,EAQE;EACA,aAAO,IAAP;EACD;;EAED,WAAO,KAAP;EACD,GAlBD;;EAoBA;;;;;;EAMA,MAAMC,UAAU,SAAVA,OAAU,CAAUjJ,MAAV,EAAkB;EAChC,WAAO,QAAO4C,IAAP,yCAAOA,IAAP,OAAgB,QAAhB,GACH5C,kBAAkB4C,IADf,GAEH5C,UACE,QAAOA,MAAP,yCAAOA,MAAP,OAAkB,QADpB,IAEE,OAAOA,OAAOsC,QAAd,KAA2B,QAF7B,IAGE,OAAOtC,OAAO4I,QAAd,KAA2B,QALjC;EAMD,GAPD;;EASA;;;;;;;;EAQA,MAAMM,eAAe,SAAfA,YAAe,CAAUC,UAAV,EAAsBC,WAAtB,EAAmCC,IAAnC,EAAyC;EAC5D,QAAI,CAACtF,MAAMoF,UAAN,CAAL,EAAwB;EACtB;EACD;;EAED7L,iBAAayG,MAAMoF,UAAN,CAAb,EAAgC,UAACG,IAAD,EAAU;EACxCA,WAAKpB,IAAL,CAAUjG,SAAV,EAAqBmH,WAArB,EAAkCC,IAAlC,EAAwCtD,MAAxC;EACD,KAFD;EAGD,GARD;;EAUA;;;;;;;;;;EAUA;EACA,MAAMwD,oBAAoB,SAApBA,iBAAoB,CAAUH,WAAV,EAAuB;EAC/C,QAAI/F,gBAAJ;;EAEA;EACA6F,iBAAa,wBAAb,EAAuCE,WAAvC,EAAoD,IAApD;;EAEA;EACA,QAAIV,aAAaU,WAAb,CAAJ,EAA+B;EAC7B1C,mBAAa0C,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QAAMI,UAAUnL,kBAAkB+K,YAAYR,QAA9B,CAAhB;;EAEA;EACAM,iBAAa,qBAAb,EAAoCE,WAApC,EAAiD;EAC/CI,sBAD+C;EAE/CC,mBAAatF;EAFkC,KAAjD;;EAKA;EACA,QACE,CAACqF,YAAY,KAAZ,IAAqBA,YAAY,MAAlC,KACAJ,YAAYM,gBAAZ,CAA6B,OAA7B,EAAsC9J,MAAtC,KAAiD,CAFnD,EAGE;EACA8G,mBAAa0C,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QAAI,CAACjF,aAAaqF,OAAb,CAAD,IAA0B/E,YAAY+E,OAAZ,CAA9B,EAAoD;EAClD;EACA,UACEjE,gBACA,CAACG,gBAAgB8D,OAAhB,CADD,IAEA,OAAOJ,YAAYO,kBAAnB,KAA0C,UAH5C,EAIE;EACA,YAAI;EACF,cAAMC,eAAeR,YAAYhB,SAAjC;EACAgB,sBAAYO,kBAAZ,CACE,UADF,EAEEpG,qBACIA,mBAAmB3B,UAAnB,CAA8BgI,YAA9B,CADJ,GAEIA,YAJN;EAMD,SARD,CAQE,OAAO/H,CAAP,EAAU;EACb;;EAED6E,mBAAa0C,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QACEI,YAAY,UAAZ,IACAzK,WAAW,cAAX,EAA2BqK,YAAYhB,SAAvC,CAFF,EAGE;EACA1B,mBAAa0C,WAAb;EACA,aAAO,IAAP;EACD;;EAED,QACEI,YAAY,SAAZ,IACAzK,WAAW,aAAX,EAA0BqK,YAAYhB,SAAtC,CAFF,EAGE;EACA1B,mBAAa0C,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QACEtE,mBACA,CAACsE,YAAYtB,iBADb,KAEC,CAACsB,YAAY/F,OAAb,IAAwB,CAAC+F,YAAY/F,OAAZ,CAAoByE,iBAF9C,KAGA/I,WAAW,IAAX,EAAiBqK,YAAYP,WAA7B,CAJF,EAKE;EACA5K,gBAAUgE,UAAUI,OAApB,EAA6B,EAAExC,SAASuJ,YAAYS,SAAZ,EAAX,EAA7B;EACA,UAAIT,YAAYhB,SAAhB,EAA2B;EACzBgB,oBAAYhB,SAAZ,GAAwB1J,cACtB0K,YAAYhB,SADU,EAEtB,IAFsB,EAGtB,MAHsB,CAAxB;EAKD,OAND,MAMO;EACLgB,oBAAYhB,SAAZ,GAAwB1J,cACtB0K,YAAYP,WADU,EAEtB,IAFsB,EAGtB,MAHsB,CAAxB;EAKD;EACF;;EAED;EACA,QAAI9D,sBAAsBqE,YAAY9G,QAAZ,KAAyB,CAAnD,EAAsD;EACpD;EACAe,gBAAU+F,YAAYP,WAAtB;EACAxF,gBAAU3E,cAAc2E,OAAd,EAAuB5C,gBAAvB,EAAsC,GAAtC,CAAV;EACA4C,gBAAU3E,cAAc2E,OAAd,EAAuB3C,WAAvB,EAAiC,GAAjC,CAAV;EACA,UAAI0I,YAAYP,WAAZ,KAA4BxF,OAAhC,EAAyC;EACvCpF,kBAAUgE,UAAUI,OAApB,EAA6B,EAAExC,SAASuJ,YAAYS,SAAZ,EAAX,EAA7B;EACAT,oBAAYP,WAAZ,GAA0BxF,OAA1B;EACD;EACF;;EAED;EACA6F,iBAAa,uBAAb,EAAsCE,WAAtC,EAAmD,IAAnD;;EAEA,WAAO,KAAP;EACD,GA7GD;;EA+GA;;;;;;;;EAQA;EACA,MAAMU,oBAAoB,SAApBA,iBAAoB,CAAUC,KAAV,EAAiBC,MAAjB,EAAyBC,KAAzB,EAAgC;EACxD;EACA,QACE3E,iBACC0E,WAAW,IAAX,IAAmBA,WAAW,MAD/B,MAECC,SAAS7I,QAAT,IAAqB6I,SAASjE,WAF/B,CADF,EAIE;EACA,aAAO,KAAP;EACD;;EAED;;;;EAIA,QAAIpB,mBAAmB7F,WAAW4B,YAAX,EAAsBqJ,MAAtB,CAAvB,EAAsD,CAAtD,MAEO,IAAIrF,mBAAmB5F,WAAW6B,YAAX,EAAsBoJ,MAAtB,CAAvB,EAAsD,CAAtD,MAGA,IAAI,CAAC1F,aAAa0F,MAAb,CAAD,IAAyBtF,YAAYsF,MAAZ,CAA7B,EAAkD;EACvD,aAAO,KAAP;;EAEA;EACD,KAJM,MAIA,IAAInE,oBAAoBmE,MAApB,CAAJ,EAAiC,CAAjC,MAIA,IACLjL,WAAW8B,iBAAX,EAA2BnC,cAAcuL,KAAd,EAAqBlJ,kBAArB,EAAsC,EAAtC,CAA3B,CADK,EAEL,CAFK,MAMA,IACL,CAACiJ,WAAW,KAAX,IAAoBA,WAAW,YAA/B,IAA+CA,WAAW,MAA3D,KACAD,UAAU,QADV,IAEAnL,cAAcqL,KAAd,EAAqB,OAArB,MAAkC,CAFlC,IAGAtE,cAAcoE,KAAd,CAJK,EAKL,CALK,MAUA,IACLlF,2BACA,CAAC9F,WAAW+B,oBAAX,EAA8BpC,cAAcuL,KAAd,EAAqBlJ,kBAArB,EAAsC,EAAtC,CAA9B,CAFI,EAGL,CAHK,MAOA,IAAI,CAACkJ,KAAL,EAAY,CAAZ,MAGA;EACL,aAAO,KAAP;EACD;;EAED,WAAO,IAAP;EACD,GA1DD;;EA4DA;;;;;;;;;;EAUA;EACA,MAAMC,sBAAsB,SAAtBA,mBAAsB,CAAUd,WAAV,EAAuB;EACjD,QAAIe,aAAJ;EACA,QAAIF,cAAJ;EACA,QAAID,eAAJ;EACA,QAAII,eAAJ;EACA,QAAIzK,UAAJ;EACA;EACAuJ,iBAAa,0BAAb,EAAyCE,WAAzC,EAAsD,IAAtD;;EAPiD,QAS3CN,UAT2C,GAS5BM,WAT4B,CAS3CN,UAT2C;;EAWjD;;EACA,QAAI,CAACA,UAAL,EAAiB;EACf;EACD;;EAED,QAAMuB,YAAY;EAChBC,gBAAU,EADM;EAEhBC,iBAAW,EAFK;EAGhBC,gBAAU,IAHM;EAIhBC,yBAAmBnG;EAJH,KAAlB;EAMA3E,QAAImJ,WAAWlJ,MAAf;;EAEA;EACA,WAAOD,GAAP,EAAY;EACVwK,aAAOrB,WAAWnJ,CAAX,CAAP;EADU,kBAEqBwK,IAFrB;EAAA,UAEFnD,IAFE,SAEFA,IAFE;EAAA,UAEIgC,YAFJ,SAEIA,YAFJ;;EAGViB,cAAQpL,WAAWsL,KAAKF,KAAhB,CAAR;EACAD,eAAS3L,kBAAkB2I,IAAlB,CAAT;;EAEA;EACAqD,gBAAUC,QAAV,GAAqBN,MAArB;EACAK,gBAAUE,SAAV,GAAsBN,KAAtB;EACAI,gBAAUG,QAAV,GAAqB,IAArB;EACAH,gBAAUK,aAAV,GAA0BC,SAA1B,CAVU;EAWVzB,mBAAa,uBAAb,EAAsCE,WAAtC,EAAmDiB,SAAnD;EACAJ,cAAQI,UAAUE,SAAlB;EACA;EACA,UAAIF,UAAUK,aAAd,EAA6B;EAC3B;EACD;;EAED;EACA;EACA;EACA;EACA,UACEV,WAAW,MAAX,IACAZ,YAAYR,QAAZ,KAAyB,KADzB,IAEAE,WAAW8B,EAHb,EAIE;EACAR,iBAAStB,WAAW8B,EAApB;EACA9B,qBAAa3K,WAAW2K,UAAX,EAAuB,EAAvB,CAAb;EACA/B,yBAAiB,IAAjB,EAAuBqC,WAAvB;EACArC,yBAAiBC,IAAjB,EAAuBoC,WAAvB;EACA,YAAIzL,aAAamL,UAAb,EAAyBsB,MAAzB,IAAmCzK,CAAvC,EAA0C;EACxCyJ,sBAAYL,YAAZ,CAAyB,IAAzB,EAA+BqB,OAAOH,KAAtC;EACD;EACF,OAZD,MAYO;EACL;EACA;EACAb,kBAAYR,QAAZ,KAAyB,OAAzB,IACAoB,WAAW,MADX,IAEAC,UAAU,MAFV,IAGAI,UAAUG,QAHV,KAIClG,aAAa0F,MAAb,KAAwB,CAACtF,YAAYsF,MAAZ,CAJ1B,CAHK,EAQL;EACA;EACD,OAVM,MAUA;EACL;EACA;EACA;EACA,YAAIhD,SAAS,IAAb,EAAmB;EACjBoC,sBAAYL,YAAZ,CAAyB/B,IAAzB,EAA+B,EAA/B;EACD;;EAEDD,yBAAiBC,IAAjB,EAAuBoC,WAAvB;EACD;;EAED;EACA,UAAI,CAACiB,UAAUG,QAAf,EAAyB;EACvB;EACD;;EAED;EACA,UAAI1F,mBAAmB/F,WAAW,MAAX,EAAmBkL,KAAnB,CAAvB,EAAkD;EAChDlD,yBAAiBC,IAAjB,EAAuBoC,WAAvB;EACA;EACD;;EAED;EACA,UACErK,WAAW,WAAX,EAAwBqK,YAAYJ,YAApC,KACAjK,WACEG,aACE,QAAQrB,UAAUnB,WAAWgJ,eAAX,CAAV,EAAuC,GAAvC,CAAR,GAAsD,GADxD,EAEE,GAFF,CADF,EAKEuE,KALF,CAFF,EASE;EACAlD,yBAAiBC,IAAjB,EAAuBoC,WAAvB;EACA;EACD;;EAED;EACA,UAAIrE,kBAAJ,EAAwB;EACtBkF,gBAAQvL,cAAcuL,KAAd,EAAqBxJ,gBAArB,EAAoC,GAApC,CAAR;EACAwJ,gBAAQvL,cAAcuL,KAAd,EAAqBvJ,WAArB,EAA+B,GAA/B,CAAR;EACD;;EAED;EACA,UAAMqJ,QAAQX,YAAYR,QAAZ,CAAqBrK,WAArB,EAAd;EACA,UAAI,CAACuL,kBAAkBC,KAAlB,EAAyBC,MAAzB,EAAiCC,KAAjC,CAAL,EAA8C;EAC5C;EACD;;EAED;EACA,UAAI;EACF,YAAIjB,YAAJ,EAAkB;EAChBI,sBAAYyB,cAAZ,CAA2B7B,YAA3B,EAAyChC,IAAzC,EAA+CiD,KAA/C;EACD,SAFD,MAEO;EACL;EACAb,sBAAYL,YAAZ,CAAyB/B,IAAzB,EAA+BiD,KAA/B;EACD;;EAEDlM,iBAASkE,UAAUI,OAAnB;EACD,OATD,CASE,OAAOR,CAAP,EAAU;EACb;;EAED;EACAqH,iBAAa,yBAAb,EAAwCE,WAAxC,EAAqD,IAArD;EACD,GArID;;EAuIA;;;;;EAKA,MAAM0B,qBAAqB,SAArBA,kBAAqB,CAAUC,QAAV,EAAoB;EAC7C,QAAIC,mBAAJ;EACA,QAAMC,iBAAiB5C,gBAAgB0C,QAAhB,CAAvB;;EAEA;EACA7B,iBAAa,yBAAb,EAAwC6B,QAAxC,EAAkD,IAAlD;;EAEA,WAAQC,aAAaC,eAAeC,QAAf,EAArB,EAAiD;EAC/C;EACAhC,mBAAa,wBAAb,EAAuC8B,UAAvC,EAAmD,IAAnD;;EAEA;EACA,UAAIzB,kBAAkByB,UAAlB,CAAJ,EAAmC;EACjC;EACD;;EAED;EACA,UAAIA,WAAW3H,OAAX,YAA8BX,gBAAlC,EAAoD;EAClDoI,2BAAmBE,WAAW3H,OAA9B;EACD;;EAED;EACA6G,0BAAoBc,UAApB;EACD;;EAED;EACA9B,iBAAa,wBAAb,EAAuC6B,QAAvC,EAAiD,IAAjD;EACD,GA3BD;;EA6BA;;;;;;;EAOA;EACA9I,YAAUkJ,QAAV,GAAqB,UAAU7D,KAAV,EAAiBpB,GAAjB,EAAsB;EACzC,QAAI2B,aAAJ;EACA,QAAIuD,qBAAJ;EACA,QAAIhC,oBAAJ;EACA,QAAIiC,gBAAJ;EACA,QAAIC,mBAAJ;EACA;;;EAGA,QAAI,CAAChE,KAAL,EAAY;EACVA,cAAQ,OAAR;EACD;;EAED;EACA,QAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAAC2B,QAAQ3B,KAAR,CAAlC,EAAkD;EAChD;EACA,UAAI,OAAOA,MAAMiE,QAAb,KAA0B,UAA9B,EAA0C;EACxC,cAAMnM,gBAAgB,4BAAhB,CAAN;EACD,OAFD,MAEO;EACLkI,gBAAQA,MAAMiE,QAAN,EAAR;EACA,YAAI,OAAOjE,KAAP,KAAiB,QAArB,EAA+B;EAC7B,gBAAMlI,gBAAgB,iCAAhB,CAAN;EACD;EACF;EACF;;EAED;EACA,QAAI,CAAC6C,UAAUM,WAAf,EAA4B;EAC1B,UACE,QAAOtB,OAAOuK,YAAd,MAA+B,QAA/B,IACA,OAAOvK,OAAOuK,YAAd,KAA+B,UAFjC,EAGE;EACA,YAAI,OAAOlE,KAAP,KAAiB,QAArB,EAA+B;EAC7B,iBAAOrG,OAAOuK,YAAP,CAAoBlE,KAApB,CAAP;EACD;;EAED,YAAI2B,QAAQ3B,KAAR,CAAJ,EAAoB;EAClB,iBAAOrG,OAAOuK,YAAP,CAAoBlE,MAAMR,SAA1B,CAAP;EACD;EACF;;EAED,aAAOQ,KAAP;EACD;;EAED;EACA,QAAI,CAACrC,UAAL,EAAiB;EACfgB,mBAAaC,GAAb;EACD;;EAED;EACAjE,cAAUI,OAAV,GAAoB,EAApB;;EAEA;EACA,QAAI,OAAOiF,KAAP,KAAiB,QAArB,EAA+B;EAC7B9B,iBAAW,KAAX;EACD;;EAED,QAAIA,QAAJ,EAAc,CAAd,MAEO,IAAI8B,iBAAiB1E,IAArB,EAA2B;EAChC;;EAEAiF,aAAOR,cAAc,OAAd,CAAP;EACA+D,qBAAevD,KAAKvE,aAAL,CAAmBQ,UAAnB,CAA8BwD,KAA9B,EAAqC,IAArC,CAAf;EACA,UAAI8D,aAAa9I,QAAb,KAA0B,CAA1B,IAA+B8I,aAAaxC,QAAb,KAA0B,MAA7D,EAAqE;EACnE;EACAf,eAAOuD,YAAP;EACD,OAHD,MAGO,IAAIA,aAAaxC,QAAb,KAA0B,MAA9B,EAAsC;EAC3Cf,eAAOuD,YAAP;EACD,OAFM,MAEA;EACL;EACAvD,aAAK4D,WAAL,CAAiBL,YAAjB;EACD;EACF,KAdM,MAcA;EACL;EACA,UACE,CAACjG,UAAD,IACA,CAACJ,kBADD,IAEA,CAACC,cAFD;EAGA;EACAsC,YAAM1J,OAAN,CAAc,GAAd,MAAuB,CAAC,CAL1B,EAME;EACA,eAAO2F,sBAAsBE,mBAAtB,GACHF,mBAAmB3B,UAAnB,CAA8B0F,KAA9B,CADG,GAEHA,KAFJ;EAGD;;EAED;EACAO,aAAOR,cAAcC,KAAd,CAAP;;EAEA;EACA,UAAI,CAACO,IAAL,EAAW;EACT,eAAO1C,aAAa,IAAb,GAAoB3B,SAA3B;EACD;EACF;;EAED;EACA,QAAIqE,QAAQ3C,UAAZ,EAAwB;EACtBwB,mBAAamB,KAAK6D,UAAlB;EACD;;EAED;EACA,QAAMC,eAAetD,gBAAgB7C,WAAW8B,KAAX,GAAmBO,IAAnC,CAArB;;EAEA;EACA,WAAQuB,cAAcuC,aAAaT,QAAb,EAAtB,EAAgD;EAC9C;EACA,UAAI9B,YAAY9G,QAAZ,KAAyB,CAAzB,IAA8B8G,gBAAgBiC,OAAlD,EAA2D;EACzD;EACD;;EAED;EACA,UAAI9B,kBAAkBH,WAAlB,CAAJ,EAAoC;EAClC;EACD;;EAED;EACA,UAAIA,YAAY/F,OAAZ,YAA+BX,gBAAnC,EAAqD;EACnDoI,2BAAmB1B,YAAY/F,OAA/B;EACD;;EAED;EACA6G,0BAAoBd,WAApB;;EAEAiC,gBAAUjC,WAAV;EACD;;EAEDiC,cAAU,IAAV;;EAEA;EACA,QAAI7F,QAAJ,EAAc;EACZ,aAAO8B,KAAP;EACD;;EAED;EACA,QAAInC,UAAJ,EAAgB;EACd,UAAIC,mBAAJ,EAAyB;EACvBkG,qBAAazH,uBAAuBqE,IAAvB,CAA4BL,KAAKvE,aAAjC,CAAb;;EAEA,eAAOuE,KAAK6D,UAAZ,EAAwB;EACtB;EACAJ,qBAAWG,WAAX,CAAuB5D,KAAK6D,UAA5B;EACD;EACF,OAPD,MAOO;EACLJ,qBAAazD,IAAb;EACD;;EAED,UAAIxC,iBAAJ,EAAuB;EACrB;;;;;;;EAOAiG,qBAAaxH,WAAWoE,IAAX,CAAgB1F,gBAAhB,EAAkC8I,UAAlC,EAA8C,IAA9C,CAAb;EACD;;EAED,aAAOA,UAAP;EACD;;EAED,QAAIM,iBAAiB5G,iBAAiB6C,KAAKf,SAAtB,GAAkCe,KAAKO,SAA5D;;EAEA;EACA,QAAIrD,kBAAJ,EAAwB;EACtB6G,uBAAiBlN,cAAckN,cAAd,EAA8BnL,gBAA9B,EAA6C,GAA7C,CAAjB;EACAmL,uBAAiBlN,cAAckN,cAAd,EAA8BlL,WAA9B,EAAwC,GAAxC,CAAjB;EACD;;EAED,WAAO6C,sBAAsBE,mBAAtB,GACHF,mBAAmB3B,UAAnB,CAA8BgK,cAA9B,CADG,GAEHA,cAFJ;EAGD,GA5KD;;EA8KA;;;;;;EAMA3J,YAAU4J,SAAV,GAAsB,UAAU3F,GAAV,EAAe;EACnCD,iBAAaC,GAAb;EACAjB,iBAAa,IAAb;EACD,GAHD;;EAKA;;;;;EAKAhD,YAAU6J,WAAV,GAAwB,YAAY;EAClC/F,aAAS,IAAT;EACAd,iBAAa,KAAb;EACD,GAHD;;EAKA;;;;;;;;;;EAUAhD,YAAU8J,gBAAV,GAA6B,UAAUC,GAAV,EAAe7B,IAAf,EAAqBF,KAArB,EAA4B;EACvD;EACA,QAAI,CAAClE,MAAL,EAAa;EACXE,mBAAa,EAAb;EACD;;EAED,QAAM8D,QAAQ1L,kBAAkB2N,GAAlB,CAAd;EACA,QAAMhC,SAAS3L,kBAAkB8L,IAAlB,CAAf;EACA,WAAOL,kBAAkBC,KAAlB,EAAyBC,MAAzB,EAAiCC,KAAjC,CAAP;EACD,GATD;;EAWA;;;;;;;EAOAhI,YAAUgK,OAAV,GAAoB,UAAU9C,UAAV,EAAsB+C,YAAtB,EAAoC;EACtD,QAAI,OAAOA,YAAP,KAAwB,UAA5B,EAAwC;EACtC;EACD;;EAEDnI,UAAMoF,UAAN,IAAoBpF,MAAMoF,UAAN,KAAqB,EAAzC;EACAlL,cAAU8F,MAAMoF,UAAN,CAAV,EAA6B+C,YAA7B;EACD,GAPD;;EASA;;;;;;;EAOAjK,YAAUkK,UAAV,GAAuB,UAAUhD,UAAV,EAAsB;EAC3C,QAAIpF,MAAMoF,UAAN,CAAJ,EAAuB;EACrBpL,eAASgG,MAAMoF,UAAN,CAAT;EACD;EACF,GAJD;;EAMA;;;;;;EAMAlH,YAAUmK,WAAV,GAAwB,UAAUjD,UAAV,EAAsB;EAC5C,QAAIpF,MAAMoF,UAAN,CAAJ,EAAuB;EACrBpF,YAAMoF,UAAN,IAAoB,EAApB;EACD;EACF,GAJD;;EAMA;;;;;EAKAlH,YAAUoK,cAAV,GAA2B,YAAY;EACrCtI,YAAQ,EAAR;EACD,GAFD;;EAIA,SAAO9B,SAAP;EACD;;AAED,eAAeD,iBAAf;;;;;;;;"}