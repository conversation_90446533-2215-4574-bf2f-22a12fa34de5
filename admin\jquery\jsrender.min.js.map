{"version": 3, "sources": ["jsrender.js"], "names": ["factory", "global", "$", "j<PERSON><PERSON><PERSON>", "exports", "module", "fn", "define", "amd", "getDerivedMethod", "baseMethod", "method", "ret", "tag", "this", "prevBase", "base", "apply", "arguments", "getMethod", "$isFunction", "_d", "noop", "tagHandlersFromProps", "tagCtx", "prop", "props", "rHasHandlers", "test", "fix", "constructor", "prototype", "retVal", "val", "dbgBreak", "console", "log", "e", "baseApply", "JsViewsError", "message", "name", "link", "$extend", "target", "source", "$viewsDelimiters", "openChars", "closeC<PERSON><PERSON>", "$isArray", "$views", "linkChar", "error", "delimOpenChar0", "delimOpenChar1", "delimCloseChar0", "delimCloseChar1", "$subSettings", "delimiters", "rTag", "$sub", "RegExp", "rTmpl", "$viewsSettings", "get<PERSON>iew", "inner", "type", "undefined", "views", "i", "l", "found", "view", "root", "_", "useKey", "get", "length", "parent", "getNestedIndex", "index", "getIndex", "getPathObject", "ob", "path", "ltOb", "prevOb", "tokens", "split", "lt", "contextParameter", "key", "value", "wrapped", "deps", "res", "obsCtxPrm", "tagElse", "callView", "newRes", "storeView", "isUpdate", "isRenderCall", "store", "ctx", "hasOwnProperty", "$helpers", "linked", "_cxp", "scope", "isTop", "_ocps", "_ocp", "_vw", "_key", "ind", "updateValue", "observable", "setProperty", "_ceo", "unshift", "cvtArgs", "data", "_ucp", "getTemplate", "tmpl", "getRsc", "$templates", "convertVal", "converter", "onError", "linkCtx", "argsLen", "bindTo", "boundTag", "bnds", "_lr", "args", "_bd", "_lc", "_tg", "bnd", "unlinked", "inline", "tagName", "convert", "onArrayChange", "flow", "tagCtxs", "_is", "extendCtx", "_er", "onRender", "convertArgs", "bound", "boundArgs", "bindFrom", "slice", "argOrProp", "arg0", "context", "convertBoundArgs", "getResource", "resourceType", "itemName", "renderTag", "parentView", "bindToOrBindFrom", "bind<PERSON><PERSON>y", "m", "isNaN", "parseInt", "topView", "tagDef", "template", "tags", "attr", "parentTag", "n", "itemRet", "tagCtxCtx", "ctxPrm", "initVal", "content", "callInit", "mapDef", "thisMap", "bd<PERSON>rgs", "tagDataMap", "contentCtx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "linkedElement", "defaultCtx", "parentTmpl", "lateR<PERSON>", "params", "tmpls", "render", "renderContent", "bndArgs", "_getTmpl", "init", "_ctr", "ths", "arrVws", "dataBoundOnly", "dataMap", "map", "parents", "parentTags", "rendering", "rndr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "linkedCtxParam", "fromIndex", "toIndex", "_dfAt", "noVws", "HTML", "_cp", "cvt", "src", "unmap", "tgt", "rWrappedInViewMarker", "links", "renderWithViews", "$converters", "html", "View", "contentTmpl", "parentView_", "self_", "self", "isArray", "id", "viewId", "cache", "_ct", "_cchCt", "scp", "indexStr", "push", "splice", "compileChildResources", "storeName", "storeNames", "resources", "jsvStores", "compileTag", "Tag", "baseTag", "compiledDef", "depends", "$tags", "_parentTmpl", "compileTmpl", "options", "lookupTemplate", "currentName", "nodeType", "elem", "document", "getElementById", "char<PERSON>t", "innerHTML", "getAttribute", "tmplAttr", "jsvTmpl", "tmplName", "setAttribute", "compiledTmpl", "tmplOrMarkup", "_html", "markup", "tmplObject", "tmplFn", "replace", "rEscapeQuotes", "getDefaultVal", "defaultVal", "call", "addParentRef", "ref", "Object", "defineProperty", "configurable", "compileViewModel", "JsvVm", "vm", "iterate", "action", "getterType", "parentRef", "j", "getterCount", "getters", "getter", "viewModels", "JSON", "parse", "childOb", "arr", "merge", "viewModel", "$expando", "getterNames", "mod", "assigned", "newModArr", "k", "model", "$observable", "refresh", "concat", "unmapArray", "modelArr", "extend", "proto", "cnstr", "privField", "set", "Function", "htmlTag", "wrapMap", "$subSettingsAdvanced", "_wm", "rFirstElem", "exec", "toLowerCase", "div", "trim", "registerStore", "storeSettings", "theStore", "item", "compile", "thisStore", "onStore", "OBJECT", "getTgt", "addSetting", "st", "Map", "baseMap", "noIteration", "isTopRenderCall", "prevData", "prevIndex", "result", "useViews", "newView", "<PERSON><PERSON><PERSON><PERSON>", "itemResult", "swapContent", "outerOnRender", "itemVar", "newCtx", "noLinking", "_wrp", "nl", "syntaxError", "helpers", "contentView", "onRenderError", "fallback", "Err", "isLinkExpr", "convertBack", "<PERSON><PERSON><PERSON><PERSON>", "pushpre<PERSON><PERSON><PERSON>nt", "shift", "loc", "substr", "rNewLine", "blockTagCheck", "block", "parseTag", "all", "bind", "colon", "codeTag", "slash", "bind2", "closeBlock", "late", "openTagName", "isLateOb", "pathBindings", "ctxProps", "paramsArgs", "paramsProps", "paramsCtxProps", "useTrigger", "allowCode", "rUnescapeQuotes", "rTestElseIf", "current", "substring", "stack", "pop", "parseParams", "rBuildHash", "onerror", "isCtxPrm", "keyToken", "keyValue", "arg", "param", "hasHandlers", "newNode", "parsed<PERSON><PERSON><PERSON>", "bindings", "astTop", "buildCode", "setPaths", "pathsArr", "paths", "skp", "paramStructure", "paramCode", "paramVals", "parseTokens", "lftPrn0", "lftPrn", "operator", "err", "eq", "path2", "prn", "comma", "lftPrn2", "apos", "quot", "rtPrn", "rtPrnDot", "prn2", "space", "full", "parsePath", "allPath", "not", "object", "helper", "viewProperty", "pathTokens", "leafToken", "subPath", "binds", "named", "bindto", "_jsvto", "bndCtx", "bd", "theOb", "_cpfn", "sb", "prm", "pathStart", "fnDp", "compiledPathStart", "compiledPath", "join", "expr", "newOb", "lftPrnFCall", "aposed", "quoted", "bndStack", "_cpPthSt", "_cp<PERSON>ey", "cpFnStore", "fnCall", "prnDp", "indexOf", "prnStack", "prnInd", "paramIndex", "boundName", "rPath", "prnStart", "0", "rBracketQuote", "rPrm", "ast", "node", "hasTag", "<PERSON><PERSON><PERSON><PERSON>", "gets<PERSON>al", "hasCnvt", "useCnvt", "tmplBindings", "boundOnErrStart", "boundOnErrEnd", "tagRender", "nestedTmpls", "nestedTmpl", "tagAndElses", "nextIsElse", "oldCode", "isElse", "isGetVal", "tagCtxFn", "tagStart", "trigger", "retStrOpen", "retStrClose", "tmplBindingKey", "templates", "converters", "code", "tmplOptions", "debug", "debugMode", "_tag", "parentContext", "getTargetProps", "propsArr", "noFunctions", "isFunction", "getTargetSorted", "mapped", "start", "end", "propParams", "filter", "sort", "directSort", "step", "reverse", "v", "a", "b", "onFilter", "sorted", "paged", "$fnRender", "tmplElem", "j<PERSON>y", "getCharEntity", "ch", "charEntities", "charCodeAt", "getCharFromEntity", "match", "token", "charsFromEntities", "htmlEncode", "text", "rIsHtml", "rHtmlEncode", "dataEncode", "rDataEncode", "dataUnencode", "rDataUnencode", "setGlobals", "jsvStoreName", "setting", "versionNumber", "rAttrEncode", "&", "<", ">", "\u0000", "'", "\"", "`", "=", "amp", "gt", "$render", "jsr", "js<PERSON><PERSON>", "jsrToJq", "jsviews", "sub", "syntaxErr", "settings", "advSet", "_thp", "_gm", "_cnvt", "_err", "_sq", "advanced", "Error", "_getOb", "getCache", "expando", "renderFile", "__express", "Array", "obj", "_jq", "jq", "_clFns", "_jsv", "if", "done", "selected", "for", "sortDataMap", "cloned", "setDataMap", "range", "include", "*", ":*", "dbg", "encode", "unencode", "url", "encodeURI", "window"], "mappings": ";;CAaC,SAASA,EAASC,GAElB,GAAIC,GAAID,EAAOE,MAEQ,iBAAZC,SACVC,OAAOD,QAAUF,EACdF,EAAQC,EAAQC,GAChB,SAASA,GACV,GAAIA,IAAMA,EAAEI,GACX,KAAM,wBAEP,OAAON,GAAQC,EAAQC,IAEG,kBAAXK,SAAyBA,OAAOC,IACjDD,OAAO,WACN,MAAOP,GAAQC,KAGhBD,EAAQC,GAAQ,IAKlB,SAASA,EAAQC,GACjB,YAsIA,SAASO,GAAiBC,EAAYC,GACrC,MAAO,YACN,GAAIC,GACHC,EAAMC,KACNC,EAAWF,EAAIG,IAKhB,OAHAH,GAAIG,KAAON,EACXE,EAAMD,EAAOM,MAAMJ,EAAKK,WACxBL,EAAIG,KAAOD,EACJH,GAIT,QAASO,GAAUT,EAAYC,GAc9B,MAXIS,IAAYT,KACfA,EAASF,EACNC,EAEEA,EAAWW,GACVX,EACAD,EAAiBa,EAAMZ,GAHxBY,EAIHX,GAEFA,EAAOU,IAAMX,GAAcA,EAAWW,IAAM,GAAK,GAE3CV,EAGR,QAASY,GAAqBV,EAAKW,GAClC,GAAIC,GACHC,EAAQF,EAAOE,KAChB,KAAKD,IAAQC,IACRC,GAAaC,KAAKH,IAAWZ,EAAIY,IAASZ,EAAIY,GAAMI,MACvDhB,EAAIY,GAAiB,YAATA,EAAqBN,EAAUN,EAAIiB,YAAYC,UAAUN,GAAOC,EAAMD,IAASC,EAAMD,IAOpG,QAASO,GAAOC,GACf,MAAOA,GAGR,QAASX,KACR,MAAO,GAGR,QAASY,GAASD,GAEjB,IAEC,KADAE,SAAQC,IAAI,4BAA8BH,GACpC,iBAEP,MAAOI,IACP,MAAOvB,MAAKE,KAAOF,KAAKwB,UAAUpB,WAAae,EAGhD,QAASM,GAAaC,GAGrB1B,KAAK2B,MAAQvC,EAAEwC,KAAO,UAAY,YAAc,SAChD5B,KAAK0B,QAAUA,GAAW1B,KAAK2B,KAGhC,QAASE,GAAQC,EAAQC,GACxB,GAAID,EAAQ,CACX,IAAK,GAAIH,KAAQI,GAChBD,EAAOH,GAAQI,EAAOJ,EAEvB,OAAOG,IA2BT,QAASE,GAAiBC,EAAWC,EAAYN,GAChD,MAAKK,GAGDE,GAASF,GACLD,EAAiB7B,MAAMiC,GAAQH,IAEvCI,GAAWT,EAAOA,EAAK,GAAKS,GACvB,cAAcvB,KAAKmB,EAAYC,EAAaG,KAChDC,EAAM,sBAEPC,GAAiBN,EAAU,GAC3BO,GAAiBP,EAAU,GAC3BQ,GAAkBP,EAAW,GAC7BQ,GAAkBR,EAAW,GAE7BS,GAAaC,YAAcL,GAAiBC,GAAgBC,GAAkBC,GAAiBL,IAG/FJ,EAAY,KAAOM,GAAiB,MAAQF,GAAW,OAASG,GAChEN,EAAa,KAAOO,GAAkB,KAAOC,GAG7CG,GAAO,uBAAyBJ,GAAkB,wCAC/CA,GAAkB,OAASA,GAAkB,QAAUC,GAAkB,QAG5EI,GAAKD,KAAO,MAAQA,GAAO,IAE3BA,GAAO,GAAIE,QAAO,MAAQd,EAAYY,GAAO,YAAcN,GAAiB,MAAQF,GAAW,OAASG,GAAiB,yCAA2CN,EAAY,KAKhLY,GAAKE,MAAQ,GAAID,QAAO,kCAAoCd,EAAY,KAAOC,GAGxEe,IAnCCN,GAAaC,WA0CtB,QAASM,GAAQC,EAAOC,GAClBA,GAAQD,KAAU,IAEtBC,EAAOD,EACPA,EAAQE,OAGT,IAAIC,GAAOC,EAAGC,EAAGC,EAChBC,EAAO1D,KACP2D,EAAgB,SAATP,CAGR,IAAID,GAIH,GADAM,EAAQL,GAAQM,EAAKN,OAASA,GAAQM,GACjCD,EAEJ,GADAH,EAAQI,EAAKJ,MACTI,EAAKE,EAAEC,QACV,IAAKN,IAAKD,GACT,GAAIG,EAAQL,EAAOE,EAAMC,GAAGO,IAAIX,EAAOC,GAAQE,EAAMC,GACpD,UAIF,KAAKA,EAAI,EAAGC,EAAIF,EAAMS,QAASN,GAASF,EAAIC,EAAGD,IAC9CE,EAAQL,EAAOE,EAAMC,GAAGO,IAAIX,EAAOC,GAAQE,EAAMC,OAI9C,IAAII,EAEVF,EAAQC,EAAKC,SACP,IAAIP,EACV,KAAOM,IAASD,GAEfA,EAAQC,EAAKN,OAASA,EAAOM,EAAOL,OACpCK,EAAOA,EAAKM,WAGbP,GAAQC,EAAKM,MAEd,OAAOP,IAASJ,OAGjB,QAASY,KACR,GAAIP,GAAO1D,KAAK8D,IAAI,OACpB,OAAOJ,GAAOA,EAAKQ,MAAQb,OAO5B,QAASc,KACR,MAAOnE,MAAKkE,MAUb,QAASE,GAAcC,EAAIC,EAAMC,EAAM/E,GAGtC,GAAIgF,GAAQC,EAAQjB,EACnBD,EAAI,CAML,IALa,IAATgB,IACH/E,EAAK,EACL+E,EAAOlB,QAGJiB,EAIH,IAHAG,EAASH,EAAKI,MAAM,KACpBlB,EAAIiB,EAAOV,OAEJM,GAAMd,EAAIC,EAAGD,IACnBiB,EAASH,EACTA,EAAKI,EAAOlB,GAAKc,EAAGI,EAAOlB,IAAMc,CAMnC,OAHIE,KACHA,EAAKI,GAAKJ,EAAKI,IAAMpB,EAAEC,GAEVH,SAAPgB,EACJ7E,EAAKgB,EAAO,GACZhB,EAAK,WACN,MAAO6E,GAAGlE,MAAMqE,EAAQpE,YACrBiE,EAGN,QAASO,GAAiBC,EAAKC,EAAOhB,GAErC,GAAIiB,GAASC,EAAMC,EAAKC,EAAWC,EAASC,EAAUC,EACrDC,EAAYtF,KACZuF,GAAYC,IAAgBpF,UAAU2D,OAAS,EAC/C0B,EAAQH,EAAUI,GACnB,IAAIb,EAAK,CAMR,GALKS,EAAU1B,IACduB,EAAUG,EAAUpB,MACpBoB,EAAYA,EAAUvF,KAEvBqF,EAAWE,EACPG,GAASA,EAAME,eAAed,KAASY,EAAQG,IAAUD,eAAed,IAE3E,GADAI,EAAMQ,EAAMZ,GACA,QAARA,GAAyB,WAARA,GAA4B,SAARA,GAA0B,eAARA,EAC1D,MAAOI,OAGRQ,GAAQpC,MAET,MAAKmC,IAAgBF,EAAU5E,QAAU4E,EAAUO,UAC7CZ,GAAQA,EAAIa,OAGhBR,EAAYA,EAAU5E,QAAUJ,GAAY2E,GACzCK,GACCA,EAAYA,EAAUS,OAAST,GAChCA,EAAUU,OAASV,EAAUI,IAAI3F,KAC9BuF,GACMjC,SAAR4B,GAAqBK,EAAU5E,SAElC4E,EAAYA,EAAU5E,OAAOgD,KAAKqC,OAEnCN,EAAQH,EAAUW,MAClBhB,EAAMQ,GAASA,EAAME,eAAed,IAAQY,EAAMZ,IAAQI,EACpDA,GAAOA,EAAIa,OAAUhC,IAAOyB,KAEhCE,IAAUH,EAAUW,MAAQX,EAAUW,YAAcpB,GAClDI,IAEDiB,KAAMjB,EACNkB,IAAKf,EACLgB,KAAMvB,IAERI,EAAIa,MACHxB,KAAM4B,GACNG,IAAK,EACLC,YAAa,SAASnF,EAAKmD,GAE1B,MADAlF,GAAEmH,WAAWtB,EAAI,IAAIuB,YAAYN,GAAM/E,GAChCnB,SAKPkF,EAAYD,GAAOA,EAAIa,MAAM,CAEhC,GAAI1F,UAAU2D,OAAS,EAKtB,MAJAiB,GAAOC,EAAI,GAAKnC,GAAK2D,KAAKxB,EAAI,GAAGD,OAASkB,IAC1ClB,EAAK0B,QAAQzB,EAAI,IACjBD,EAAKc,KAAOZ,EAELF,CAQR,IANAG,EAAUD,EAAUC,QACpBE,EAASJ,EAAI,GACVC,EAAUnF,KAAOmF,EAAUnF,IAAI4G,QAC9BzB,EAAUnF,IAAI4G,QAAQxB,EAAS,GAAGD,EAAUmB,KAC5CpB,EAAI,GAAGA,EAAI,GAAG2B,KAAM3B,EAAI,GAAInC,IAC7BmC,EAAI,GAAGiB,KACNX,EAEH,MADAzC,IAAK+D,KAAKhC,EAAKC,EAAOQ,EAAWJ,GAC1BI,CAERL,GAAMI,EAaR,MAVIJ,IAAO3E,GAAY2E,KAKtBF,EAAU,WACT,MAAOE,GAAI9E,MAAQH,MAAQA,OAASb,EAAqBa,KAAXoF,EAAiBhF,YAEhEyB,EAAQkD,EAASE,IAEXF,GAAWE,GAKpB,QAAS6B,GAAYC,GACpB,MAAOA,KAASA,EAAKvH,GAClBuH,EACA/G,KAAKgH,OAAO,YAAaD,IAASE,GAAWF,IAOjD,QAASG,GAAWC,EAAWzD,EAAMhD,EAAQ0G,GAG5C,GAAIrH,GAAKsH,EAASvC,EAAOwC,EAASC,EAEjCC,EAA6B,gBAAX9G,IAAuBgD,EAAKqD,KAAKU,KAAK/G,EAAO,EAWhE,IATgB2C,SAAZ+D,GAAyBI,GAAYA,EAASE,MACjDN,EAAU,IAEK/D,SAAZ+D,EACH1G,EAAS0G,GAAWxG,SAAW+G,MAAOP,IAC5BI,IACV9G,EAAS8G,EAAS9D,EAAKkD,KAAMlD,EAAMZ,KAEpC0E,EAAWA,EAASI,KAAOJ,EACvBL,GAAaK,EAAU,CAI1B,GAHAH,EAAU3D,EAAKmE,IACf9H,EAAMsH,GAAWA,EAAQtH,IACzBW,EAAOgD,KAAOA,GACT3D,EAAK,CAiBT,GAhBAA,EAAM8B,EAAQ,GAAIiB,IAAKgF,KACtBlE,GACCmE,IAAKP,EACLQ,UAAU,EACVrD,GAAIjE,EAAOiE,IAEZsD,QAASZ,EACTa,QAAS,IACTC,QAAShB,EACTiB,eAAe,EACfC,MAAM,EACN3H,OAAQA,EACR4H,SAAU5H,GACV6H,IAAK,QAENjB,EAAU5G,EAAOiH,KAAK5D,OAClBuD,EAAQ,EAEX,IADAC,EAASxH,EAAIwH,UACND,KACNC,EAAOb,QAAQY,EAGbD,KACHA,EAAQtH,IAAMA,EACdA,EAAIsH,QAAUA,GAEf3G,EAAOgF,IAAM8C,EAAU9H,EAAOgF,KAAM2B,EAAUA,EAAQ3D,KAAOA,GAAMgC,KACnEjF,EAAqBV,EAAKW,GAE3BX,EAAI0I,IAAMrB,GAAWtC,EACrB/E,EAAI2F,IAAMhF,EAAOgF,KAAO3F,EAAI2F,QAC5BhF,EAAOgF,IAAMrC,OACbyB,EAAQ/E,EAAI4G,UAAU,GACtB5G,EAAI0I,IAAMrB,GAAWtC,MAErBA,GAAQpE,EAAOiH,KAAK,EAOrB,OAHA7C,GAAQ0C,GAAY9D,EAAKE,EAAE8E,SACxBhF,EAAKE,EAAE8E,SAAS5D,EAAOpB,EAAM3D,GAC7B+E,EACazB,QAATyB,EAAqBA,EAAQ,GAGrC,QAAS6D,GAAYxD,EAASyD,GAC7B,GAAIpF,GAAGqB,EAAKgE,EAAWlB,EAAMmB,EAAU/I,EAAKoH,EAC3CzG,EAASV,IAEV,IAAIU,EAAOwH,SAGV,GAFAnI,EAAMW,EACNA,GAAUX,EAAIuI,UAAY5H,IAASyE,GAAS,IACvCzE,EACJ,WAGDX,GAAMW,EAAOX,GAed,IAZA+I,EAAW/I,EAAI+I,SACfnB,EAAOjH,EAAOiH,MAETR,EAAYpH,EAAIoI,UAAY,GAAKhB,IAAcA,IACnDA,EAA0B,SAAdA,EACT9D,OACC3C,EAAOgD,KAAKsD,OAAO,aAAcG,IAAc7E,EAAM,uBAAyB6E,EAAY,MAG3FA,IAAcyB,IACjBjB,EAAOA,EAAKoB,SAETD,EAAU,CAGb,IAFAD,KACArF,EAAIsF,EAAS/E,OACNP,KACNqB,EAAMiE,EAAStF,GACfqF,EAAUnC,QAAQsC,EAAUtI,EAAQmE,GAEjC+D,KACHjB,EAAOkB,GAGT,GAAI1B,EAAW,CAEd,GADAA,EAAYA,EAAUhH,MAAMJ,EAAK8I,GAAalB,GAC5BtE,SAAd8D,EACH,MAAOQ,EASR,IAPAmB,EAAWA,IAAa,GACxBtF,EAAIsF,EAAS/E,OACR5B,GAASgF,KAAeA,EAAU8B,QAAS,GAAgB,IAANzF,GAAW2D,EAAUpD,SAAWP,IAAK2D,EAAU8B,QACxG9B,GAAaA,GACb2B,GAAY,GACZtF,EAAI,GAEDoF,EACHjB,EAAOR,MAEP,MAAO3D,KACNqB,EAAMiE,EAAStF,IACVqB,IAAQA,IACZ8C,EAAK9C,GAAOsC,EAAU3D,IAK1B,MAAOmE,GAGR,QAASqB,GAAUE,EAASrE,GAE3B,MADAqE,GAAUA,GAASrE,IAAQA,EAAM,OAAS,SACnCqE,GAAWA,EAAQrE,GAG3B,QAASsE,GAAiBhE,GACzB,MAAOnF,MAAK2G,QAAQxB,EAAS,GAQ9B,QAASiE,GAAYC,EAAcC,GAClC,GAAIrE,GAAKQ,EACR/B,EAAO1D,IACR,IAAI,GAAKsJ,IAAaA,EAAU,CAC/B,KAAgBjG,SAAR4B,GAAsBvB,GAC7B+B,EAAQ/B,EAAKqD,MAAQrD,EAAKqD,KAAKsC,GAC/BpE,EAAMQ,GAASA,EAAM6D,GACrB5F,EAAOA,EAAKM,MAEb,OAAOiB,IAAO7C,GAAOiH,GAAcC,IAIrC,QAASC,GAAUrB,EAASsB,EAAYzC,EAAMuB,EAAS/C,EAAU6B,GAChE,QAASqC,GAAiBrG,GACzB,GAAIsG,GAAY3J,EAAIqD,EAEpB,IAAkBC,SAAdqG,EAGH,IAFAA,EAAYvH,GAASuH,GAAaA,GAAaA,GAC/CC,EAAID,EAAU3F,OACP4F,KACN9E,EAAM6E,EAAUC,GACXC,MAAMC,SAAShF,MACnB6E,EAAUC,GAAKE,SAAShF,GAK3B,OAAO6E,KAAc,GAGtBF,EAAaA,GAAcM,EAC3B,IAAI/J,GAAKgK,EAAQC,EAAUC,EAAMC,EAAMC,EAAW3G,EAAGmG,EAAGS,EAAGC,EAAS3J,EAAQ4J,EAAWC,EAAQhD,EAAQuB,EAAU0B,EAChHC,EAASC,EAAUC,EAAQC,EAASjD,EAAMkD,EAAQjK,EAAOkK,EAAYC,EAAYlG,EAAKmG,EAAgBC,EAAcC,EAAeC,EACnI5H,EAAI,EACJzD,EAAM,GACNuH,EAAUmC,EAAW3B,MAAO,EAC5BnC,EAAM8D,EAAW9D,IACjB0F,EAAarE,GAAQyC,EAAWzC,KAEhCS,EAA8B,gBAAZc,IAAwBkB,EAAWzC,KAAKU,KAAKa,EAAQ,EAsBxE,KApBoB,QAAhBJ,EAAQK,KACXxI,EAAMmI,EACNA,EAAUnI,EAAImI,QACdI,EAAUvI,EAAIuI,QACd0B,EAAWjK,EAAIiK,WAEfD,EAASP,EAAWxC,OAAO,OAAQkB,IAAY5F,EAAM,kBAAoB4F,EAAU,OACnF8B,EAAWD,EAAOC,UAEH3G,SAAZ+D,GAAyBI,IAAaA,EAASE,IAAOqC,EAAOsB,YAAc7D,EAASE,OAAO,GAASF,EAASE,OAChHN,EAAU,IAEK/D,SAAZ+D,GACHtH,GAAOsH,EACPkB,EAAUlB,IAAYxG,SAAW+G,QAAU2D,QAAS1K,aAC1C4G,IACVc,EAAUd,EAASgC,EAAW5C,KAAM4C,EAAY1G,KAGjDU,EAAI8E,EAAQvE,OACLR,EAAIC,EAAGD,IACb7C,EAAS4H,EAAQ/E,GACjBkH,EAAU/J,EAAOqG,OACZM,IAAYA,EAAQtH,KAAOwD,IAAM8D,EAAQtH,IAAIkI,QAAUlI,EAAI0I,KAAOgC,IAAYA,IAAUA,KAGxFA,GAAWW,EAAWG,QACzB7K,EAAOqG,KAAOrG,EAAO+J,QAAUW,EAAWG,MAAMd,EAAU,IAE3D/J,EAAOwD,MAAQX,EACf7C,EAAO6J,OAAS3F,EAChBlE,EAAO8K,OAASC,EAChB/K,EAAOiG,QAAUgC,EACjBjI,EAAOgL,QAAUvC,EACjBzI,EAAOgD,KAAO8F,EACd9I,EAAOgF,IAAM8C,EAAUA,EAAU9H,EAAOgF,IAAKqE,GAAUA,EAAOrE,KAAMA,KAEjEqB,EAAOrG,EAAOE,MAAMmG,QAEvBrG,EAAOqG,KAAOyC,EAAWmC,SAAS5E,GAClCrG,EAAO+J,QAAU/J,EAAO+J,SAAW/J,EAAOqG,MAGtChH,EA0BMsH,GAAWA,EAAQ7H,GAAGkI,MAChCgD,IAAa3K,EAAI6L,OAtBjB7L,EAAM,GAAIgK,GAAO8B,KACjBnB,IAAa3K,EAAI6L,KAEjB7L,EAAIiE,OAASmG,EAAYzE,GAAOA,EAAI3F,IACpCA,EAAIuI,QAAUA,EAEVjB,IACHtH,EAAIkI,QAAS,EACbZ,EAAQtH,IAAMA,GAEfA,EAAIsH,QAAUA,GACVtH,EAAI6D,EAAEmE,IAAMP,GAAYH,EAAQ7H,KAEnCO,EAAI6D,EAAEkI,IAAMpL,EAAO4K,OAAO1K,MAAM,QAChCb,EAAI6D,EAAEe,GAAK2D,EAAQ3D,GACnB5E,EAAI6D,EAAEmI,WACIhM,EAAIiM,eACd1J,EAAM4F,EAAU,4BAA8BA,EAAU,OAO1D4C,EAAa/K,EAAIkM,QAEjBvL,EAAOX,IAAMA,EACT+K,GAAcxC,IACjB5H,EAAOwL,IAAM5D,EAAQ/E,GAAG2I,KAEpBnM,EAAIsI,OACRiC,EAAY5J,EAAOgF,IAAMhF,EAAOgF,QAGhCuE,EAAOlK,EAAIoM,QAAU7B,EAAU8B,WAAa1G,GAAO8C,EAAU8B,EAAU8B,WAAY1G,EAAI0G,gBACnFjC,IACHF,EAAKE,EAAUjC,SAAWiC,GAG3BF,EAAKlK,EAAImI,SAAWoC,EAAUvK,IAAMA,EACpCuK,EAAU5J,OAASA,EAGrB,MAAMX,EAAI0I,IAAMrB,GAAU,CAGzB,IAFA3G,EAAqBV,EAAKuI,EAAQ,IAClCvI,EAAIsM,WAAaC,KAAMvM,EAAIsM,WACtB9I,EAAI,EAAGA,EAAIC,EAAGD,IAAK,CAKvB,GAJA7C,EAASX,EAAIW,OAAS4H,EAAQ/E,GAC9B3C,EAAQF,EAAOE,MACfb,EAAI2F,IAAMhF,EAAOgF,KAEZnC,EAAG,CAyCP,GAxCImH,IACH3K,EAAI6L,KAAKlL,EAAQ2G,EAAStH,EAAI2F,KAC9BgF,EAAWrH,QAEP3C,EAAOiH,KAAK5D,QAAUrD,EAAO6L,cAAe,GAASxM,EAAIwM,cAAe,IAC5E7L,EAAOiH,KAAOA,GAAQjH,EAAOgD,KAAKkD,MAClClG,EAAO4K,OAAO3D,MAAQ,UAGvBJ,EAASkC,EAAiB,UAEPpG,SAAftD,EAAIwH,SACPxH,EAAIwH,OAASA,GAGOlE,SAAjBtD,EAAI+I,SACP/I,EAAI+I,SAAWW,EAAiB,YACtB1J,EAAIwH,SACdxH,EAAI+I,SAAW/I,EAAIwH,OAASA,GAE7BuB,EAAW/I,EAAI+I,UAAYvB,EAE3B0D,EAAe1D,EAAOxD,OACtBiH,EAAiBlC,EAAS/E,OAEtBhE,EAAI6D,EAAEmE,MAAQmD,EAAgBnL,EAAImL,iBACrCnL,EAAImL,cAAgBA,EAAgB/I,GAAS+I,GAAiBA,GAAgBA,GAE1ED,IAAiBC,EAAcnH,QAClCzB,EAAM,6CAGJ4I,EAAgBnL,EAAIyM,kBACvBzM,EAAIyM,eAAiBtB,EAAgB/I,GAAS+I,GAAiBA,GAAgBA,GAE3EF,IAAmBE,EAAcnH,QACpCzB,EAAM,sDAIJwG,EAIH,IAHA/I,EAAI6D,EAAE6I,aACN1M,EAAI6D,EAAE8I,WACNtC,EAAIY,EACGZ,KAGN,IAFAvF,EAAMiE,EAASsB,GACfT,EAAIsB,EACGtB,KACF9E,IAAQ0C,EAAOoC,KAClB5J,EAAI6D,EAAE6I,UAAU9C,GAAKS,EACrBrK,EAAI6D,EAAE8I,QAAQtC,GAAKT,EAMnBtC,KAGHA,EAAQ6C,KAAOnK,EAAImK,KAAO7C,EAAQ6C,MAAQnK,EAAImK,MAAQ7C,EAAQsF,OAE/DzC,EAAOnK,EAAImK,KACXnK,EAAI6D,EAAEgJ,MAAQ1C,GAAQA,IAAS2C,GAGhC,GADAlF,EAAO5H,EAAI4G,QAAQpD,GACfxD,EAAIyM,eAIP,IAHA3B,EAAS9K,EAAI4G,QAAQpD,EAAG,GACxBoG,EAAIqB,EACJG,EAAapL,EAAIiB,YAAYC,UAAUyE,IAChCiE,MACFY,EAASxK,EAAIyM,eAAe7C,MAC/B9E,EAAMiE,EAASa,GACfa,EAAUK,EAAOlB,GAEjBjJ,EAAOgF,IAAI6E,GAAUzH,GAAKgK,IACzB3B,GAA0B9H,SAAZmH,EAAwBW,EAAWZ,GAASC,EAC9CnH,SAAZmH,GAAyBxB,EAAUtI,EAAO4K,OAAQzG,GAClDnE,EAAOgD,KACP3D,EAAI6D,EAAEmE,MAAQhI,IAAKA,EAAKgN,IAAKhN,EAAIoI,QAAS9B,IAAKsD,EAAGxE,QAAS5B,MAK1DoH,EAAS/J,EAAMqL,SAAWnB,KAAgBnD,EAAK5D,QAAUnD,EAAMqL,WACnErB,EAAUlK,EAAOwL,IACZtB,GAAWA,EAAQoC,MAAQrF,EAAK,KAAMpC,IACtCqF,GAAWA,EAAQoC,KACtBpC,EAAQqC,QAETtC,EAAOuB,IAAIvE,EAAK,GAAIjH,EAAQkK,GAAU7K,EAAI6D,EAAEmE,KAC5C6C,EAAUlK,EAAOwL,KAElBvE,GAAQiD,EAAQsC,MAGjB7C,EAAUhH,OACNtD,EAAIyL,SACPnB,EAAUtK,EAAIyL,OAAOrL,MAAMJ,EAAK4H,GAC5B6B,EAAW3D,QAAUwE,IAAY8C,GAAqBrM,KAAKuJ,KAK9DtD,GACCqG,UAEDrG,EAAKyE,OAASzE,EAAKvH,GAAK,WACvB,MAAO6K,IAERA,EAAUgD,EAAgBtG,EAAMyC,EAAW5C,KAAMvD,QAAW,EAAMmG,EAAYnG,OAAWA,OAAWtD,KAGjG4H,EAAK5D,SACT4D,GAAQ6B,IAEOnG,SAAZgH,IACHU,EAAapD,EAAK,GACd5H,EAAIgL,aACPA,EAAahL,EAAIgL,cAAe,EAAOvB,EAAazJ,EAAIgL,WAAWA,IAEpEV,EAAU3J,EAAO8K,OAAOT,GAAY,KAAUxF,EAAWlC,OAAY,KAEtEvD,EAAMA,EACHA,GAAOuK,GAAW,IACNhH,SAAZgH,EACC,GAAKA,EACLhH,OAELtD,EAAIsM,UAAYtM,EAAIsM,UAAUC,KAW/B,MATAvM,GAAIW,OAAS4H,EAAQ,GACrBvI,EAAI2F,IAAM3F,EAAIW,OAAOgF,IAEjB3F,EAAI6D,EAAEgJ,OAAS7M,EAAIkI,SAEtBnI,EAAe,SAAToK,EACHoD,GAAYC,KAAKzN,GACjB,IAEG0H,GAAYgC,EAAW5F,EAAE8E,SAE7Bc,EAAW5F,EAAE8E,SAAS5I,EAAK0J,EAAYzJ,GACvCD,EAOJ,QAAS0N,GAAKtE,EAAS9F,EAAMoG,EAAY5C,EAAMoD,EAAUnF,EAAK6D,EAAU+E,GAEvE,GAAInK,GAAOoK,EAAkBC,EAC5BC,EAAO5N,KACP6N,EAAmB,UAATzK,CAIXwK,GAAKnD,QAAUgD,EACfG,EAAKtK,MAAQuK,QACbD,EAAKhH,KAAOA,EACZgH,EAAK7G,KAAOiD,EACZ2D,EAAQC,EAAKhK,GACZiB,IAAK,EAELhB,OAAQgK,EAAU,EAAI,EACtBC,GAAI,GAAKC,KACTrF,SAAUA,EACVjB,SAEDmG,EAAK/H,SAAW6C,EAChBkF,EAAKxK,KAAOA,GAAQ,MAChBA,IACHwK,EAAKI,OAASC,IAAKtL,GAAauL,SAG5B1E,GAAkC,QAApBA,EAAWpG,QAC5BwK,EAAKlI,IAAMwD,OAAevF,KAAOiK,EAAKhH,OAGpCgH,EAAK5J,OAASwF,IACjBoE,EAAKjK,KAAO6F,EAAW7F,MAAQiK,EAC/BtK,EAAQkG,EAAWlG,MACnBoK,EAAclE,EAAW5F,EACzBgK,EAAK5H,MAAQ0H,EAAYS,IACzBP,EAAK7H,QAAUmD,EAAQnJ,KAAOmJ,EAAQnJ,MAAQyJ,EAAW9D,IAAI3F,OAAS6N,EAAK5H,OAASwD,EAAWzD,OAAS6H,EAEpGF,EAAY7J,QAGfP,EAAMqK,EAAM9I,IAAM,IAAM6I,EAAY7J,UAAY+J,EAChDA,EAAK1J,MAAQkK,GACbR,EAAKzJ,SAAWF,GACNX,EAAMS,UAAY4J,EAAM9I,IAAM+I,EAAK1J,MAAQW,GACrDvB,EAAM+K,KAAKT,GAEXtK,EAAMgL,OAAOzJ,EAAK,EAAG+I,GAItBA,EAAKlI,IAAMwD,GAAWM,EAAW9D,KACvBtC,IACVwK,EAAKjK,KAAOiK,GAwBd,QAASW,GAAsBnD,GAC9B,GAAIoD,GAAWC,EAAYC,CAC3B,KAAKF,IAAaG,IACjBF,EAAaD,EAAY,IACrBpD,EAAWqD,KACdC,EAAYtD,EAAWqD,GACvBrD,EAAWqD,MACXrM,GAAOqM,GAAYC,EAAWtD,IASjC,QAASwD,GAAWjN,EAAMoI,EAAQqB,GAIjC,QAASyD,KACR,GAAI9O,GAAMC,IACVD,GAAI6D,GACHoE,UAAU,GAEXjI,EAAIkI,QAAS,EACblI,EAAImI,QAAUvG,EATf,GAAIoF,GAAM+H,EAASnO,EAClBoO,EAAc,GAAIjM,IAAKgF,GAqBxB,IAVIxH,GAAYyJ,GAEfA,GACCiF,QAASjF,EAAOiF,QAChBxD,OAAQzB,GAEC,GAAKA,IAAWA,IAC1BA,GAAUC,SAAUD,IAGjB+E,EAAU/E,EAAO+E,QAAS,CAC7B/E,EAAO1B,OAAS0B,EAAO1B,KACvByG,EAAU,GAAKA,IAAYA,EACvB1D,GAAcA,EAAWnB,KAAK6E,IAAYG,GAAMH,GACjDA,EACEA,GACJxM,EAAM,aAAeyH,EAAO+E,QAAU,eAEvCC,EAAclN,EAAQkN,EAAaD,EAEnC,KAAKnO,IAAQoJ,GACZgF,EAAYpO,GAAQN,EAAUyO,EAAQnO,GAAOoJ,EAAOpJ,QAGrDoO,GAAclN,EAAQkN,EAAahF,EAYpC,OARsC1G,WAAjC0D,EAAOgI,EAAY/E,YACvB+E,EAAY/E,SAAW,GAAKjD,IAASA,EAAQE,GAAWF,IAASE,GAAWF,GAASA,IAErF8H,EAAI5N,UAAY8N,GAAa/N,YAAc+N,EAAYlD,KAAOgD,EAE3DzD,IACH2D,EAAYG,YAAc9D,GAEpB2D,EAGR,QAASvN,GAAUmG,GAGlB,MAAO3H,MAAKE,KAAKC,MAAMH,KAAM2H,GAO9B,QAASwH,GAAYxN,EAAMoF,EAAMqE,EAAYgE,GAI5C,QAASC,GAAevK,GAGvB,GAAIwK,GAAavI,CACjB,IAAK,GAAKjC,IAAUA,GAAUA,EAAMyK,SAAW,IAAMC,EAAO1K,GAAQ,CACnE,IAAK0K,EACJ,GAAI,sBAAsB1O,KAAKgE,IAG1BiC,EAAOE,GAAWtF,EAAOA,GAAQmD,IACpCA,EAAQiC,EAIRyI,EAAOC,SAASC,eAAe5K,OAE1B,IAAwB,MAApBA,EAAM6K,OAAO,GACvBH,EAAOC,SAASC,eAAe5K,EAAMiE,MAAM,QACrC,IAAI3J,EAAEI,KAAOsD,GAAKE,MAAMlC,KAAKgE,GACnC,IACC0K,EAAOpQ,EAAE0F,EAAO2K,UAAU,GACzB,MAAOlO,IAGPiO,IACkB,WAAjBA,EAAKtH,SACR5F,EAAMwC,EAAQ,2BAA6B0K,EAAKtH,SAE7CkH,EAEHtK,EAAQ0K,EAAKI,WAIbN,EAAcE,EAAKK,aAAaC,IAC5BR,IACCA,IAAgBS,IACnBjL,EAAQmC,GAAWqI,SACZrI,IAAWqI,IACRlQ,EAAEI,KACZsF,EAAQ1F,EAAEwH,KAAK4I,GAAMO,MAGlBT,GAAgBxK,IACpBnD,EAAOA,IAASvC,EAAEI,GAAKuQ,GAAUjL,GACjCA,EAAQqK,EAAYxN,EAAM6N,EAAKI,UAAWxE,EAAYgE,IAEvDtK,EAAMkL,SAAWrO,EAAOA,GAAQ2N,EAC5B3N,IAASoO,KACZ9I,GAAWtF,GAAQmD,GAEpB0K,EAAKS,aAAaH,GAAUnO,GACxBvC,EAAEI,IACLJ,EAAEwH,KAAK4I,EAAMO,GAASjL,KAIzB0K,EAAOnM,WACIyB,GAAMtF,KACjBsF,EAAQzB,OAGT,OAAOyB,GAGR,GAAI0K,GAAMU,EACTC,EAAepJ,EAAOA,GAAQ,EA4B/B,IA3BAjE,GAAKsN,MAAQ9C,GAAYC,KAGT,IAAZ6B,IACHA,EAAU/L,OACV8M,EAAed,EAAec,IAK/Bf,EAAUA,IAAYrI,EAAKsJ,OACxBtJ,EAAKU,KACJ5F,KAAYkF,GACZA,MAIJqI,EAAQY,SAAWZ,EAAQY,UAAYrO,GAAQ,UAC3CyJ,IACHgE,EAAQF,YAAc9D,IAIlB+E,GAAgBpJ,EAAKsJ,SAAWF,EAAed,EAAetI,EAAKsJ,UAAYF,EAAa3Q,KAEhG2Q,EAAeA,EAAaE,QAERhN,SAAjB8M,EAoBH,MAnBIA,GAAa3E,QAAUzE,EAAKyE,OAE3B2E,EAAa5E,QAChB2E,EAAeC,IAKhBpJ,EAAOuJ,EAAWH,EAAcf,GAEhCmB,EAAOJ,EAAaK,QAAQC,GAAe,QAAS1J,IAEhDmJ,IACJA,EAAerO,EAAQ,WACtB,MAAOqO,GAAa1E,OAAOrL,MAAM+P,EAAc9P,YAC7C2G,GAEHwH,EAAsB2B,IAEhBA,EAUT,QAASQ,GAAcC,EAAY/J,GAClC,MAAOtG,IAAYqQ,GAChBA,EAAWC,KAAKhK,GAChB+J,EAGJ,QAASE,GAAaxM,EAAIyM,EAAK9M,GAC9B+M,OAAOC,eAAe3M,EAAIyM,GACzBhM,MAAOd,EACPiN,cAAc,IAIhB,QAASC,GAAiBvP,EAAMyB,GAiB/B,QAAS+N,GAAMxJ,GACd3G,EAAYb,MAAMH,KAAM2H,GAGzB,QAASyJ,KACR,MAAO,IAAID,GAAM/Q,WAGlB,QAASiR,GAAQzK,EAAM0K,GAGtB,IAFA,GAAIC,GAAYZ,EAAYhQ,EAAM0D,EAAImN,EACrCC,EAAI,EACEA,EAAIC,EAAaD,IACvB9Q,EAAOgR,EAAQF,GACfF,EAAalO,OACT1C,EAAO,KAAOA,IACjB4Q,EAAa5Q,EACbA,EAAO4Q,EAAWK,OAClBJ,EAAYD,EAAWC,WAEEnO,UAArBgB,EAAKuC,EAAKjG,KAAwB4Q,GAAuDlO,UAAxCsN,EAAaY,EAAWZ,cAC7EtM,EAAKqM,EAAcC,EAAY/J,IAEhC0K,EAAOjN,EAAIkN,GAAcM,EAAWN,EAAWnO,MAAOzC,EAAM6Q,GAI9D,QAAStF,GAAItF,GACZA,EAAOA,EAAO,KAAOA,EAClBkL,KAAKC,MAAMnL,GACXA,CACH,IAAIpD,GAAG7C,EAAMqR,EAASR,EACrBC,EAAI,EACJpN,EAAKuC,EACLqL,IAED,IAAI9P,GAASyE,GAAO,CAGnB,IAFAA,EAAOA,MACPpD,EAAIoD,EAAK7C,OACF0N,EAAEjO,EAAGiO,IACXQ,EAAI5D,KAAKrO,KAAKkM,IAAItF,EAAK6K,IAKxB,OAHAQ,GAAI1J,IAAM5G,EACVsQ,EAAIhF,MAAQA,EACZgF,EAAIC,MAAQA,EACLD,EAGR,GAAIrL,EAAM,CAST,IARAyK,EAAQzK,EAAM,SAASvC,EAAI8N,GACtBA,IACH9N,EAAK8N,EAAUjG,IAAI7H,IAEpB4N,EAAI5D,KAAKhK,KAEVA,EAAKrE,KAAKG,MAAMH,KAAMiS,GACtBR,EAAIC,EACGD,KAGN,GAFAO,EAAUC,EAAIR,GACdD,EAAYG,EAAQF,GAAGD,UACnBA,GAAaQ,GAAWA,EAAQ/E,MACnC,GAAI9K,GAAS6P,GAEZ,IADAxO,EAAIwO,EAAQjO,OACLP,KACNqN,EAAamB,EAAQxO,GAAIgO,EAAWnN,OAGrCwM,GAAamB,EAASR,EAAWnN,EAIpC,KAAK1D,IAAQiG,GACRjG,IAASyR,IAAaC,EAAY1R,KACrC0D,EAAG1D,GAAQiG,EAAKjG,IAInB,MAAO0D,GAGR,QAAS6N,GAAMtL,EAAM5C,EAAQwN,GAC5B5K,EAAOA,EAAO,KAAOA,EAClBkL,KAAKC,MAAMnL,GACXA,CAEH,IAAI6K,GAAGjO,EAAGmG,EAAGhJ,EAAM2R,EAAK7O,EAAO8O,EAAUlO,EAAImO,EAAWR,EACvDS,EAAI,EACJC,EAAQ1S,IAET,IAAImC,GAASuQ,GAAQ,CAKpB,IAJAH,KACAC,KACAhP,EAAIoD,EAAK7C,OACT4F,EAAI+I,EAAM3O,OACH0O,EAAEjP,EAAGiP,IAAK,CAGhB,IAFApO,EAAKuC,EAAK6L,GACVhP,GAAQ,EACHgO,EAAE,EAAGA,EAAE9H,IAAMlG,EAAOgO,IACpBc,EAASd,KAGba,EAAMI,EAAMjB,GAER3D,IACHyE,EAASd,GAAKhO,EAAQqK,EAAK,KAAOA,EAC/BzJ,EAAGyJ,KAAQuE,EAAYvE,GAAMwE,EAAIxE,KAAQwE,EAAIxE,MAASzJ,EAAGyJ,GAC1DA,EAAGwE,EAAKjO,IAGRZ,IACH6O,EAAIJ,MAAM7N,GACVmO,EAAUnE,KAAKiE,KAEfE,EAAUnE,KAAK2D,EAAUZ,EAAGlF,IAAI7H,IAC5BmN,GACHX,EAAamB,EAASR,EAAWxN,IASpC,YALI2O,EACHA,EAAYD,GAAOE,QAAQJ,GAAW,GAEtCE,EAAMpE,OAAOnO,MAAMuS,GAAQ,EAAGA,EAAM3O,QAAQ8O,OAAOL,KAIrDnB,EAAQzK,EAAM,SAASvC,EAAI8N,EAAWP,EAAQJ,GACzCW,EACHO,EAAMd,KAAUM,MAAM7N,EAAIqO,EAAOlB,GACvBkB,EAAMd,OAAcvN,GAC9BqO,EAAMd,GAAQvN,IAGhB,KAAK1D,IAAQiG,GACRjG,IAASyR,IAAaC,EAAY1R,KACrC+R,EAAM/R,GAAQiG,EAAKjG,IAKtB,QAASsM,KAKR,QAAS6F,GAAWC,GAInB,IAHA,GAAId,MACH1O,EAAI,EACJC,EAAIuP,EAAShP,OACPR,EAAEC,EAAGD,IACX0O,EAAI5D,KAAK0E,EAASxP,GAAG0J,QAEtB,OAAOgF,GAXR,GAAI5N,GAAI1D,EAAM4Q,EAAiBzM,EAC9B2N,EAAI,EACJC,EAAQ1S,IAYT,IAAImC,GAASuQ,GACZ,MAAOI,GAAWJ,EAGnB,KADArO,KACOoO,EAAIf,EAAae,IACvB9R,EAAOgR,EAAQc,GACflB,EAAalO,OACT1C,EAAO,KAAOA,IACjB4Q,EAAa5Q,EACbA,EAAO4Q,EAAWK,QAEnB9M,EAAQ4N,EAAM/R,KACd0D,EAAG1D,GAAQ4Q,GAAczM,GAAS+M,EAAWN,EAAWnO,MACrDjB,GAAS2C,GACRgO,EAAWhO,GACXA,EAAMmI,QACPnI,CAEJ,KAAKnE,IAAQ+R,IACRA,EAAM/M,eAAehF,IAA6B,MAAnBA,EAAKgP,OAAO,IAAe0C,EAAY1R,EAAKoI,MAAM,KAAQpI,IAASyR,IAAa9R,GAAYoS,EAAM/R,MACpI0D,EAAG1D,GAAQ+R,EAAM/R,GAGnB,OAAO0D,GAjMR,GAAId,GAAGvC,EAAagD,EACnB6N,EAAa7R,KACb2R,EAAUvO,EAAKuO,QACfqB,EAAS5P,EAAK4P,OACdlF,EAAK1K,EAAK0K,GACVmF,EAAQ7T,EAAE4T,QACTzK,IAAK5G,GAAQ,UACbsL,MAAOA,EACPiF,MAAOA,GACLc,GACHrL,EAAO,GACPuL,EAAQ,GACRxB,EAAcC,EAAUA,EAAQ5N,OAAS,EACzC4O,EAAcvT,EAAEmH,WAChB8L,IAwLD,KAFAlB,EAAMlQ,UAAYgS,EAEb1P,EAAE,EAAGA,EAAImO,EAAanO,KAC1B,SAAUqO,GACTA,EAASA,EAAOA,QAAUA,EAC1BS,EAAYT,GAAUrO,EAAE,CACxB,IAAI4P,GAAY,IAAMvB,CAEtBjK,KAASA,EAAO,IAAM,IAAMiK,EAC5BsB,GAAS,QAAUC,EAAY,MAAQvB,EAAS,MAChDqB,EAAMrB,GAAUqB,EAAMrB,IAAW,SAASzQ,GACzC,MAAKf,WAAU2D,YAGX4O,EACHA,EAAY3S,MAAMwG,YAAYoL,EAAQzQ,GAEtCnB,KAAKmT,GAAahS,GALXnB,KAAKmT,IASVR,IACHM,EAAMrB,GAAQwB,IAAMH,EAAMrB,GAAQwB,KAAO,SAASjS,GACjDnB,KAAKmT,GAAahS,KAGlBwQ,EAAQpO,GAqBZ,OAjBA2P,GAAQ,GAAIG,UAAS1L,EAAMuL,GAE3BlS,EAAc,WACbkS,EAAM/S,MAAMH,KAAMI,YAEd4D,EAAS5D,UAAUsR,EAAc,KACpCb,EAAa7Q,KAAMI,UAAUsR,GAAc1N,IAI7ChD,EAAYC,UAAYgS,EACxBA,EAAMjS,YAAcA,EAEpBoQ,EAAGlF,IAAMA,EACTkF,EAAGO,QAAUA,EACbP,EAAG4B,OAASA,EACZ5B,EAAGtD,GAAKA,EACDsD,EAGR,QAASd,GAAWD,EAAQjB,GAE3B,GAAIkE,GACHC,EAAUC,GAAqBC,QAC/B1M,GACCwE,SACA6B,SACA3F,QACAc,IAAK,WACLiD,OAAQC,EAoBV,OAjBI2D,KACHrI,EAAOlF,EAAQkF,EAAMqI,IAGtBrI,EAAKsJ,OAASA,EACTtJ,EAAKuM,UAETA,EAAUI,GAAWC,KAAKtD,GAC1BtJ,EAAKuM,QAAUA,EAAUA,EAAQ,GAAGM,cAAgB,IAErDN,EAAUC,EAAQxM,EAAKuM,SACnBA,GAAWA,IAAYC,EAAQM,MAGlC9M,EAAKsJ,OAASjR,EAAE0U,KAAK/M,EAAKsJ,SAGpBtJ,EAUR,QAASgN,GAAcvF,EAAWwF,GAYjC,QAASC,GAAStS,EAAMuS,EAAM9I,GAO7B,GAAI+I,GAAS7K,EAAU8K,EACtBC,EAAUvR,GAAKuR,QAAQ7F,EAExB,IAAI7M,SAAeA,KAAS2S,KAAW3S,EAAK4N,WAAa5N,EAAK0O,SAAW1O,EAAK4S,UAA0B,cAAd/F,GAA6B7M,EAAKgQ,SAAWhQ,EAAKqR,QAAS,CAKpJ,IAAK1J,IAAY3H,GAChBsS,EAAS3K,EAAU3H,EAAK2H,GAAW4K,EAEpC,OAAOA,IAAQ9R,GAqChB,MAlCIT,IAAQ,GAAKA,IAASA,IACzByJ,EAAa8I,EACbA,EAAOvS,EACPA,EAAO0B,QAER+Q,EAAYhJ,EACK,cAAdoD,EACCpD,EACCA,EAAWqD,GAAcrD,EAAWqD,OACtCwF,EACHE,EAAUH,EAAcG,QAEX9Q,SAAT6Q,IACHA,EAAOC,EAAUxS,EAAOyS,EAAUzS,GAClCA,EAAO0B,QAEK,OAAT6Q,EAECvS,SACIyS,GAAUzS,IAGdwS,IACHD,EAAOC,EAAQvD,KAAKwD,EAAWzS,EAAMuS,EAAM9I,EAAY,OACvD8I,EAAK3L,IAAMiG,GAER7M,IACHyS,EAAUzS,GAAQuS,IAGhBG,GAEHA,EAAQ1S,EAAMuS,EAAM9I,EAAY+I,GAE1BD,EAGR,GAAIzF,GAAaD,EAAY,GAC7BpM,IAAOqM,GAAcwF,EAYtB,QAASO,GAAWC,GACnBxR,GAAewR,GAAMxR,GAAewR,IAAO,SAAS3P,GACnD,MAAO1E,WAAU2D,QACbpB,GAAa8R,GAAM3P,EAAO7B,IAC3BN,GAAa8R,IAQlB,QAASxI,GAAQtB,GAChB,QAAS+J,GAAI3S,EAAQqN,GACpBpP,KAAKkN,IAAMvC,EAAO4J,OAAOxS,EAAQqN,GACjCA,EAAQlD,IAAMlM,KAiBf,MAdIM,IAAYqK,KAEfA,GACC4J,OAAQ5J,IAINA,EAAOgK,UACVhK,EAAS9I,EAAQA,KAAY8I,EAAOgK,SAAUhK,IAG/CA,EAAOuB,IAAM,SAASnK,EAAQqN,GAC7B,MAAO,IAAIsF,GAAI3S,EAAQqN,IAEjBzE,EAkBR,QAASc,GAAc7E,EAAMsC,EAAS0L,EAAapL,EAAY3E,EAAK6D,GACnE,GAAInF,GAAGC,EAAGzD,EAAKgH,EAAMrG,EAAQmU,EAAiBC,EAAUC,EACvDrR,EAAO8F,EACPwL,EAAS,EAwBV,IAtBI9L,KAAY,GACf0L,EAAc1L,EACdA,EAAU7F,cACO6F,KAAYoL,KAC7BpL,EAAU7F,SAGPtD,EAAMC,KAAKD,MAEdW,EAASV,KACT0D,EAAOA,GAAQhD,EAAOgD,KACtBqD,EAAOrD,EAAKiI,SAAS5L,EAAIiK,UAAYtJ,EAAOqG,MACvC3G,UAAU2D,SACd6C,EAAO7G,EAAIgL,YAAczK,GAAYP,EAAIgL,YACtCnE,EAAO7G,EAAIgL,WAAWnE,GACtBlD,IAIJqD,EAAO/G,KAGJ+G,EAAM,CAeT,IAdKyC,GAAc5C,GAAqB,SAAbA,EAAK2B,MAC/B7E,EAAOkD,GAGJlD,GAAQkD,IAASlD,IAEpBkD,EAAOlD,EAAKkD,MAGbiO,GAAmBnR,EACnB8B,GAAeA,IAAgBqP,EAC3BA,KACF3L,EAAUA,OAAevF,KAAOiD,IAE7BpB,IAAgBgO,GAAqByB,UAAYlO,EAAKkO,UAAYvR,GAAQA,IAASoG,GACvFkL,EAAS3H,EAAgBtG,EAAMH,EAAMsC,EAAS0L,EAAalR,EAAMmB,EAAK6D,EAAU3I,OAC1E,CAWN,GAVI2D,GACHoR,EAAWpR,EAAKkD,KAChBmO,EAAYrR,EAAKQ,MACjBR,EAAKQ,MAAQkK,KAEb1K,EAAOoG,GACPgL,EAAWpR,EAAKkD,KAChBlD,EAAKkD,KAAOA,EACZlD,EAAKgC,IAAMwD,GAER/G,GAASyE,KAAUgO,EAGtB,IAAKrR,EAAI,EAAGC,EAAIoD,EAAK7C,OAAQR,EAAIC,EAAGD,IACnCG,EAAKQ,MAAQX,EACbG,EAAKkD,KAAOA,EAAKrD,GACjByR,GAAUjO,EAAKvH,GAAGoH,EAAKrD,GAAIG,EAAMZ,QAGlCY,GAAKkD,KAAOA,EACZoO,GAAUjO,EAAKvH,GAAGoH,EAAMlD,EAAMZ,GAE/BY,GAAKkD,KAAOkO,EACZpR,EAAKQ,MAAQ6Q,EAEVF,IACHrP,GAAenC,QAGjB,MAAO2R,GAGR,QAAS3H,GAAgBtG,EAAMH,EAAMsC,EAAS0L,EAAalR,EAAMmB,EAAK6D,EAAU3I,GAI/E,GAAIwD,GAAGC,EAAG0R,EAASC,EAAWC,EAAYC,EAAa5H,EAAa6H,EAAetF,EAAUuF,EAASC,EAAQ9U,EAAQ+U,EACrHT,EAAS,EA0EV,IAxEIjV,IAEHiQ,EAAWjQ,EAAImI,QACfxH,EAASX,EAAIW,OACbwI,EAAUA,EAAUV,EAAUU,EAASnJ,EAAI2F,KAAO3F,EAAI2F,IAElDqB,IAASrD,EAAK+G,QACjBgD,EAAc1G,IAASrD,EAAKgC,IAAIgQ,KAC7BhS,EAAKgC,IAAIgQ,KACTrS,OACO0D,IAASrG,EAAO+J,QACtB1D,IAAShH,EAAIiK,UAChByD,EAAc/M,EAAOqG,KACrBmC,EAAQwM,KAAOhV,EAAO+J,SAEtBgD,EAAc/M,EAAO+J,SAAW/G,EAAK+G,QAGtCgD,EAAc/J,EAAK+G,QAGhB/J,EAAOE,MAAMgB,QAAS,IAIzBsH,EAAUA,MACVA,EAAQtH,MAAO,IAIb8B,IACHgF,EAAWA,GAAYhF,EAAKE,EAAE8E,SAC9B+M,EAAYvM,GAAWA,EAAQtH,QAAS,EAEpC6T,GAAa/R,EAAKE,EAAE+R,KACvBjN,EAAWrF,QAGZ6F,EAAUV,EAAUU,EAASxF,EAAKgC,KAClChF,GAAUX,GAAO2D,EAAK3D,IACnB2D,EAAK3D,IAAIuI,QAAQ5E,EAAKyB,SACtBzE,IAGA6U,EAAU7U,GAAUA,EAAOE,MAAM2U,WACjB,MAAfA,EAAQ,IACXK,EAAY,yBAEbL,EAAUA,EAAQxM,MAAM,IAGrBlE,KAAQ,IACXwQ,GAAc,EACdxQ,EAAM,GAIH6D,GAAY3I,GAAOA,EAAI6D,EAAEgJ,QAC5BlE,EAAWrF,QAEZiS,EAAgB5M,EACZA,KAAa,IAEhB4M,EAAgBjS,OAChBqF,EAAWhF,EAAKE,EAAE8E,UAGnBQ,EAAUnC,EAAK8O,QACZrN,EAAUzB,EAAK8O,QAAS3M,GACxBA,EAEHsM,EAAStM,EACL/G,GAASyE,KAAUgO,EActB,IAXAM,EAAUG,EACP3R,EACSL,SAARwB,GAAqBnB,GACpB,GAAI8J,GAAKtE,EAAS,QAASxF,EAAMkD,EAAMG,EAAMlC,EAAK6D,EAAU+E,GACjEyH,EAAQtR,EAAE+R,GAAIF,EACV/R,GAAQA,EAAKE,EAAEC,SAElBqR,EAAQtR,EAAEmE,KAAOhI,GAAOA,EAAI6D,EAAEmE,KAAOhI,EAErCmV,EAAQnV,IAAMA,GAEVwD,EAAI,EAAGC,EAAIoD,EAAK7C,OAAQR,EAAIC,EAAGD,IAEnC4R,EAAY,GAAI3H,GAAKgI,EAAQ,OAAQN,EAAStO,EAAKrD,GAAIwD,GAAOlC,GAAO,GAAKtB,EAAGmF,EAAUwM,EAAQzK,SAC3F8K,KACFJ,EAAUzP,IAAM7D,KAAY2T,IAASD,GAAWzS,GAAKgK,IAAIlG,EAAKrD,GAAI,QAAS4R,IAE7EC,EAAarO,EAAKvH,GAAGoH,EAAKrD,GAAI4R,EAAWrS,IACzCkS,GAAUE,EAAQtR,EAAE8E,SAAWwM,EAAQtR,EAAE8E,SAAS0M,EAAYD,GAAaC,MAK5EF,GAAUG,EAAc3R,EAAO,GAAI8J,GAAKgI,EAAQxF,GAAY,OAAQtM,EAAMkD,EAAMG,EAAMlC,EAAK6D,EAAU+E,GAEjG8H,KACFL,EAAQxP,IAAM7D,KAAY2T,IAASD,GAAWzS,GAAKgK,IAAIlG,EAAM,QAASsO,IAGxEA,EAAQnV,IAAMA,EACdmV,EAAQtR,EAAE+R,GAAKF,EACfT,GAAUjO,EAAKvH,GAAGoH,EAAMsO,EAASpS,GAMlC,OAJI/C,KACHmV,EAAQ/P,QAAUzE,EAAOwD,MACzBxD,EAAOoV,YAAcZ,GAEfI,EAAgBA,EAAcN,EAAQE,GAAWF,EAUzD,QAASe,GAAcxU,EAAGmC,EAAMsS,GAC/B,GAAItU,GAAuB2B,SAAb2S,EACX1V,GAAY0V,GACXA,EAASpF,KAAKlN,EAAKkD,KAAMrF,EAAGmC,GAC5BsS,GAAY,GACb,YAAczU,EAAEG,SAASH,GAAK,GAKjC,OAHIoB,IAAayE,SAA+F/D,UAAnF2S,EAAWrT,GAAayE,QAAQwJ,KAAKlN,EAAKkD,KAAMrF,EAAGyU,GAAYtU,EAASgC,MACpGhC,EAAUsU,GAEJtS,IAASA,EAAKmE,IAAMyF,GAAYC,KAAK7L,GAAWA,EAGxD,QAASY,GAAMZ,GACd,KAAM,IAAIoB,IAAKmT,IAAIvU,GAGpB,QAASkU,GAAYlU,GACpBY,EAAM,iBAAmBZ,GAG1B,QAAS6O,GAAOF,EAAQtJ,EAAMmP,EAAYC,EAAaC,GAKtD,QAASC,GAAqBC,GAC7BA,GAASC,EACLD,GACH7L,EAAQ4D,KAAKgC,EAAOmG,OAAOD,EAAKD,GAAO9F,QAAQiG,GAAU,QAI3D,QAASC,GAAcxO,EAASyO,GAC3BzO,IACHA,GAAW,KAEX0N,GACCe,EACG,KAAOA,EAAQ,mBAAqBzO,EAAU,cAAgBA,EAC9D,2BAA6BA,GAAW,mBAAqBmI,IAInE,QAASuG,GAASC,EAAKC,EAAM5O,EAASf,EAAW4P,EAAOxJ,EAAMyJ,EAAS1L,EAAQ2L,EAAOC,EAAOC,EAAYjT,IAmCpG8S,GAAWF,GAAQG,IAAU/O,GAAWoD,GAA+B,MAArBA,EAAOvC,WAAqBmO,IACjFtB,EAAYiB,GAITtJ,IACHwJ,EAAQ,IACR5P,EAAY0F,IAEboK,EAAQA,GAASf,IAAeE,CAEhC,IAAIgB,GAAMC,EAAaC,EACtBC,GAAgBT,GAAQZ,SACxBtV,EAAQ,GACR+G,EAAO,GACP6P,EAAW,GACXC,EAAa,GACbC,EAAc,GACdC,EAAiB,GACjBvQ,EAAU,GACVwQ,EAAa,GAEbjB,GAASM,IAAUF,CAGpB7O,GAAUA,IAAYoD,EAASA,GAAU,QAASyL,GAClDV,EAAqBnS,GACrBqS,EAAMrS,EAAQ2S,EAAI9S,OACdiT,EACCa,GACHpN,EAAQ4D,MAAM,IAAK,KAAO/C,EAAOkF,QAAQ,KAAM,UAAUA,QAAQsH,GAAiB,MAAQ,QAEjF5P,GACM,SAAZA,IACC6P,GAAYjX,KAAKwK,IACpBsK,EAAY,8CAEb2B,EAAeS,EAAQ,SACvBA,EAAQ,IAAM3H,EAAO4H,UAAUD,EAAQ,IAAK9T,GAC5CmT,EAAcW,EAAQ,KAAOA,EAAQ,IAAMpC,EAAY,eAAiBiB,GAExEmB,EAAUE,EAAMC,MAChB1N,EAAUuN,EAAQ,GAClBrB,GAAQ,GAELrL,GAEH8M,EAAY9M,EAAOkF,QAAQiG,GAAU,KAAMc,EAAcxQ,EAAMmP,GAC7D1F,QAAQ6H,GAAY,SAASxB,EAAKyB,EAASC,EAAU1T,EAAK2T,EAAUC,EAAUC,EAAKC,GA4BnF,MA3BY,UAAR9T,IACH4T,EAAW,aAERE,IACHrB,EAAWA,GAAyB,MAAbqB,EAAM,IAE9B9T,EAAM,IAAM2T,EAAW,KACnBE,GACH/Q,GAAQ4Q,EAAWE,EAAW,IAC9BhB,GAAc,IAAMkB,EAAQ,MAClBJ,GACVf,GAAY3S,EAAM,SAAW4T,EAAW,KAAOE,EAAQ,WAEvDhB,GAAkB9S,EAAM,IAAM8T,EAAQ,MAC5BL,EACVlR,GAAWqR,GAEM,YAAbD,IACHZ,GAAca,GAEE,eAAbD,IACHpB,EAAiB,UAAVuB,GAER/X,GAASiE,EAAM4T,EAAW,IAC1Bf,GAAe7S,EAAM,IAAM8T,EAAQ,KACnCC,EAAcA,GAAe/X,GAAaC,KAAK0X,IAEzC,KACLzP,MAAM,MAGPwO,GAAgBA,EAAa,IAChCA,EAAaY,MAGdU,GACE3Q,EACAf,KAAegP,GAAeyC,GAAe,GAC7CjC,MACAmC,EAAYrB,IAA2B,MAAZvP,EAAkB,WAAa,IAAKwP,EAAaC,GAC5EmB,EAAYnR,IAAqB,MAAZO,EAAkB,QAAU,IAAKtH,EAAO4W,GAC7DpQ,EACAwQ,EACAR,EACAE,EACAC,GAAgB,GAElB9M,EAAQ4D,KAAKwK,GACTlC,IACHuB,EAAM7J,KAAK2J,GACXA,EAAUa,EACVb,EAAQ,IAAMzB,EACdyB,EAAQ,IAAMX,IAELF,IACVT,EAAcS,IAAea,EAAQ,IAAMb,IAAea,EAAQ,KAAOb,EAAYa,EAAQ,IAC7FA,EAAQ,IAAM3H,EAAO4H,UAAUD,EAAQ,IAAK9T,GAC5C8T,EAAUE,EAAMC,OAEjBzB,GAAesB,GAAWb,GAC1B1M,EAAUuN,EAAQ,GAInB,GAAIzU,GAAGyR,EAAQ6D,EAASD,EAAaG,EACpClB,EAAYlV,GAAakV,WAAa9Q,GAAQA,EAAK8Q,WAC/C5U,GAAe4U,aAAc,EACjCmB,KACAzC,EAAM,EACN2B,KACAzN,EAAUuO,EACVhB,GAAW,CAAC,CAACgB,EAgCd,IA9BInB,GAAa9Q,EAAKwB,MACrBxB,EAAK8Q,UAAYA,GAUd3B,IACiB7S,SAAhB8S,IACH9F,EAASA,EAAOtH,MAAM,GAAIoN,EAAYpS,OAAS,GAAKtB,IAErD4N,EAAS9N,GAAiB8N,EAAS3N,IAGpCgU,EAAcwB,EAAM,IAAMA,EAAM,GAAG,GAAGC,MAAM,IAE5C9H,EAAOG,QAAQ3N,GAAM+T,GAErBP,EAAqBhG,EAAOtM,SAExBwS,EAAMyC,EAAOA,EAAOjV,OAAS,KAChC2S,EAAc,GAAKH,IAAQA,IAASA,EAAI,MAAQA,EAAI,KAAQA,EAAI,IAK7DL,EAAY,CAIf,IAHAlB,EAASiE,EAAUD,EAAQ3I,EAAQ6F,GACnC6C,KACAxV,EAAIyV,EAAOjV,OACJR,KACNwV,EAASrS,QAAQsS,EAAOzV,GAAG,GAE5B2V,GAASlE,EAAQ+D,OAEjB/D,GAASiE,EAAUD,EAAQjS,EAE5B,OAAOiO,GAGR,QAASkE,GAAS1Z,EAAI2Z,GACrB,GAAItU,GAAKuU,EACR7V,EAAI,EACJC,EAAI2V,EAASpV,MAGd,KAFAvE,EAAGwF,QACHxF,EAAG4Z,SACI7V,EAAIC,EAAGD,IAAK,CAClB/D,EAAG4Z,MAAM/K,KAAK+K,EAAQD,EAAS5V,GAC/B,KAAKsB,IAAOuU,GACC,WAARvU,GAAoBuU,EAAMzT,eAAed,IAAQuU,EAAMvU,GAAKd,SAAWqV,EAAMvU,GAAKwU,MACrF7Z,EAAGwF,KAAOxF,EAAGwF,KAAK6N,OAAOuG,EAAMvU,MAMnC,QAASiU,GAAYnR,EAAM/G,EAAO8E,GACjC,OAAQiC,EAAKoB,MAAM,MAAQnI,EAAMmI,MAAM,MAAQrD,EAAIqD,MAAM,OAG1D,QAASuQ,GAAeC,EAAWC,GAClC,MAAO,qBAAuBD,EAAU,GAAK,gBAAkBA,EAAU,GAAK,KAC1EA,EAAU,GAAK,aAAeA,EAAU,GAAK,IAAM,IACpD,eAAiBC,EAAU,GAAK,gBAAkBA,EAAU,GAAK,KAChEA,EAAU,GAAK,aAAeA,EAAU,GAAK,IAAM,IAGxD,QAASpB,GAAY9M,EAAQiM,EAAcxQ,EAAMmP,GAEhD,QAASuD,GAAY5C,EAAK6C,EAASC,EAAQ/Q,EAAOtE,EAAMsV,EAAUC,EAAKC,EAAIC,EAAO3C,EAAM4C,EAC7EC,EAAOC,EAASC,EAAMC,EAAMC,EAAOC,EAAUC,EAAMC,EAAOtW,EAAOuW,GAK3E,QAASC,GAAUC,EAASC,EAAKC,EAAQC,EAAQpX,EAAMqX,EAAcC,EAAYC,GAIhF,GADAC,EAAqB,MAAXL,EACNA,IACHvW,EAAOA,EAAKyE,MAAM6R,EAAI7W,QAClB,mBAAmBjD,KAAKma,GAAW3W,IACtCsR,EAAY+E,GAERO,IACJP,GAAWvD,GACNlB,EAAa,GAAK,sBAAwB,OAC3C,KAEA4E,EACA,gBAAkBA,EAAS,KAC3BpX,EACC,OACA,SACD0T,EACA,iBAAmBlB,EAAa,GAAK,KAAO,uBAC5C,KAEA+E,GACCF,EACA,IAAMA,EACND,EACC,GACCpX,EAAO,GAAK,IAAMmX,IACjBG,GAAc,KACjBC,EAAYH,EAAS,GAAKpX,EAAOqX,GAAgB,GAAKF,EAAQ,KACnEF,GAAqBM,EAAY,IAAMA,EAAY,GAEnDN,EAAUC,GAA+B,cAAxBD,EAAQ5R,MAAM,EAAG,GAC/B4R,EAAQ5R,MAAM,GACd4R,IACAvD,GACElB,EAAa,IAAK,WAAa8D,EAAM,MAAM,KAC5C,KAGDjB,GAAU,CAEb,GADAoC,EAAkB,YAAVC,EAAuBC,EAAS9D,EAAa+D,OAAS/D,EAAa+D,WAAgBC,EAAOC,GAC9FC,EAAQP,GAAWC,EAAMA,EAAMpX,OAAO,IACzC,GAAI0X,EAAMC,MAAO,CAChB,KAAOD,EAAME,IACZF,EAAQA,EAAME,EAEXF,GAAMG,MACLH,EAAM1T,MACTzD,EAAO,IAAMA,EAAKyE,MAAM,IAEzB0S,EAAME,GAAKrX,EACXmX,EAAM1T,IAAM0T,EAAM1T,KAAmB,MAAZzD,EAAK,SAIhC6W,GAAM9M,KAAK/J,EAER0V,KAAQkB,IACXW,EAAUC,GAAQzV,EAClB0V,EAAkBD,GAAQE,EAAaF,GAAM/X,QAIhD,MAAO4W,GAIJ/R,IAAUkR,IACbxV,EAAOsE,EAAQtE,GAEhBsV,EAAWA,GAAY,GACvBM,EAAUA,GAAW,GACrBP,EAASA,GAAUD,GAAWQ,EAC9B5V,EAAOA,GAAQyV,EAEX3C,IAASA,GAAQ,OAAOtW,KAAK2Z,EAAKvW,EAAM,OAC3CI,EAAOA,EAAKyE,MAAM,GAAGrE,MAAM,KAAKuX,KAAK,MAItCjC,EAAMA,GAAOO,GAAQ,EACrB,IAAI2B,GAAMf,EAAOM,EAAOU,EAAOjB,EAASkB,EAAatc,EACpDuG,EAAMnC,CAEP,KAAKmY,IAAWC,EAAQ,CAIvB,GAHIzC,GACHjE,EAAYtK,GAETgP,GAAYvB,EAAU,CAKzB,GADAmD,EAAOL,EAAUC,EAAK,GAClBrB,EAAK1W,OAAS,EAAIsC,GAAO6V,GAAQ,GAAI,CAKxC,GAJAA,EAAO9c,EAAE0U,KAAK2G,EAAK1R,MAAMmT,EAAM7V,EAAMwQ,EAAI9S,SACzCoX,EAAQE,GAAUkB,EAAST,EAAK,GAAGN,GAEnCC,EAAQN,EAAMA,EAAMpX,OAAO,GACvB0X,GAASA,EAAMG,IAAK,CACvB,KAAOH,EAAME,IAAMF,EAAME,GAAGC,KAC3BH,EAAQA,EAAME,EAEfQ,GAAQV,EAAME,IAAMrX,KAAMmX,EAAME,GAAI5T,IAAK0T,EAAM1T,SAE/CoT,GAAM9M,KAAK8N,GAAS7X,KAAM6W,EAAMhD,OAE7BsD,IAASA,EAAME,KAAOQ,IACzBH,EAAaF,GAAQE,EAAaF,EAAK,GAAG/S,MAAM0S,EAAMe,UAAYR,EAAaF,GAC/EE,EAAaF,EAAK,GAAKE,EAAaF,EAAK,GAAG/S,MAAM,EAAG0S,EAAMe,WAE5DL,EAAMK,SAAWT,EAAkBD,EAAK,GACxCK,EAAMM,OAASP,EAEfF,EAAaF,IAASrB,EAAK1R,MAAMgM,EAAW7Q,GAC5C6Q,EAAY7Q,EAEZiY,EAAMT,MAAQgB,GAAUR,GAAQQ,GAAUR,IACzC,GAAI7I,UAAS,cACd,KAAO6I,EAAO,wBAA0BF,EAAaF,IAAmB,MAAVzB,EAAgB,KAAOA,GAAS,oBAE9F2B,EAAaF,EAAK,IAAOa,EAAOC,IAAUpJ,GAAqBxF,MAAQ,kBAAqBkO,EAAK1L,QAAQC,GAAe,QAAU,IAAOuL,EAAaF,GAEtJK,EAAMP,IAAML,EAAOC,GACnBW,EAAMpU,IAAMoU,EAAMpU,KAAOoU,EAAM7X,MAAQ6X,EAAM7X,KAAKuY,QAAQ,MAAQ,EAEnEb,EAAaF,GAAQ,GAEV,MAAR9B,IACHA,EAAM,WAEQ,MAAXL,IACHA,EAAS,WA4GX,MAzGA7Z,GAAOuc,GAEHA,GAAUlC,EAAOkC,EAASxF,EAAMqD,EAAU,KAC3CoC,GAEEA,GAAUlC,EAAOkC,EAASzF,EAAMqD,EAAU,MAG5CP,GAECmD,IAAWF,IAAS,EACpBG,EAAOH,GAAS,EAChB7D,IACC8C,EAAUC,KAAUzV,IACpBkV,EAASgB,EAAST,IAASN,OAC3BQ,EAAaF,GAAQ,GACrBC,EAAkBD,GAAQ,GAE3BnC,GACC,KACAa,EACCoC,EACA,IAECI,EAAavC,EAAK1R,MAAMiU,EAAY3W,IAAM+U,GACzCA,EAAQ6B,EAAY5B,GAAS,EAAO,MACrC,OAAS2B,GAAcA,EAAa3W,EAAMwQ,EAAI9S,OAAQgV,GAAYxB,EAAalJ,KAAKkN,EAAOC,OAAU,OAEvG1B,GAEEgC,GAAQlG,EAAYtK,GAASyN,GAAYxB,EAAaY,MAAOiD,EAAQ,IAAM9W,EAAM2Y,EAAYrU,EAAOoU,EAAa3W,EAAMwQ,EAAI9S,OAC5HgV,IAAcA,EAAWwC,EAAOC,GAAKjE,EAAa6D,MAAcrC,EAASM,KAAOzQ,GAAQtE,EAAO,KAC/FA,EAEEA,EAAKI,MAAM,KAAKuX,KAAK,KAAKzL,QAAQ1N,GAAKoa,MAAOxC,IAC7CV,GAAOJ,GAETA,EAECA,EACAS,EAEW,MAAVA,EAAgB,KAAO,IACvBJ,GACE0C,EAAOC,IAAUhH,EAAYtK,GAAS,KACvCoO,EACC,IACC2C,EAASlC,EAAMmC,EAASlC,EAAM,MAIrCiC,GAAWC,GACXjC,IACHsC,EAAOC,IAAS,EAChBA,KAIE7D,IACEsD,GAAWC,IACXjC,IACCyC,EAASF,EAAM,KAClBrB,EAASgB,IAAWT,GACpBgB,EAASF,EAAM,IAAK,GAErBO,EAAWJ,EAAOH,EAAM,IAErB5C,IACH+C,EAAOH,EAAM,GAAKZ,EAAaF,GAAM/X,QAAU4V,EAAS,EAAI,IACxDrV,GAAQ+V,KACXkB,EAASgB,IAAWT,IAASN,OAC7BsB,EAASF,EAAM,IAAK,KAKvBZ,EAAaF,IAASE,EAAaF,IAAO,IAAMrB,EAAK1R,MAAMgM,EAAW7Q,GACtE6Q,EAAY7Q,EAAM2S,EAAI9S,OAEjBsY,GAAWC,KACXF,EAAczC,GAAUmD,EAASF,EAAM,MAC1CZ,EAAaF,EAAK,IAAMnC,EACxBoC,EAAkBD,EAAK,MAEZ,MAAR9B,GAAekB,IAAYiB,IAC9BH,EAAaF,GAAQE,EAAaF,EAAK,GAAG/S,MAAMoU,GAAYnB,EAAaF,GACzEE,EAAaF,EAAK,GAAKE,EAAaF,EAAK,GAAG/S,MAAM,EAAGoU,KAGvDnB,EAAaF,IAASM,EAActc,EAAIiJ,MAAM,GAAKjJ,GAG/Cuc,GAAWC,IAAUtC,IACzB4C,IACItY,GAAgB,MAAR0V,IACX2C,EAAOC,IAAS,IAIbP,GAAWC,IAAU/B,IACrBxB,IACHiD,EAAaF,IAAS9B,GAEvBla,GAAOka,GAEDla,EAGR,GAAIsb,GAAOC,EAAQ4B,EAAWjI,EAC7BsH,EACAD,EACAtD,EAAWxB,GAAgBA,EAAa,GACxCgE,GAAUC,GAAIzC,GACdwD,GAAYa,EAAG7B,GACfyB,EAAa,EAGbJ,EAAQ,EACRd,EAAO,EACPiB,KACAI,EAAW,EACXL,KACAH,KACAd,KACAE,GAAqBqB,EAAG,GACxBpB,GAAgBoB,EAAE,IAClBrI,EAAY,CAWb,OATkB,MAAdzJ,EAAO,KACVA,EAASA,EAAOkF,QAAQ6M,GAAe,MAExCrI,GAAU1J,GAAUvE,EAAO,IAAM,KAAKyJ,QAAQ1N,GAAKwa,KAAM7D,GAErDV,IACH/D,EAASgH,EAAa,KAGfY,GAAS5H,GAAUY,EAAYtK,GAGxC,QAAS2N,GAAUsE,EAAKxW,EAAMmP,GAG7B,GAAI3S,GAAGia,EAAMtV,EAASf,EAAWzG,EAAQ+c,EAAQC,EAAYC,EAASC,EAASC,EAASC,EAAcvG,EAAcjM,EAAQyS,EAC3HC,EAAeC,EAAWC,EAAalO,EAAUmO,EAAYC,EAAa3T,EAAS4F,EAAQgO,EAAYC,EAASC,EAAQC,EAAUC,EAClIrX,EAASsX,EAAUC,EAAStT,EAAYuT,EAAYC,EACpDC,EAAiB,EACjB7J,EAAWzB,GAAqByB,UAAYlO,EAAKkO,UAAYlO,EAAKkD,MAAQlD,EAAKgY,WAAahY,EAAK8O,SAAW9O,EAAKiY,WACjHC,EAAO,GACPC,KACA1b,EAAI+Z,EAAIxZ,MAgBT,KAdI,GAAKgD,IAASA,GACjBiJ,EAAWkG,EAAa,cAAgBnP,EAAKyJ,QAAQiG,GAAU,KAAK1N,MAAM,MAAS,IAAMhC,EACzFA,EAAO,IAEPiJ,EAAWjJ,EAAKiJ,UAAY,UACxBjJ,EAAK8Q,YACRqH,EAAYrH,WAAY,GAErB9Q,EAAKoY,QACRD,EAAYC,OAAQ,GAErBrB,EAAe/W,EAAKU,KACpByW,EAAcnX,EAAKwE,OAEfhI,EAAI,EAAGA,EAAIC,EAAGD,IAKlB,GAHAia,EAAOD,EAAIha,GAGP,GAAKia,IAASA,EAEjByB,GAAQ,KAAOzB,EAAO,QAItB,IADAtV,EAAUsV,EAAK,GACC,MAAZtV,EAEH+W,GAAQ,MAAQzB,EAAK,GAAK,gBACpB,CA4DN,GA3DArW,EAAYqW,EAAK,GACjB/S,GAAWyL,GAAcsH,EAAK,GAC9B9c,EAAS4Y,EAAekE,EAAK,GAAIlS,EAASkS,EAAK,IAC/CmB,EAAUnB,EAAK,GACfnS,EAAamS,EAAK,GACdA,EAAK,IACRoB,EAAa,yBACbC,EAAc,sCAEdD,EAAa,YACbC,EAAc,IAEfxO,EAASmN,EAAK,KAAOA,EAAK,IAAIhN,QAAQsH,GAAiB,OACnDyG,EAAqB,SAAZrW,GACRqP,GACHA,EAAalJ,KAAKmP,EAAK,KAGxBpW,EAAUoW,EAAK,IAAM7a,GAAayc,aAAc,GAAS,YACrDtB,IAAiBvG,EAAeiG,EAAK,MACxCjG,GAAgBA,GAChBuH,EAAiBhB,EAAazP,KAAK,KAGrC4G,EAAWA,GAAY3J,EAAO,IAAMA,EAAO,IAAMiM,GAAgB,iBAAiBzW,KAAKwK,EAAO,KAI1FkT,EAAuB,MAAZtW,GACVf,IACHe,EAAUf,IAAc0F,GAAO,IAAM1F,EAAYe,IAG9CuC,IAEH0T,EAAa7N,EAAWD,EAAQ6O,GAChCf,EAAWnO,SAAWA,EAAW,IAAM9H,EAEvCiW,EAAWlJ,SAAWkJ,EAAWlJ,UAAYA,EAC7CgE,EAAUxO,EAAS0T,GACnBlJ,EAAWkJ,EAAWlJ,SACtBiJ,EAAY7P,KAAK8P,IAGbI,IAEJH,EAAclW,EACd+M,EAAWA,GAAY/M,KAAa+G,GAAM/G,KAAa+G,GAAM/G,GAASG,MAEtEiW,EAAUW,EACVA,EAAO,IAERZ,EAAad,EAAIha,EAAI,GACrB8a,EAAaA,GAAgC,SAAlBA,EAAW,IAEvCK,EAAWtX,EAAU,iBAAmB,MACxC2W,EAAkB,GAClBC,EAAgB,GAEZQ,IAAajH,GAAgBoH,GAAWxX,GAAaA,IAAc0F,IAAQxB,GAAa,CAS3F,GAPAoT,EAAW,GAAIpL,UAAS,cAAe,MAAQrD,EAAW,OAAS8O,EAAkB,IAAM5W,EACxF0W,EAAa,IAAMle,EAAS,KAAOme,GACtCJ,EAAShW,IAAMrB,EACfqX,EAASY,KAAOnX,EAChBuW,EAAS7W,MAAQ2P,EACjBkH,EAAS/W,IAAM2D,EAEX6K,EACH,MAAOuI,EAGRvF,GAASuF,EAAUlH,GACnB0G,EAAY,MAAQ9W,EAAY,UAChC0W,GAAU,EACVE,EAAkBE,EAAYa,EAAiB,IAC/Cd,EAAgB,IAgBjB,GAdAiB,GAAST,GACLtI,GAAc9O,EAAU,SAAW,IAAM,UAAYsX,IAAab,GACjEA,EAAUxa,OAAW4R,EAAW2I,GAAU,EAAMK,GAAaQ,GAC3DX,EAAagB,EAAiB,GAAKL,EAAWK,GAChD,IAAMpe,EAAS,KAAO,KACX,MAAZwH,GACEwV,GAAa,EAAM,KAAOpS,EAAO,GAAK,MACtCqS,GAAU,EAAM,OAASrS,EAAO,GAAK,cAAgB4K,EAAa,QAAU,UAG9EuH,GAAS,EAAM,oCACdhT,EAAUyT,EAAYna,OAAS,SAAW,IAC3CrD,EAAS,MAET0d,IAAgBC,EAAY,CAK/B,GAFAY,EAAO,IAAMA,EAAKlW,MAAM,MAAS,IACjCkV,EAAY,MAAQG,EAAc,eAC9BlI,GAAcqB,EAAc,CAU/B,GARA0H,EAAO,GAAI5L,UAAS,cAAe,OAASrD,EAAW,IAAM8O,EAAiB,IAAMV,EAAcQ,EAAaK,EAC5GJ,GACHI,EAAKxW,IAAMrB,EACX6X,EAAKI,KAAOjB,EACR7G,GACH2B,EAAS4E,EAAagB,EAAiB,GAAKG,EAAM1H,GAEnD0H,EAAKvX,IAAM2D,EACP6K,EACH,MAAO+I,EAERlB,GAAkBE,EAAYa,EAAiB,cAC/Cd,EAAgB,IAMjBiB,EAAOX,EAAUI,EAAWT,GAAa1G,GAAgBuH,GAAkBG,GAAQ,IACnF1H,EAAe,EACf6G,EAAc,EAEXhX,IAAYiX,IACfpJ,GAAW,EACXgK,GAAQ,oBAAsB/I,EAAa,OAAS,MAAQ6H,EAAkB,iBAAmB3W,EAAU,IAAM4W,EAAgB,MAAQ9H,EAAa,GAAK,cAM/J+I,EAAO,MAAQjP,GACXkP,EAAYC,MAAQ,cAAgB,IACrC,WACC1B,EAAS,YAAc,KACvBG,EAAU,aAAe,KACzBF,EAAa,aAAe,KAC5BxH,GACEsH,EAAK,GACJ,OACA,IACC,MACH,WACFyB,GACC/I,EAAa,KAAO,iBAExB,KACC+I,EAAO,GAAI5L,UAAS,cAAe4L,GAClC,MAAO1d,GACRqU,EAAY,8BAAgCqJ,EAAO,SAAW1d,EAAEG,SAASH,GAAK,KAM/E,MAJIwF,KACHA,EAAKvH,GAAKyf,EACVlY,EAAKkO,WAAaA,GAEZgK,EAQR,QAASzW,GAAUU,EAASoW,GAG3B,MAAOpW,IAAWA,IAAYoW,EAC1BA,EACAzd,EAAQA,KAAYyd,GAAgBpW,GACpCA,EACDoW,GAAiBzd,KAAYyd,GAGjC,QAASC,GAAexd,EAAQrB,GAG/B,GAAImE,GAAKlE,EACRuL,EAAMxL,EAAOwL,IACbsT,EAAWtT,GAAOA,EAAIsT,QAEvB,KAAKA,EAAU,CAEd,GADAA,WACWzd,KAAWuS,IAAUhU,GAAYyB,GAC3C,IAAK8C,IAAO9C,GACXpB,EAAOoB,EAAO8C,GACVA,IAAQuN,KAAYrQ,EAAO4D,eAAed,IAAUnE,EAAOE,MAAM6e,aAAgBrgB,EAAEsgB,WAAW/e,IACjG6e,EAASnR,MAAMxJ,IAAKA,EAAKlE,KAAMA,GAI9BuL,KACHA,EAAIsT,SAAWtT,EAAIkD,SAAWoQ,GAGhC,MAAOG,GAAgBH,EAAU9e,GAGlC,QAASif,GAAgB7a,EAAOpE,GAE/B,GAAIkf,GAAQC,EAAOC,EAClB/f,EAAMW,EAAOX,IACba,EAAQF,EAAOE,MACfmf,EAAarf,EAAO4K,OAAO1K,MAC3Bof,EAASpf,EAAMof,OACfC,EAAOrf,EAAMqf,KACbC,EAAaD,KAAS,EACtBE,EAAOtW,SAASjJ,EAAMuf,MACtBC,EAAUxf,EAAMwf,WAAe,CAEhC,KAAKje,GAAS2C,GACb,MAAOA,EAsDR,IApDIob,GAAcD,GAAQ,GAAKA,IAASA,GAEvCL,EAAS9a,EAAMoH,IAAI,SAASgI,EAAM3Q,GAEjC,MADA2Q,GAAOgM,EAAahM,EAAO9P,EAAc8P,EAAM+L,IACvC1c,EAAGA,EAAG8c,EAAG,GAAKnM,IAASA,EAAOA,EAAKN,cAAgBM,KAG5D0L,EAAOK,KAAK,SAASK,EAAGC,GACvB,MAAOD,GAAED,EAAIE,EAAEF,EAAID,EAAUE,EAAED,EAAIE,EAAEF,GAAKD,EAAU,IAGrDtb,EAAQ8a,EAAO1T,IAAI,SAASgI,GAC3B,MAAOpP,GAAMoP,EAAK3Q,OAER0c,GAAQG,EAAU,KAAOrgB,EAAIkM,UACxCnH,EAAQA,EAAMiE,SAEXzI,GAAY2f,KACfnb,EAAQA,EAAMmb,KAAK,WAClB,MAAOA,GAAK9f,MAAMO,EAAQN,cAGxBggB,EAAU,KAAOH,GAAQ3f,GAAY2f,MACxCnb,EAAQA,EAAMsb,WAGXtb,EAAMkb,QAAUA,IACnBlb,EAAQA,EAAMkb,OAAOA,EAAQtf,GACzBA,EAAOX,IAAIygB,UACd9f,EAAOX,IAAIygB,SAAS9f,IAIlBqf,EAAWU,SACdb,EAAUK,GAAQG,EAAU,EAAKtb,EAAQA,EAAMiE,QAC3ChJ,EAAI0gB,OACPrhB,EAAEmH,WAAWxG,EAAI0gB,QAAQ7N,QAAQgN,GAEjClf,EAAOwL,IAAIuU,OAASb,GAItBC,EAAQjf,EAAMif,MACdC,EAAMlf,EAAMkf,KACRC,EAAWF,OAAmBxc,SAAVwc,GAAuBE,EAAWD,KAAezc,SAARyc,KAChED,EAAQC,EAAM,GAEVlW,MAAMiW,IAAWjW,MAAMkW,KAC3BD,GAASA,GAAS,EAClBC,EAAczc,SAARyc,GAAqBA,EAAMhb,EAAMf,OAASe,EAAMf,QAAU+b,EAChEhb,EAAQA,EAAMiE,MAAM8W,EAAOC,IAExBK,EAAO,EAAG,CAIb,IAHAN,EAAQ,EACRC,EAAMhb,EAAMf,OACZ6b,KACOC,EAAMC,EAAKD,GAAOM,EACxBP,EAAOvR,KAAKvJ,EAAM+a,GAEnB/a,GAAQ8a,EAMT,MAJIG,GAAWW,OAAS3gB,EAAI2gB,OAC3B/N,YAAY5S,EAAI2gB,OAAO9N,QAAQ9N,GAGzBA,EAWR,QAAS6b,GAAU/Z,EAAMsC,EAAS0L,GACjC,GAAIgM,GAAW5gB,KAAK6gB,SAAW7gB,KAAK,IAAMsC,EAAM,qBAC/CyE,EAAO6Z,EAAS/Q,aAAaC,GAE9B,OAAOrE,GAAcmF,KAAK7J,GAAQ3H,EAAEwH,KAAKga,GAAU7Q,KAAY9I,GAAW2Z,GACzEha,EAAMsC,EAAS0L,GAKjB,QAASkM,GAAcC,GAEtB,MAAOC,IAAaD,KAAQC,GAAaD,GAAM,KAAOA,EAAGE,WAAW,GAAK,KAG1E,QAASC,GAAkBC,EAAOC,GAEjC,MAAOC,IAAkBD,IAAU,GAGpC,QAASE,GAAWC,GAEnB,MAAele,SAARke,EAAoBC,GAAQ1gB,KAAKygB,KAAU,GAAKA,GAAM/Q,QAAQiR,GAAaX,IAAkBS,EAAO,GAG5G,QAASG,GAAWH,GAElB,MAAO,GAAKA,IAASA,EAAOA,EAAK/Q,QAAQmR,GAAab,GAAiBS,EAGzE,QAASK,IAAaL,GAEpB,MAAO,GAAKA,IAASA,EAAOA,EAAK/Q,QAAQqR,GAAeX,GAAqBK,EAhrF/E,GAAIO,IAAa1iB,KAAM,CAEvBA,GAAIA,GAAKA,EAAEI,GAAKJ,EAAID,EAAOE,MAE3B,IACC0iB,IAAclf,GAAmBiH,GAAS1H,GAAQgQ,GAGlD9R,GAAa6B,GAAU8E,GAAYqG,GAAa1H,GAAUqJ,GAAOnM,GAAMH,GAAc6Q,GAAsBvQ,GAC3GV,GAAgBC,GAAgBC,GAAiBC,GAAiBL,GAAU2f,GAE5Exc,GAPGyc,GAAgB,UAEnB/b,GAAO,OAMPuQ,GAAW,sBACXqB,GAAkB,cAClBrH,GAAgB,UAChB4H,GAAa,2EACbN,GAAc,QACdrE,GAAa,cACbwO,GAAc,iBACdV,GAAU,iBACV3gB,GAAe,4BACfsM,GAAuB,0BACvBsU,GAAcS,GACdP,GAAc,SACdE,GAAgB,iBAChBxE,GAAgB,mBAChBtP,GAAS,EACTiT,IACCmB,IAAK,QACLC,IAAK,OACLC,IAAK,OACLC,KAAQ,OACRC,IAAK,QACLC,IAAK,QACLC,IAAK,QACLC,IAAK,SAENrB,IACCsB,IAAK,IACLC,GAAI,IACJje,GAAI,KAELkI,GAAO,OACPyH,GAAS,SACTxE,GAAW,gBACXC,GAAU,UACV3B,GAAW,8CACXsO,MACAmG,MAEAC,GAAM3jB,EAAO4jB,SACbC,GAAUF,IAAO1jB,IAAMA,EAAEoM,OAEzBmD,IACC3E,UACCmK,QAAShF,GAEVpP,KACCoU,QAASvF,GAEVuD,WACCgC,QAASjD,GAEV4J,UACA3T,aAwnFF,IApnFC/E,IACC6gB,QAAShB,GACTiB,KAEChG,MAAO,+GAGPI,KAAM,yPAGN9P,KAAMA,EACNyI,IAAKxU,EACL8O,OAAQA,EACRwB,MAAOqG,EACPpF,OAAQnR,EACR2G,UAAWA,EACX2a,UAAWvN,EACXvB,SACCrK,SAAU,SAASrI,EAAMuS,GACX,OAATA,QACI2O,IAAQlhB,GACLA,IACVkhB,GAAQlhB,GAAQuS,KAInBM,WAAYA,EACZ4O,UACCvL,WAAW,GAEZwL,OAAQ7iB,EACR8iB,KAAM7iB,EACN8iB,IAAKljB,EACLyH,IAAK,aACL0b,MAAOtc,EACPmY,KAAM9V,EACNd,IAAKnG,EACLmhB,KAAM1N,EACNjJ,IAAK5L,EACLwiB,IAAK,SAAStC,GAIb,MAHc,gBAAVA,GACHxL,EAAY,IAENwL,IAGTgC,UACCxgB,WAAYZ,EACZ2hB,SAAU,SAAS7e,GAClB,MAAOA,IAEJjD,EAAQ2R,GAAsB1O,GAC9BhC,GAAKugB,SACLpgB,IAECuQ,KAGNtH,IAAKD,IA+ENxK,EAAaR,UAAY,GAAI2iB,QAAS5iB,YAAcS,EAqHrDwC,EAAe+K,QAAU,WACxB,OAAQhP,KAAK8D,IAAI,QAAS,UAO3BK,EAAS6K,QAAU,QAmnBnBxB,EAAKvM,WACJ6C,IAAKZ,EACLiB,SAAUA,EACVoG,OAAQ3F,EACRoC,OAAQoC,EACRuC,SAAU7E,EACV+c,OAAQzf,EACR0f,SAAU,SAASjf,GAIlB,MAHIlC,IAAauL,OAASlO,KAAKgO,MAAMC,MACpCjO,KAAKgO,OAASC,IAAKtL,GAAauL,SAEN7K,SAApBrD,KAAKgO,MAAMnJ,GAAqB7E,KAAKgO,MAAMnJ,GAAQ7E,KAAKgO,MAAMnJ,GAAO6X,GAAU7X,GAAK7E,KAAK4G,KAAM5G,KAAM8C,KAE7GyF,IAAK,QA2uDNzF,GAAOV,GAAO8gB,IACdjgB,GAAiBb,GAAOghB,WAElBN,IAAO1jB,GAAKA,EAAEoM,QAAS,CAE5B,IAAKuW,KAAgBpT,IACpBoF,EAAcgO,GAAcpT,GAAUoT,IAiBvC,IAdAzU,GAAclL,GAAO4c,WACrBpZ,GAAWxD,GAAOyT,QAClB5G,GAAQ7M,GAAO6H,KAEfnH,GAAKgF,IAAI7G,WACRO,UAAWA,EACXmF,QAASgC,EACT+C,QAASvC,EACToB,OAAQ3F,GAGTkF,GAAUhH,GAAKgH,QAAU,GAAI0D,GAGzBpO,GAOH,GAFAA,EAAEI,GAAGgM,OAASmV,EACdvO,GAAWhT,EAAE2kB,QACT3kB,EAAEmH,WAAY,CACjB,GAAI0b,MAAmBA,GAAgB7iB,EAAEkE,MAAM2f,SAE9C,KAAM,6CAA+ChB,EAEtDpgB,GAAQiB,GAAM1D,EAAEkE,MAAM4f,KACtB9gB,GAAO8J,IAAM9M,EAAEkE,MAAM4I,SAOtB9M,MAEI0iB,KACH3iB,EAAO4jB,SAAW3jB,GAKnBA,EAAE4kB,WAAa5kB,EAAE6kB,UAAY7kB,EAAE+U,QAAU,WAAa,KAAM,kDAG5D/U,EAAEsgB,WAAa,SAASrb,GACvB,MAAqB,kBAAPA,IAGfjF,EAAEyO,QAAUqW,MAAMrW,SAAW,SAASsW,GACrC,MAAmC,sBAAhB,SAAEvT,KAAKuT,IAG3BrhB,GAAKshB,IAAM,SAASC,GACfA,IAAOjlB,IACVyC,EAAQwiB,EAAIjlB,GACZA,EAAIilB,EACJjlB,EAAEI,GAAGgM,OAASmV,QACPvhB,GAAE2jB,SACT3Q,GAAWhT,EAAE2kB,UAIf3kB,EAAE2jB,SAAWd,EAEdtf,IAAeG,GAAKsgB,SACpBzgB,GAAakV,WAAY,EACzBvX,GAAclB,EAAEsgB,WAChBtgB,EAAEoM,OAASqX,GACXzjB,EAAEkE,MAAQlB,GACVhD,EAAE2f,UAAY9X,GAAa7E,GAAO2c,SAElC,KAAKiD,KAAWrf,IACf6R,EAAWwN,KAWX/e,GAAemc,UAAY,SAASA,GACpC,MAAqB/b,UAAd+b,EACJzc,GAAayc,WAEdzc,GAAa2hB,QAAU3hB,GAAa2hB,SACpC3hB,GAAayc,UAAYA,EACzBzc,GAAayE,QAAUgY,EAAY,KAAOA,EACvC,WAAa,MAAOA,IACpB9e,GAAY8e,GACXA,EACA/b,OACJJ,OACA,GAEHuQ,GAAuB7Q,GAAaghB,UACnC3V,OAAO,EACPiH,UAAU,EACVsP,MAAM,GAKPtV,IACCuV,MACChZ,OAAQ,SAASrK,GAKhB,GAAIyM,GAAO5N,KACVU,EAASkN,EAAKlN,OACdZ,EAAO8N,EAAKvB,UAAUoY,OAAStjB,IAAQT,EAAOiH,KAAK5D,SAAWrD,EAAOwD,OAClE,IACC0J,EAAKvB,UAAUoY,MAAO,OACxB7W,EAAK8W,SAAWhkB,EAAOwD,OAE1B,OAAOpE,IAERiL,YAAY,EACZ1C,MAAM,GAEPsc,OACCC,YAAa3Y,EAAQ0T,GACrB/T,KAAM,SAASzK,EAAK0jB,GACnB7kB,KAAK8kB,WAAW9kB,KAAKsI,UAEtBkD,OAAQ,SAASrK,GAGhB,GAAI2D,GAAyB+I,EAAStK,EAAWuc,EAAKK,EACrDvS,EAAO5N,KACPU,EAASkN,EAAKlN,OACdqkB,EAAQrkB,EAAO6L,cAAe,EAC9B3L,EAAQF,EAAOE,MACfyQ,EAAU0T,GAASrkB,EAAOiH,KAAK5D,OAC/BiR,EAAS,GACTyP,EAAO,CAER,KAAK7W,EAAKvB,UAAUoY,KAAM,CAGzB,GAFA3f,EAAQuM,EAAUlQ,EAAMT,EAAOgD,KAAKkD,KAEhCme,EAKH,IAJAA,EAAQnkB,EAAMwf,QAAU,UAAY,OACpCN,GAAOlf,EAAMkf,IACbK,GAAQvf,EAAMuf,MAAQ,EACtBrb,KACKvB,GAAK3C,EAAMif,OAAS,GAAIC,EAAMvc,GAAK4c,EAAO,EAAG5c,GAAK4c,EACtDrb,EAAMigB,GAAOxhB,EAGDF,UAAVyB,IACH+I,EAAU1L,GAAS2C,GACnBkQ,GAAUtU,EAAO8K,OAAO1G,GAAQuM,GAAWzQ,EAAMgU,aAGjD6P,GAAQ5W,EAAU/I,EAAMf,OAAS,IAE9B6J,EAAKvB,UAAUoY,KAAOA,KACzB7W,EAAK8W,SAAWhkB,EAAOwD,OAIzB,MAAO8Q,IAER8P,WAAY,SAASxc,GAIpB,IAHA,GAAI5H,GAAQE,EAAO8W,EAClB9J,EAAO5N,KACPwD,EAAI8E,EAAQvE,OACNP,KACN9C,EAAS4H,EAAQ9E,GACjB5C,EAAQF,EAAOE,MACf8W,EAAchX,EAAO4K,OAAO1K,MAC5BF,EAAO6L,WAA2BlJ,SAAdzC,EAAMkf,KAAqBpf,EAAOiH,KAAK5D,OAAS,EACpEnD,EAAMqL,QAAWvL,EAAO6L,cAAe,GAASpK,GAASzB,EAAOiH,KAAK,MACnE+P,EAAYuI,MAAQvI,EAAYmI,OAASnI,EAAYoI,KAAOpI,EAAYyI,MAAQzI,EAAYsI,QAAUtI,EAAY0I,SAChHxf,EAAMqf,MAAQrf,EAAMif,OAASjf,EAAMkf,KAAOlf,EAAMuf,MAAQvf,EAAMof,QAAUpf,EAAMwf,UAC9ExS,EAAKgX,aAGXvc,MAAM,GAEPzH,OACCkO,QAAS,MACT7C,QAASA,EAAQsT,GACjB3T,KAAMpL,EACN6H,MAAM,GAEP2c,SACC3c,MAAM,GAEP4c,KAECzZ,OAAQtK,EACRmH,MAAM,GAEP6c,MAEC1Z,OAAQtK,EACRmH,MAAM,GAEP8c,IAAKvf,GAASuf,IAAM7X,GAAY6X,IAAM/jB,IAGvCkM,IACCC,KAAM+T,EACNpX,KAAMoX,EACN8D,OAAQ1D,EACR2D,SAAUzD,GACV0D,IAAK,SAAS/D,GAEb,MAAele,SAARke,EAAoBgE,UAAU,GAAKhE,GAAiB,OAATA,EAAgBA,EAAO,MAY5E,MAPA5e,IAAeG,GAAKsgB,SACpBjhB,IAAY/C,GAAG0jB,IAAKjV,QACpB5K,GAAeL,WAAW,KAAM,KAAM,KAElCogB,IACHF,GAAIxf,MAAM4f,IAAIkB,IAAIhlB,GAEZA,GAAK0jB,IACT0C", "file": "jsrender.min.js", "sourcesContent": ["/*! JsRender v1.0.11: http://jsviews.com/#jsrender */\n/*! **VERSION FOR WEB** (For NODE.JS see http://jsviews.com/download/jsrender-node.js) */\n/*\n * Best-of-breed templating in browser or on Node.js.\n * Does not require jQuery, or HTML DOM\n * Integrates with JsViews (http://jsviews.com/#jsviews)\n *\n * Copyright 2021, <PERSON>\n * Released under the MIT License.\n */\n\n//jshint -W018, -W041, -W120\n\n(function(factory, global) {\n\t// global var is the this object, which is window when running in the usual browser environment\n\tvar $ = global.jQuery;\n\n\tif (typeof exports === \"object\") { // CommonJS e.g. Browserify\n\t\tmodule.exports = $\n\t\t\t? factory(global, $)\n\t\t\t: function($) { // If no global jQuery, take optional jQuery passed as parameter: require('jsrender')(jQuery)\n\t\t\t\tif ($ && !$.fn) {\n\t\t\t\t\tthrow \"Provide jQuery or null\";\n\t\t\t\t}\n\t\t\t\treturn factory(global, $);\n\t\t\t};\n\t} else if (typeof define === \"function\" && define.amd) { // AMD script loader, e.g. RequireJS\n\t\tdefine(function() {\n\t\t\treturn factory(global);\n\t\t});\n\t} else { // Browser using plain <script> tag\n\t\tfactory(global, false);\n\t}\n} (\n\n// factory (for jsrender.js)\nfunction(global, $) {\n\"use strict\";\n\n//========================== Top-level vars ==========================\n\n// global var is the this object, which is window when running in the usual browser environment\nvar setGlobals = $ === false; // Only set globals if script block in browser (not AMD and not CommonJS)\n\n$ = $ && $.fn ? $ : global.jQuery; // $ is jQuery passed in by CommonJS loader (Browserify), or global jQuery.\n\nvar versionNumber = \"v1.0.11\",\n\tjsvStoreName, rTag, rTmplString, topView, $views, $expando,\n\t_ocp = \"_ocp\",      // Observable contextual parameter\n\n\t$isFunction, $isArray, $templates, $converters, $helpers, $tags, $sub, $subSettings, $subSettingsAdvanced, $viewsSettings,\n\tdelimOpenChar0, delimOpenChar1, delimCloseChar0, delimCloseChar1, linkChar, setting, baseOnError,\n\n\tisRenderCall,\n\trNewLine = /[ \\t]*(\\r\\n|\\n|\\r)/g,\n\trUnescapeQuotes = /\\\\(['\"\\\\])/g, // Unescape quotes and trim\n\trEscapeQuotes = /['\"\\\\]/g, // Escape quotes and \\ character\n\trBuildHash = /(?:\\x08|^)(onerror:)?(?:(~?)(([\\w$.]+):)?([^\\x08]+))\\x08(,)?([^\\x08]+)/gi,\n\trTestElseIf = /^if\\s/,\n\trFirstElem = /<(\\w+)[>\\s]/,\n\trAttrEncode = /[\\x00`><\"'&=]/g, // Includes > encoding since rConvertMarkers in JsViews does not skip > characters in attribute strings\n\trIsHtml = /[\\x00`><\\\"'&=]/,\n\trHasHandlers = /^on[A-Z]|^convert(Back)?$/,\n\trWrappedInViewMarker = /^\\#\\d+_`[\\s\\S]*\\/\\d+_`$/,\n\trHtmlEncode = rAttrEncode,\n\trDataEncode = /[&<>]/g,\n\trDataUnencode = /&(amp|gt|lt);/g,\n\trBracketQuote = /\\[['\"]?|['\"]?\\]/g,\n\tviewId = 0,\n\tcharEntities = {\n\t\t\"&\": \"&amp;\",\n\t\t\"<\": \"&lt;\",\n\t\t\">\": \"&gt;\",\n\t\t\"\\x00\": \"&#0;\",\n\t\t\"'\": \"&#39;\",\n\t\t'\"': \"&#34;\",\n\t\t\"`\": \"&#96;\",\n\t\t\"=\": \"&#61;\"\n\t},\n\tcharsFromEntities = {\n\t\tamp: \"&\",\n\t\tgt: \">\",\n\t\tlt: \"<\"\n\t},\n\tHTML = \"html\",\n\tOBJECT = \"object\",\n\ttmplAttr = \"data-jsv-tmpl\",\n\tjsvTmpl = \"jsvTmpl\",\n\tindexStr = \"For #index in nested block use #getIndex().\",\n\tcpFnStore = {},     // Compiled furnctions for computed values in template expressions (properties, methods, helpers)\n\t$render = {},\n\n\tjsr = global.jsrender,\n\tjsrToJq = jsr && $ && !$.render, // JsRender already loaded, without jQuery. but we will re-load it now to attach to jQuery\n\n\tjsvStores = {\n\t\ttemplate: {\n\t\t\tcompile: compileTmpl\n\t\t},\n\t\ttag: {\n\t\t\tcompile: compileTag\n\t\t},\n\t\tviewModel: {\n\t\t\tcompile: compileViewModel\n\t\t},\n\t\thelper: {},\n\t\tconverter: {}\n\t};\n\n\t// views object ($.views if jQuery is loaded, jsrender.views if no jQuery, e.g. in Node.js)\n\t$views = {\n\t\tjsviews: versionNumber,\n\t\tsub: {\n\t\t\t// subscription, e.g. JsViews integration\n\t\t\trPath: /^(!*?)(?:null|true|false|\\d[\\d.]*|([\\w$]+|\\.|~([\\w$]+)|#(view|([\\w$]+))?)([\\w$.^]*?)(?:[.[^]([\\w$]+)\\]?)?)$/g,\n\t\t\t//        not                               object     helper    view  viewProperty pathTokens      leafToken\n\n\t\t\trPrm: /(\\()(?=\\s*\\()|(?:([([])\\s*)?(?:(\\^?)(~?[\\w$.^]+)?\\s*((\\+\\+|--)|\\+|-|~(?![\\w$])|&&|\\|\\||===|!==|==|!=|<=|>=|[<>%*:?\\/]|(=))\\s*|(!*?(@)?[#~]?[\\w$.^]+)([([])?)|(,\\s*)|(?:(\\()\\s*)?\\\\?(?:(')|(\"))|(?:\\s*(([)\\]])(?=[.^]|\\s*$|[^([])|[)\\]])([([]?))|(\\s+)/g,\n\t\t\t//   lftPrn0           lftPrn         bound     path               operator     err                                          eq      path2 late            prn      comma  lftPrn2          apos quot        rtPrn  rtPrnDot                  prn2     space\n\n\t\t\tView: View,\n\t\t\tErr: JsViewsError,\n\t\t\ttmplFn: tmplFn,\n\t\t\tparse: parseParams,\n\t\t\textend: $extend,\n\t\t\textendCtx: extendCtx,\n\t\t\tsyntaxErr: syntaxError,\n\t\t\tonStore: {\n\t\t\t\ttemplate: function(name, item) {\n\t\t\t\t\tif (item === null) {\n\t\t\t\t\t\tdelete $render[name];\n\t\t\t\t\t} else if (name) {\n\t\t\t\t\t\t$render[name] = item;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\taddSetting: addSetting,\n\t\t\tsettings: {\n\t\t\t\tallowCode: false\n\t\t\t},\n\t\t\tadvSet: noop, // Update advanced settings\n\t\t\t_thp: tagHandlersFromProps,\n\t\t\t_gm: getMethod,\n\t\t\t_tg: function() {}, // Constructor for tagDef\n\t\t\t_cnvt: convertVal,\n\t\t\t_tag: renderTag,\n\t\t\t_er: error,\n\t\t\t_err: onRenderError,\n\t\t\t_cp: retVal, // Get observable contextual parameters (or properties) ~foo=expr. In JsRender, simply returns val.\n\t\t\t_sq: function(token) {\n\t\t\t\tif (token === \"constructor\") {\n\t\t\t\t\tsyntaxError(\"\");\n\t\t\t\t}\n\t\t\t\treturn token;\n\t\t\t}\n\t\t},\n\t\tsettings: {\n\t\t\tdelimiters: $viewsDelimiters,\n\t\t\tadvanced: function(value) {\n\t\t\t\treturn value\n\t\t\t\t\t? (\n\t\t\t\t\t\t\t$extend($subSettingsAdvanced, value),\n\t\t\t\t\t\t\t$sub.advSet(),\n\t\t\t\t\t\t\t$viewsSettings\n\t\t\t\t\t\t)\n\t\t\t\t\t\t: $subSettingsAdvanced;\n\t\t\t\t}\n\t\t},\n\t\tmap: dataMap // If jsObservable loaded first, use that definition of dataMap\n\t};\n\nfunction getDerivedMethod(baseMethod, method) {\n\treturn function() {\n\t\tvar ret,\n\t\t\ttag = this,\n\t\t\tprevBase = tag.base;\n\n\t\ttag.base = baseMethod; // Within method call, calling this.base will call the base method\n\t\tret = method.apply(tag, arguments); // Call the method\n\t\ttag.base = prevBase; // Replace this.base to be the base method of the previous call, for chained calls\n\t\treturn ret;\n\t};\n}\n\nfunction getMethod(baseMethod, method) {\n\t// For derived methods (or handlers declared declaratively as in {{:foo onChange=~fooChanged}} replace by a derived method, to allow using this.base(...)\n\t// or this.baseApply(arguments) to call the base implementation. (Equivalent to this._super(...) and this._superApply(arguments) in jQuery UI)\n\tif ($isFunction(method)) {\n\t\tmethod = getDerivedMethod(\n\t\t\t\t!baseMethod\n\t\t\t\t\t? noop // no base method implementation, so use noop as base method\n\t\t\t\t\t: baseMethod._d\n\t\t\t\t\t\t? baseMethod // baseMethod is a derived method, so use it\n\t\t\t\t\t\t: getDerivedMethod(noop, baseMethod), // baseMethod is not derived so make its base method be the noop method\n\t\t\t\tmethod\n\t\t\t);\n\t\tmethod._d = (baseMethod && baseMethod._d || 0) + 1; // Add flag for derived method (incremented for derived of derived...)\n\t}\n\treturn method;\n}\n\nfunction tagHandlersFromProps(tag, tagCtx) {\n\tvar prop,\n\t\tprops = tagCtx.props;\n\tfor (prop in props) {\n\t\tif (rHasHandlers.test(prop) && !(tag[prop] && tag[prop].fix)) { // Don't override handlers with fix expando (used in datepicker and spinner)\n\t\t\ttag[prop] = prop !== \"convert\" ? getMethod(tag.constructor.prototype[prop], props[prop]) : props[prop];\n\t\t\t// Copy over the onFoo props, convert and convertBack from tagCtx.props to tag (overrides values in tagDef).\n\t\t\t// Note: unsupported scenario: if handlers are dynamically added ^onFoo=expression this will work, but dynamically removing will not work.\n\t\t}\n\t}\n}\n\nfunction retVal(val) {\n\treturn val;\n}\n\nfunction noop() {\n\treturn \"\";\n}\n\nfunction dbgBreak(val) {\n\t// Usage examples: {{dbg:...}}, {{:~dbg(...)}}, {{dbg .../}}, {^{for ... onAfterLink=~dbg}} etc.\n\ttry {\n\t\tconsole.log(\"JsRender dbg breakpoint: \" + val);\n\t\tthrow \"dbg breakpoint\"; // To break here, stop on caught exceptions.\n\t}\n\tcatch (e) {}\n\treturn this.base ? this.baseApply(arguments) : val;\n}\n\nfunction JsViewsError(message) {\n\t// Error exception type for JsViews/JsRender\n\t// Override of $.views.sub.Error is possible\n\tthis.name = ($.link ? \"JsViews\" : \"JsRender\") + \" Error\";\n\tthis.message = message || this.name;\n}\n\nfunction $extend(target, source) {\n\tif (target) {\n\t\tfor (var name in source) {\n\t\t\ttarget[name] = source[name];\n\t\t}\n\t\treturn target;\n\t}\n}\n\n(JsViewsError.prototype = new Error()).constructor = JsViewsError;\n\n//========================== Top-level functions ==========================\n\n//===================\n// views.delimiters\n//===================\n\n\t/**\n\t* Set the tag opening and closing delimiters and 'link' character. Default is \"{{\", \"}}\" and \"^\"\n\t* openChars, closeChars: opening and closing strings, each with two characters\n\t* $.views.settings.delimiters(...)\n\t*\n\t* @param {string}   openChars\n\t* @param {string}   [closeChars]\n\t* @param {string}   [link]\n\t* @returns {Settings}\n\t*\n\t* Get delimiters\n\t* delimsArray = $.views.settings.delimiters()\n\t*\n\t* @returns {string[]}\n\t*/\nfunction $viewsDelimiters(openChars, closeChars, link) {\n\tif (!openChars) {\n\t\treturn $subSettings.delimiters;\n\t}\n\tif ($isArray(openChars)) {\n\t\treturn $viewsDelimiters.apply($views, openChars);\n\t}\n\tlinkChar = link ? link[0] : linkChar;\n\tif (!/^(\\W|_){5}$/.test(openChars + closeChars + linkChar)) {\n\t\terror(\"Invalid delimiters\"); // Must be non-word characters, and openChars and closeChars must each be length 2\n\t}\n\tdelimOpenChar0 = openChars[0];\n\tdelimOpenChar1 = openChars[1];\n\tdelimCloseChar0 = closeChars[0];\n\tdelimCloseChar1 = closeChars[1];\n\n\t$subSettings.delimiters = [delimOpenChar0 + delimOpenChar1, delimCloseChar0 + delimCloseChar1, linkChar];\n\n\t// Escape the characters - since they could be regex special characters\n\topenChars = \"\\\\\" + delimOpenChar0 + \"(\\\\\" + linkChar + \")?\\\\\" + delimOpenChar1; // Default is \"{^{\"\n\tcloseChars = \"\\\\\" + delimCloseChar0 + \"\\\\\" + delimCloseChar1;                   // Default is \"}}\"\n\t// Build regex with new delimiters\n\t//          [tag    (followed by / space or })  or cvtr+colon or html or code] followed by space+params then convertBack?\n\trTag = \"(?:(\\\\w+(?=[\\\\/\\\\s\\\\\" + delimCloseChar0 + \"]))|(\\\\w+)?(:)|(>)|(\\\\*))\\\\s*((?:[^\\\\\"\n\t\t+ delimCloseChar0 + \"]|\\\\\" + delimCloseChar0 + \"(?!\\\\\" + delimCloseChar1 + \"))*?)\";\n\n\t// Make rTag available to JsViews (or other components) for parsing binding expressions\n\t$sub.rTag = \"(?:\" + rTag + \")\";\n\t//                        { ^? {   tag+params slash?  or closingTag                                                   or comment\n\trTag = new RegExp(\"(?:\" + openChars + rTag + \"(\\\\/)?|\\\\\" + delimOpenChar0 + \"(\\\\\" + linkChar + \")?\\\\\" + delimOpenChar1 + \"(?:(?:\\\\/(\\\\w+))\\\\s*|!--[\\\\s\\\\S]*?--))\" + closeChars, \"g\");\n\n\t// Default:  bind     tagName         cvt   cln html code    params            slash   bind2         closeBlk  comment\n\t//      /(?:{(\\^)?{(?:(\\w+(?=[\\/\\s}]))|(\\w+)?(:)|(>)|(\\*))\\s*((?:[^}]|}(?!}))*?)(\\/)?|{(\\^)?{(?:(?:\\/(\\w+))\\s*|!--[\\s\\S]*?--))}}\n\n\t$sub.rTmpl = new RegExp(\"^\\\\s|\\\\s$|<.*>|([^\\\\\\\\]|^)[{}]|\" + openChars + \".*\" + closeChars);\n\t// $sub.rTmpl looks for initial or final white space, html tags or { or } char not preceded by \\\\, or JsRender tags {{xxx}}.\n\t// Each of these strings are considered NOT to be jQuery selectors\n\treturn $viewsSettings;\n}\n\n//=========\n// View.get\n//=========\n\nfunction getView(inner, type) { //view.get(inner, type)\n\tif (!type && inner !== true) {\n\t\t// view.get(type)\n\t\ttype = inner;\n\t\tinner = undefined;\n\t}\n\n\tvar views, i, l, found,\n\t\tview = this,\n\t\troot = type === \"root\";\n\t\t// view.get(\"root\") returns view.root, view.get() returns view.parent, view.get(true) returns view.views[0].\n\n\tif (inner) {\n\t\t// Go through views - this one, and all nested ones, depth-first - and return first one with given type.\n\t\t// If type is undefined, i.e. view.get(true), return first child view.\n\t\tfound = type && view.type === type && view;\n\t\tif (!found) {\n\t\t\tviews = view.views;\n\t\t\tif (view._.useKey) {\n\t\t\t\tfor (i in views) {\n\t\t\t\t\tif (found = type ? views[i].get(inner, type) : views[i]) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tfor (i = 0, l = views.length; !found && i < l; i++) {\n\t\t\t\t\tfound = type ? views[i].get(inner, type) : views[i];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else if (root) {\n\t\t// Find root view. (view whose parent is top view)\n\t\tfound = view.root;\n\t} else if (type) {\n\t\twhile (view && !found) {\n\t\t\t// Go through views - this one, and all parent ones - and return first one with given type.\n\t\t\tfound = view.type === type ? view : undefined;\n\t\t\tview = view.parent;\n\t\t}\n\t} else {\n\t\tfound = view.parent;\n\t}\n\treturn found || undefined;\n}\n\nfunction getNestedIndex() {\n\tvar view = this.get(\"item\");\n\treturn view ? view.index : undefined;\n}\n\ngetNestedIndex.depends = function() {\n\treturn [this.get(\"item\"), \"index\"];\n};\n\nfunction getIndex() {\n\treturn this.index;\n}\n\ngetIndex.depends = \"index\";\n\n//==================\n// View.ctxPrm, etc.\n//==================\n\n/* Internal private: view._getOb() */\nfunction getPathObject(ob, path, ltOb, fn) {\n\t// Iterate through path to late paths: @a.b.c paths\n\t// Return \"\" (or noop if leaf is a function @a.b.c(...) ) if intermediate object not yet available\n\tvar prevOb, tokens, l,\n\t\ti = 0;\n\tif (ltOb === 1) {\n\t\tfn = 1;\n\t\tltOb = undefined;\n\t}\n\t// Paths like ^a^b^c or ~^a^b^c will not throw if an object in path is undefined.\n\tif (path) {\n\t\ttokens = path.split(\".\");\n\t\tl = tokens.length;\n\n\t\tfor (; ob && i < l; i++) {\n\t\t\tprevOb = ob;\n\t\t\tob = tokens[i] ? ob[tokens[i]] : ob;\n\t\t}\n\t}\n\tif (ltOb) {\n\t\tltOb.lt = ltOb.lt || i<l; // If i < l there was an object in the path not yet available\n\t}\n\treturn ob === undefined\n\t\t? fn ? noop : \"\"\n\t\t: fn ? function() {\n\t\t\treturn ob.apply(prevOb, arguments);\n\t\t} : ob;\n}\n\nfunction contextParameter(key, value, get) {\n\t// Helper method called as view.ctxPrm(key) for helpers or template parameters ~foo - from compiled template or from context callback\n\tvar wrapped, deps, res, obsCtxPrm, tagElse, callView, newRes,\n\t\tstoreView = this,\n\t\tisUpdate = !isRenderCall && arguments.length > 1,\n\t\tstore = storeView.ctx;\n\tif (key) {\n\t\tif (!storeView._) { // tagCtx.ctxPrm() call\n\t\t\ttagElse = storeView.index;\n\t\t\tstoreView = storeView.tag;\n\t\t}\n\t\tcallView = storeView;\n\t\tif (store && store.hasOwnProperty(key) || (store = $helpers).hasOwnProperty(key)) {\n\t\t\tres = store[key];\n\t\t\tif (key === \"tag\" || key === \"tagCtx\" || key === \"root\" || key === \"parentTags\") {\n\t\t\t\treturn res;\n\t\t\t}\n\t\t} else {\n\t\t\tstore = undefined;\n\t\t}\n\t\tif (!isRenderCall && storeView.tagCtx || storeView.linked) { // Data-linked view, or tag instance\n\t\t\tif (!res || !res._cxp) {\n\t\t\t\t// Not a contextual parameter\n\t\t\t\t// Set storeView to tag (if this is a tag.ctxPrm() call) or to root view (\"data\" view of linked template)\n\t\t\t\tstoreView = storeView.tagCtx || $isFunction(res)\n\t\t\t\t\t? storeView // Is a tag, not a view, or is a computed contextual parameter, so scope to the callView, no the 'scope view'\n\t\t\t\t\t: (storeView = storeView.scope || storeView,\n\t\t\t\t\t\t!storeView.isTop && storeView.ctx.tag // If this view is in a tag, set storeView to the tag\n\t\t\t\t\t\t\t|| storeView);\n\t\t\t\tif (res !== undefined && storeView.tagCtx) {\n\t\t\t\t\t// If storeView is a tag, but the contextual parameter has been set at at higher level (e.g. helpers)...\n\t\t\t\t\tstoreView = storeView.tagCtx.view.scope; // then move storeView to the outer level (scope of tag container view)\n\t\t\t\t}\n\t\t\t\tstore = storeView._ocps;\n\t\t\t\tres = store && store.hasOwnProperty(key) && store[key] || res;\n\t\t\t\tif (!(res && res._cxp) && (get || isUpdate)) {\n\t\t\t\t\t// Create observable contextual parameter\n\t\t\t\t\t(store || (storeView._ocps = storeView._ocps || {}))[key]\n\t\t\t\t\t\t= res\n\t\t\t\t\t\t= [{\n\t\t\t\t\t\t\t_ocp: res, // The observable contextual parameter value\n\t\t\t\t\t\t\t_vw: callView,\n\t\t\t\t\t\t\t_key: key\n\t\t\t\t\t\t}];\n\t\t\t\t\tres._cxp = {\n\t\t\t\t\t\tpath: _ocp,\n\t\t\t\t\t\tind: 0,\n\t\t\t\t\t\tupdateValue: function(val, path) {\n\t\t\t\t\t\t\t$.observable(res[0]).setProperty(_ocp, val); // Set the value (res[0]._ocp)\n\t\t\t\t\t\t\treturn this;\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (obsCtxPrm = res && res._cxp) {\n\t\t\t\t// If this helper resource is an observable contextual parameter\n\t\t\t\tif (arguments.length > 2) {\n\t\t\t\t\tdeps = res[1] ? $sub._ceo(res[1].deps) : [_ocp]; // fn deps (with any exprObs cloned using $sub._ceo)\n\t\t\t\t\tdeps.unshift(res[0]); // view\n\t\t\t\t\tdeps._cxp = obsCtxPrm;\n\t\t\t\t\t// In a context callback for a contextual param, we set get = true, to get ctxPrm [view, dependencies...] array - needed for observe call\n\t\t\t\t\treturn deps;\n\t\t\t\t}\n\t\t\t\ttagElse = obsCtxPrm.tagElse;\n\t\t\t\tnewRes = res[1] // linkFn for compiled expression\n\t\t\t\t\t? obsCtxPrm.tag && obsCtxPrm.tag.cvtArgs\n\t\t\t\t\t\t? obsCtxPrm.tag.cvtArgs(tagElse, 1)[obsCtxPrm.ind] // = tag.bndArgs() - for tag contextual parameter\n\t\t\t\t\t\t: res[1](res[0].data, res[0], $sub) // = fn(data, view, $sub) for compiled binding expression\n\t\t\t\t\t: res[0]._ocp; // Observable contextual parameter (uninitialized, or initialized as static expression, so no path dependencies)\n\t\t\t\tif (isUpdate) {\n\t\t\t\t\t$sub._ucp(key, value, storeView, obsCtxPrm); // Update observable contextual parameter\n\t\t\t\t\treturn storeView;\n\t\t\t\t}\n\t\t\t\tres = newRes;\n\t\t\t}\n\t\t}\n\t\tif (res && $isFunction(res)) {\n\t\t\t// If a helper is of type function we will wrap it, so if called with no this pointer it will be called with the\n\t\t\t// view as 'this' context. If the helper ~foo() was in a data-link expression, the view will have a 'temporary' linkCtx property too.\n\t\t\t// Note that helper functions on deeper paths will have specific this pointers, from the preceding path.\n\t\t\t// For example, ~util.foo() will have the ~util object as 'this' pointer\n\t\t\twrapped = function() {\n\t\t\t\treturn res.apply((!this || this === global) ? callView : this, arguments);\n\t\t\t};\n\t\t\t$extend(wrapped, res); // Attach same expandos (if any) to the wrapped function\n\t\t}\n\t\treturn wrapped || res;\n\t}\n}\n\n/* Internal private: view._getTmpl() */\nfunction getTemplate(tmpl) {\n\treturn tmpl && (tmpl.fn\n\t\t? tmpl\n\t\t: this.getRsc(\"templates\", tmpl) || $templates(tmpl)); // not yet compiled\n}\n\n//==============\n// views._cnvt\n//==============\n\nfunction convertVal(converter, view, tagCtx, onError) {\n\t// Called from compiled template code for {{:}}\n\t// self is template object or linkCtx object\n\tvar tag, linkCtx, value, argsLen, bindTo,\n\t\t// If tagCtx is an integer, then it is the key for the compiled function to return the boundTag tagCtx\n\t\tboundTag = typeof tagCtx === \"number\" && view.tmpl.bnds[tagCtx-1];\n\n\tif (onError === undefined && boundTag && boundTag._lr) { // lateRender\n\t\tonError = \"\";\n\t}\n\tif (onError !== undefined) {\n\t\ttagCtx = onError = {props: {}, args: [onError]};\n\t} else if (boundTag) {\n\t\ttagCtx = boundTag(view.data, view, $sub);\n\t}\n\tboundTag = boundTag._bd && boundTag;\n\tif (converter || boundTag) {\n\t\tlinkCtx = view._lc; // For data-link=\"{cvt:...}\"... See onDataLinkedTagChange\n\t\ttag = linkCtx && linkCtx.tag;\n\t\ttagCtx.view = view;\n\t\tif (!tag) {\n\t\t\ttag = $extend(new $sub._tg(), {\n\t\t\t\t_: {\n\t\t\t\t\tbnd: boundTag,\n\t\t\t\t\tunlinked: true,\n\t\t\t\t\tlt: tagCtx.lt // If a late path @some.path has not returned @some object, mark tag as late\n\t\t\t\t},\n\t\t\t\tinline: !linkCtx,\n\t\t\t\ttagName: \":\",\n\t\t\t\tconvert: converter,\n\t\t\t\tonArrayChange: true,\n\t\t\t\tflow: true,\n\t\t\t\ttagCtx: tagCtx,\n\t\t\t\ttagCtxs: [tagCtx],\n\t\t\t\t_is: \"tag\"\n\t\t\t});\n\t\t\targsLen = tagCtx.args.length;\n\t\t\tif (argsLen>1) {\n\t\t\t\tbindTo = tag.bindTo = [];\n\t\t\t\twhile (argsLen--) {\n\t\t\t\t\tbindTo.unshift(argsLen); // Bind to all the arguments - generate bindTo array: [0,1,2...]\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (linkCtx) {\n\t\t\t\tlinkCtx.tag = tag;\n\t\t\t\ttag.linkCtx = linkCtx;\n\t\t\t}\n\t\t\ttagCtx.ctx = extendCtx(tagCtx.ctx, (linkCtx ? linkCtx.view : view).ctx);\n\t\t\ttagHandlersFromProps(tag, tagCtx);\n\t\t}\n\t\ttag._er = onError && value;\n\t\ttag.ctx = tagCtx.ctx || tag.ctx || {};\n\t\ttagCtx.ctx = undefined;\n\t\tvalue = tag.cvtArgs()[0]; // If there is a convertBack but no convert, converter will be \"true\"\n\t\ttag._er = onError && value;\n\t} else {\n\t\tvalue = tagCtx.args[0];\n\t}\n\n\t// Call onRender (used by JsViews if present, to add binding annotations around rendered content)\n\tvalue = boundTag && view._.onRender\n\t\t? view._.onRender(value, view, tag)\n\t\t: value;\n\treturn value != undefined ? value : \"\";\n}\n\nfunction convertArgs(tagElse, bound) { // tag.cvtArgs() or tag.cvtArgs(tagElse?, true?)\n\tvar l, key, boundArgs, args, bindFrom, tag, converter,\n\t\ttagCtx = this;\n\n\tif (tagCtx.tagName) {\n\t\ttag = tagCtx;\n\t\ttagCtx = (tag.tagCtxs || [tagCtx])[tagElse||0];\n\t\tif (!tagCtx) {\n\t\t\treturn;\n\t\t}\n\t} else {\n\t\ttag = tagCtx.tag;\n\t}\n\n\tbindFrom = tag.bindFrom;\n\targs = tagCtx.args;\n\n\tif ((converter = tag.convert) && \"\" + converter === converter) {\n\t\tconverter = converter === \"true\"\n\t\t\t? undefined\n\t\t\t: (tagCtx.view.getRsc(\"converters\", converter) || error(\"Unknown converter: '\" + converter + \"'\"));\n\t}\n\n\tif (converter && !bound) { // If there is a converter, use a copy of the tagCtx.args array for rendering, and replace the args[0] in\n\t\targs = args.slice(); // the copied array with the converted value. But we do not modify the value of tag.tagCtx.args[0] (the original args array)\n\t}\n\tif (bindFrom) { // Get the values of the boundArgs\n\t\tboundArgs = [];\n\t\tl = bindFrom.length;\n\t\twhile (l--) {\n\t\t\tkey = bindFrom[l];\n\t\t\tboundArgs.unshift(argOrProp(tagCtx, key));\n\t\t}\n\t\tif (bound) {\n\t\t\targs = boundArgs; // Call to bndArgs() - returns the boundArgs\n\t\t}\n\t}\n\tif (converter) {\n\t\tconverter = converter.apply(tag, boundArgs || args);\n\t\tif (converter === undefined) {\n\t\t\treturn args; // Returning undefined from a converter is equivalent to not having a converter.\n\t\t}\n\t\tbindFrom = bindFrom || [0];\n\t\tl = bindFrom.length;\n\t\tif (!$isArray(converter) || (converter.arg0 !== false && (l === 1 || converter.length !== l || converter.arg0))) {\n\t\t\tconverter = [converter]; // Returning converter as first arg, even if converter value is an array\n\t\t\tbindFrom = [0];\n\t\t\tl = 1;\n\t\t}\n\t\tif (bound) {        // Call to bndArgs() - so apply converter to all boundArgs\n\t\t\targs = converter; // The array of values returned from the converter\n\t\t} else {            // Call to cvtArgs()\n\t\t\twhile (l--) {\n\t\t\t\tkey = bindFrom[l];\n\t\t\t\tif (+key === key) {\n\t\t\t\t\targs[key] = converter[l];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\treturn args;\n}\n\nfunction argOrProp(context, key) {\n\tcontext = context[+key === key ? \"args\" : \"props\"];\n\treturn context && context[key];\n}\n\nfunction convertBoundArgs(tagElse) { // tag.bndArgs()\n\treturn this.cvtArgs(tagElse, 1);\n}\n\n//=============\n// views.tag\n//=============\n\n/* view.getRsc() */\nfunction getResource(resourceType, itemName) {\n\tvar res, store,\n\t\tview = this;\n\tif (\"\" + itemName === itemName) {\n\t\twhile ((res === undefined) && view) {\n\t\t\tstore = view.tmpl && view.tmpl[resourceType];\n\t\t\tres = store && store[itemName];\n\t\t\tview = view.parent;\n\t\t}\n\t\treturn res || $views[resourceType][itemName];\n\t}\n}\n\nfunction renderTag(tagName, parentView, tmpl, tagCtxs, isUpdate, onError) {\n\tfunction bindToOrBindFrom(type) {\n\t\tvar bindArray = tag[type];\n\n\t\tif (bindArray !== undefined) {\n\t\t\tbindArray = $isArray(bindArray) ? bindArray : [bindArray];\n\t\t\tm = bindArray.length;\n\t\t\twhile (m--) {\n\t\t\t\tkey = bindArray[m];\n\t\t\t\tif (!isNaN(parseInt(key))) {\n\t\t\t\t\tbindArray[m] = parseInt(key); // Convert \"0\" to 0, etc.\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn bindArray || [0];\n\t}\n\n\tparentView = parentView || topView;\n\tvar tag, tagDef, template, tags, attr, parentTag, l, m, n, itemRet, tagCtx, tagCtxCtx, ctxPrm, bindTo, bindFrom, initVal,\n\t\tcontent, callInit, mapDef, thisMap, args, bdArgs, props, tagDataMap, contentCtx, key, bindFromLength, bindToLength, linkedElement, defaultCtx,\n\t\ti = 0,\n\t\tret = \"\",\n\t\tlinkCtx = parentView._lc || false, // For data-link=\"{myTag...}\"... See onDataLinkedTagChange\n\t\tctx = parentView.ctx,\n\t\tparentTmpl = tmpl || parentView.tmpl,\n\t\t// If tagCtxs is an integer, then it is the key for the compiled function to return the boundTag tagCtxs\n\t\tboundTag = typeof tagCtxs === \"number\" && parentView.tmpl.bnds[tagCtxs-1];\n\n\tif (tagName._is === \"tag\") {\n\t\ttag = tagName;\n\t\ttagName = tag.tagName;\n\t\ttagCtxs = tag.tagCtxs;\n\t\ttemplate = tag.template;\n\t} else {\n\t\ttagDef = parentView.getRsc(\"tags\", tagName) || error(\"Unknown tag: {{\" + tagName + \"}} \");\n\t\ttemplate = tagDef.template;\n\t}\n\tif (onError === undefined && boundTag && (boundTag._lr = (tagDef.lateRender && boundTag._lr!== false || boundTag._lr))) {\n\t\tonError = \"\"; // If lateRender, set temporary onError, to skip initial rendering (and render just \"\")\n\t}\n\tif (onError !== undefined) {\n\t\tret += onError;\n\t\ttagCtxs = onError = [{props: {}, args: [], params: {props:{}}}];\n\t} else if (boundTag) {\n\t\ttagCtxs = boundTag(parentView.data, parentView, $sub);\n\t}\n\n\tl = tagCtxs.length;\n\tfor (; i < l; i++) {\n\t\ttagCtx = tagCtxs[i];\n\t\tcontent = tagCtx.tmpl;\n\t\tif (!linkCtx || !linkCtx.tag || i && !linkCtx.tag.inline || tag._er || content && +content===content) {\n\t\t\t// Initialize tagCtx\n\t\t\t// For block tags, tagCtx.tmpl is an integer > 0\n\t\t\tif (content && parentTmpl.tmpls) {\n\t\t\t\ttagCtx.tmpl = tagCtx.content = parentTmpl.tmpls[content - 1]; // Set the tmpl property to the content of the block tag\n\t\t\t}\n\t\t\ttagCtx.index = i;\n\t\t\ttagCtx.ctxPrm = contextParameter;\n\t\t\ttagCtx.render = renderContent;\n\t\t\ttagCtx.cvtArgs = convertArgs;\n\t\t\ttagCtx.bndArgs = convertBoundArgs;\n\t\t\ttagCtx.view = parentView;\n\t\t\ttagCtx.ctx = extendCtx(extendCtx(tagCtx.ctx, tagDef && tagDef.ctx), ctx); // Clone and extend parentView.ctx\n\t\t}\n\t\tif (tmpl = tagCtx.props.tmpl) {\n\t\t\t// If the tmpl property is overridden, set the value (when initializing, or, in case of binding: ^tmpl=..., when updating)\n\t\t\ttagCtx.tmpl = parentView._getTmpl(tmpl);\n\t\t\ttagCtx.content = tagCtx.content || tagCtx.tmpl;\n\t\t}\n\n\t\tif (!tag) {\n\t\t\t// This will only be hit for initial tagCtx (not for {{else}}) - if the tag instance does not exist yet\n\t\t\t// If the tag has not already been instantiated, we will create a new instance.\n\t\t\t// ~tag will access the tag, even within the rendering of the template content of this tag.\n\t\t\t// From child/descendant tags, can access using ~tag.parent, or ~parentTags.tagName\n\t\t\ttag = new tagDef._ctr();\n\t\t\tcallInit = !!tag.init;\n\n\t\t\ttag.parent = parentTag = ctx && ctx.tag;\n\t\t\ttag.tagCtxs = tagCtxs;\n\n\t\t\tif (linkCtx) {\n\t\t\t\ttag.inline = false;\n\t\t\t\tlinkCtx.tag = tag;\n\t\t\t}\n\t\t\ttag.linkCtx = linkCtx;\n\t\t\tif (tag._.bnd = boundTag || linkCtx.fn) {\n\t\t\t\t// Bound if {^{tag...}} or data-link=\"{tag...}\"\n\t\t\t\ttag._.ths = tagCtx.params.props[\"this\"]; // Tag has a this=expr binding, to get javascript reference to tag instance\n\t\t\t\ttag._.lt = tagCtxs.lt; // If a late path @some.path has not returned @some object, mark tag as late\n\t\t\t\ttag._.arrVws = {};\n\t\t\t} else if (tag.dataBoundOnly) {\n\t\t\t\terror(tagName + \" must be data-bound:\\n{^{\" + tagName + \"}}\");\n\t\t\t}\n\t\t\t//TODO better perf for childTags() - keep child tag.tags array, (and remove child, when disposed)\n\t\t\t// tag.tags = [];\n\t\t} else if (linkCtx && linkCtx.fn._lr) {\n\t\t\tcallInit = !!tag.init;\n\t\t}\n\t\ttagDataMap = tag.dataMap;\n\n\t\ttagCtx.tag = tag;\n\t\tif (tagDataMap && tagCtxs) {\n\t\t\ttagCtx.map = tagCtxs[i].map; // Copy over the compiled map instance from the previous tagCtxs to the refreshed ones\n\t\t}\n\t\tif (!tag.flow) {\n\t\t\ttagCtxCtx = tagCtx.ctx = tagCtx.ctx || {};\n\n\t\t\t// tags hash: tag.ctx.tags, merged with parentView.ctx.tags,\n\t\t\ttags = tag.parents = tagCtxCtx.parentTags = ctx && extendCtx(tagCtxCtx.parentTags, ctx.parentTags) || {};\n\t\t\tif (parentTag) {\n\t\t\t\ttags[parentTag.tagName] = parentTag;\n\t\t\t\t//TODO better perf for childTags: parentTag.tags.push(tag);\n\t\t\t}\n\t\t\ttags[tag.tagName] = tagCtxCtx.tag = tag;\n\t\t\ttagCtxCtx.tagCtx = tagCtx;\n\t\t}\n\t}\n\tif (!(tag._er = onError)) {\n\t\ttagHandlersFromProps(tag, tagCtxs[0]);\n\t\ttag.rendering = {rndr: tag.rendering}; // Provide object for state during render calls to tag and elses. (Used by {{if}} and {{for}}...)\n\t\tfor (i = 0; i < l; i++) { // Iterate tagCtx for each {{else}} block\n\t\t\ttagCtx = tag.tagCtx = tagCtxs[i];\n\t\t\tprops = tagCtx.props;\n\t\t\ttag.ctx = tagCtx.ctx;\n\n\t\t\tif (!i) {\n\t\t\t\tif (callInit) {\n\t\t\t\t\ttag.init(tagCtx, linkCtx, tag.ctx);\n\t\t\t\t\tcallInit = undefined;\n\t\t\t\t}\n\t\t\t\tif (!tagCtx.args.length && tagCtx.argDefault !== false && tag.argDefault !== false) {\n\t\t\t\t\ttagCtx.args = args = [tagCtx.view.data]; // Missing first arg defaults to the current data context\n\t\t\t\t\ttagCtx.params.args = [\"#data\"];\n\t\t\t\t}\n\n\t\t\t\tbindTo = bindToOrBindFrom(\"bindTo\");\n\n\t\t\t\tif (tag.bindTo !== undefined) {\n\t\t\t\t\ttag.bindTo = bindTo;\n\t\t\t\t}\n\n\t\t\t\tif (tag.bindFrom !== undefined) {\n\t\t\t\t\ttag.bindFrom = bindToOrBindFrom(\"bindFrom\");\n\t\t\t\t} else if (tag.bindTo) {\n\t\t\t\t\ttag.bindFrom = tag.bindTo = bindTo;\n\t\t\t\t}\n\t\t\t\tbindFrom = tag.bindFrom || bindTo;\n\n\t\t\t\tbindToLength = bindTo.length;\n\t\t\t\tbindFromLength = bindFrom.length;\n\n\t\t\t\tif (tag._.bnd && (linkedElement = tag.linkedElement)) {\n\t\t\t\t\ttag.linkedElement = linkedElement = $isArray(linkedElement) ? linkedElement: [linkedElement];\n\n\t\t\t\t\tif (bindToLength !== linkedElement.length) {\n\t\t\t\t\t\terror(\"linkedElement not same length as bindTo\");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (linkedElement = tag.linkedCtxParam) {\n\t\t\t\t\ttag.linkedCtxParam = linkedElement = $isArray(linkedElement) ? linkedElement: [linkedElement];\n\n\t\t\t\t\tif (bindFromLength !== linkedElement.length) {\n\t\t\t\t\t\terror(\"linkedCtxParam not same length as bindFrom/bindTo\");\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (bindFrom) {\n\t\t\t\t\ttag._.fromIndex = {}; // Hash of bindFrom index which has same path value as bindTo index. fromIndex = tag._.fromIndex[toIndex]\n\t\t\t\t\ttag._.toIndex = {}; // Hash of bindFrom index which has same path value as bindTo index. fromIndex = tag._.fromIndex[toIndex]\n\t\t\t\t\tn = bindFromLength;\n\t\t\t\t\twhile (n--) {\n\t\t\t\t\t\tkey = bindFrom[n];\n\t\t\t\t\t\tm = bindToLength;\n\t\t\t\t\t\twhile (m--) {\n\t\t\t\t\t\t\tif (key === bindTo[m]) {\n\t\t\t\t\t\t\t\ttag._.fromIndex[m] = n;\n\t\t\t\t\t\t\t\ttag._.toIndex[n] = m;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (linkCtx) {\n\t\t\t\t\t// Set attr on linkCtx to ensure outputting to the correct target attribute.\n\t\t\t\t\t// Setting either linkCtx.attr or this.attr in the init() allows per-instance choice of target attrib.\n\t\t\t\t\tlinkCtx.attr = tag.attr = linkCtx.attr || tag.attr || linkCtx._dfAt;\n\t\t\t\t}\n\t\t\t\tattr = tag.attr;\n\t\t\t\ttag._.noVws = attr && attr !== HTML;\n\t\t\t}\n\t\t\targs = tag.cvtArgs(i);\n\t\t\tif (tag.linkedCtxParam) {\n\t\t\t\tbdArgs = tag.cvtArgs(i, 1);\n\t\t\t\tm = bindFromLength;\n\t\t\t\tdefaultCtx = tag.constructor.prototype.ctx;\n\t\t\t\twhile (m--) {\n\t\t\t\t\tif (ctxPrm = tag.linkedCtxParam[m]) {\n\t\t\t\t\t\tkey = bindFrom[m];\n\t\t\t\t\t\tinitVal = bdArgs[m];\n\t\t\t\t\t\t// Create tag contextual parameter\n\t\t\t\t\t\ttagCtx.ctx[ctxPrm] = $sub._cp(\n\t\t\t\t\t\t\tdefaultCtx && initVal === undefined ? defaultCtx[ctxPrm]: initVal,\n\t\t\t\t\t\t\tinitVal !== undefined && argOrProp(tagCtx.params, key),\n\t\t\t\t\t\t\ttagCtx.view,\n\t\t\t\t\t\t\ttag._.bnd && {tag: tag, cvt: tag.convert, ind: m, tagElse: i}\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ((mapDef = props.dataMap || tagDataMap) && (args.length || props.dataMap)) {\n\t\t\t\tthisMap = tagCtx.map;\n\t\t\t\tif (!thisMap || thisMap.src !== args[0] || isUpdate) {\n\t\t\t\t\tif (thisMap && thisMap.src) {\n\t\t\t\t\t\tthisMap.unmap(); // only called if observable map - not when only used in JsRender, e.g. by {{props}}\n\t\t\t\t\t}\n\t\t\t\t\tmapDef.map(args[0], tagCtx, thisMap, !tag._.bnd);\n\t\t\t\t\tthisMap = tagCtx.map;\n\t\t\t\t}\n\t\t\t\targs = [thisMap.tgt];\n\t\t\t}\n\n\t\t\titemRet = undefined;\n\t\t\tif (tag.render) {\n\t\t\t\titemRet = tag.render.apply(tag, args);\n\t\t\t\tif (parentView.linked && itemRet && !rWrappedInViewMarker.test(itemRet)) {\n\t\t\t\t\t// When a tag renders content from the render method, with data linking then we need to wrap with view markers, if absent,\n\t\t\t\t\t// to provide a contentView for the tag, which will correctly dispose bindings if deleted. The 'tmpl' for this view will\n\t\t\t\t\t// be a dumbed-down template which will always return the itemRet string (no matter what the data is). The itemRet string\n\t\t\t\t\t// is not compiled as template markup, so can include \"{{\" or \"}}\" without triggering syntax errors\n\t\t\t\t\ttmpl = { // 'Dumbed-down' template which always renders 'static' itemRet string\n\t\t\t\t\t\tlinks: []\n\t\t\t\t\t};\n\t\t\t\t\ttmpl.render = tmpl.fn = function() {\n\t\t\t\t\t\treturn itemRet;\n\t\t\t\t\t};\n\t\t\t\t\titemRet = renderWithViews(tmpl, parentView.data, undefined, true, parentView, undefined, undefined, tag);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!args.length) {\n\t\t\t\targs = [parentView]; // no arguments - (e.g. {{else}}) get data context from view.\n\t\t\t}\n\t\t\tif (itemRet === undefined) {\n\t\t\t\tcontentCtx = args[0]; // Default data context for wrapped block content is the first argument\n\t\t\t\tif (tag.contentCtx) { // Set tag.contentCtx to true, to inherit parent context, or to a function to provide alternate context.\n\t\t\t\t\tcontentCtx = tag.contentCtx === true ? parentView : tag.contentCtx(contentCtx);\n\t\t\t\t}\n\t\t\t\titemRet = tagCtx.render(contentCtx, true) || (isUpdate ? undefined : \"\");\n\t\t\t}\n\t\t\tret = ret\n\t\t\t\t? ret + (itemRet || \"\")\n\t\t\t\t: itemRet !== undefined\n\t\t\t\t\t? \"\" + itemRet\n\t\t\t\t\t: undefined; // If no return value from render, and no template/content tagCtx.render(...), return undefined\n\t\t}\n\t\ttag.rendering = tag.rendering.rndr; // Remove tag.rendering object (if this is outermost render call. (In case of nested calls)\n\t}\n\ttag.tagCtx = tagCtxs[0];\n\ttag.ctx = tag.tagCtx.ctx;\n\n\tif (tag._.noVws && tag.inline) {\n\t\t// inline tag with attr set to \"text\" will insert HTML-encoded content - as if it was element-based innerText\n\t\tret = attr === \"text\"\n\t\t\t? $converters.html(ret)\n\t\t\t: \"\";\n\t}\n\treturn boundTag && parentView._.onRender\n\t\t// Call onRender (used by JsViews if present, to add binding annotations around rendered content)\n\t\t? parentView._.onRender(ret, parentView, tag)\n\t\t: ret;\n}\n\n//=================\n// View constructor\n//=================\n\nfunction View(context, type, parentView, data, template, key, onRender, contentTmpl) {\n\t// Constructor for view object in view hierarchy. (Augmented by JsViews if JsViews is loaded)\n\tvar views, parentView_, tag, self_,\n\t\tself = this,\n\t\tisArray = type === \"array\";\n\t\t// If the data is an array, this is an 'array view' with a views array for each child 'item view'\n\t\t// If the data is not an array, this is an 'item view' with a views 'hash' object for any child nested views\n\n\tself.content = contentTmpl;\n\tself.views = isArray ? [] : {};\n\tself.data = data;\n\tself.tmpl = template;\n\tself_ = self._ = {\n\t\tkey: 0,\n\t\t// ._.useKey is non zero if is not an 'array view' (owning a data array). Use this as next key for adding to child views hash\n\t\tuseKey: isArray ? 0 : 1,\n\t\tid: \"\" + viewId++,\n\t\tonRender: onRender,\n\t\tbnds: {}\n\t};\n\tself.linked = !!onRender;\n\tself.type = type || \"top\";\n\tif (type) {\n\t\tself.cache = {_ct: $subSettings._cchCt}; // Used for caching results of computed properties and helpers (view.getCache)\n\t}\n\n\tif (!parentView || parentView.type === \"top\") {\n\t\t(self.ctx = context || {}).root = self.data;\n\t}\n\n\tif (self.parent = parentView) {\n\t\tself.root = parentView.root || self; // view whose parent is top view\n\t\tviews = parentView.views;\n\t\tparentView_ = parentView._;\n\t\tself.isTop = parentView_.scp; // Is top content view of a link(\"#container\", ...) call\n\t\tself.scope = (!context.tag || context.tag === parentView.ctx.tag) && !self.isTop && parentView.scope || self;\n\t\t// Scope for contextParams - closest non flow tag ancestor or root view\n\t\tif (parentView_.useKey) {\n\t\t\t// Parent is not an 'array view'. Add this view to its views object\n\t\t\t// self._key = is the key in the parent view hash\n\t\t\tviews[self_.key = \"_\" + parentView_.useKey++] = self;\n\t\t\tself.index = indexStr;\n\t\t\tself.getIndex = getNestedIndex;\n\t\t} else if (views.length === (self_.key = self.index = key)) { // Parent is an 'array view'. Add this view to its views array\n\t\t\tviews.push(self); // Adding to end of views array. (Using push when possible - better perf than splice)\n\t\t} else {\n\t\t\tviews.splice(key, 0, self); // Inserting in views array\n\t\t}\n\t\t// If no context was passed in, use parent context\n\t\t// If context was passed in, it should have been merged already with parent context\n\t\tself.ctx = context || parentView.ctx;\n\t} else if (type) {\n\t\tself.root = self; // view whose parent is top view\n\t}\n}\n\nView.prototype = {\n\tget: getView,\n\tgetIndex: getIndex,\n\tctxPrm: contextParameter,\n\tgetRsc: getResource,\n\t_getTmpl: getTemplate,\n\t_getOb: getPathObject,\n\tgetCache: function(key) { // Get cached value of computed value\n\t\tif ($subSettings._cchCt > this.cache._ct) {\n\t\t\tthis.cache = {_ct: $subSettings._cchCt};\n\t\t}\n\t\treturn this.cache[key] !== undefined ? this.cache[key] : (this.cache[key] = cpFnStore[key](this.data, this, $sub));\n\t},\n\t_is: \"view\"\n};\n\n//====================================================\n// Registration\n//====================================================\n\nfunction compileChildResources(parentTmpl) {\n\tvar storeName, storeNames, resources;\n\tfor (storeName in jsvStores) {\n\t\tstoreNames = storeName + \"s\";\n\t\tif (parentTmpl[storeNames]) {\n\t\t\tresources = parentTmpl[storeNames];        // Resources not yet compiled\n\t\t\tparentTmpl[storeNames] = {};               // Remove uncompiled resources\n\t\t\t$views[storeNames](resources, parentTmpl); // Add back in the compiled resources\n\t\t}\n\t}\n}\n\n//===============\n// compileTag\n//===============\n\nfunction compileTag(name, tagDef, parentTmpl) {\n\tvar tmpl, baseTag, prop,\n\t\tcompiledDef = new $sub._tg();\n\n\tfunction Tag() {\n\t\tvar tag = this;\n\t\ttag._ = {\n\t\t\tunlinked: true\n\t\t};\n\t\ttag.inline = true;\n\t\ttag.tagName = name;\n\t}\n\n\tif ($isFunction(tagDef)) {\n\t\t// Simple tag declared as function. No presenter instantation.\n\t\ttagDef = {\n\t\t\tdepends: tagDef.depends,\n\t\t\trender: tagDef\n\t\t};\n\t} else if (\"\" + tagDef === tagDef) {\n\t\ttagDef = {template: tagDef};\n\t}\n\n\tif (baseTag = tagDef.baseTag) {\n\t\ttagDef.flow = !!tagDef.flow; // Set flow property, so defaults to false even if baseTag has flow=true\n\t\tbaseTag = \"\" + baseTag === baseTag\n\t\t\t? (parentTmpl && parentTmpl.tags[baseTag] || $tags[baseTag])\n\t\t\t: baseTag;\n\t\tif (!baseTag) {\n\t\t\terror('baseTag: \"' + tagDef.baseTag + '\" not found');\n\t\t}\n\t\tcompiledDef = $extend(compiledDef, baseTag);\n\n\t\tfor (prop in tagDef) {\n\t\t\tcompiledDef[prop] = getMethod(baseTag[prop], tagDef[prop]);\n\t\t}\n\t} else {\n\t\tcompiledDef = $extend(compiledDef, tagDef);\n\t}\n\n\t// Tag declared as object, used as the prototype for tag instantiation (control/presenter)\n\tif ((tmpl = compiledDef.template) !== undefined) {\n\t\tcompiledDef.template = \"\" + tmpl === tmpl ? ($templates[tmpl] || $templates(tmpl)) : tmpl;\n\t}\n\t(Tag.prototype = compiledDef).constructor = compiledDef._ctr = Tag;\n\n\tif (parentTmpl) {\n\t\tcompiledDef._parentTmpl = parentTmpl;\n\t}\n\treturn compiledDef;\n}\n\nfunction baseApply(args) {\n\t// In derived method (or handler declared declaratively as in {{:foo onChange=~fooChanged}} can call base method,\n\t// using this.baseApply(arguments) (Equivalent to this._superApply(arguments) in jQuery UI)\n\treturn this.base.apply(this, args);\n}\n\n//===============\n// compileTmpl\n//===============\n\nfunction compileTmpl(name, tmpl, parentTmpl, options) {\n\t// tmpl is either a template object, a selector for a template script block, or the name of a compiled template\n\n\t//==== nested functions ====\n\tfunction lookupTemplate(value) {\n\t\t// If value is of type string - treat as selector, or name of compiled template\n\t\t// Return the template object, if already compiled, or the markup string\n\t\tvar currentName, tmpl;\n\t\tif ((\"\" + value === value) || value.nodeType > 0 && (elem = value)) {\n\t\t\tif (!elem) {\n\t\t\t\tif (/^\\.?\\/[^\\\\:*?\"<>]*$/.test(value)) {\n\t\t\t\t\t// value=\"./some/file.html\" (or \"/some/file.html\")\n\t\t\t\t\t// If the template is not named, use \"./some/file.html\" as name.\n\t\t\t\t\tif (tmpl = $templates[name = name || value]) {\n\t\t\t\t\t\tvalue = tmpl;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// BROWSER-SPECIFIC CODE (not on Node.js):\n\t\t\t\t\t\t// Look for server-generated script block with id \"./some/file.html\"\n\t\t\t\t\t\telem = document.getElementById(value);\n\t\t\t\t\t}\n\t\t\t\t} else if (value.charAt(0) === \"#\") {\n\t\t\t\t\telem = document.getElementById(value.slice(1));\n\t\t\t\t} else if ($.fn && !$sub.rTmpl.test(value)) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\telem = $(value, document)[0]; // if jQuery is loaded, test for selector returning elements, and get first element\n\t\t\t\t\t} catch (e) {}\n\t\t\t\t}// END BROWSER-SPECIFIC CODE\n\t\t\t} //BROWSER-SPECIFIC CODE\n\t\t\tif (elem) {\n\t\t\t\tif (elem.tagName !== \"SCRIPT\") {\n\t\t\t\t\terror(value + \": Use script block, not \" + elem.tagName);\n\t\t\t\t}\n\t\t\t\tif (options) {\n\t\t\t\t\t// We will compile a new template using the markup in the script element\n\t\t\t\t\tvalue = elem.innerHTML;\n\t\t\t\t} else {\n\t\t\t\t\t// We will cache a single copy of the compiled template, and associate it with the name\n\t\t\t\t\t// (renaming from a previous name if there was one).\n\t\t\t\t\tcurrentName = elem.getAttribute(tmplAttr);\n\t\t\t\t\tif (currentName) {\n\t\t\t\t\t\tif (currentName !== jsvTmpl) {\n\t\t\t\t\t\t\tvalue = $templates[currentName];\n\t\t\t\t\t\t\tdelete $templates[currentName];\n\t\t\t\t\t\t} else if ($.fn) {\n\t\t\t\t\t\t\tvalue = $.data(elem)[jsvTmpl]; // Get cached compiled template\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (!currentName || !value) { // Not yet compiled, or cached version lost\n\t\t\t\t\t\tname = name || ($.fn ? jsvTmpl : value);\n\t\t\t\t\t\tvalue = compileTmpl(name, elem.innerHTML, parentTmpl, options);\n\t\t\t\t\t}\n\t\t\t\t\tvalue.tmplName = name = name || currentName;\n\t\t\t\t\tif (name !== jsvTmpl) {\n\t\t\t\t\t\t$templates[name] = value;\n\t\t\t\t\t}\n\t\t\t\t\telem.setAttribute(tmplAttr, name);\n\t\t\t\t\tif ($.fn) {\n\t\t\t\t\t\t$.data(elem, jsvTmpl, value);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} // END BROWSER-SPECIFIC CODE\n\t\t\telem = undefined;\n\t\t} else if (!value.fn) {\n\t\t\tvalue = undefined;\n\t\t\t// If value is not a string. HTML element, or compiled template, return undefined\n\t\t}\n\t\treturn value;\n\t}\n\n\tvar elem, compiledTmpl,\n\t\ttmplOrMarkup = tmpl = tmpl || \"\";\n\t$sub._html = $converters.html;\n\n\t//==== Compile the template ====\n\tif (options === 0) {\n\t\toptions = undefined;\n\t\ttmplOrMarkup = lookupTemplate(tmplOrMarkup); // Top-level compile so do a template lookup\n\t}\n\n\t// If options, then this was already compiled from a (script) element template declaration.\n\t// If not, then if tmpl is a template object, use it for options\n\toptions = options || (tmpl.markup\n\t\t? tmpl.bnds\n\t\t\t? $extend({}, tmpl)\n\t\t\t: tmpl\n\t\t: {}\n\t);\n\n\toptions.tmplName = options.tmplName || name || \"unnamed\";\n\tif (parentTmpl) {\n\t\toptions._parentTmpl = parentTmpl;\n\t}\n\t// If tmpl is not a markup string or a selector string, then it must be a template object\n\t// In that case, get it from the markup property of the object\n\tif (!tmplOrMarkup && tmpl.markup && (tmplOrMarkup = lookupTemplate(tmpl.markup)) && tmplOrMarkup.fn) {\n\t\t// If the string references a compiled template object, need to recompile to merge any modified options\n\t\ttmplOrMarkup = tmplOrMarkup.markup;\n\t}\n\tif (tmplOrMarkup !== undefined) {\n\t\tif (tmplOrMarkup.render || tmpl.render) {\n\t\t\t// tmpl is already compiled, so use it\n\t\t\tif (tmplOrMarkup.tmpls) {\n\t\t\t\tcompiledTmpl = tmplOrMarkup;\n\t\t\t}\n\t\t} else {\n\t\t\t// tmplOrMarkup is a markup string, not a compiled template\n\t\t\t// Create template object\n\t\t\ttmpl = tmplObject(tmplOrMarkup, options);\n\t\t\t// Compile to AST and then to compiled function\n\t\t\ttmplFn(tmplOrMarkup.replace(rEscapeQuotes, \"\\\\$&\"), tmpl);\n\t\t}\n\t\tif (!compiledTmpl) {\n\t\t\tcompiledTmpl = $extend(function() {\n\t\t\t\treturn compiledTmpl.render.apply(compiledTmpl, arguments);\n\t\t\t}, tmpl);\n\n\t\t\tcompileChildResources(compiledTmpl);\n\t\t}\n\t\treturn compiledTmpl;\n\t}\n}\n\n//==== /end of function compileTmpl ====\n\n//=================\n// compileViewModel\n//=================\n\nfunction getDefaultVal(defaultVal, data) {\n\treturn $isFunction(defaultVal)\n\t\t? defaultVal.call(data)\n\t\t: defaultVal;\n}\n\nfunction addParentRef(ob, ref, parent) {\n\tObject.defineProperty(ob, ref, {\n\t\tvalue: parent,\n\t\tconfigurable: true\n\t});\n}\n\nfunction compileViewModel(name, type) {\n\tvar i, constructor, parent,\n\t\tviewModels = this,\n\t\tgetters = type.getters,\n\t\textend = type.extend,\n\t\tid = type.id,\n\t\tproto = $.extend({\n\t\t\t_is: name || \"unnamed\",\n\t\t\tunmap: unmap,\n\t\t\tmerge: merge\n\t\t}, extend),\n\t\targs = \"\",\n\t\tcnstr = \"\",\n\t\tgetterCount = getters ? getters.length : 0,\n\t\t$observable = $.observable,\n\t\tgetterNames = {};\n\n\tfunction JsvVm(args) {\n\t\tconstructor.apply(this, args);\n\t}\n\n\tfunction vm() {\n\t\treturn new JsvVm(arguments);\n\t}\n\n\tfunction iterate(data, action) {\n\t\tvar getterType, defaultVal, prop, ob, parentRef,\n\t\t\tj = 0;\n\t\tfor (; j < getterCount; j++) {\n\t\t\tprop = getters[j];\n\t\t\tgetterType = undefined;\n\t\t\tif (prop + \"\" !== prop) {\n\t\t\t\tgetterType = prop;\n\t\t\t\tprop = getterType.getter;\n\t\t\t\tparentRef = getterType.parentRef;\n\t\t\t}\n\t\t\tif ((ob = data[prop]) === undefined && getterType && (defaultVal = getterType.defaultVal) !== undefined) {\n\t\t\t\tob = getDefaultVal(defaultVal, data);\n\t\t\t}\n\t\t\taction(ob, getterType && viewModels[getterType.type], prop, parentRef);\n\t\t}\n\t}\n\n\tfunction map(data) {\n\t\tdata = data + \"\" === data\n\t\t\t? JSON.parse(data) // Accept JSON string\n\t\t\t: data;            // or object/array\n\t\tvar l, prop, childOb, parentRef,\n\t\t\tj = 0,\n\t\t\tob = data,\n\t\t\tarr = [];\n\n\t\tif ($isArray(data)) {\n\t\t\tdata = data || [];\n\t\t\tl = data.length;\n\t\t\tfor (; j<l; j++) {\n\t\t\t\tarr.push(this.map(data[j]));\n\t\t\t}\n\t\t\tarr._is = name;\n\t\t\tarr.unmap = unmap;\n\t\t\tarr.merge = merge;\n\t\t\treturn arr;\n\t\t}\n\n\t\tif (data) {\n\t\t\titerate(data, function(ob, viewModel) {\n\t\t\t\tif (viewModel) { // Iterate to build getters arg array (value, or mapped value)\n\t\t\t\t\tob = viewModel.map(ob);\n\t\t\t\t}\n\t\t\t\tarr.push(ob);\n\t\t\t});\n\t\t\tob = this.apply(this, arr); // Instantiate this View Model, passing getters args array to constructor\n\t\t\tj = getterCount;\n\t\t\twhile (j--) {\n\t\t\t\tchildOb = arr[j];\n\t\t\t\tparentRef = getters[j].parentRef;\n\t\t\t\tif (parentRef && childOb && childOb.unmap) {\n\t\t\t\t\tif ($isArray(childOb)) {\n\t\t\t\t\t\tl = childOb.length;\n\t\t\t\t\t\twhile (l--) {\n\t\t\t\t\t\t\taddParentRef(childOb[l], parentRef, ob);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\taddParentRef(childOb, parentRef, ob);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tfor (prop in data) { // Copy over any other properties. that are not get/set properties\n\t\t\t\tif (prop !== $expando && !getterNames[prop]) {\n\t\t\t\t\tob[prop] = data[prop];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn ob;\n\t}\n\n\tfunction merge(data, parent, parentRef) {\n\t\tdata = data + \"\" === data\n\t\t\t? JSON.parse(data) // Accept JSON string\n\t\t\t: data;            // or object/array\n\n\t\tvar j, l, m, prop, mod, found, assigned, ob, newModArr, childOb,\n\t\t\tk = 0,\n\t\t\tmodel = this;\n\n\t\tif ($isArray(model)) {\n\t\t\tassigned = {};\n\t\t\tnewModArr = [];\n\t\t\tl = data.length;\n\t\t\tm = model.length;\n\t\t\tfor (; k<l; k++) {\n\t\t\t\tob = data[k];\n\t\t\t\tfound = false;\n\t\t\t\tfor (j=0; j<m && !found; j++) {\n\t\t\t\t\tif (assigned[j]) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tmod = model[j];\n\n\t\t\t\t\tif (id) {\n\t\t\t\t\t\tassigned[j] = found = id + \"\" === id\n\t\t\t\t\t\t? (ob[id] && (getterNames[id] ? mod[id]() : mod[id]) === ob[id])\n\t\t\t\t\t\t: id(mod, ob);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (found) {\n\t\t\t\t\tmod.merge(ob);\n\t\t\t\t\tnewModArr.push(mod);\n\t\t\t\t} else {\n\t\t\t\t\tnewModArr.push(childOb = vm.map(ob));\n\t\t\t\t\tif (parentRef) {\n\t\t\t\t\t\taddParentRef(childOb, parentRef, parent);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ($observable) {\n\t\t\t\t$observable(model).refresh(newModArr, true);\n\t\t\t} else {\n\t\t\t\tmodel.splice.apply(model, [0, model.length].concat(newModArr));\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\t\titerate(data, function(ob, viewModel, getter, parentRef) {\n\t\t\tif (viewModel) {\n\t\t\t\tmodel[getter]().merge(ob, model, parentRef); // Update typed property\n\t\t\t} else if (model[getter]() !== ob) {\n\t\t\t\tmodel[getter](ob); // Update non-typed property\n\t\t\t}\n\t\t});\n\t\tfor (prop in data) {\n\t\t\tif (prop !== $expando && !getterNames[prop]) {\n\t\t\t\tmodel[prop] = data[prop];\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction unmap() {\n\t\tvar ob, prop, getterType, arr, value,\n\t\t\tk = 0,\n\t\t\tmodel = this;\n\n\t\tfunction unmapArray(modelArr) {\n\t\t\tvar arr = [],\n\t\t\t\ti = 0,\n\t\t\t\tl = modelArr.length;\n\t\t\tfor (; i<l; i++) {\n\t\t\t\tarr.push(modelArr[i].unmap());\n\t\t\t}\n\t\t\treturn arr;\n\t\t}\n\n\t\tif ($isArray(model)) {\n\t\t\treturn unmapArray(model);\n\t\t}\n\t\tob = {};\n\t\tfor (; k < getterCount; k++) {\n\t\t\tprop = getters[k];\n\t\t\tgetterType = undefined;\n\t\t\tif (prop + \"\" !== prop) {\n\t\t\t\tgetterType = prop;\n\t\t\t\tprop = getterType.getter;\n\t\t\t}\n\t\t\tvalue = model[prop]();\n\t\t\tob[prop] = getterType && value && viewModels[getterType.type]\n\t\t\t\t? $isArray(value)\n\t\t\t\t\t? unmapArray(value)\n\t\t\t\t\t: value.unmap()\n\t\t\t\t: value;\n\t\t}\n\t\tfor (prop in model) {\n\t\t\tif (model.hasOwnProperty(prop) && (prop.charAt(0) !== \"_\" || !getterNames[prop.slice(1)]) && prop !== $expando && !$isFunction(model[prop])) {\n\t\t\t\tob[prop] = model[prop];\n\t\t\t}\n\t\t}\n\t\treturn ob;\n\t}\n\n\tJsvVm.prototype = proto;\n\n\tfor (i=0; i < getterCount; i++) {\n\t\t(function(getter) {\n\t\t\tgetter = getter.getter || getter;\n\t\t\tgetterNames[getter] = i+1;\n\t\t\tvar privField = \"_\" + getter;\n\n\t\t\targs += (args ? \",\" : \"\") + getter;\n\t\t\tcnstr += \"this.\" + privField + \" = \" + getter + \";\\n\";\n\t\t\tproto[getter] = proto[getter] || function(val) {\n\t\t\t\tif (!arguments.length) {\n\t\t\t\t\treturn this[privField]; // If there is no argument, use as a getter\n\t\t\t\t}\n\t\t\t\tif ($observable) {\n\t\t\t\t\t$observable(this).setProperty(getter, val);\n\t\t\t\t} else {\n\t\t\t\t\tthis[privField] = val;\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tif ($observable) {\n\t\t\t\tproto[getter].set = proto[getter].set || function(val) {\n\t\t\t\t\tthis[privField] = val; // Setter called by observable property change\n\t\t\t\t};\n\t\t\t}\n\t\t})(getters[i]);\n\t}\n\n\t// Constructor for new viewModel instance.\n\tcnstr = new Function(args, cnstr);\n\n\tconstructor = function() {\n\t\tcnstr.apply(this, arguments);\n\t\t// Pass additional parentRef str and parent obj to have a parentRef pointer on instance\n\t\tif (parent = arguments[getterCount + 1]) {\n\t\t\taddParentRef(this, arguments[getterCount], parent);\n\t\t}\n\t};\n\n\tconstructor.prototype = proto;\n\tproto.constructor = constructor;\n\n\tvm.map = map;\n\tvm.getters = getters;\n\tvm.extend = extend;\n\tvm.id = id;\n\treturn vm;\n}\n\nfunction tmplObject(markup, options) {\n\t// Template object constructor\n\tvar htmlTag,\n\t\twrapMap = $subSettingsAdvanced._wm || {}, // Only used in JsViews. Otherwise empty: {}\n\t\ttmpl = {\n\t\t\ttmpls: [],\n\t\t\tlinks: {}, // Compiled functions for link expressions\n\t\t\tbnds: [],\n\t\t\t_is: \"template\",\n\t\t\trender: renderContent\n\t\t};\n\n\tif (options) {\n\t\ttmpl = $extend(tmpl, options);\n\t}\n\n\ttmpl.markup = markup;\n\tif (!tmpl.htmlTag) {\n\t\t// Set tmpl.tag to the top-level HTML tag used in the template, if any...\n\t\thtmlTag = rFirstElem.exec(markup);\n\t\ttmpl.htmlTag = htmlTag ? htmlTag[1].toLowerCase() : \"\";\n\t}\n\thtmlTag = wrapMap[tmpl.htmlTag];\n\tif (htmlTag && htmlTag !== wrapMap.div) {\n\t\t// When using JsViews, we trim templates which are inserted into HTML contexts where text nodes are not rendered (i.e. not 'Phrasing Content').\n\t\t// Currently not trimmed for <li> tag. (Not worth adding perf cost)\n\t\ttmpl.markup = $.trim(tmpl.markup);\n\t}\n\n\treturn tmpl;\n}\n\n//==============\n// registerStore\n//==============\n\n/**\n* Internal. Register a store type (used for template, tags, helpers, converters)\n*/\nfunction registerStore(storeName, storeSettings) {\n\n/**\n* Generic store() function to register item, named item, or hash of items\n* Also used as hash to store the registered items\n* Used as implementation of $.templates(), $.views.templates(), $.views.tags(), $.views.helpers() and $.views.converters()\n*\n* @param {string|hash} name         name - or selector, in case of $.templates(). Or hash of items\n* @param {any}         [item]       (e.g. markup for named template)\n* @param {template}    [parentTmpl] For item being registered as private resource of template\n* @returns {any|$.views} item, e.g. compiled template - or $.views in case of registering hash of items\n*/\n\tfunction theStore(name, item, parentTmpl) {\n\t\t// The store is also the function used to add items to the store. e.g. $.templates, or $.views.tags\n\n\t\t// For store of name 'thing', Call as:\n\t\t//    $.views.things(items[, parentTmpl]),\n\t\t// or $.views.things(name[, item, parentTmpl])\n\n\t\tvar compile, itemName, thisStore, cnt,\n\t\t\tonStore = $sub.onStore[storeName];\n\n\t\tif (name && typeof name === OBJECT && !name.nodeType && !name.markup && !name.getTgt && !(storeName === \"viewModel\" && name.getters || name.extend)) {\n\t\t\t// Call to $.views.things(items[, parentTmpl]),\n\n\t\t\t// Adding items to the store\n\t\t\t// If name is a hash, then item is parentTmpl. Iterate over hash and call store for key.\n\t\t\tfor (itemName in name) {\n\t\t\t\ttheStore(itemName, name[itemName], item);\n\t\t\t}\n\t\t\treturn item || $views;\n\t\t}\n\t\t// Adding a single unnamed item to the store\n\t\tif (name && \"\" + name !== name) { // name must be a string\n\t\t\tparentTmpl = item;\n\t\t\titem = name;\n\t\t\tname = undefined;\n\t\t}\n\t\tthisStore = parentTmpl\n\t\t\t? storeName === \"viewModel\"\n\t\t\t\t? parentTmpl\n\t\t\t\t: (parentTmpl[storeNames] = parentTmpl[storeNames] || {})\n\t\t\t: theStore;\n\t\tcompile = storeSettings.compile;\n\n\t\tif (item === undefined) {\n\t\t\titem = compile ? name : thisStore[name];\n\t\t\tname = undefined;\n\t\t}\n\t\tif (item === null) {\n\t\t\t// If item is null, delete this entry\n\t\t\tif (name) {\n\t\t\t\tdelete thisStore[name];\n\t\t\t}\n\t\t} else {\n\t\t\tif (compile) {\n\t\t\t\titem = compile.call(thisStore, name, item, parentTmpl, 0) || {};\n\t\t\t\titem._is = storeName; // Only do this for compiled objects (tags, templates...)\n\t\t\t}\n\t\t\tif (name) {\n\t\t\t\tthisStore[name] = item;\n\t\t\t}\n\t\t}\n\t\tif (onStore) {\n\t\t\t// e.g. JsViews integration\n\t\t\tonStore(name, item, parentTmpl, compile);\n\t\t}\n\t\treturn item;\n\t}\n\n\tvar storeNames = storeName + \"s\";\n\t$views[storeNames] = theStore;\n}\n\n/**\n* Add settings such as:\n* $.views.settings.allowCode(true)\n* @param {boolean} value\n* @returns {Settings}\n*\n* allowCode = $.views.settings.allowCode()\n* @returns {boolean}\n*/\nfunction addSetting(st) {\n\t$viewsSettings[st] = $viewsSettings[st] || function(value) {\n\t\treturn arguments.length\n\t\t\t? ($subSettings[st] = value, $viewsSettings)\n\t\t\t: $subSettings[st];\n\t};\n}\n\n//========================\n// dataMap for render only\n//========================\n\nfunction dataMap(mapDef) {\n\tfunction Map(source, options) {\n\t\tthis.tgt = mapDef.getTgt(source, options);\n\t\toptions.map = this;\n\t}\n\n\tif ($isFunction(mapDef)) {\n\t\t// Simple map declared as function\n\t\tmapDef = {\n\t\t\tgetTgt: mapDef\n\t\t};\n\t}\n\n\tif (mapDef.baseMap) {\n\t\tmapDef = $extend($extend({}, mapDef.baseMap), mapDef);\n\t}\n\n\tmapDef.map = function(source, options) {\n\t\treturn new Map(source, options);\n\t};\n\treturn mapDef;\n}\n\n//==============\n// renderContent\n//==============\n\n/** Render the template as a string, using the specified data and helpers/context\n* $(\"#tmpl\").render(), tmpl.render(), tagCtx.render(), $.render.namedTmpl()\n*\n* @param {any}        data\n* @param {hash}       [context]           helpers or context\n* @param {boolean}    [noIteration]\n* @param {View}       [parentView]        internal\n* @param {string}     [key]               internal\n* @param {function}   [onRender]          internal\n* @returns {string}   rendered template   internal\n*/\nfunction renderContent(data, context, noIteration, parentView, key, onRender) {\n\tvar i, l, tag, tmpl, tagCtx, isTopRenderCall, prevData, prevIndex,\n\t\tview = parentView,\n\t\tresult = \"\";\n\n\tif (context === true) {\n\t\tnoIteration = context; // passing boolean as second param - noIteration\n\t\tcontext = undefined;\n\t} else if (typeof context !== OBJECT) {\n\t\tcontext = undefined; // context must be a boolean (noIteration) or a plain object\n\t}\n\n\tif (tag = this.tag) {\n\t\t// This is a call from renderTag or tagCtx.render(...)\n\t\ttagCtx = this;\n\t\tview = view || tagCtx.view;\n\t\ttmpl = view._getTmpl(tag.template || tagCtx.tmpl);\n\t\tif (!arguments.length) {\n\t\t\tdata = tag.contentCtx && $isFunction(tag.contentCtx)\n\t\t\t\t? data = tag.contentCtx(data)\n\t\t\t\t: view; // Default data context for wrapped block content is the first argument\n\t\t}\n\t} else {\n\t\t// This is a template.render(...) call\n\t\ttmpl = this;\n\t}\n\n\tif (tmpl) {\n\t\tif (!parentView && data && data._is === \"view\") {\n\t\t\tview = data; // When passing in a view to render or link (and not passing in a parent view) use the passed-in view as parentView\n\t\t}\n\n\t\tif (view && data === view) {\n\t\t\t// Inherit the data from the parent view.\n\t\t\tdata = view.data;\n\t\t}\n\n\t\tisTopRenderCall = !view;\n\t\tisRenderCall = isRenderCall || isTopRenderCall;\n\t\tif (isTopRenderCall) {\n\t\t\t(context = context || {}).root = data; // Provide ~root as shortcut to top-level data.\n\t\t}\n\t\tif (!isRenderCall || $subSettingsAdvanced.useViews || tmpl.useViews || view && view !== topView) {\n\t\t\tresult = renderWithViews(tmpl, data, context, noIteration, view, key, onRender, tag);\n\t\t} else {\n\t\t\tif (view) { // In a block\n\t\t\t\tprevData = view.data;\n\t\t\t\tprevIndex = view.index;\n\t\t\t\tview.index = indexStr;\n\t\t\t} else {\n\t\t\t\tview = topView;\n\t\t\t\tprevData = view.data;\n\t\t\t\tview.data = data;\n\t\t\t\tview.ctx = context;\n\t\t\t}\n\t\t\tif ($isArray(data) && !noIteration) {\n\t\t\t\t// Create a view for the array, whose child views correspond to each data item. (Note: if key and parentView are passed in\n\t\t\t\t// along with parent view, treat as insert -e.g. from view.addViews - so parentView is already the view item for array)\n\t\t\t\tfor (i = 0, l = data.length; i < l; i++) {\n\t\t\t\t\tview.index = i;\n\t\t\t\t\tview.data = data[i];\n\t\t\t\t\tresult += tmpl.fn(data[i], view, $sub);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tview.data = data;\n\t\t\t\tresult += tmpl.fn(data, view, $sub);\n\t\t\t}\n\t\t\tview.data = prevData;\n\t\t\tview.index = prevIndex;\n\t\t}\n\t\tif (isTopRenderCall) {\n\t\t\tisRenderCall = undefined;\n\t\t}\n\t}\n\treturn result;\n}\n\nfunction renderWithViews(tmpl, data, context, noIteration, view, key, onRender, tag) {\n\t// Render template against data as a tree of subviews (nested rendered template instances), or as a string (top-level template).\n\t// If the data is the parent view, treat as noIteration, re-render with the same data context.\n\t// tmpl can be a string (e.g. rendered by a tag.render() method), or a compiled template.\n\tvar i, l, newView, childView, itemResult, swapContent, contentTmpl, outerOnRender, tmplName, itemVar, newCtx, tagCtx, noLinking,\n\t\tresult = \"\";\n\n\tif (tag) {\n\t\t// This is a call from renderTag or tagCtx.render(...)\n\t\ttmplName = tag.tagName;\n\t\ttagCtx = tag.tagCtx;\n\t\tcontext = context ? extendCtx(context, tag.ctx) : tag.ctx;\n\n\t\tif (tmpl === view.content) { // {{xxx tmpl=#content}}\n\t\t\tcontentTmpl = tmpl !== view.ctx._wrp // We are rendering the #content\n\t\t\t\t? view.ctx._wrp // #content was the tagCtx.props.tmpl wrapper of the block content - so within this view, #content will now be the view.ctx._wrp block content\n\t\t\t\t: undefined; // #content was the view.ctx._wrp block content - so within this view, there is no longer any #content to wrap.\n\t\t} else if (tmpl !== tagCtx.content) {\n\t\t\tif (tmpl === tag.template) { // Rendering {{tag}} tag.template, replacing block content.\n\t\t\t\tcontentTmpl = tagCtx.tmpl; // Set #content to block content (or wrapped block content if tagCtx.props.tmpl is set)\n\t\t\t\tcontext._wrp = tagCtx.content; // Pass wrapped block content to nested views\n\t\t\t} else { // Rendering tagCtx.props.tmpl wrapper\n\t\t\t\tcontentTmpl = tagCtx.content || view.content; // Set #content to wrapped block content\n\t\t\t}\n\t\t} else {\n\t\t\tcontentTmpl = view.content; // Nested views inherit same wrapped #content property\n\t\t}\n\n\t\tif (tagCtx.props.link === false) {\n\t\t\t// link=false setting on block tag\n\t\t\t// We will override inherited value of link by the explicit setting link=false taken from props\n\t\t\t// The child views of an unlinked view are also unlinked. So setting child back to true will not have any effect.\n\t\t\tcontext = context || {};\n\t\t\tcontext.link = false;\n\t\t}\n\t}\n\n\tif (view) {\n\t\tonRender = onRender || view._.onRender;\n\t\tnoLinking = context && context.link === false;\n\n\t\tif (noLinking && view._.nl) {\n\t\t\tonRender = undefined;\n\t\t}\n\n\t\tcontext = extendCtx(context, view.ctx);\n\t\ttagCtx = !tag && view.tag\n\t\t\t? view.tag.tagCtxs[view.tagElse]\n\t\t\t: tagCtx;\n\t}\n\n\tif (itemVar = tagCtx && tagCtx.props.itemVar) {\n\t\tif (itemVar[0] !== \"~\") {\n\t\t\tsyntaxError(\"Use itemVar='~myItem'\");\n\t\t}\n\t\titemVar = itemVar.slice(1);\n\t}\n\n\tif (key === true) {\n\t\tswapContent = true;\n\t\tkey = 0;\n\t}\n\n\t// If link===false, do not call onRender, so no data-linking marker nodes\n\tif (onRender && tag && tag._.noVws) {\n\t\tonRender = undefined;\n\t}\n\touterOnRender = onRender;\n\tif (onRender === true) {\n\t\t// Used by view.refresh(). Don't create a new wrapper view.\n\t\touterOnRender = undefined;\n\t\tonRender = view._.onRender;\n\t}\n\t// Set additional context on views created here, (as modified context inherited from the parent, and to be inherited by child views)\n\tcontext = tmpl.helpers\n\t\t? extendCtx(tmpl.helpers, context)\n\t\t: context;\n\n\tnewCtx = context;\n\tif ($isArray(data) && !noIteration) {\n\t\t// Create a view for the array, whose child views correspond to each data item. (Note: if key and view are passed in\n\t\t// along with parent view, treat as insert -e.g. from view.addViews - so view is already the view item for array)\n\t\tnewView = swapContent\n\t\t\t? view\n\t\t\t: (key !== undefined && view)\n\t\t\t\t|| new View(context, \"array\", view, data, tmpl, key, onRender, contentTmpl);\n\t\tnewView._.nl= noLinking;\n\t\tif (view && view._.useKey) {\n\t\t\t// Parent is not an 'array view'\n\t\t\tnewView._.bnd = !tag || tag._.bnd && tag; // For array views that are data bound for collection change events, set the\n\t\t\t// view._.bnd property to true for top-level link() or data-link=\"{for}\", or to the tag instance for a data-bound tag, e.g. {^{for ...}}\n\t\t\tnewView.tag = tag;\n\t\t}\n\t\tfor (i = 0, l = data.length; i < l; i++) {\n\t\t\t// Create a view for each data item.\n\t\t\tchildView = new View(newCtx, \"item\", newView, data[i], tmpl, (key || 0) + i, onRender, newView.content);\n\t\t\tif (itemVar) {\n\t\t\t\t(childView.ctx = $extend({}, newCtx))[itemVar] = $sub._cp(data[i], \"#data\", childView);\n\t\t\t}\n\t\t\titemResult = tmpl.fn(data[i], childView, $sub);\n\t\t\tresult += newView._.onRender ? newView._.onRender(itemResult, childView) : itemResult;\n\t\t}\n\t} else {\n\t\t// Create a view for singleton data object. The type of the view will be the tag name, e.g. \"if\" or \"mytag\" except for\n\t\t// \"item\", \"array\" and \"data\" views. A \"data\" view is from programmatic render(object) against a 'singleton'.\n\t\tnewView = swapContent ? view : new View(newCtx, tmplName || \"data\", view, data, tmpl, key, onRender, contentTmpl);\n\n\t\tif (itemVar) {\n\t\t\t(newView.ctx = $extend({}, newCtx))[itemVar] = $sub._cp(data, \"#data\", newView);\n\t\t}\n\n\t\tnewView.tag = tag;\n\t\tnewView._.nl = noLinking;\n\t\tresult += tmpl.fn(data, newView, $sub);\n\t}\n\tif (tag) {\n\t\tnewView.tagElse = tagCtx.index;\n\t\ttagCtx.contentView = newView;\n\t}\n\treturn outerOnRender ? outerOnRender(result, newView) : result;\n}\n\n//===========================\n// Build and compile template\n//===========================\n\n// Generate a reusable function that will serve to render a template against data\n// (Compile AST then build template function)\n\nfunction onRenderError(e, view, fallback) {\n\tvar message = fallback !== undefined\n\t\t? $isFunction(fallback)\n\t\t\t? fallback.call(view.data, e, view)\n\t\t\t: fallback || \"\"\n\t\t: \"{Error: \" + (e.message||e) + \"}\";\n\n\tif ($subSettings.onError && (fallback = $subSettings.onError.call(view.data, e, fallback && message, view)) !== undefined) {\n\t\tmessage = fallback; // There is a settings.debugMode(handler) onError override. Call it, and use return value (if any) to replace message\n\t}\n\treturn view && !view._lc ? $converters.html(message) : message; // For data-link=\\\"{... onError=...}\"... See onDataLinkedTagChange\n}\n\nfunction error(message) {\n\tthrow new $sub.Err(message);\n}\n\nfunction syntaxError(message) {\n\terror(\"Syntax error\\n\" + message);\n}\n\nfunction tmplFn(markup, tmpl, isLinkExpr, convertBack, hasElse) {\n\t// Compile markup to AST (abtract syntax tree) then build the template function code from the AST nodes\n\t// Used for compiling templates, and also by JsViews to build functions for data link expressions\n\n\t//==== nested functions ====\n\tfunction pushprecedingContent(shift) {\n\t\tshift -= loc;\n\t\tif (shift) {\n\t\t\tcontent.push(markup.substr(loc, shift).replace(rNewLine, \"\\\\n\"));\n\t\t}\n\t}\n\n\tfunction blockTagCheck(tagName, block) {\n\t\tif (tagName) {\n\t\t\ttagName += '}}';\n\t\t\t//\t\t\t'{{include}} block has {{/for}} with no open {{for}}'\n\t\t\tsyntaxError((\n\t\t\t\tblock\n\t\t\t\t\t? '{{' + block + '}} block has {{/' + tagName + ' without {{' + tagName\n\t\t\t\t\t: 'Unmatched or missing {{/' + tagName) + ', in template:\\n' + markup);\n\t\t}\n\t}\n\n\tfunction parseTag(all, bind, tagName, converter, colon, html, codeTag, params, slash, bind2, closeBlock, index) {\n/*\n\n     bind     tagName         cvt   cln html code    params            slash   bind2         closeBlk  comment\n/(?:{(\\^)?{(?:(\\w+(?=[\\/\\s}]))|(\\w+)?(:)|(>)|(\\*))\\s*((?:[^}]|}(?!}))*?)(\\/)?|{(\\^)?{(?:(?:\\/(\\w+))\\s*|!--[\\s\\S]*?--))}}/g\n\n(?:\n  {(\\^)?{            bind\n  (?:\n    (\\w+             tagName\n      (?=[\\/\\s}])\n    )\n    |\n    (\\w+)?(:)        converter colon\n    |\n    (>)              html\n    |\n    (\\*)             codeTag\n  )\n  \\s*\n  (                  params\n    (?:[^}]|}(?!}))*?\n  )\n  (\\/)?              slash\n  |\n  {(\\^)?{            bind2\n  (?:\n    (?:\\/(\\w+))\\s*   closeBlock\n    |\n    !--[\\s\\S]*?--    comment\n  )\n)\n}}/g\n\n*/\n\t\tif (codeTag && bind || slash && !tagName || params && params.slice(-1) === \":\" || bind2) {\n\t\t\tsyntaxError(all);\n\t\t}\n\n\t\t// Build abstract syntax tree (AST): [tagName, converter, params, content, hash, bindings, contentMarkup]\n\t\tif (html) {\n\t\t\tcolon = \":\";\n\t\t\tconverter = HTML;\n\t\t}\n\t\tslash = slash || isLinkExpr && !hasElse;\n\n\t\tvar late, openTagName, isLateOb,\n\t\t\tpathBindings = (bind || isLinkExpr) && [[]], // pathBindings is an array of arrays for arg bindings and a hash of arrays for prop bindings\n\t\t\tprops = \"\",\n\t\t\targs = \"\",\n\t\t\tctxProps = \"\",\n\t\t\tparamsArgs = \"\",\n\t\t\tparamsProps = \"\",\n\t\t\tparamsCtxProps = \"\",\n\t\t\tonError = \"\",\n\t\t\tuseTrigger = \"\",\n\t\t\t// Block tag if not self-closing and not {{:}} or {{>}} (special case) and not a data-link expression\n\t\t\tblock = !slash && !colon;\n\n\t\t//==== nested helper function ====\n\t\ttagName = tagName || (params = params || \"#data\", colon); // {{:}} is equivalent to {{:#data}}\n\t\tpushprecedingContent(index);\n\t\tloc = index + all.length; // location marker - parsed up to here\n\t\tif (codeTag) {\n\t\t\tif (allowCode) {\n\t\t\t\tcontent.push([\"*\", \"\\n\" + params.replace(/^:/, \"ret+= \").replace(rUnescapeQuotes, \"$1\") + \";\\n\"]);\n\t\t\t}\n\t\t} else if (tagName) {\n\t\t\tif (tagName === \"else\") {\n\t\t\t\tif (rTestElseIf.test(params)) {\n\t\t\t\t\tsyntaxError('For \"{{else if expr}}\" use \"{{else expr}}\"');\n\t\t\t\t}\n\t\t\t\tpathBindings = current[9] && [[]];\n\t\t\t\tcurrent[10] = markup.substring(current[10], index); // contentMarkup for block tag\n\t\t\t\topenTagName = current[11] || current[0] || syntaxError(\"Mismatched: \" + all);\n\t\t\t\t// current[0] is tagName, but for {{else}} nodes, current[11] is tagName of preceding open tag\n\t\t\t\tcurrent = stack.pop();\n\t\t\t\tcontent = current[2];\n\t\t\t\tblock = true;\n\t\t\t}\n\t\t\tif (params) {\n\t\t\t\t// remove newlines from the params string, to avoid compiled code errors for unterminated strings\n\t\t\t\tparseParams(params.replace(rNewLine, \" \"), pathBindings, tmpl, isLinkExpr)\n\t\t\t\t\t.replace(rBuildHash, function(all, onerror, isCtxPrm, key, keyToken, keyValue, arg, param) {\n\t\t\t\t\t\tif (key === \"this:\") {\n\t\t\t\t\t\t\tkeyValue = \"undefined\"; // this=some.path is always a to parameter (one-way), so don't need to compile/evaluate some.path initialization\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (param) {\n\t\t\t\t\t\t\tisLateOb = isLateOb || param[0] === \"@\";\n\t\t\t\t\t\t}\n\t\t\t\t\t\tkey = \"'\" + keyToken + \"':\";\n\t\t\t\t\t\tif (arg) {\n\t\t\t\t\t\t\targs += isCtxPrm + keyValue + \",\";\n\t\t\t\t\t\t\tparamsArgs += \"'\" + param + \"',\";\n\t\t\t\t\t\t} else if (isCtxPrm) { // Contextual parameter, ~foo=expr\n\t\t\t\t\t\t\tctxProps += key + 'j._cp(' + keyValue + ',\"' + param + '\",view),';\n\t\t\t\t\t\t\t// Compiled code for evaluating tagCtx on a tag will have: ctx:{'foo':j._cp(compiledExpr, \"expr\", view)}\n\t\t\t\t\t\t\tparamsCtxProps += key + \"'\" + param + \"',\";\n\t\t\t\t\t\t} else if (onerror) {\n\t\t\t\t\t\t\tonError += keyValue;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (keyToken === \"trigger\") {\n\t\t\t\t\t\t\t\tuseTrigger += keyValue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (keyToken === \"lateRender\") {\n\t\t\t\t\t\t\t\tlate = param !== \"false\"; // Render after first pass\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tprops += key + keyValue + \",\";\n\t\t\t\t\t\t\tparamsProps += key + \"'\" + param + \"',\";\n\t\t\t\t\t\t\thasHandlers = hasHandlers || rHasHandlers.test(keyToken);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn \"\";\n\t\t\t\t\t}).slice(0, -1);\n\t\t\t}\n\n\t\t\tif (pathBindings && pathBindings[0]) {\n\t\t\t\tpathBindings.pop(); // Remove the binding that was prepared for next arg. (There is always an extra one ready).\n\t\t\t}\n\n\t\t\tnewNode = [\n\t\t\t\t\ttagName,\n\t\t\t\t\tconverter || !!convertBack || hasHandlers || \"\",\n\t\t\t\t\tblock && [],\n\t\t\t\t\tparsedParam(paramsArgs || (tagName === \":\" ? \"'#data',\" : \"\"), paramsProps, paramsCtxProps), // {{:}} equivalent to {{:#data}}\n\t\t\t\t\tparsedParam(args || (tagName === \":\" ? \"data,\" : \"\"), props, ctxProps),\n\t\t\t\t\tonError,\n\t\t\t\t\tuseTrigger,\n\t\t\t\t\tlate,\n\t\t\t\t\tisLateOb,\n\t\t\t\t\tpathBindings || 0\n\t\t\t\t];\n\t\t\tcontent.push(newNode);\n\t\t\tif (block) {\n\t\t\t\tstack.push(current);\n\t\t\t\tcurrent = newNode;\n\t\t\t\tcurrent[10] = loc; // Store current location of open tag, to be able to add contentMarkup when we reach closing tag\n\t\t\t\tcurrent[11] = openTagName; // Used for checking syntax (matching close tag)\n\t\t\t}\n\t\t} else if (closeBlock) {\n\t\t\tblockTagCheck(closeBlock !== current[0] && closeBlock !== current[11] && closeBlock, current[0]); // Check matching close tag name\n\t\t\tcurrent[10] = markup.substring(current[10], index); // contentMarkup for block tag\n\t\t\tcurrent = stack.pop();\n\t\t}\n\t\tblockTagCheck(!current && closeBlock);\n\t\tcontent = current[2];\n\t}\n\t//==== /end of nested functions ====\n\n\tvar i, result, newNode, hasHandlers, bindings,\n\t\tallowCode = $subSettings.allowCode || tmpl && tmpl.allowCode\n\t\t\t|| $viewsSettings.allowCode === true, // include direct setting of settings.allowCode true for backward compat only\n\t\tastTop = [],\n\t\tloc = 0,\n\t\tstack = [],\n\t\tcontent = astTop,\n\t\tcurrent = [,,astTop];\n\n\tif (allowCode && tmpl._is) {\n\t\ttmpl.allowCode = allowCode;\n\t}\n\n//TODO\tresult = tmplFnsCache[markup]; // Only cache if template is not named and markup length < ...,\n//and there are no bindings or subtemplates?? Consider standard optimization for data-link=\"a.b.c\"\n//\t\tif (result) {\n//\t\t\ttmpl.fn = result;\n//\t\t} else {\n\n//\t\tresult = markup;\n\tif (isLinkExpr) {\n\t\tif (convertBack !== undefined) {\n\t\t\tmarkup = markup.slice(0, -convertBack.length - 2) + delimCloseChar0;\n\t\t}\n\t\tmarkup = delimOpenChar0 + markup + delimCloseChar1;\n\t}\n\n\tblockTagCheck(stack[0] && stack[0][2].pop()[0]);\n\t// Build the AST (abstract syntax tree) under astTop\n\tmarkup.replace(rTag, parseTag);\n\n\tpushprecedingContent(markup.length);\n\n\tif (loc = astTop[astTop.length - 1]) {\n\t\tblockTagCheck(\"\" + loc !== loc && (+loc[10] === loc[10]) && loc[0]);\n\t}\n//\t\t\tresult = tmplFnsCache[markup] = buildCode(astTop, tmpl);\n//\t\t}\n\n\tif (isLinkExpr) {\n\t\tresult = buildCode(astTop, markup, isLinkExpr);\n\t\tbindings = [];\n\t\ti = astTop.length;\n\t\twhile (i--) {\n\t\t\tbindings.unshift(astTop[i][9]); // With data-link expressions, pathBindings array for tagCtx[i] is astTop[i][9]\n\t\t}\n\t\tsetPaths(result, bindings);\n\t} else {\n\t\tresult = buildCode(astTop, tmpl);\n\t}\n\treturn result;\n}\n\nfunction setPaths(fn, pathsArr) {\n\tvar key, paths,\n\t\ti = 0,\n\t\tl = pathsArr.length;\n\tfn.deps = [];\n\tfn.paths = []; // The array of path binding (array/dictionary)s for each tag/else block's args and props\n\tfor (; i < l; i++) {\n\t\tfn.paths.push(paths = pathsArr[i]);\n\t\tfor (key in paths) {\n\t\t\tif (key !== \"_jsvto\" && paths.hasOwnProperty(key) && paths[key].length && !paths[key].skp) {\n\t\t\t\tfn.deps = fn.deps.concat(paths[key]); // deps is the concatenation of the paths arrays for the different bindings\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction parsedParam(args, props, ctx) {\n\treturn [args.slice(0, -1), props.slice(0, -1), ctx.slice(0, -1)];\n}\n\nfunction paramStructure(paramCode, paramVals) {\n\treturn '\\n\\tparams:{args:[' + paramCode[0] + '],\\n\\tprops:{' + paramCode[1] + '}'\n\t\t+ (paramCode[2] ? ',\\n\\tctx:{' + paramCode[2] + '}' : \"\")\n\t\t+ '},\\n\\targs:[' + paramVals[0] + '],\\n\\tprops:{' + paramVals[1] + '}'\n\t\t+ (paramVals[2] ? ',\\n\\tctx:{' + paramVals[2] + '}' : \"\");\n}\n\nfunction parseParams(params, pathBindings, tmpl, isLinkExpr) {\n\n\tfunction parseTokens(all, lftPrn0, lftPrn, bound, path, operator, err, eq, path2, late, prn,\n\t\t\t\t\t\t\t\t\t\t\t\tcomma, lftPrn2, apos, quot, rtPrn, rtPrnDot, prn2, space, index, full) {\n\t// /(\\()(?=\\s*\\()|(?:([([])\\s*)?(?:(\\^?)(~?[\\w$.^]+)?\\s*((\\+\\+|--)|\\+|-|~(?![\\w$])|&&|\\|\\||===|!==|==|!=|<=|>=|[<>%*:?\\/]|(=))\\s*|(!*?(@)?[#~]?[\\w$.^]+)([([])?)|(,\\s*)|(?:(\\()\\s*)?\\\\?(?:(')|(\"))|(?:\\s*(([)\\]])(?=[.^]|\\s*$|[^([])|[)\\]])([([]?))|(\\s+)/g,\n\t//lftPrn0           lftPrn         bound     path               operator     err                                          eq      path2 late            prn      comma  lftPrn2          apos quot        rtPrn  rtPrnDot                  prn2     space\n\t// (left paren? followed by (path? followed by operator) or (path followed by paren?)) or comma or apos or quot or right paren or space\n\n\t\tfunction parsePath(allPath, not, object, helper, view, viewProperty, pathTokens, leafToken) {\n\t\t\t// /^(!*?)(?:null|true|false|\\d[\\d.]*|([\\w$]+|\\.|~([\\w$]+)|#(view|([\\w$]+))?)([\\w$.^]*?)(?:[.[^]([\\w$]+)\\]?)?)$/g,\n\t\t\t//    not                               object     helper    view  viewProperty pathTokens      leafToken\n\t\t\tsubPath = object === \".\";\n\t\t\tif (object) {\n\t\t\t\tpath = path.slice(not.length);\n\t\t\t\tif (/^\\.?constructor$/.test(leafToken||path)) {\n\t\t\t\t\tsyntaxError(allPath);\n\t\t\t\t}\n\t\t\t\tif (!subPath) {\n\t\t\t\t\tallPath = (late // late path @a.b.c: not throw on 'property of undefined' if a undefined, and will use _getOb() after linking to resolve late.\n\t\t\t\t\t\t\t? (isLinkExpr ? '' : '(ltOb.lt=ltOb.lt||') + '(ob='\n\t\t\t\t\t\t\t: \"\"\n\t\t\t\t\t\t)\n\t\t\t\t\t\t+ (helper\n\t\t\t\t\t\t\t? 'view.ctxPrm(\"' + helper + '\")'\n\t\t\t\t\t\t\t: view\n\t\t\t\t\t\t\t\t? \"view\"\n\t\t\t\t\t\t\t\t: \"data\")\n\t\t\t\t\t\t+ (late\n\t\t\t\t\t\t\t? ')===undefined' + (isLinkExpr ? '' : ')') + '?\"\":view._getOb(ob,\"'\n\t\t\t\t\t\t\t: \"\"\n\t\t\t\t\t\t)\n\t\t\t\t\t\t+ (leafToken\n\t\t\t\t\t\t\t? (viewProperty\n\t\t\t\t\t\t\t\t? \".\" + viewProperty\n\t\t\t\t\t\t\t\t: helper\n\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t: (view ? \"\" : \".\" + object)\n\t\t\t\t\t\t\t\t) + (pathTokens || \"\")\n\t\t\t\t\t\t\t: (leafToken = helper ? \"\" : view ? viewProperty || \"\" : object, \"\"));\n\t\t\t\t\tallPath = allPath + (leafToken ? \".\" + leafToken : \"\");\n\n\t\t\t\t\tallPath = not + (allPath.slice(0, 9) === \"view.data\"\n\t\t\t\t\t\t? allPath.slice(5) // convert #view.data... to data...\n\t\t\t\t\t\t: allPath)\n\t\t\t\t\t+ (late\n\t\t\t\t\t\t\t? (isLinkExpr ? '\"': '\",ltOb') + (prn ? ',1)':')')\n\t\t\t\t\t\t\t: \"\"\n\t\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tif (bindings) {\n\t\t\t\t\tbinds = named === \"_linkTo\" ? (bindto = pathBindings._jsvto = pathBindings._jsvto || []) : bndCtx.bd;\n\t\t\t\t\tif (theOb = subPath && binds[binds.length-1]) {\n\t\t\t\t\t\tif (theOb._cpfn) { // Computed property exprOb\n\t\t\t\t\t\t\twhile (theOb.sb) {\n\t\t\t\t\t\t\t\ttheOb = theOb.sb;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (theOb.prm) {\n\t\t\t\t\t\t\t\tif (theOb.bnd) {\n\t\t\t\t\t\t\t\t\tpath = \"^\" + path.slice(1);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\ttheOb.sb = path;\n\t\t\t\t\t\t\t\ttheOb.bnd = theOb.bnd || path[0] === \"^\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tbinds.push(path);\n\t\t\t\t\t}\n\t\t\t\t\tif (prn && !subPath) {\n\t\t\t\t\t\tpathStart[fnDp] = ind;\n\t\t\t\t\t\tcompiledPathStart[fnDp] = compiledPath[fnDp].length;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn allPath;\n\t\t}\n\n\t\t//bound = bindings && bound;\n\t\tif (bound && !eq) {\n\t\t\tpath = bound + path; // e.g. some.fn(...)^some.path - so here path is \"^some.path\"\n\t\t}\n\t\toperator = operator || \"\";\n\t\tlftPrn2 = lftPrn2 || \"\";\n\t\tlftPrn = lftPrn || lftPrn0 || lftPrn2;\n\t\tpath = path || path2;\n\n\t\tif (late && (late = !/\\)|]/.test(full[index-1]))) {\n\t\t\tpath = path.slice(1).split(\".\").join(\"^\"); // Late path @z.b.c. Use \"^\" rather than \".\" to ensure that deep binding will be used\n\t\t}\n\t\t// Could do this - but not worth perf cost?? :-\n\t\t// if (!path.lastIndexOf(\"#data.\", 0)) { path = path.slice(6); } // If path starts with \"#data.\", remove that.\n\t\tprn = prn || prn2 || \"\";\n\t\tvar expr, binds, theOb, newOb, subPath, lftPrnFCall, ret,\n\t\t\tind = index;\n\n\t\tif (!aposed && !quoted) {\n\t\t\tif (err) {\n\t\t\t\tsyntaxError(params);\n\t\t\t}\n\t\t\tif (rtPrnDot && bindings) {\n\t\t\t\t// This is a binding to a path in which an object is returned by a helper/data function/expression, e.g. foo()^x.y or (a?b:c)^x.y\n\t\t\t\t// We create a compiled function to get the object instance (which will be called when the dependent data of the subexpression changes,\n\t\t\t\t// to return the new object, and trigger re-binding of the subsequent path)\n\t\t\t\texpr = pathStart[fnDp-1];\n\t\t\t\tif (full.length - 1 > ind - (expr || 0)) { // We need to compile a subexpression\n\t\t\t\t\texpr = $.trim(full.slice(expr, ind + all.length));\n\t\t\t\t\tbinds = bindto || bndStack[fnDp-1].bd;\n\t\t\t\t\t// Insert exprOb object, to be used during binding to return the computed object\n\t\t\t\t\ttheOb = binds[binds.length-1];\n\t\t\t\t\tif (theOb && theOb.prm) {\n\t\t\t\t\t\twhile (theOb.sb && theOb.sb.prm) {\n\t\t\t\t\t\t\ttheOb = theOb.sb;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnewOb = theOb.sb = {path: theOb.sb, bnd: theOb.bnd};\n\t\t\t\t\t} else {\n\t\t\t\t\t\tbinds.push(newOb = {path: binds.pop()}); // Insert exprOb object, to be used during binding to return the computed object\n\t\t\t\t\t}\n\t\t\t\t\tif (theOb && theOb.sb === newOb) {\n\t\t\t\t\t\tcompiledPath[fnDp] = compiledPath[fnDp-1].slice(theOb._cpPthSt) + compiledPath[fnDp];\n\t\t\t\t\t\tcompiledPath[fnDp-1] = compiledPath[fnDp-1].slice(0, theOb._cpPthSt);\n\t\t\t\t\t}\n\t\t\t\t\tnewOb._cpPthSt = compiledPathStart[fnDp-1];\n\t\t\t\t\tnewOb._cpKey = expr;\n\n\t\t\t\t\tcompiledPath[fnDp] += full.slice(prevIndex, index);\n\t\t\t\t\tprevIndex = index;\n\n\t\t\t\t\tnewOb._cpfn = cpFnStore[expr] = cpFnStore[expr] || // Compiled function for computed value: get from store, or compile and store\n\t\t\t\t\t\tnew Function(\"data,view,j\", // Compiled function for computed value in template\n\t\t\t\t\t\"//\" + expr + \"\\nvar v;\\nreturn ((v=\" + compiledPath[fnDp] + (rtPrn === \"]\" ? \")]\" : rtPrn) + \")!=null?v:null);\");\n\n\t\t\t\t\tcompiledPath[fnDp-1] += (fnCall[prnDp] && $subSettingsAdvanced.cache ? \"view.getCache(\\\"\" + expr.replace(rEscapeQuotes, \"\\\\$&\") + \"\\\"\" : compiledPath[fnDp]);\n\n\t\t\t\t\tnewOb.prm = bndCtx.bd;\n\t\t\t\t\tnewOb.bnd = newOb.bnd || newOb.path && newOb.path.indexOf(\"^\") >= 0;\n\t\t\t\t}\n\t\t\t\tcompiledPath[fnDp] = \"\";\n\t\t\t}\n\t\t\tif (prn === \"[\") {\n\t\t\t\tprn = \"[j._sq(\";\n\t\t\t}\n\t\t\tif (lftPrn === \"[\") {\n\t\t\t\tlftPrn = \"[j._sq(\";\n\t\t\t}\n\t\t}\n\t\tret = (aposed\n\t\t\t// within single-quoted string\n\t\t\t? (aposed = !apos, (aposed ? all : lftPrn2 + '\"'))\n\t\t\t: quoted\n\t\t\t// within double-quoted string\n\t\t\t\t? (quoted = !quot, (quoted ? all : lftPrn2 + '\"'))\n\t\t\t\t:\n\t\t\t(\n\t\t\t\t(lftPrn\n\t\t\t\t\t? (\n\t\t\t\t\t\tprnStack[++prnDp] = true,\n\t\t\t\t\t\tprnInd[prnDp] = 0,\n\t\t\t\t\t\tbindings && (\n\t\t\t\t\t\t\tpathStart[fnDp++] = ind++,\n\t\t\t\t\t\t\tbndCtx = bndStack[fnDp] = {bd: []},\n\t\t\t\t\t\t\tcompiledPath[fnDp] = \"\",\n\t\t\t\t\t\t\tcompiledPathStart[fnDp] = 1\n\t\t\t\t\t\t),\n\t\t\t\t\t\tlftPrn) // Left paren, (not a function call paren)\n\t\t\t\t\t: \"\")\n\t\t\t\t+ (space\n\t\t\t\t\t? (prnDp\n\t\t\t\t\t\t? \"\" // A space within parens or within function call parens, so not a separator for tag args\n\t\t\t// New arg or prop - so insert backspace \\b (\\x08) as separator for named params, used subsequently by rBuildHash, and prepare new bindings array\n\t\t\t\t\t\t: (paramIndex = full.slice(paramIndex, ind), named\n\t\t\t\t\t\t\t? (named = boundName = bindto = false, \"\\b\")\n\t\t\t\t\t\t\t: \"\\b,\") + paramIndex + (paramIndex = ind + all.length, bindings && pathBindings.push(bndCtx.bd = []), \"\\b\")\n\t\t\t\t\t)\n\t\t\t\t\t: eq\n\t\t\t// named param. Remove bindings for arg and create instead bindings array for prop\n\t\t\t\t\t\t? (fnDp && syntaxError(params), bindings && pathBindings.pop(), named = \"_\" + path, boundName = bound, paramIndex = ind + all.length,\n\t\t\t\t\t\t\t\tbindings && ((bindings = bndCtx.bd = pathBindings[named] = []), bindings.skp = !bound), path + ':')\n\t\t\t\t\t\t: path\n\t\t\t// path\n\t\t\t\t\t\t\t? (path.split(\"^\").join(\".\").replace($sub.rPath, parsePath)\n\t\t\t\t\t\t\t\t+ (prn || operator)\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t: operator\n\t\t\t// operator\n\t\t\t\t\t\t\t\t? operator\n\t\t\t\t\t\t\t\t: rtPrn\n\t\t\t// function\n\t\t\t\t\t\t\t\t\t? rtPrn === \"]\" ? \")]\" : \")\"\n\t\t\t\t\t\t\t\t\t: comma\n\t\t\t\t\t\t\t\t\t\t? (fnCall[prnDp] || syntaxError(params), \",\") // We don't allow top-level literal arrays or objects\n\t\t\t\t\t\t\t\t\t\t: lftPrn0\n\t\t\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t\t\t: (aposed = apos, quoted = quot, '\"')\n\t\t\t))\n\t\t);\n\n\t\tif (!aposed && !quoted) {\n\t\t\tif (rtPrn) {\n\t\t\t\tfnCall[prnDp] = false;\n\t\t\t\tprnDp--;\n\t\t\t}\n\t\t}\n\n\t\tif (bindings) {\n\t\t\tif (!aposed && !quoted) {\n\t\t\t\tif (rtPrn) {\n\t\t\t\t\tif (prnStack[prnDp+1]) {\n\t\t\t\t\t\tbndCtx = bndStack[--fnDp];\n\t\t\t\t\t\tprnStack[prnDp+1] = false;\n\t\t\t\t\t}\n\t\t\t\t\tprnStart = prnInd[prnDp+1];\n\t\t\t\t}\n\t\t\t\tif (prn) {\n\t\t\t\t\tprnInd[prnDp+1] = compiledPath[fnDp].length + (lftPrn ? 1 : 0);\n\t\t\t\t\tif (path || rtPrn) {\n\t\t\t\t\t\tbndCtx = bndStack[++fnDp] = {bd: []};\n\t\t\t\t\t\tprnStack[prnDp+1] = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcompiledPath[fnDp] = (compiledPath[fnDp]||\"\") + full.slice(prevIndex, index);\n\t\t\tprevIndex = index+all.length;\n\n\t\t\tif (!aposed && !quoted) {\n\t\t\t\tif (lftPrnFCall = lftPrn && prnStack[prnDp+1]) {\n\t\t\t\t\tcompiledPath[fnDp-1] += lftPrn;\n\t\t\t\t\tcompiledPathStart[fnDp-1]++;\n\t\t\t\t}\n\t\t\t\tif (prn === \"(\" && subPath && !newOb) {\n\t\t\t\t\tcompiledPath[fnDp] = compiledPath[fnDp-1].slice(prnStart) + compiledPath[fnDp];\n\t\t\t\t\tcompiledPath[fnDp-1] = compiledPath[fnDp-1].slice(0, prnStart);\n\t\t\t\t}\n\t\t\t}\n\t\t\tcompiledPath[fnDp] += lftPrnFCall ? ret.slice(1) : ret;\n\t\t}\n\n\t\tif (!aposed && !quoted && prn) {\n\t\t\tprnDp++;\n\t\t\tif (path && prn === \"(\") {\n\t\t\t\tfnCall[prnDp] = true;\n\t\t\t}\n\t\t}\n\n\t\tif (!aposed && !quoted && prn2) {\n\t\t\tif (bindings) {\n\t\t\t\tcompiledPath[fnDp] += prn;\n\t\t\t}\n\t\t\tret += prn;\n\t\t}\n\t\treturn ret;\n\t}\n\n\tvar named, bindto, boundName, result,\n\t\tquoted, // boolean for string content in double quotes\n\t\taposed, // or in single quotes\n\t\tbindings = pathBindings && pathBindings[0], // bindings array for the first arg\n\t\tbndCtx = {bd: bindings},\n\t\tbndStack = {0: bndCtx},\n\t\tparamIndex = 0, // list,\n\t\t// The following are used for tracking path parsing including nested paths, such as \"a.b(c^d + (e))^f\", and chained computed paths such as\n\t\t// \"a.b().c^d().e.f().g\" - which has four chained paths, \"a.b()\", \"^c.d()\", \".e.f()\" and \".g\"\n\t\tprnDp = 0,     // For tracking paren depth (not function call parens)\n\t\tfnDp = 0,      // For tracking depth of function call parens\n\t\tprnInd = {},   // We are in a function call\n\t\tprnStart = 0,  // tracks the start of the current path such as c^d() in the above example\n\t\tprnStack = {}, // tracks parens which are not function calls, and so are associated with new bndStack contexts\n\t\tfnCall = {},   // We are in a function call\n\t\tpathStart = {},// tracks the start of the current path such as c^d() in the above example\n\t\tcompiledPathStart = {0: 0},\n\t\tcompiledPath = {0:\"\"},\n\t\tprevIndex = 0;\n\n\tif (params[0] === \"@\") {\n\t\tparams = params.replace(rBracketQuote, \".\");\n\t}\n\tresult = (params + (tmpl ? \" \" : \"\")).replace($sub.rPrm, parseTokens);\n\n\tif (bindings) {\n\t\tresult = compiledPath[0];\n\t}\n\n\treturn !prnDp && result || syntaxError(params); // Syntax error if unbalanced parens in params expression\n}\n\nfunction buildCode(ast, tmpl, isLinkExpr) {\n\t// Build the template function code from the AST nodes, and set as property on the passed-in template object\n\t// Used for compiling templates, and also by JsViews to build functions for data link expressions\n\tvar i, node, tagName, converter, tagCtx, hasTag, hasEncoder, getsVal, hasCnvt, useCnvt, tmplBindings, pathBindings, params, boundOnErrStart,\n\t\tboundOnErrEnd, tagRender, nestedTmpls, tmplName, nestedTmpl, tagAndElses, content, markup, nextIsElse, oldCode, isElse, isGetVal, tagCtxFn,\n\t\tonError, tagStart, trigger, lateRender, retStrOpen, retStrClose,\n\t\ttmplBindingKey = 0,\n\t\tuseViews = $subSettingsAdvanced.useViews || tmpl.useViews || tmpl.tags || tmpl.templates || tmpl.helpers || tmpl.converters,\n\t\tcode = \"\",\n\t\ttmplOptions = {},\n\t\tl = ast.length;\n\n\tif (\"\" + tmpl === tmpl) {\n\t\ttmplName = isLinkExpr ? 'data-link=\"' + tmpl.replace(rNewLine, \" \").slice(1, -1) + '\"' : tmpl;\n\t\ttmpl = 0;\n\t} else {\n\t\ttmplName = tmpl.tmplName || \"unnamed\";\n\t\tif (tmpl.allowCode) {\n\t\t\ttmplOptions.allowCode = true;\n\t\t}\n\t\tif (tmpl.debug) {\n\t\t\ttmplOptions.debug = true;\n\t\t}\n\t\ttmplBindings = tmpl.bnds;\n\t\tnestedTmpls = tmpl.tmpls;\n\t}\n\tfor (i = 0; i < l; i++) {\n\t\t// AST nodes: [0: tagName, 1: converter, 2: content, 3: params, 4: code, 5: onError, 6: trigger, 7:pathBindings, 8: contentMarkup]\n\t\tnode = ast[i];\n\n\t\t// Add newline for each callout to t() c() etc. and each markup string\n\t\tif (\"\" + node === node) {\n\t\t\t// a markup string to be inserted\n\t\t\tcode += '+\"' + node + '\"';\n\t\t} else {\n\t\t\t// a compiled tag expression to be inserted\n\t\t\ttagName = node[0];\n\t\t\tif (tagName === \"*\") {\n\t\t\t\t// Code tag: {{* }}\n\t\t\t\tcode += \";\\n\" + node[1] + \"\\nret=ret\";\n\t\t\t} else {\n\t\t\t\tconverter = node[1];\n\t\t\t\tcontent = !isLinkExpr && node[2];\n\t\t\t\ttagCtx = paramStructure(node[3], params = node[4]);\n\t\t\t\ttrigger = node[6];\n\t\t\t\tlateRender = node[7];\n\t\t\t\tif (node[8]) { // latePath @a.b.c or @~a.b.c\n\t\t\t\t\tretStrOpen = \"\\nvar ob,ltOb={},ctxs=\";\n\t\t\t\t\tretStrClose = \";\\nctxs.lt=ltOb.lt;\\nreturn ctxs;\";\n\t\t\t\t} else {\n\t\t\t\t\tretStrOpen = \"\\nreturn \";\n\t\t\t\t\tretStrClose = \"\";\n\t\t\t\t}\n\t\t\t\tmarkup = node[10] && node[10].replace(rUnescapeQuotes, \"$1\");\n\t\t\t\tif (isElse = tagName === \"else\") {\n\t\t\t\t\tif (pathBindings) {\n\t\t\t\t\t\tpathBindings.push(node[9]);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tonError = node[5] || $subSettings.debugMode !== false && \"undefined\"; // If debugMode not false, set default onError handler on tag to \"undefined\" (see onRenderError)\n\t\t\t\t\tif (tmplBindings && (pathBindings = node[9])) { // Array of paths, or false if not data-bound\n\t\t\t\t\t\tpathBindings = [pathBindings];\n\t\t\t\t\t\ttmplBindingKey = tmplBindings.push(1); // Add placeholder in tmplBindings for compiled function\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tuseViews = useViews || params[1] || params[2] || pathBindings || /view.(?!index)/.test(params[0]);\n\t\t\t\t// useViews is for perf optimization. For render() we only use views if necessary - for the more advanced scenarios.\n\t\t\t\t// We use views if there are props, contextual properties or args with #... (other than #index) - but you can force\n\t\t\t\t// using the full view infrastructure, (and pay a perf price) by opting in: Set useViews: true on the template, manually...\n\t\t\t\tif (isGetVal = tagName === \":\") {\n\t\t\t\t\tif (converter) {\n\t\t\t\t\t\ttagName = converter === HTML ? \">\" : converter + tagName;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (content) { // TODO optimize - if content.length === 0 or if there is a tmpl=\"...\" specified - set content to null / don't run this compilation code - since content won't get used!!\n\t\t\t\t\t\t// Create template object for nested template\n\t\t\t\t\t\tnestedTmpl = tmplObject(markup, tmplOptions);\n\t\t\t\t\t\tnestedTmpl.tmplName = tmplName + \"/\" + tagName;\n\t\t\t\t\t\t// Compile to AST and then to compiled function\n\t\t\t\t\t\tnestedTmpl.useViews = nestedTmpl.useViews || useViews;\n\t\t\t\t\t\tbuildCode(content, nestedTmpl);\n\t\t\t\t\t\tuseViews = nestedTmpl.useViews;\n\t\t\t\t\t\tnestedTmpls.push(nestedTmpl);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!isElse) {\n\t\t\t\t\t\t// This is not an else tag.\n\t\t\t\t\t\ttagAndElses = tagName;\n\t\t\t\t\t\tuseViews = useViews || tagName && (!$tags[tagName] || !$tags[tagName].flow);\n\t\t\t\t\t\t// Switch to a new code string for this bound tag (and its elses, if it has any) - for returning the tagCtxs array\n\t\t\t\t\t\toldCode = code;\n\t\t\t\t\t\tcode = \"\";\n\t\t\t\t\t}\n\t\t\t\t\tnextIsElse = ast[i + 1];\n\t\t\t\t\tnextIsElse = nextIsElse && nextIsElse[0] === \"else\";\n\t\t\t\t}\n\t\t\t\ttagStart = onError ? \";\\ntry{\\nret+=\" : \"\\n+\";\n\t\t\t\tboundOnErrStart = \"\";\n\t\t\t\tboundOnErrEnd = \"\";\n\n\t\t\t\tif (isGetVal && (pathBindings || trigger || converter && converter !== HTML || lateRender)) {\n\t\t\t\t\t// For convertVal we need a compiled function to return the new tagCtx(s)\n\t\t\t\t\ttagCtxFn = new Function(\"data,view,j\", \"// \" + tmplName + \" \" + (++tmplBindingKey) + \" \" + tagName\n\t\t\t\t\t\t+ retStrOpen + \"{\" + tagCtx + \"};\" + retStrClose);\n\t\t\t\t\ttagCtxFn._er = onError;\n\t\t\t\t\ttagCtxFn._tag = tagName;\n\t\t\t\t\ttagCtxFn._bd = !!pathBindings; // data-linked tag {^{.../}}\n\t\t\t\t\ttagCtxFn._lr = lateRender;\n\n\t\t\t\t\tif (isLinkExpr) {\n\t\t\t\t\t\treturn tagCtxFn;\n\t\t\t\t\t}\n\n\t\t\t\t\tsetPaths(tagCtxFn, pathBindings);\n\t\t\t\t\ttagRender = 'c(\"' + converter + '\",view,';\n\t\t\t\t\tuseCnvt = true;\n\t\t\t\t\tboundOnErrStart = tagRender + tmplBindingKey + \",\";\n\t\t\t\t\tboundOnErrEnd = \")\";\n\t\t\t\t}\n\t\t\t\tcode += (isGetVal\n\t\t\t\t\t? (isLinkExpr ? (onError ? \"try{\\n\" : \"\") + \"return \" : tagStart) + (useCnvt // Call _cnvt if there is a converter: {{cnvt: ... }} or {^{cnvt: ... }}\n\t\t\t\t\t\t? (useCnvt = undefined, useViews = hasCnvt = true, tagRender + (tagCtxFn\n\t\t\t\t\t\t\t? ((tmplBindings[tmplBindingKey - 1] = tagCtxFn), tmplBindingKey) // Store the compiled tagCtxFn in tmpl.bnds, and pass the key to convertVal()\n\t\t\t\t\t\t\t: \"{\" + tagCtx + \"}\") + \")\")\n\t\t\t\t\t\t: tagName === \">\"\n\t\t\t\t\t\t\t? (hasEncoder = true, \"h(\" + params[0] + \")\")\n\t\t\t\t\t\t\t: (getsVal = true, \"((v=\" + params[0] + ')!=null?v:' + (isLinkExpr ? 'null)' : '\"\")'))\n\t\t\t\t\t\t\t// Non strict equality so data-link=\"title{:expr}\" with expr=null/undefined removes title attribute\n\t\t\t\t\t)\n\t\t\t\t\t: (hasTag = true, \"\\n{view:view,content:false,tmpl:\" // Add this tagCtx to the compiled code for the tagCtxs to be passed to renderTag()\n\t\t\t\t\t\t+ (content ? nestedTmpls.length : \"false\") + \",\" // For block tags, pass in the key (nestedTmpls.length) to the nested content template\n\t\t\t\t\t\t+ tagCtx + \"},\"));\n\n\t\t\t\tif (tagAndElses && !nextIsElse) {\n\t\t\t\t\t// This is a data-link expression or an inline tag without any elses, or the last {{else}} of an inline tag\n\t\t\t\t\t// We complete the code for returning the tagCtxs array\n\t\t\t\t\tcode = \"[\" + code.slice(0, -1) + \"]\";\n\t\t\t\t\ttagRender = 't(\"' + tagAndElses + '\",view,this,';\n\t\t\t\t\tif (isLinkExpr || pathBindings) {\n\t\t\t\t\t\t// This is a bound tag (data-link expression or inline bound tag {^{tag ...}}) so we store a compiled tagCtxs function in tmp.bnds\n\t\t\t\t\t\tcode = new Function(\"data,view,j\", \" // \" + tmplName + \" \" + tmplBindingKey + \" \" + tagAndElses + retStrOpen + code\n\t\t\t\t\t\t\t+ retStrClose);\n\t\t\t\t\t\tcode._er = onError;\n\t\t\t\t\t\tcode._tag = tagAndElses;\n\t\t\t\t\t\tif (pathBindings) {\n\t\t\t\t\t\t\tsetPaths(tmplBindings[tmplBindingKey - 1] = code, pathBindings);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcode._lr = lateRender;\n\t\t\t\t\t\tif (isLinkExpr) {\n\t\t\t\t\t\t\treturn code; // For a data-link expression we return the compiled tagCtxs function\n\t\t\t\t\t\t}\n\t\t\t\t\t\tboundOnErrStart = tagRender + tmplBindingKey + \",undefined,\";\n\t\t\t\t\t\tboundOnErrEnd = \")\";\n\t\t\t\t\t}\n\n\t\t\t\t\t// This is the last {{else}} for an inline tag.\n\t\t\t\t\t// For a bound tag, pass the tagCtxs fn lookup key to renderTag.\n\t\t\t\t\t// For an unbound tag, include the code directly for evaluating tagCtxs array\n\t\t\t\t\tcode = oldCode + tagStart + tagRender + (pathBindings && tmplBindingKey || code) + \")\";\n\t\t\t\t\tpathBindings = 0;\n\t\t\t\t\ttagAndElses = 0;\n\t\t\t\t}\n\t\t\t\tif (onError && !nextIsElse) {\n\t\t\t\t\tuseViews = true;\n\t\t\t\t\tcode += ';\\n}catch(e){ret' + (isLinkExpr ? \"urn \" : \"+=\") + boundOnErrStart + 'j._err(e,view,' + onError + ')' + boundOnErrEnd + ';}' + (isLinkExpr ? \"\" : '\\nret=ret');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Include only the var references that are needed in the code\n\tcode = \"// \" + tmplName\n\t\t+ (tmplOptions.debug ? \"\\ndebugger;\" : \"\")\n\t\t+ \"\\nvar v\"\n\t\t+ (hasTag ? \",t=j._tag\" : \"\")                // has tag\n\t\t+ (hasCnvt ? \",c=j._cnvt\" : \"\")              // converter\n\t\t+ (hasEncoder ? \",h=j._html\" : \"\")           // html converter\n\t\t+ (isLinkExpr\n\t\t\t\t? (node[8] // late @... path?\n\t\t\t\t\t\t? \", ob\"\n\t\t\t\t\t\t: \"\"\n\t\t\t\t\t) + \";\\n\"\n\t\t\t\t: ',ret=\"\"')\n\t\t+ code\n\t\t+ (isLinkExpr ? \"\\n\" : \";\\nreturn ret;\");\n\n\ttry {\n\t\tcode = new Function(\"data,view,j\", code);\n\t} catch (e) {\n\t\tsyntaxError(\"Compiled template code:\\n\\n\" + code + '\\n: \"' + (e.message||e) + '\"');\n\t}\n\tif (tmpl) {\n\t\ttmpl.fn = code;\n\t\ttmpl.useViews = !!useViews;\n\t}\n\treturn code;\n}\n\n//==========\n// Utilities\n//==========\n\n// Merge objects, in particular contexts which inherit from parent contexts\nfunction extendCtx(context, parentContext) {\n\t// Return copy of parentContext, unless context is defined and is different, in which case return a new merged context\n\t// If neither context nor parentContext are defined, return undefined\n\treturn context && context !== parentContext\n\t\t? (parentContext\n\t\t\t? $extend($extend({}, parentContext), context)\n\t\t\t: context)\n\t\t: parentContext && $extend({}, parentContext);\n}\n\nfunction getTargetProps(source, tagCtx) {\n\t// this pointer is theMap - which has tagCtx.props too\n\t// arguments: tagCtx.args.\n\tvar key, prop,\n\t\tmap = tagCtx.map,\n\t\tpropsArr = map && map.propsArr;\n\n\tif (!propsArr) { // map.propsArr is the full array of {key:..., prop:...} objects\n\t\tpropsArr = [];\n\t\tif (typeof source === OBJECT || $isFunction(source)) {\n\t\t\tfor (key in source) {\n\t\t\t\tprop = source[key];\n\t\t\t\tif (key !== $expando && source.hasOwnProperty(key) && (!tagCtx.props.noFunctions || !$.isFunction(prop))) {\n\t\t\t\t\tpropsArr.push({key: key, prop: prop});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (map) {\n\t\t\tmap.propsArr = map.options && propsArr; // If bound {^{props}} and not isRenderCall, store propsArr on map (map.options is defined only for bound, && !isRenderCall)\n\t\t}\n\t}\n\treturn getTargetSorted(propsArr, tagCtx); // Obtains map.tgt, by filtering, sorting and splicing the full propsArr\n}\n\nfunction getTargetSorted(value, tagCtx) {\n\t// getTgt\n\tvar mapped, start, end,\n\t\ttag = tagCtx.tag,\n\t\tprops = tagCtx.props,\n\t\tpropParams = tagCtx.params.props,\n\t\tfilter = props.filter,\n\t\tsort = props.sort,\n\t\tdirectSort = sort === true,\n\t\tstep = parseInt(props.step),\n\t\treverse = props.reverse ? -1 : 1;\n\n\tif (!$isArray(value)) {\n\t\treturn value;\n\t}\n\tif (directSort || sort && \"\" + sort === sort) {\n\t\t// Temporary mapped array holds objects with index and sort-value\n\t\tmapped = value.map(function(item, i) {\n\t\t\titem = directSort ? item : getPathObject(item, sort);\n\t\t\treturn {i: i, v: \"\" + item === item ? item.toLowerCase() : item};\n\t\t});\n\t\t// Sort mapped array\n\t\tmapped.sort(function(a, b) {\n\t\t\treturn a.v > b.v ? reverse : a.v < b.v ? -reverse : 0;\n\t\t});\n\t\t// Map to new array with resulting order\n\t\tvalue = mapped.map(function(item){\n\t\t\treturn value[item.i];\n\t\t});\n\t} else if ((sort || reverse < 0) && !tag.dataMap) {\n\t\tvalue = value.slice(); // Clone array first if not already a new array\n\t}\n\tif ($isFunction(sort)) {\n\t\tvalue = value.sort(function() { // Wrap the sort function to provide tagCtx as 'this' pointer\n\t\t\treturn sort.apply(tagCtx, arguments);\n\t\t});\n\t}\n\tif (reverse < 0 && (!sort || $isFunction(sort))) { // Reverse result if not already reversed in sort\n\t\tvalue = value.reverse();\n\t}\n\n\tif (value.filter && filter) { // IE8 does not support filter\n\t\tvalue = value.filter(filter, tagCtx);\n\t\tif (tagCtx.tag.onFilter) {\n\t\t\ttagCtx.tag.onFilter(tagCtx);\n\t\t}\n\t}\n\n\tif (propParams.sorted) {\n\t\tmapped = (sort || reverse < 0) ? value : value.slice();\n\t\tif (tag.sorted) {\n\t\t\t$.observable(tag.sorted).refresh(mapped); // Note that this might cause the start and end props to be modified - e.g. by pager tag control\n\t\t} else {\n\t\t\ttagCtx.map.sorted = mapped;\n\t\t}\n\t}\n\n\tstart = props.start; // Get current value - after possible changes triggered by tag.sorted refresh() above\n\tend = props.end;\n\tif (propParams.start && start === undefined || propParams.end && end === undefined) {\n\t\tstart = end = 0;\n\t}\n\tif (!isNaN(start) || !isNaN(end)) { // start or end specified, but not the auto-create Number array scenario of {{for start=xxx end=yyy}}\n\t\tstart = +start || 0;\n\t\tend = end === undefined || end > value.length ? value.length : +end;\n\t\tvalue = value.slice(start, end);\n\t}\n\tif (step > 1) {\n\t\tstart = 0;\n\t\tend = value.length;\n\t\tmapped = [];\n\t\tfor (; start<end; start+=step) {\n\t\t\tmapped.push(value[start]);\n\t\t}\n\t\tvalue = mapped;\n\t}\n\tif (propParams.paged && tag.paged) {\n\t\t$observable(tag.paged).refresh(value);\n\t}\n\n\treturn value;\n}\n\n/** Render the template as a string, using the specified data and helpers/context\n* $(\"#tmpl\").render()\n*\n* @param {any}        data\n* @param {hash}       [helpersOrContext]\n* @param {boolean}    [noIteration]\n* @returns {string}   rendered template\n*/\nfunction $fnRender(data, context, noIteration) {\n\tvar tmplElem = this.jquery && (this[0] || error('Unknown template')), // Targeted element not found for jQuery template selector such as \"#myTmpl\"\n\t\ttmpl = tmplElem.getAttribute(tmplAttr);\n\n\treturn renderContent.call(tmpl && $.data(tmplElem)[jsvTmpl] || $templates(tmplElem),\n\t\tdata, context, noIteration);\n}\n\n//========================== Register converters ==========================\n\nfunction getCharEntity(ch) {\n\t// Get character entity for HTML, Attribute and optional data encoding\n\treturn charEntities[ch] || (charEntities[ch] = \"&#\" + ch.charCodeAt(0) + \";\");\n}\n\nfunction getCharFromEntity(match, token) {\n\t// Get character from HTML entity, for optional data unencoding\n\treturn charsFromEntities[token] || \"\";\n}\n\nfunction htmlEncode(text) {\n\t// HTML encode: Replace < > & ' \" ` etc. by corresponding entities.\n\treturn text != undefined ? rIsHtml.test(text) && (\"\" + text).replace(rHtmlEncode, getCharEntity) || text : \"\";\n}\n\nfunction dataEncode(text) {\n\t// Encode just < > and & - intended for 'safe data' along with {{:}} rather than {{>}}\n  return \"\" + text === text ? text.replace(rDataEncode, getCharEntity) : text;\n}\n\nfunction dataUnencode(text) {\n  // Unencode just < > and & - intended for 'safe data' along with {{:}} rather than {{>}}\n  return \"\" + text === text ? text.replace(rDataUnencode, getCharFromEntity) : text;\n}\n\n//========================== Initialize ==========================\n\n$sub = $views.sub;\n$viewsSettings = $views.settings;\n\nif (!(jsr || $ && $.render)) {\n\t// JsRender/JsViews not already loaded (or loaded without jQuery, and we are now moving from jsrender namespace to jQuery namepace)\n\tfor (jsvStoreName in jsvStores) {\n\t\tregisterStore(jsvStoreName, jsvStores[jsvStoreName]);\n\t}\n\n\t$converters = $views.converters;\n\t$helpers = $views.helpers;\n\t$tags = $views.tags;\n\n\t$sub._tg.prototype = {\n\t\tbaseApply: baseApply,\n\t\tcvtArgs: convertArgs,\n\t\tbndArgs: convertBoundArgs,\n\t\tctxPrm: contextParameter\n\t};\n\n\ttopView = $sub.topView = new View();\n\n\t//BROWSER-SPECIFIC CODE\n\tif ($) {\n\n\t\t////////////////////////////////////////////////////////////////////////////////////////////////\n\t\t// jQuery (= $) is loaded\n\n\t\t$.fn.render = $fnRender;\n\t\t$expando = $.expando;\n\t\tif ($.observable) {\n\t\t\tif (versionNumber !== (versionNumber = $.views.jsviews)) {\n\t\t\t\t// Different version of jsRender was loaded\n\t\t\t\tthrow \"jquery.observable.js requires jsrender.js \" + versionNumber;\n\t\t\t}\n\t\t\t$extend($sub, $.views.sub); // jquery.observable.js was loaded before jsrender.js\n\t\t\t$views.map = $.views.map;\n\t\t}\n\n\t} else {\n\t\t////////////////////////////////////////////////////////////////////////////////////////////////\n\t\t// jQuery is not loaded.\n\n\t\t$ = {};\n\n\t\tif (setGlobals) {\n\t\t\tglobal.jsrender = $; // We are loading jsrender.js from a script element, not AMD or CommonJS, so set global\n\t\t}\n\n\t\t// Error warning if jsrender.js is used as template engine on Node.js (e.g. Express or Hapi...)\n\t\t// Use jsrender-node.js instead...\n\t\t$.renderFile = $.__express = $.compile = function() { throw \"Node.js: use npm jsrender, or jsrender-node.js\"; };\n\n\t\t//END BROWSER-SPECIFIC CODE\n\t\t$.isFunction = function(ob) {\n\t\t\treturn typeof ob === \"function\";\n\t\t};\n\n\t\t$.isArray = Array.isArray || function(obj) {\n\t\t\treturn ({}.toString).call(obj) === \"[object Array]\";\n\t\t};\n\n\t\t$sub._jq = function(jq) { // private method to move from JsRender APIs from jsrender namespace to jQuery namespace\n\t\t\tif (jq !== $) {\n\t\t\t\t$extend(jq, $); // map over from jsrender namespace to jQuery namespace\n\t\t\t\t$ = jq;\n\t\t\t\t$.fn.render = $fnRender;\n\t\t\t\tdelete $.jsrender;\n\t\t\t\t$expando = $.expando;\n\t\t\t}\n\t\t};\n\n\t\t$.jsrender = versionNumber;\n\t}\n\t$subSettings = $sub.settings;\n\t$subSettings.allowCode = false;\n\t$isFunction = $.isFunction;\n\t$.render = $render;\n\t$.views = $views;\n\t$.templates = $templates = $views.templates;\n\n\tfor (setting in $subSettings) {\n\t\taddSetting(setting);\n\t}\n\n\t/**\n\t* $.views.settings.debugMode(true)\n\t* @param {boolean} debugMode\n\t* @returns {Settings}\n\t*\n\t* debugMode = $.views.settings.debugMode()\n\t* @returns {boolean}\n\t*/\n\t($viewsSettings.debugMode = function(debugMode) {\n\t\treturn debugMode === undefined\n\t\t\t? $subSettings.debugMode\n\t\t\t: (\n\t\t\t\t$subSettings._clFns && $subSettings._clFns(), // Clear linkExprStore (cached compiled expressions), since debugMode setting affects compilation for expressions\n\t\t\t\t$subSettings.debugMode = debugMode,\n\t\t\t\t$subSettings.onError = debugMode + \"\" === debugMode\n\t\t\t\t\t? function() { return debugMode; }\n\t\t\t\t\t: $isFunction(debugMode)\n\t\t\t\t\t\t? debugMode\n\t\t\t\t\t\t: undefined,\n\t\t\t\t$viewsSettings);\n\t})(false); // jshint ignore:line\n\n\t$subSettingsAdvanced = $subSettings.advanced = {\n\t\tcache: true, // By default use cached values of computed values (Otherwise, set advanced cache setting to false)\n\t\tuseViews: false,\n\t\t_jsv: false // For global access to JsViews store\n\t};\n\n\t//========================== Register tags ==========================\n\n\t$tags({\n\t\t\"if\": {\n\t\t\trender: function(val) {\n\t\t\t\t// This function is called once for {{if}} and once for each {{else}}.\n\t\t\t\t// We will use the tag.rendering object for carrying rendering state across the calls.\n\t\t\t\t// If not done (a previous block has not been rendered), look at expression for this block and render the block if expression is truthy\n\t\t\t\t// Otherwise return \"\"\n\t\t\t\tvar self = this,\n\t\t\t\t\ttagCtx = self.tagCtx,\n\t\t\t\t\tret = (self.rendering.done || !val && (tagCtx.args.length || !tagCtx.index))\n\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t: (self.rendering.done = true,\n\t\t\t\t\t\t\tself.selected = tagCtx.index,\n\t\t\t\t\t\t\tundefined); // Test is satisfied, so render content on current context\n\t\t\t\treturn ret;\n\t\t\t},\n\t\t\tcontentCtx: true, // Inherit parent view data context\n\t\t\tflow: true\n\t\t},\n\t\t\"for\": {\n\t\t\tsortDataMap: dataMap(getTargetSorted),\n\t\t\tinit: function(val, cloned) {\n\t\t\t\tthis.setDataMap(this.tagCtxs);\n\t\t\t},\n\t\t\trender: function(val) {\n\t\t\t\t// This function is called once for {{for}} and once for each {{else}}.\n\t\t\t\t// We will use the tag.rendering object for carrying rendering state across the calls.\n\t\t\t\tvar value, filter, srtField, isArray, i, sorted, end, step,\n\t\t\t\t\tself = this,\n\t\t\t\t\ttagCtx = self.tagCtx,\n\t\t\t\t\trange = tagCtx.argDefault === false,\n\t\t\t\t\tprops = tagCtx.props,\n\t\t\t\t\titerate = range || tagCtx.args.length, // Not final else and not auto-create range\n\t\t\t\t\tresult = \"\",\n\t\t\t\t\tdone = 0;\n\n\t\t\t\tif (!self.rendering.done) {\n\t\t\t\t\tvalue = iterate ? val : tagCtx.view.data; // For the final else, defaults to current data without iteration.\n\n\t\t\t\t\tif (range) {\n\t\t\t\t\t\trange = props.reverse ? \"unshift\" : \"push\";\n\t\t\t\t\t\tend = +props.end;\n\t\t\t\t\t\tstep = +props.step || 1;\n\t\t\t\t\t\tvalue = []; // auto-create integer array scenario of {{for start=xxx end=yyy}}\n\t\t\t\t\t\tfor (i = +props.start || 0; (end - i) * step > 0; i += step) {\n\t\t\t\t\t\t\tvalue[range](i);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (value !== undefined) {\n\t\t\t\t\t\tisArray = $isArray(value);\n\t\t\t\t\t\tresult += tagCtx.render(value, !iterate || props.noIteration);\n\t\t\t\t\t\t// Iterates if data is an array, except on final else - or if noIteration property\n\t\t\t\t\t\t// set to true. (Use {{include}} to compose templates without array iteration)\n\t\t\t\t\t\tdone += isArray ? value.length : 1;\n\t\t\t\t\t}\n\t\t\t\t\tif (self.rendering.done = done) {\n\t\t\t\t\t\tself.selected = tagCtx.index;\n\t\t\t\t\t}\n\t\t\t\t\t// If nothing was rendered we will look at the next {{else}}. Otherwise, we are done.\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\tsetDataMap: function(tagCtxs) {\n\t\t\t\tvar tagCtx, props, paramsProps,\n\t\t\t\t\tself = this,\n\t\t\t\t\tl = tagCtxs.length;\n\t\t\t\twhile (l--) {\n\t\t\t\t\ttagCtx = tagCtxs[l];\n\t\t\t\t\tprops = tagCtx.props;\n\t\t\t\t\tparamsProps = tagCtx.params.props;\n\t\t\t\t\ttagCtx.argDefault = props.end === undefined || tagCtx.args.length > 0; // Default to #data except for auto-create range scenario {{for start=xxx end=yyy step=zzz}}\n\t\t\t\t\tprops.dataMap = (tagCtx.argDefault !== false && $isArray(tagCtx.args[0]) &&\n\t\t\t\t\t\t(paramsProps.sort || paramsProps.start || paramsProps.end || paramsProps.step || paramsProps.filter || paramsProps.reverse\n\t\t\t\t\t\t|| props.sort || props.start || props.end || props.step || props.filter || props.reverse))\n\t\t\t\t\t\t&& self.sortDataMap;\n\t\t\t\t}\n\t\t\t},\n\t\t\tflow: true\n\t\t},\n\t\tprops: {\n\t\t\tbaseTag: \"for\",\n\t\t\tdataMap: dataMap(getTargetProps),\n\t\t\tinit: noop, // Don't execute the base init() of the \"for\" tag\n\t\t\tflow: true\n\t\t},\n\t\tinclude: {\n\t\t\tflow: true\n\t\t},\n\t\t\"*\": {\n\t\t\t// {{* code... }} - Ignored if template.allowCode and $.views.settings.allowCode are false. Otherwise include code in compiled template\n\t\t\trender: retVal,\n\t\t\tflow: true\n\t\t},\n\t\t\":*\": {\n\t\t\t// {{:* returnedExpression }} - Ignored if template.allowCode and $.views.settings.allowCode are false. Otherwise include code in compiled template\n\t\t\trender: retVal,\n\t\t\tflow: true\n\t\t},\n\t\tdbg: $helpers.dbg = $converters.dbg = dbgBreak // Register {{dbg/}}, {{dbg:...}} and ~dbg() to throw and catch, as breakpoints for debugging.\n\t});\n\n\t$converters({\n\t\thtml: htmlEncode,\n\t\tattr: htmlEncode, // Includes > encoding since rConvertMarkers in JsViews does not skip > characters in attribute strings\n\t\tencode: dataEncode,\n\t\tunencode: dataUnencode, // Includes > encoding since rConvertMarkers in JsViews does not skip > characters in attribute strings\n\t\turl: function(text) {\n\t\t\t// URL encoding helper.\n\t\t\treturn text != undefined ? encodeURI(\"\" + text) : text === null ? text : \"\"; // null returns null, e.g. to remove attribute. undefined returns \"\"\n\t\t}\n\t});\n}\n//========================== Define default delimiters ==========================\n$subSettings = $sub.settings;\n$isArray = ($||jsr).isArray;\n$viewsSettings.delimiters(\"{{\", \"}}\", \"^\");\n\nif (jsrToJq) { // Moving from jsrender namespace to jQuery namepace - copy over the stored items (templates, converters, helpers...)\n\tjsr.views.sub._jq($);\n}\nreturn $ || jsr;\n}, window));\n"]}