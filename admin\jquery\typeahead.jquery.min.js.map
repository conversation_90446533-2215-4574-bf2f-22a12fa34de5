{"version": 3, "sources": ["typeahead.jquery.js"], "names": ["root", "factory", "define", "amd", "a0", "exports", "module", "require", "j<PERSON><PERSON><PERSON>", "this", "$", "_", "is<PERSON><PERSON>", "test", "navigator", "userAgent", "match", "isBlankString", "str", "escapeRegExChars", "replace", "isString", "obj", "isNumber", "isArray", "isFunction", "isObject", "isPlainObject", "isUndefined", "isElement", "nodeType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toStr", "s", "bind", "proxy", "each", "collection", "cb", "index", "value", "map", "filter", "grep", "every", "result", "key", "val", "call", "some", "mixin", "extend", "identity", "x", "clone", "getIdGenerator", "counter", "templatify", "String", "defer", "fn", "setTimeout", "debounce", "func", "wait", "immediate", "timeout", "later", "callNow", "context", "args", "arguments", "apply", "clearTimeout", "throttle", "previous", "Date", "now", "remaining", "stringify", "JSON", "noop", "WWW", "defaultClassNames", "wrapper", "input", "hint", "menu", "dataset", "suggestion", "selectable", "empty", "open", "cursor", "highlight", "o", "www", "classes", "css", "buildCss", "html", "buildHtml", "selectors", "buildSelectors", "c", "v", "k", "position", "display", "top", "left", "borderColor", "boxShadow", "opacity", "verticalAlign", "backgroundColor", "inputWithNoHint", "zIndex", "ltr", "right", "rtl", "backgroundImage", "EventBus", "deprecationMap", "el", "error", "$el", "render", "cursorchange", "select", "autocomplete", "prototype", "_trigger", "type", "$e", "Event", "unshift", "trigger", "before", "slice", "isDefaultPrevented", "deprecatedType", "EventEmitter", "splitter", "nextTick", "nextTickFn", "window", "setImmediate", "getNextTick", "onSync", "types", "on", "onAsync", "off", "_callbacks", "split", "shift", "callbacks", "syncFlush", "asyncFlush", "getFlush", "sync", "concat", "async", "method", "bindContext", "push", "cancelled", "i", "len", "length", "doc", "defaults", "node", "pattern", "tagName", "className", "wordsOnly", "caseSensitive", "regex", "patterns", "regexStr", "escapedPatterns", "join", "RegExp", "getRegex", "traverse", "hightlightTextNode", "childNode", "childNodes", "textNode", "patternNode", "wrapperNode", "exec", "data", "createElement", "splitText", "append<PERSON><PERSON><PERSON>", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "document", "Input", "specialKeyCodeMap", "$input", "$hint", "query", "queryWhenFocused", "hasFocus", "$overflowHelper", "visibility", "whiteSpace", "fontFamily", "fontSize", "fontStyle", "fontVariant", "fontWeight", "wordSpacing", "letterSpacing", "textIndent", "textRendering", "textTransform", "insertAfter", "_checkLanguageDirection", "setHint", "getHint", "clearHint", "clearHintIfInvalid", "9", "27", "37", "39", "13", "38", "40", "normalizeQuery", "_onBlur", "resetInputValue", "_onFocus", "_onKeydown", "keyName", "which", "keyCode", "_managePreventDefault", "_should<PERSON><PERSON>ger", "_onInput", "_setQuery", "getInputValue", "preventDefault", "withModifier", "dir", "toLowerCase", "attr", "silent", "areEquivalent", "hasDifferentWhitespace", "a", "b", "onBlur", "onFocus", "onKeydown", "onInput", "that", "focus", "blur", "getLangDir", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setInputValue", "hasQueryChangedSinceLastFocus", "valIsPrefixOfHint", "indexOf", "hasOverflow", "is", "constraint", "width", "text", "isCursorAtEnd", "valueLength", "selectionStart", "range", "selection", "createRange", "moveStart", "destroy", "remove", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "Dataset", "keys", "nameGenerator", "templates", "displayFn", "notFound", "source", "name", "limit", "displayKey", "pending", "header", "footer", "__ttAdapter", "_resetLastSuggestion", "addClass", "extractData", "_overwrite", "suggestions", "_renderSuggestions", "_renderPending", "_renderNotFound", "_empty", "_append", "$lastSuggestion", "_appendSuggestions", "$fragment", "_getSuggestionsFragment", "children", "last", "prepend", "_getHeader", "append", "_getFooter", "after", "template", "fragment", "createDocumentFragment", "_injectQuery", "_query", "update", "canceled", "syncCalled", "rendered", "cancel", "clear", "isEmpty", "<PERSON><PERSON>", "$node", "datasets", "oDataset", "find", "first", "appendTo", "_onSelectableClick", "currentTarget", "_onRendered", "toggleClass", "_allDatasetsEmpty", "_onCleared", "_propagate", "_getSelectables", "_removeCursor", "$selectable", "getActiveSelectable", "removeClass", "_ensureVisible", "elTop", "elBottom", "nodeScrollTop", "nodeHeight", "outerHeight", "scrollTop", "height", "parseInt", "onSelectableClick", "isOpen", "hasClass", "close", "setLanguageDirection", "selectableRelativeToCursor", "delta", "$selectables", "$oldCursor", "newIndex", "eq", "setCursor", "getSelectableData", "getTopSelectable", "isValidUpdate", "DefaultMenu", "_show", "_hide", "hide", "Typeahead", "onFocused", "onBlurred", "onEnterKeyed", "onTabKeyed", "onEscKeyed", "onUpKeyed", "onDownKeyed", "onLeftKeyed", "onRightKeyed", "on<PERSON>ueryChanged", "onWhitespaceChanged", "eventBus", "<PERSON><PERSON><PERSON><PERSON>", "enabled", "active", "activate", "_hacks", "_onSelectableClicked", "_onAsyncRequested", "_onAsyncCanceled", "_onAsyncReceived", "_onDatasetRendered", "_onDatasetCleared", "_onLangDirChanged", "$menu", "isActive", "hasActive", "activeElement", "has", "stopImmediatePropagation", "_updateHint", "_onFocused", "_minLengthMet", "_onBlurred", "_onEnterKeyed", "_onTabKeyed", "_onEscKeyed", "_onUpKeyed", "moveCursor", "_onDownKeyed", "_onLeftKeyed", "_onRightKeyed", "_onQueryChanged", "e", "_onWhitespaceChanged", "_openIfActive", "<PERSON><PERSON><PERSON><PERSON>", "isEnabled", "enable", "disable", "deactivate", "setVal", "getVal", "$candidate", "payload", "ctx", "methods", "old", "ttEach", "$els", "typeahead", "buildHintFromInput", "removeData", "backgroundAttachment", "backgroundClip", "<PERSON><PERSON><PERSON><PERSON>", "backgroundPosition", "backgroundRepeat", "backgroundSize", "prop", "removeAttr", "spellcheck", "tabindex", "prepInput", "attrs", "style", "$elOrNull", "initialize", "classNames", "attach", "$wrapper", "defaultHint", "defaultMenu", "MenuConstructor", "d", "wrap", "parent", "t", "success", "newVal", "detach", "revert", "noConflict"], "mappings": ";;;;;CAMA,SAAUA,EAAMC,GACU,mBAAXC,QAAyBA,OAAOC,IACvCD,OAAO,eAAgB,CAAE,WAAY,SAASE,GAC1C,OAAOH,EAAQG,MAEO,iBAAZC,QACdC,OAAOD,QAAUJ,EAAQM,QAAQ,WAEjCN,EAAQO,QARhB,CAUGC,GAAM,SAASC,GACd,IAAIC,EAAI,WACJ,aACA,MAAO,CACHC,OAAQ,WACJ,QAAO,kBAAkBC,KAAKC,UAAUC,YAAaD,UAAUC,UAAUC,MAAM,4BAA4B,IAE/GC,cAAe,SAASC,GACpB,OAAQA,GAAO,QAAQL,KAAKK,IAEhCC,iBAAkB,SAASD,GACvB,OAAOA,EAAIE,QAAQ,sCAAuC,SAE9DC,SAAU,SAASC,GACf,MAAsB,iBAARA,GAElBC,SAAU,SAASD,GACf,MAAsB,iBAARA,GAElBE,QAASd,EAAEc,QACXC,WAAYf,EAAEe,WACdC,SAAUhB,EAAEiB,cACZC,YAAa,SAASN,GAClB,YAAsB,IAARA,GAElBO,UAAW,SAASP,GAChB,SAAUA,GAAwB,IAAjBA,EAAIQ,WAEzBC,SAAU,SAAST,GACf,OAAOA,aAAeZ,GAE1BsB,MAAO,SAAeC,GAClB,OAAOtB,EAAEiB,YAAYK,IAAY,OAANA,EAAa,GAAKA,EAAI,IAErDC,KAAMxB,EAAEyB,MACRC,KAAM,SAASC,EAAYC,GACvB5B,EAAE0B,KAAKC,GACP,SAAqBE,EAAOC,GACxB,OAAOF,EAAGE,EAAOD,OAGzBE,IAAK/B,EAAE+B,IACPC,OAAQhC,EAAEiC,KACVC,MAAO,SAAStB,EAAKT,GACjB,IAAIgC,GAAS,EACb,OAAKvB,GAGLZ,EAAE0B,KAAKd,GAAK,SAASwB,EAAKC,GACtB,KAAMF,EAAShC,EAAKmC,KAAK,KAAMD,EAAKD,EAAKxB,IACrC,OAAO,OAGNuB,GAPEA,GASfI,KAAM,SAAS3B,EAAKT,GAChB,IAAIgC,GAAS,EACb,OAAKvB,GAGLZ,EAAE0B,KAAKd,GAAK,SAASwB,EAAKC,GACtB,GAAIF,EAAShC,EAAKmC,KAAK,KAAMD,EAAKD,EAAKxB,GACnC,OAAO,OAGNuB,GAPEA,GASfK,MAAOxC,EAAEyC,OACTC,SAAU,SAASC,GACf,OAAOA,GAEXC,MAAO,SAAShC,GACZ,OAAOZ,EAAEyC,QAAO,EAAM,GAAI7B,IAE9BiC,eAAgB,WACZ,IAAIC,EAAU,EACd,OAAO,WACH,OAAOA,MAGfC,WAAY,SAAoBnC,GAC5B,OAAOZ,EAAEe,WAAWH,GAAOA,EAC3B,WACI,OAAOoC,OAAOpC,KAGtBqC,MAAO,SAASC,GACZC,WAAWD,EAAI,IAEnBE,SAAU,SAASC,EAAMC,EAAMC,GAC3B,IAAIC,EAASrB,EACb,OAAO,WACH,IAAsCsB,EAAOC,EAAzCC,EAAU5D,KAAM6D,EAAOC,UAa3B,OAZAJ,EAAQ,WACJD,EAAU,KACLD,IACDpB,EAASkB,EAAKS,MAAMH,EAASC,KAGrCF,EAAUH,IAAcC,EACxBO,aAAaP,GACbA,EAAUL,WAAWM,EAAOH,GACxBI,IACAvB,EAASkB,EAAKS,MAAMH,EAASC,IAE1BzB,IAGf6B,SAAU,SAASX,EAAMC,GACrB,IAAIK,EAASC,EAAMJ,EAASrB,EAAQ8B,EAAUR,EAO9C,OANAQ,EAAW,EACXR,EAAQ,WACJQ,EAAW,IAAIC,KACfV,EAAU,KACVrB,EAASkB,EAAKS,MAAMH,EAASC,IAE1B,WACH,IAAIO,EAAM,IAAID,KAAQE,EAAYd,GAAQa,EAAMF,GAWhD,OAVAN,EAAU5D,KACV6D,EAAOC,UACHO,GAAa,GACbL,aAAaP,GACbA,EAAU,KACVS,EAAWE,EACXhC,EAASkB,EAAKS,MAAMH,EAASC,IACrBJ,IACRA,EAAUL,WAAWM,EAAOW,IAEzBjC,IAGfkC,UAAW,SAAShC,GAChB,OAAOpC,EAAEU,SAAS0B,GAAOA,EAAMiC,KAAKD,UAAUhC,IAElDkC,KAAM,cArIN,GAwIJC,EAAM,WACN,aACA,IAAIC,EAAoB,CACpBC,QAAS,oBACTC,MAAO,WACPC,KAAM,UACNC,KAAM,UACNC,QAAS,aACTC,WAAY,gBACZC,WAAY,gBACZC,MAAO,WACPC,KAAM,UACNC,OAAQ,YACRC,UAAW,gBAEf,OACA,SAAeC,GACX,IAAIC,EAAKC,EAQT,OAPAA,EAAUtF,EAAEuC,MAAM,GAAIiC,EAAmBY,GAOlC,CACHG,KAPJF,EAAM,CACFE,IAAKC,IACLF,QAASA,EACTG,KAAMC,EAAUJ,GAChBK,UAAWC,EAAeN,KAGjBC,IACTE,KAAMJ,EAAII,KACVH,QAASD,EAAIC,QACbK,UAAWN,EAAIM,UACfpD,MAAO,SAAS6C,GACZpF,EAAEuC,MAAM6C,EAAGC,MAIvB,SAASK,EAAUG,GACf,MAAO,CACHpB,QAAS,gBAAkBoB,EAAEpB,QAAU,YACvCG,KAAM,eAAiBiB,EAAEjB,KAAO,YAGxC,SAASgB,EAAeN,GACpB,IAAIK,EAAY,GAIhB,OAHA3F,EAAEyB,KAAK6D,GAAS,SAASQ,EAAGC,GACxBJ,EAAUI,GAAK,IAAMD,KAElBH,EAEX,SAASH,IACL,IAAID,EAAM,CACNd,QAAS,CACLuB,SAAU,WACVC,QAAS,gBAEbtB,KAAM,CACFqB,SAAU,WACVE,IAAK,IACLC,KAAM,IACNC,YAAa,cACbC,UAAW,OACXC,QAAS,KAEb5B,MAAO,CACHsB,SAAU,WACVO,cAAe,MACfC,gBAAiB,eAErBC,gBAAiB,CACbT,SAAU,WACVO,cAAe,OAEnB3B,KAAM,CACFoB,SAAU,WACVE,IAAK,OACLC,KAAM,IACNO,OAAQ,MACRT,QAAS,QAEbU,IAAK,CACDR,KAAM,IACNS,MAAO,QAEXC,IAAK,CACDV,KAAM,OACNS,MAAO,OAQf,OALI5G,EAAEC,UACFD,EAAEuC,MAAMgD,EAAIb,MAAO,CACfoC,gBAAiB,wFAGlBvB,GA5FL,GA+FNwB,EAAW,WACX,aACA,IAAeC,EAQf,SAASD,EAAS3B,GACTA,GAAMA,EAAE6B,IACTlH,EAAEmH,MAAM,mCAEZpH,KAAKqH,IAAMpH,EAAEqF,EAAE6B,IAwBnB,MAnCY,aACZD,EAAiB,CACbI,OAAQ,WACRC,aAAc,gBACdC,OAAQ,WACRC,aAAc,iBAQlBvH,EAAEuC,MAAMwE,EAASS,UAAW,CACxBC,SAAU,SAASC,EAAM/D,GACrB,IAAIgE,EAIJ,OAHAA,EAAK5H,EAAE6H,MAhBH,aAgBqBF,IACxB/D,EAAOA,GAAQ,IAAIkE,QAAQF,GAC5B7H,KAAKqH,IAAIW,QAAQjE,MAAM/D,KAAKqH,IAAKxD,GAC1BgE,GAEXI,OAAQ,SAASL,GACb,IAAI/D,EAGJ,OAFAA,EAAO,GAAGqE,MAAM3F,KAAKuB,UAAW,GAC3B9D,KAAK2H,SAAS,SAAWC,EAAM/D,GAC1BsE,sBAEdH,QAAS,SAASJ,GACd,IAAIQ,EACJpI,KAAK2H,SAASC,EAAM,GAAGM,MAAM3F,KAAKuB,UAAW,KACzCsE,EAAiBlB,EAAeU,KAChC5H,KAAK2H,SAASS,EAAgB,GAAGF,MAAM3F,KAAKuB,UAAW,OAI5DmD,EAtCI,GAwCXoB,EAAe,WACf,aACA,IAAIC,EAAW,MAAOC,EAiEtB,WACI,IAAIC,EAEAA,EADAC,OAAOC,aACM,SAA8BvF,GACvCuF,cAAa,WACTvF,QAIK,SAA4BA,GACrCC,YAAW,WACPD,MACD,IAGX,OAAOqF,EAhFsBG,GACjC,MAAO,CACHC,OAyBJ,SAAgBC,EAAOhH,EAAI+B,GACvB,OAAOkF,EAAGvG,KAAKvC,KAAM,OAAQ6I,EAAOhH,EAAI+B,IAzBxCmF,QAqBJ,SAAiBF,EAAOhH,EAAI+B,GACxB,OAAOkF,EAAGvG,KAAKvC,KAAM,QAAS6I,EAAOhH,EAAI+B,IArBzCoF,IA0BJ,SAAaH,GACT,IAAIjB,EACJ,IAAK5H,KAAKiJ,WACN,OAAOjJ,KAEX6I,EAAQA,EAAMK,MAAMZ,GACpB,KAAOV,EAAOiB,EAAMM,gBACTnJ,KAAKiJ,WAAWrB,GAE3B,OAAO5H,MAlCPgI,QAoCJ,SAAiBa,GACb,IAAIjB,EAAMwB,EAAWvF,EAAMwF,EAAWC,EACtC,IAAKtJ,KAAKiJ,WACN,OAAOjJ,KAEX6I,EAAQA,EAAMK,MAAMZ,GACpBzE,EAAO,GAAGqE,MAAM3F,KAAKuB,UAAW,GAChC,MAAQ8D,EAAOiB,EAAMM,WAAaC,EAAYpJ,KAAKiJ,WAAWrB,KAC1DyB,EAAYE,EAASH,EAAUI,KAAMxJ,KAAM,CAAE4H,GAAO6B,OAAO5F,IAC3DyF,EAAaC,EAASH,EAAUM,MAAO1J,KAAM,CAAE4H,GAAO6B,OAAO5F,IAC7DwF,KAAed,EAASe,GAE5B,OAAOtJ,OA9CX,SAAS8I,EAAGa,EAAQd,EAAOhH,EAAI+B,GAC3B,IAAIgE,EACJ,IAAK/F,EACD,OAAO7B,KAKX,IAHA6I,EAAQA,EAAMK,MAAMZ,GACpBzG,EAAK+B,EAqET,SAAqBT,EAAIS,GACrB,OAAOT,EAAG1B,KAAO0B,EAAG1B,KAAKmC,GAAW,WAChCT,EAAGY,MAAMH,EAAS,GAAGsE,MAAM3F,KAAKuB,UAAW,KAvEhC8F,CAAY/H,EAAI+B,GAAW/B,EAC1C7B,KAAKiJ,WAAajJ,KAAKiJ,YAAc,GAC9BrB,EAAOiB,EAAMM,SAChBnJ,KAAKiJ,WAAWrB,GAAQ5H,KAAKiJ,WAAWrB,IAAS,CAC7C4B,KAAM,GACNE,MAAO,IAEX1J,KAAKiJ,WAAWrB,GAAM+B,GAAQE,KAAKhI,GAEvC,OAAO7B,KAiCX,SAASuJ,EAASH,EAAWxF,EAASC,GAClC,OACA,WAEI,IADA,IAAIiG,EACKC,EAAI,EAAGC,EAAMZ,EAAUa,QAASH,GAAaC,EAAIC,EAAKD,GAAK,EAChED,GAAkD,IAAtCV,EAAUW,GAAGhG,MAAMH,EAASC,GAE5C,OAAQiG,IAhED,GA0FfzE,EAAY,SAAS6E,GACrB,aACA,IAAIC,EAAW,CACXC,KAAM,KACNC,QAAS,KACTC,QAAS,SACTC,UAAW,KACXC,WAAW,EACXC,eAAe,GAEnB,OAAO,SAAoBnF,GACvB,IAAIoF,GACJpF,EAAIpF,EAAEuC,MAAM,GAAI0H,EAAU7E,IACnB8E,MAAS9E,EAAE+E,UAGlB/E,EAAE+E,QAAUnK,EAAEa,QAAQuE,EAAE+E,SAAW/E,EAAE+E,QAAU,CAAE/E,EAAE+E,SACnDK,EA0BJ,SAAkBC,EAAUF,EAAeD,GAEvC,IADA,IAA0BI,EAAtBC,EAAkB,GACbd,EAAI,EAAGC,EAAMW,EAASV,OAAQF,EAAIC,EAAKD,IAC5Cc,EAAgBhB,KAAK3J,EAAEQ,iBAAiBiK,EAASZ,KAGrD,OADAa,EAAWJ,EAAY,OAASK,EAAgBC,KAAK,KAAO,OAAS,IAAMD,EAAgBC,KAAK,KAAO,IAChGL,EAAgB,IAAIM,OAAOH,GAAY,IAAIG,OAAOH,EAAU,KAhC3DI,CAAS1F,EAAE+E,QAAS/E,EAAEmF,cAAenF,EAAEkF,WAc/C,SAASS,EAAS9D,EAAI+D,GAElB,IADA,IAAIC,EACKpB,EAAI,EAAGA,EAAI5C,EAAGiE,WAAWnB,OAAQF,IADV,KAE5BoB,EAAYhE,EAAGiE,WAAWrB,IACZ1I,SACV0I,GAAKmB,EAAmBC,GAAa,EAAI,EAEzCF,EAASE,EAAWD,GApBhCD,CAAS3F,EAAE8E,MACX,SAA4BiB,GACxB,IAAI9K,EAAO+K,EAAaC,GACpBhL,EAAQmK,EAAMc,KAAKH,EAASI,SAC5BF,EAAcrB,EAAIwB,cAAcpG,EAAEgF,SAClChF,EAAEiF,YAAcgB,EAAYhB,UAAYjF,EAAEiF,YAC1Ce,EAAcD,EAASM,UAAUpL,EAAMuB,QAC3B6J,UAAUpL,EAAM,GAAG0J,QAC/BsB,EAAYK,YAAYN,EAAYO,WAAU,IAC9CR,EAASS,WAAWC,aAAaR,EAAaD,IAElD,QAAS/K,OA7BL,CAmDdkI,OAAOuD,UACLC,EAAQ,WACR,aACA,IAAIC,EAUJ,SAASD,EAAM3G,EAAGC,GAmLlB,IAA6B4G,GAlLzB7G,EAAIA,GAAK,IACFV,OACH3E,EAAEmH,MAAM,oBAEZ7B,EAAI9C,MAAMzC,MACVA,KAAKoM,MAAQnM,EAAEqF,EAAET,MACjB7E,KAAKmM,OAASlM,EAAEqF,EAAEV,OAClB5E,KAAKqM,MAAQrM,KAAKmM,OAAO7J,MACzBtC,KAAKsM,iBAAmBtM,KAAKuM,WAAavM,KAAKqM,MAAQ,KACvDrM,KAAKwM,iBAyKoBL,EAzKkBnM,KAAKmM,OA0KzClM,EAAE,kCAAkCwF,IAAI,CAC3CS,SAAU,WACVuG,WAAY,SACZC,WAAY,MACZC,WAAYR,EAAO1G,IAAI,eACvBmH,SAAUT,EAAO1G,IAAI,aACrBoH,UAAWV,EAAO1G,IAAI,cACtBqH,YAAaX,EAAO1G,IAAI,gBACxBsH,WAAYZ,EAAO1G,IAAI,eACvBuH,YAAab,EAAO1G,IAAI,gBACxBwH,cAAed,EAAO1G,IAAI,kBAC1ByH,WAAYf,EAAO1G,IAAI,eACvB0H,cAAehB,EAAO1G,IAAI,kBAC1B2H,cAAejB,EAAO1G,IAAI,oBAC3B4H,YAAYlB,IAvLfnM,KAAKsN,0BACqB,IAAtBtN,KAAKoM,MAAMnC,SACXjK,KAAKuN,QAAUvN,KAAKwN,QAAUxN,KAAKyN,UAAYzN,KAAK0N,mBAAqBxN,EAAEsE,MAqKnF,OA3LA0H,EAAoB,CAChByB,EAAG,MACHC,GAAI,MACJC,GAAI,OACJC,GAAI,QACJC,GAAI,QACJC,GAAI,KACJC,GAAI,QAkBRhC,EAAMiC,eAAiB,SAASzN,GAC5B,OAAOP,EAAEqB,MAAMd,GAAKE,QAAQ,QAAS,IAAIA,QAAQ,UAAW,MAEhET,EAAEuC,MAAMwJ,EAAMvE,UAAWW,EAAc,CACnC8F,QAAS,WACLnO,KAAKoO,kBACLpO,KAAKgI,QAAQ,YAEjBqG,SAAU,WACNrO,KAAKsM,iBAAmBtM,KAAKqM,MAC7BrM,KAAKgI,QAAQ,YAEjBsG,WAAY,SAAmBzG,GAC3B,IAAI0G,EAAUrC,EAAkBrE,EAAG2G,OAAS3G,EAAG4G,SAC/CzO,KAAK0O,sBAAsBH,EAAS1G,GAChC0G,GAAWvO,KAAK2O,eAAeJ,EAAS1G,IACxC7H,KAAKgI,QAAQuG,EAAU,QAAS1G,IAGxC+G,SAAU,WACN5O,KAAK6O,UAAU7O,KAAK8O,iBACpB9O,KAAK0N,qBACL1N,KAAKsN,2BAEToB,sBAAuB,SAA8BH,EAAS1G,GAC1D,IAAIkH,EACJ,OAAQR,GACN,IAAK,KACL,IAAK,OACHQ,GAAkBC,EAAanH,GAC/B,MAEF,QACEkH,GAAiB,EAErBA,GAAkBlH,EAAGkH,kBAEzBJ,eAAgB,SAAuBJ,EAAS1G,GAC5C,IAAIG,EACJ,OAAQuG,GACN,IAAK,MACHvG,GAAWgH,EAAanH,GACxB,MAEF,QACEG,GAAU,EAEd,OAAOA,GAEXsF,wBAAyB,WACrB,IAAI2B,GAAOjP,KAAKmM,OAAO1G,IAAI,cAAgB,OAAOyJ,cAC9ClP,KAAKiP,MAAQA,IACbjP,KAAKiP,IAAMA,EACXjP,KAAKoM,MAAM+C,KAAK,MAAOF,GACvBjP,KAAKgI,QAAQ,iBAAkBiH,KAGvCJ,UAAW,SAAkBvM,EAAK8M,GAC9B,IAAIC,EAAeC,EA0HGC,EAAGC,EAAHD,EAzHejN,EAyHZkN,EAzHiBxP,KAAKqM,MAC/CiD,KADAD,EA0HGpD,EAAMiC,eAAeqB,KAAOtD,EAAMiC,eAAesB,KAzHXxP,KAAKqM,MAAMpC,SAAW3H,EAAI2H,OACnEjK,KAAKqM,MAAQ/J,EACR8M,GAAWC,GAEJD,GAAUE,GAClBtP,KAAKgI,QAAQ,oBAAqBhI,KAAKqM,OAFvCrM,KAAKgI,QAAQ,eAAgBhI,KAAKqM,QAK1C5K,KAAM,WACF,IAAiBgO,EAAQC,EAASC,EAAWC,EAAzCC,EAAO7P,KAgBX,OAfAyP,EAASvP,EAAEuB,KAAKzB,KAAKmO,QAASnO,MAC9B0P,EAAUxP,EAAEuB,KAAKzB,KAAKqO,SAAUrO,MAChC2P,EAAYzP,EAAEuB,KAAKzB,KAAKsO,WAAYtO,MACpC4P,EAAU1P,EAAEuB,KAAKzB,KAAK4O,SAAU5O,MAChCA,KAAKmM,OAAOrD,GAAG,UAAW2G,GAAQ3G,GAAG,WAAY4G,GAAS5G,GAAG,aAAc6G,IACtEzP,EAAEC,UAAYD,EAAEC,SAAW,EAC5BH,KAAKmM,OAAOrD,GAAG,WAAY8G,GAE3B5P,KAAKmM,OAAOrD,GAAG,0CAA0C,SAASjB,GAC1DqE,EAAkBrE,EAAG2G,OAAS3G,EAAG4G,UAGrCvO,EAAEgD,MAAMhD,EAAEuB,KAAKoO,EAAKjB,SAAUiB,EAAMhI,OAGrC7H,MAEX8P,MAAO,WACH9P,KAAKmM,OAAO2D,SAEhBC,KAAM,WACF/P,KAAKmM,OAAO4D,QAEhBC,WAAY,WACR,OAAOhQ,KAAKiP,KAEhBgB,SAAU,WACN,OAAOjQ,KAAKqM,OAAS,IAEzB6D,SAAU,SAAkB5N,EAAK8M,GAC7BpP,KAAKmQ,cAAc7N,GACnBtC,KAAK6O,UAAUvM,EAAK8M,IAExBgB,8BAA+B,WAC3B,OAAOpQ,KAAKqM,QAAUrM,KAAKsM,kBAE/BwC,cAAe,WACX,OAAO9O,KAAKmM,OAAO7J,OAEvB6N,cAAe,SAAuBpO,GAClC/B,KAAKmM,OAAO7J,IAAIP,GAChB/B,KAAK0N,qBACL1N,KAAKsN,2BAETc,gBAAiB,WACbpO,KAAKmQ,cAAcnQ,KAAKqM,QAE5BmB,QAAS,WACL,OAAOxN,KAAKoM,MAAM9J,OAEtBiL,QAAS,SAAiBxL,GACtB/B,KAAKoM,MAAM9J,IAAIP,IAEnB0L,UAAW,WACPzN,KAAKuN,QAAQ,KAEjBG,mBAAoB,WAChB,IAAIpL,EAAKuC,EAAMwL,EAGfA,GAFA/N,EAAMtC,KAAK8O,oBACXjK,EAAO7E,KAAKwN,YAC8C,IAAtB3I,EAAKyL,QAAQhO,KAC/B,KAARA,GAAc+N,IAAsBrQ,KAAKuQ,gBACvCvQ,KAAKyN,aAErBlB,SAAU,WACN,OAAOvM,KAAKmM,OAAOqE,GAAG,WAE1BD,YAAa,WACT,IAAIE,EAAazQ,KAAKmM,OAAOuE,QAAU,EAEvC,OADA1Q,KAAKwM,gBAAgBmE,KAAK3Q,KAAK8O,iBACxB9O,KAAKwM,gBAAgBkE,SAAWD,GAE3CG,cAAe,WACX,IAAIC,EAAaC,EAAgBC,EAGjC,OAFAF,EAAc7Q,KAAKmM,OAAO7J,MAAM2H,OAChC6G,EAAiB9Q,KAAKmM,OAAO,GAAG2E,eAC5B5Q,EAAEY,SAASgQ,GACJA,IAAmBD,GACnB7E,SAASgF,aAChBD,EAAQ/E,SAASgF,UAAUC,eACrBC,UAAU,aAAcL,GACvBA,IAAgBE,EAAMJ,KAAK1G,SAI1CkH,QAAS,WACLnR,KAAKoM,MAAMpD,IAAI,OACfhJ,KAAKmM,OAAOnD,IAAI,OAChBhJ,KAAKwM,gBAAgB4E,SACrBpR,KAAKoM,MAAQpM,KAAKmM,OAASnM,KAAKwM,gBAAkBvM,EAAE,YAGrDgM,EAqBP,SAAS+C,EAAanH,GAClB,OAAOA,EAAGwJ,QAAUxJ,EAAGyJ,SAAWzJ,EAAG0J,SAAW1J,EAAG2J,UApN/C,GAuNRC,EAAU,WACV,aACA,IAAIC,EAAMC,EAMV,SAASF,EAAQnM,EAAGC,GAmMpB,IAAqB9E,EAnBC0F,EAOAyL,EAAWC,GAtL7BvM,EAAIA,GAAK,IACPsM,UAAYtM,EAAEsM,WAAa,GAC7BtM,EAAEsM,UAAUE,SAAWxM,EAAEsM,UAAUE,UAAYxM,EAAEsM,UAAU1M,MACtDI,EAAEyM,QACH9R,EAAEmH,MAAM,kBAEP9B,EAAE8E,MACHnK,EAAEmH,MAAM,gBAER9B,EAAE0M,OAyLWvR,EAzLU6E,EAAE0M,MA0LtB,mBAAmB5R,KAAKK,KAzL3BR,EAAEmH,MAAM,yBAA2B9B,EAAE0M,MAEzCzM,EAAI9C,MAAMzC,MACVA,KAAKqF,YAAcC,EAAED,UACrBrF,KAAKgS,KAAO1M,EAAE0M,MAAQL,IACtB3R,KAAKiS,MAAQ3M,EAAE2M,OAAS,EACxBjS,KAAK6R,WAgKL1L,GADkBA,EA/JYb,EAAEa,SAAWb,EAAE4M,aAgKxBhS,EAAEoE,UAChBpE,EAAEc,WAAWmF,GAAWA,EAC/B,SAAmBtF,GACf,OAAOA,EAAIsF,KAlKfnG,KAAK4R,WAqKaA,EArKYtM,EAAEsM,UAqKHC,EArKc7R,KAAK6R,UAsKzC,CACHC,SAAUF,EAAUE,UAAY5R,EAAE8C,WAAW4O,EAAUE,UACvDK,QAASP,EAAUO,SAAWjS,EAAE8C,WAAW4O,EAAUO,SACrDC,OAAQR,EAAUQ,QAAUlS,EAAE8C,WAAW4O,EAAUQ,QACnDC,OAAQT,EAAUS,QAAUnS,EAAE8C,WAAW4O,EAAUS,QACnDrN,WAAY4M,EAAU5M,YAE1B,SAA4BpB,GACxB,OAAO3D,EAAE,SAAS0Q,KAAKkB,EAAUjO,OA7KrC5D,KAAK+R,OAASzM,EAAEyM,OAAOO,YAAchN,EAAEyM,OAAOO,cAAgBhN,EAAEyM,OAChE/R,KAAK0J,MAAQxJ,EAAEiB,YAAYmE,EAAEoE,OAAS1J,KAAK+R,OAAO9H,OAAS,IAAM3E,EAAEoE,MACnE1J,KAAKuS,uBACLvS,KAAKqH,IAAMpH,EAAEqF,EAAE8E,MAAMoI,SAASxS,KAAKwF,QAAQT,SAASyN,SAASxS,KAAKwF,QAAQT,QAAU,IAAM/E,KAAKgS,MAyJnG,OApLAN,EAAO,CACHpP,IAAK,wBACLzB,IAAK,wBAET8Q,EAAgBzR,EAAE4C,iBAyBlB2O,EAAQgB,YAAc,SAAqBtL,GACvC,IAAIE,EAAMpH,EAAEkH,GACZ,OAAIE,EAAIoE,KAAKiG,EAAK7Q,KACP,CACHyB,IAAK+E,EAAIoE,KAAKiG,EAAKpP,MAAQ,GAC3BzB,IAAKwG,EAAIoE,KAAKiG,EAAK7Q,MAAQ,MAG5B,MAEXX,EAAEuC,MAAMgP,EAAQ/J,UAAWW,EAAc,CACrCqK,WAAY,SAAmBrG,EAAOsG,IAClCA,EAAcA,GAAe,IACb1I,OACZjK,KAAK4S,mBAAmBvG,EAAOsG,GACxB3S,KAAK0J,OAAS1J,KAAK4R,UAAUO,QACpCnS,KAAK6S,eAAexG,IACZrM,KAAK0J,OAAS1J,KAAK4R,UAAUE,SACrC9R,KAAK8S,gBAAgBzG,GAErBrM,KAAK+S,SAET/S,KAAKgI,QAAQ,WAAYhI,KAAKgS,KAAMW,GAAa,IAErDK,QAAS,SAAgB3G,EAAOsG,IAC5BA,EAAcA,GAAe,IACb1I,QAAUjK,KAAKiT,gBAAgBhJ,OAC3CjK,KAAKkT,mBAAmB7G,EAAOsG,GACxBA,EAAY1I,OACnBjK,KAAK4S,mBAAmBvG,EAAOsG,IACvB3S,KAAKiT,gBAAgBhJ,QAAUjK,KAAK4R,UAAUE,UACtD9R,KAAK8S,gBAAgBzG,GAEzBrM,KAAKgI,QAAQ,WAAYhI,KAAKgS,KAAMW,GAAa,IAErDC,mBAAoB,SAA2BvG,EAAOsG,GAClD,IAAIQ,EACJA,EAAYnT,KAAKoT,wBAAwB/G,EAAOsG,GAChD3S,KAAKiT,gBAAkBE,EAAUE,WAAWC,OAC5CtT,KAAKqH,IAAI1B,KAAKwN,GAAWI,QAAQvT,KAAKwT,WAAWnH,EAAOsG,IAAcc,OAAOzT,KAAK0T,WAAWrH,EAAOsG,KAExGO,mBAAoB,SAA2B7G,EAAOsG,GAClD,IAAIQ,EAAWF,EAEfA,GADAE,EAAYnT,KAAKoT,wBAAwB/G,EAAOsG,IACpBU,WAAWC,OACvCtT,KAAKiT,gBAAgBU,MAAMR,GAC3BnT,KAAKiT,gBAAkBA,GAE3BJ,eAAgB,SAAuBxG,GACnC,IAAIuH,EAAW5T,KAAK4R,UAAUO,QAC9BnS,KAAKuS,uBACLqB,GAAY5T,KAAKqH,IAAI1B,KAAKiO,EAAS,CAC/BvH,MAAOA,EACPtH,QAAS/E,KAAKgS,SAGtBc,gBAAiB,SAAwBzG,GACrC,IAAIuH,EAAW5T,KAAK4R,UAAUE,SAC9B9R,KAAKuS,uBACLqB,GAAY5T,KAAKqH,IAAI1B,KAAKiO,EAAS,CAC/BvH,MAAOA,EACPtH,QAAS/E,KAAKgS,SAGtBe,OAAQ,WACJ/S,KAAKqH,IAAInC,QACTlF,KAAKuS,wBAETa,wBAAyB,SAAgC/G,EAAOsG,GAC5D,IAAiBkB,EAAbhE,EAAO7P,KAaX,OAZA6T,EAAW7H,SAAS8H,yBACpB5T,EAAEyB,KAAKgR,GAAa,SAA2B3N,GAC3C,IAAIqC,EAAKzD,EACTA,EAAUiM,EAAKkE,aAAa1H,EAAOrH,GACnCqC,EAAMpH,EAAE4P,EAAK+B,UAAU5M,WAAWpB,IAAU6H,KAAKiG,EAAK7Q,IAAKmE,GAAYyG,KAAKiG,EAAKpP,IAAKuN,EAAKgC,UAAU7M,IAAawN,SAAS3C,EAAKrK,QAAQR,WAAa,IAAM6K,EAAKrK,QAAQP,YACxK4O,EAASjI,YAAYvE,EAAI,OAE7BrH,KAAKqF,WAAaA,EAAU,CACxBkF,UAAWvK,KAAKwF,QAAQH,UACxB+E,KAAMyJ,EACNxJ,QAASgC,IAENpM,EAAE4T,IAEbH,WAAY,SAAmBrH,EAAOsG,GAClC,OAAO3S,KAAK4R,UAAUS,OAASrS,KAAK4R,UAAUS,OAAO,CACjDhG,MAAOA,EACPsG,YAAaA,EACb5N,QAAS/E,KAAKgS,OACb,MAETwB,WAAY,SAAmBnH,EAAOsG,GAClC,OAAO3S,KAAK4R,UAAUQ,OAASpS,KAAK4R,UAAUQ,OAAO,CACjD/F,MAAOA,EACPsG,YAAaA,EACb5N,QAAS/E,KAAKgS,OACb,MAETO,qBAAsB,WAClBvS,KAAKiT,gBAAkBhT,KAE3B8T,aAAc,SAAqB1H,EAAOxL,GACtC,OAAOX,EAAEe,SAASJ,GAAOX,EAAEuC,MAAM,CAC7BuR,OAAQ3H,GACTxL,GAAOA,GAEdoT,OAAQ,SAAgB5H,GACpB,IAAIwD,EAAO7P,KAAMkU,GAAW,EAAOC,GAAa,EAAOC,EAAW,EASlE,SAAS5K,EAAKmJ,GACNwB,IAGJA,GAAa,EACbxB,GAAeA,GAAe,IAAIzK,MAAM,EAAG2H,EAAKoC,OAChDmC,EAAWzB,EAAY1I,OACvB4F,EAAK6C,WAAWrG,EAAOsG,GACnByB,EAAWvE,EAAKoC,OAASpC,EAAKnG,OAC9BmG,EAAK7H,QAAQ,iBAAkBqE,IAjBvCrM,KAAKqU,SACLrU,KAAKqU,OAAS,WACVH,GAAW,EACXrE,EAAKwE,OAASpU,EAAEuE,KAChBqL,EAAKnG,OAASmG,EAAK7H,QAAQ,gBAAiBqE,IAEhDrM,KAAK+R,OAAO1F,EAAO7C,GAcnB,SAAemJ,GACXA,EAAcA,GAAe,IACxBuB,GAAYE,EAAWvE,EAAKoC,QAC7BpC,EAAKwE,OAASpU,EAAEuE,KAChBqL,EAAKmD,QAAQ3G,EAAOsG,EAAYzK,MAAM,EAAG2H,EAAKoC,MAAQmC,IACtDA,GAAYzB,EAAY1I,OACxB4F,EAAKnG,OAASmG,EAAK7H,QAAQ,gBAAiBqE,QAnBnD8H,GAAc3K,EAAK,KAuBxB6K,OAAQpU,EAAEuE,KACV8P,MAAO,WACHtU,KAAK+S,SACL/S,KAAKqU,SACLrU,KAAKgI,QAAQ,YAEjBuM,QAAS,WACL,OAAOvU,KAAKqH,IAAImJ,GAAG,WAEvBW,QAAS,WACLnR,KAAKqH,IAAMpH,EAAE,YAGdwR,EAvLG,GA+MV+C,EAAO,WACP,aACA,SAASA,EAAKlP,EAAGC,GACb,IAAIsK,EAAO7P,MACXsF,EAAIA,GAAK,IACF8E,MACHnK,EAAEmH,MAAM,oBAEZ7B,EAAI9C,MAAMzC,MACVA,KAAKyU,MAAQxU,EAAEqF,EAAE8E,MACjBpK,KAAKqM,MAAQ,KACbrM,KAAK0U,SAAWxU,EAAE8B,IAAIsD,EAAEoP,UACxB,SAA2BC,GACvB,IAAIvK,EAAOyF,EAAK4E,MAAMG,KAAKD,EAASvK,MAAMyK,QAE1C,OADAF,EAASvK,KAAOA,EAAKH,OAASG,EAAOnK,EAAE,SAAS6U,SAASjF,EAAK4E,OACvD,IAAIhD,EAAQkD,EAAUpP,MAyHrC,OAtHArF,EAAEuC,MAAM+R,EAAK9M,UAAWW,EAAc,CAClC0M,mBAAoB,SAA2BlN,GAC3C7H,KAAKgI,QAAQ,oBAAqB/H,EAAE4H,EAAGmN,iBAE3CC,YAAa,SAAoBrN,EAAM7C,EAAS4N,EAAajJ,GACzD1J,KAAKyU,MAAMS,YAAYlV,KAAKwF,QAAQN,MAAOlF,KAAKmV,qBAChDnV,KAAKgI,QAAQ,kBAAmBjD,EAAS4N,EAAajJ,IAE1D0L,WAAY,WACRpV,KAAKyU,MAAMS,YAAYlV,KAAKwF,QAAQN,MAAOlF,KAAKmV,qBAChDnV,KAAKgI,QAAQ,mBAEjBqN,WAAY,WACRrV,KAAKgI,QAAQjE,MAAM/D,KAAM8D,YAE7BqR,kBAAmB,WACf,OAAOjV,EAAEiC,MAAMnC,KAAK0U,UACpB,SAAwB3P,GACpB,OAAOA,EAAQwP,cAGvBe,gBAAiB,WACb,OAAOtV,KAAKyU,MAAMG,KAAK5U,KAAK6F,UAAUZ,aAE1CsQ,cAAe,WACX,IAAIC,EAAcxV,KAAKyV,sBACvBD,GAAeA,EAAYE,YAAY1V,KAAKwF,QAAQJ,SAExDuQ,eAAgB,SAAuBtO,GACnC,IAAIuO,EAAOC,EAAUC,EAAeC,EAEpCF,GADAD,EAAQvO,EAAInB,WAAWE,KACJiB,EAAI2O,aAAY,GACnCF,EAAgB9V,KAAKyU,MAAMwB,YAC3BF,EAAa/V,KAAKyU,MAAMyB,SAAWC,SAASnW,KAAKyU,MAAMhP,IAAI,cAAe,IAAM0Q,SAASnW,KAAKyU,MAAMhP,IAAI,iBAAkB,IACtHmQ,EAAQ,EACR5V,KAAKyU,MAAMwB,UAAUH,EAAgBF,GAC9BG,EAAaF,GACpB7V,KAAKyU,MAAMwB,UAAUH,GAAiBD,EAAWE,KAGzDtU,KAAM,WACF,IAAiB2U,EAAbvG,EAAO7P,KAMX,OALAoW,EAAoBlW,EAAEuB,KAAKzB,KAAK+U,mBAAoB/U,MACpDA,KAAKyU,MAAM3L,GAAG,WAAY9I,KAAK6F,UAAUZ,WAAYmR,GACrDlW,EAAEyB,KAAK3B,KAAK0U,UAAU,SAAS3P,GAC3BA,EAAQ6D,OAAO,iBAAkBiH,EAAKwF,WAAYxF,GAAMjH,OAAO,gBAAiBiH,EAAKwF,WAAYxF,GAAMjH,OAAO,gBAAiBiH,EAAKwF,WAAYxF,GAAMjH,OAAO,WAAYiH,EAAKoF,YAAapF,GAAMjH,OAAO,UAAWiH,EAAKuF,WAAYvF,MAEjO7P,MAEXqW,OAAQ,WACJ,OAAOrW,KAAKyU,MAAM6B,SAAStW,KAAKwF,QAAQL,OAE5CA,KAAM,WACFnF,KAAKyU,MAAMjC,SAASxS,KAAKwF,QAAQL,OAErCoR,MAAO,WACHvW,KAAKyU,MAAMiB,YAAY1V,KAAKwF,QAAQL,MACpCnF,KAAKuV,iBAETiB,qBAAsB,SAA8BvH,GAChDjP,KAAKyU,MAAMtF,KAAK,MAAOF,IAE3BwH,2BAA4B,SAAoCC,GAC5D,IAAIC,EAAcC,EAAsBC,EAOxC,OANAD,EAAa5W,KAAKyV,sBAClBkB,EAAe3W,KAAKsV,mBAKC,KADrBuB,GADAA,IADAA,GADWD,EAAaD,EAAa7U,MAAM8U,IAAe,GACpCF,GACC,IAAMC,EAAa1M,OAAS,GAAK,IACjC,EAAI0M,EAAa1M,OAAS,EAAI4M,GAC5B,KAAOF,EAAaG,GAAGD,IAEpDE,UAAW,SAAmBvB,GAC1BxV,KAAKuV,iBACDC,EAAcA,GAAeA,EAAYX,WACzCW,EAAYhD,SAASxS,KAAKwF,QAAQJ,QAClCpF,KAAK2V,eAAeH,KAG5BwB,kBAAmB,SAA2B3P,GAC1C,OAAOA,GAAOA,EAAI4C,OAASwH,EAAQgB,YAAYpL,GAAO,MAE1DoO,oBAAqB,WACjB,IAAID,EAAcxV,KAAKsV,kBAAkBrT,OAAOjC,KAAK6F,UAAUT,QAAQyP,QACvE,OAAOW,EAAYvL,OAASuL,EAAc,MAE9CyB,iBAAkB,WACd,IAAIzB,EAAcxV,KAAKsV,kBAAkBT,QACzC,OAAOW,EAAYvL,OAASuL,EAAc,MAE9CvB,OAAQ,SAAgB5H,GACpB,IAAI6K,EAAgB7K,IAAUrM,KAAKqM,MAKnC,OAJI6K,IACAlX,KAAKqM,MAAQA,EACbnM,EAAEyB,KAAK3B,KAAK0U,UAGhB,SAAuB3P,GACnBA,EAAQkP,OAAO5H,OAFZ6K,GAKXhS,MAAO,WACHhF,EAAEyB,KAAK3B,KAAK0U,UAGZ,SAAsB3P,GAClBA,EAAQuP,WAHZtU,KAAKqM,MAAQ,KACbrM,KAAKyU,MAAMjC,SAASxS,KAAKwF,QAAQN,QAKrCiM,QAAS,WACLnR,KAAKyU,MAAMzL,IAAI,OACfhJ,KAAKyU,MAAQxU,EAAE,SACfC,EAAEyB,KAAK3B,KAAK0U,UACZ,SAAwB3P,GACpBA,EAAQoM,gBAIbqD,EAxIA,GA0IP2C,EAAc,WACd,aACA,IAAI3V,EAAIgT,EAAK9M,UACb,SAASyP,IACL3C,EAAKzQ,MAAM/D,KAAM,GAAGkI,MAAM3F,KAAKuB,UAAW,IAsC9C,OApCA5D,EAAEuC,MAAM0U,EAAYzP,UAAW8M,EAAK9M,UAAW,CAC3CvC,KAAM,WAEF,OADCnF,KAAKmV,qBAAuBnV,KAAKoX,QAC3B5V,EAAE2D,KAAKpB,MAAM/D,KAAM,GAAGkI,MAAM3F,KAAKuB,UAAW,KAEvDyS,MAAO,WAEH,OADAvW,KAAKqX,QACE7V,EAAE+U,MAAMxS,MAAM/D,KAAM,GAAGkI,MAAM3F,KAAKuB,UAAW,KAExDmR,YAAa,WAMT,OALIjV,KAAKmV,oBACLnV,KAAKqX,QAELrX,KAAKqW,UAAYrW,KAAKoX,QAEnB5V,EAAEyT,YAAYlR,MAAM/D,KAAM,GAAGkI,MAAM3F,KAAKuB,UAAW,KAE9DsR,WAAY,WAMR,OALIpV,KAAKmV,oBACLnV,KAAKqX,QAELrX,KAAKqW,UAAYrW,KAAKoX,QAEnB5V,EAAE4T,WAAWrR,MAAM/D,KAAM,GAAGkI,MAAM3F,KAAKuB,UAAW,KAE7D0S,qBAAsB,SAA8BvH,GAEhD,OADAjP,KAAKyU,MAAMhP,IAAY,QAARwJ,EAAgBjP,KAAKyF,IAAIoB,IAAM7G,KAAKyF,IAAIsB,KAChDvF,EAAEgV,qBAAqBzS,MAAM/D,KAAM,GAAGkI,MAAM3F,KAAKuB,UAAW,KAEvEuT,MAAO,WACHrX,KAAKyU,MAAM6C,QAEfF,MAAO,WACHpX,KAAKyU,MAAMhP,IAAI,UAAW,YAG3B0R,EA1CO,GA4CdI,EAAY,WACZ,aACA,SAASA,EAAUjS,EAAGC,GAClB,IAAIiS,EAAWC,EAAWC,EAAcC,EAAYC,EAAYC,EAAWC,EAAaC,EAAaC,EAAcC,EAAgBC,GACnI5S,EAAIA,GAAK,IACFV,OACH3E,EAAEmH,MAAM,iBAEP9B,EAAER,MACH7E,EAAEmH,MAAM,gBAEP9B,EAAE6S,UACHlY,EAAEmH,MAAM,qBAEZ7B,EAAI9C,MAAMzC,MACVA,KAAKmY,SAAW7S,EAAE6S,SAClBnY,KAAKoY,UAAYlY,EAAEY,SAASwE,EAAE8S,WAAa9S,EAAE8S,UAAY,EACzDpY,KAAK4E,MAAQU,EAAEV,MACf5E,KAAK8E,KAAOQ,EAAER,KACd9E,KAAKqY,SAAU,EACfrY,KAAKsY,QAAS,EACdtY,KAAK4E,MAAM2H,YAAcvM,KAAKuY,WAC9BvY,KAAKiP,IAAMjP,KAAK4E,MAAMoL,aACtBhQ,KAAKwY,SACLxY,KAAK8E,KAAKrD,OAAOmH,OAAO,oBAAqB5I,KAAKyY,qBAAsBzY,MAAM4I,OAAO,iBAAkB5I,KAAK0Y,kBAAmB1Y,MAAM4I,OAAO,gBAAiB5I,KAAK2Y,iBAAkB3Y,MAAM4I,OAAO,gBAAiB5I,KAAK4Y,iBAAkB5Y,MAAM4I,OAAO,kBAAmB5I,KAAK6Y,mBAAoB7Y,MAAM4I,OAAO,iBAAkB5I,KAAK8Y,kBAAmB9Y,MACzVwX,EAAYzR,EAAE/F,KAAM,WAAY,OAAQ,cACxCyX,EAAY1R,EAAE/F,KAAM,aAAc,cAClC0X,EAAe3R,EAAE/F,KAAM,WAAY,SAAU,iBAC7C2X,EAAa5R,EAAE/F,KAAM,WAAY,SAAU,eAC3C4X,EAAa7R,EAAE/F,KAAM,WAAY,eACjC6X,EAAY9R,EAAE/F,KAAM,WAAY,OAAQ,cACxC8X,EAAc/R,EAAE/F,KAAM,WAAY,OAAQ,gBAC1C+X,EAAchS,EAAE/F,KAAM,WAAY,SAAU,gBAC5CgY,EAAejS,EAAE/F,KAAM,WAAY,SAAU,iBAC7CiY,EAAiBlS,EAAE/F,KAAM,gBAAiB,mBAC1CkY,EAAsBnS,EAAE/F,KAAM,gBAAiB,wBAC/CA,KAAK4E,MAAMnD,OAAOmH,OAAO,UAAW4O,EAAWxX,MAAM4I,OAAO,UAAW6O,EAAWzX,MAAM4I,OAAO,aAAc8O,EAAc1X,MAAM4I,OAAO,WAAY+O,EAAY3X,MAAM4I,OAAO,WAAYgP,EAAY5X,MAAM4I,OAAO,UAAWiP,EAAW7X,MAAM4I,OAAO,YAAakP,EAAa9X,MAAM4I,OAAO,YAAamP,EAAa/X,MAAM4I,OAAO,aAAcoP,EAAchY,MAAM4I,OAAO,eAAgBqP,EAAgBjY,MAAM4I,OAAO,oBAAqBsP,EAAqBlY,MAAM4I,OAAO,iBAAkB5I,KAAK+Y,kBAAmB/Y,MAkO9f,OAhOAE,EAAEuC,MAAM8U,EAAU7P,UAAW,CACzB8Q,OAAQ,WACJ,IAAIrM,EAAQ6M,EACZ7M,EAASnM,KAAK4E,MAAMuH,QAAUlM,EAAE,SAChC+Y,EAAQhZ,KAAK8E,KAAK2P,OAASxU,EAAE,SAC7BkM,EAAOrD,GAAG,WAAW,SAASjB,GAC1B,IAAIyQ,EAAQW,EAAUC,EACtBZ,EAAStM,SAASmN,cAClBF,EAAWD,EAAMxI,GAAG8H,GACpBY,EAAYF,EAAMI,IAAId,GAAQrO,OAAS,EACnC/J,EAAEC,WAAa8Y,GAAYC,KAC3BrR,EAAGkH,iBACHlH,EAAGwR,2BACHnZ,EAAEgD,OAAM,WACJiJ,EAAO2D,eAInBkJ,EAAMlQ,GAAG,gBAAgB,SAASjB,GAC9BA,EAAGkH,qBAGX0J,qBAAsB,SAA6B7Q,EAAMP,GACrDrH,KAAKwH,OAAOH,IAEhByR,kBAAmB,WACf9Y,KAAKsZ,eAETT,mBAAoB,SAA2BjR,EAAM7C,EAAS4N,EAAajJ,GACvE1J,KAAKsZ,cACLtZ,KAAKmY,SAASnQ,QAAQ,SAAU2K,EAAajJ,EAAO3E,IAExD2T,kBAAmB,SAA0B9Q,EAAM7C,EAASsH,GACxDrM,KAAKmY,SAASnQ,QAAQ,eAAgBqE,EAAOtH,IAEjD4T,iBAAkB,SAAyB/Q,EAAM7C,EAASsH,GACtDrM,KAAKmY,SAASnQ,QAAQ,cAAeqE,EAAOtH,IAEhD6T,iBAAkB,SAAyBhR,EAAM7C,EAASsH,GACtDrM,KAAKmY,SAASnQ,QAAQ,eAAgBqE,EAAOtH,IAEjDwU,WAAY,WACRvZ,KAAKwZ,iBAAmBxZ,KAAK8E,KAAKmP,OAAOjU,KAAK4E,MAAMqL,aAExDwJ,WAAY,WACJzZ,KAAK4E,MAAMwL,iCACXpQ,KAAKmY,SAASnQ,QAAQ,SAAUhI,KAAK4E,MAAMqL,aAGnDyJ,cAAe,SAAsB9R,EAAMC,GACvC,IAAI2N,GACAA,EAAcxV,KAAK8E,KAAK2Q,wBACxBzV,KAAKwH,OAAOgO,IAAgB3N,EAAGkH,kBAGvC4K,YAAa,SAAoB/R,EAAMC,GACnC,IAAI2N,GACAA,EAAcxV,KAAK8E,KAAK2Q,uBACxBzV,KAAKwH,OAAOgO,IAAgB3N,EAAGkH,kBACxByG,EAAcxV,KAAK8E,KAAKmS,qBAC/BjX,KAAKyH,aAAa+N,IAAgB3N,EAAGkH,kBAG7C6K,YAAa,WACT5Z,KAAKuW,SAETsD,WAAY,WACR7Z,KAAK8Z,YAAY,IAErBC,aAAc,WACV/Z,KAAK8Z,WAAW,IAEpBE,aAAc,WACO,QAAbha,KAAKiP,KAAiBjP,KAAK4E,MAAMgM,iBACjC5Q,KAAKyH,aAAazH,KAAK8E,KAAKmS,qBAGpCgD,cAAe,WACM,QAAbja,KAAKiP,KAAiBjP,KAAK4E,MAAMgM,iBACjC5Q,KAAKyH,aAAazH,KAAK8E,KAAKmS,qBAGpCiD,gBAAiB,SAAwBC,EAAG9N,GACxCrM,KAAKwZ,cAAcnN,GAASrM,KAAK8E,KAAKmP,OAAO5H,GAASrM,KAAK8E,KAAKI,SAEpEkV,qBAAsB,WAClBpa,KAAKsZ,eAETP,kBAAmB,SAA0BoB,EAAGlL,GACxCjP,KAAKiP,MAAQA,IACbjP,KAAKiP,IAAMA,EACXjP,KAAK8E,KAAK0R,qBAAqBvH,KAGvCoL,cAAe,WACXra,KAAKiZ,YAAcjZ,KAAKmF,QAE5BqU,cAAe,SAAsBnN,GAEjC,OADAA,EAAQnM,EAAEU,SAASyL,GAASA,EAAQrM,KAAK4E,MAAMqL,YAAc,IAChDhG,QAAUjK,KAAKoY,WAEhCkB,YAAa,WACT,IAAI9D,EAAa/J,EAAMnJ,EAAK+J,EAAOiO,EAA+B/Z,EAClEiV,EAAcxV,KAAK8E,KAAKmS,mBACxBxL,EAAOzL,KAAK8E,KAAKkS,kBAAkBxB,GACnClT,EAAMtC,KAAK4E,MAAMkK,iBACbrD,GAASvL,EAAEM,cAAc8B,IAAStC,KAAK4E,MAAM2L,cAO7CvQ,KAAK4E,MAAM6I,aANXpB,EAAQJ,EAAMiC,eAAe5L,GAC7BgY,EAAepa,EAAEQ,iBAAiB2L,IAElC9L,EADkB,IAAIwK,OAAO,OAASuP,EAAe,SAAU,KACvC9O,KAAKC,EAAKnJ,OACzBtC,KAAK4E,MAAM2I,QAAQjL,EAAM/B,EAAM,MAKhDga,UAAW,WACP,OAAOva,KAAKqY,SAEhBmC,OAAQ,WACJxa,KAAKqY,SAAU,GAEnBoC,QAAS,WACLza,KAAKqY,SAAU,GAEnBY,SAAU,WACN,OAAOjZ,KAAKsY,QAEhBC,SAAU,WACN,QAAIvY,KAAKiZ,eAEGjZ,KAAKua,aAAeva,KAAKmY,SAASlQ,OAAO,aAGjDjI,KAAKsY,QAAS,EACdtY,KAAKmY,SAASnQ,QAAQ,WACf,IAGf0S,WAAY,WACR,OAAK1a,KAAKiZ,aAECjZ,KAAKmY,SAASlQ,OAAO,UAG5BjI,KAAKsY,QAAS,EACdtY,KAAKuW,QACLvW,KAAKmY,SAASnQ,QAAQ,SACf,IAGfqO,OAAQ,WACJ,OAAOrW,KAAK8E,KAAKuR,UAErBlR,KAAM,WAMF,OALKnF,KAAKqW,UAAarW,KAAKmY,SAASlQ,OAAO,UACxCjI,KAAK8E,KAAKK,OACVnF,KAAKsZ,cACLtZ,KAAKmY,SAASnQ,QAAQ,SAEnBhI,KAAKqW,UAEhBE,MAAO,WAOH,OANIvW,KAAKqW,WAAarW,KAAKmY,SAASlQ,OAAO,WACvCjI,KAAK8E,KAAKyR,QACVvW,KAAK4E,MAAM6I,YACXzN,KAAK4E,MAAMwJ,kBACXpO,KAAKmY,SAASnQ,QAAQ,WAElBhI,KAAKqW,UAEjBsE,OAAQ,SAAgBrY,GACpBtC,KAAK4E,MAAMsL,SAAShQ,EAAEqB,MAAMe,KAEhCsY,OAAQ,WACJ,OAAO5a,KAAK4E,MAAMqL,YAEtBzI,OAAQ,SAAgBgO,GACpB,IAAI/J,EAAOzL,KAAK8E,KAAKkS,kBAAkBxB,GACvC,SAAI/J,GAASzL,KAAKmY,SAASlQ,OAAO,SAAUwD,EAAK5K,QAC7Cb,KAAK4E,MAAMsL,SAASzE,EAAKnJ,KAAK,GAC9BtC,KAAKmY,SAASnQ,QAAQ,SAAUyD,EAAK5K,KACrCb,KAAKuW,SACE,IAIf9O,aAAc,SAAsB+N,GAChC,IAAInJ,EAAOZ,EAIX,OAHAY,EAAQrM,KAAK4E,MAAMqL,gBACnBxE,EAAOzL,KAAK8E,KAAKkS,kBAAkBxB,KACjBnJ,IAAUZ,EAAKnJ,MACjBtC,KAAKmY,SAASlQ,OAAO,eAAgBwD,EAAK5K,QACtDb,KAAK4E,MAAMsL,SAASzE,EAAKnJ,KACzBtC,KAAKmY,SAASnQ,QAAQ,eAAgByD,EAAK5K,MACpC,IAIfiZ,WAAY,SAAoBpD,GAC5B,IAAIrK,EAAOwO,EAAYpP,EAAMqP,EAM7B,OALAzO,EAAQrM,KAAK4E,MAAMqL,WACnB4K,EAAa7a,KAAK8E,KAAK2R,2BAA2BC,GAElDoE,GADArP,EAAOzL,KAAK8E,KAAKkS,kBAAkB6D,IAClBpP,EAAK5K,IAAM,OACfb,KAAKwZ,iBAAmBxZ,KAAK8E,KAAKmP,OAAO5H,MAClCrM,KAAKmY,SAASlQ,OAAO,eAAgB6S,KACrD9a,KAAK8E,KAAKiS,UAAU8D,GAChBpP,EACAzL,KAAK4E,MAAMuL,cAAc1E,EAAKnJ,MAE9BtC,KAAK4E,MAAMwJ,kBACXpO,KAAKsZ,eAETtZ,KAAKmY,SAASnQ,QAAQ,eAAgB8S,IAC/B,IAIf3J,QAAS,WACLnR,KAAK4E,MAAMuM,UACXnR,KAAK8E,KAAKqM,aAGXoG,EACP,SAASxR,EAAEgV,GACP,IAAIC,EAAU,GAAG9S,MAAM3F,KAAKuB,UAAW,GACvC,OAAO,WACH,IAAID,EAAO,GAAGqE,MAAM3F,KAAKuB,WACzB5D,EAAEyB,KAAKqZ,GAAS,SAASrR,GACrB,OAAOoR,EAAIpR,GAAQ5F,MAAMgX,EAAKlX,QA5Q9B,IAiRhB,WACI,aACA,IAAIoX,EAAKvJ,EAAMsJ,EAuKf,SAASE,EAAOC,EAAMhY,GAClBgY,EAAKxZ,MAAK,WACN,IAAsByZ,EAAlBjP,EAASlM,EAAED,OACdob,EAAYjP,EAAOV,KAAKiG,EAAK0J,aAAejY,EAAGiY,EAAWjP,MAGnE,SAASkP,EAAmBlP,EAAQ5G,GAChC,OAAO4G,EAAOtJ,QAAQ2P,SAASjN,EAAIC,QAAQX,MAAMyW,aAAa7V,IAAIF,EAAIE,IAAIZ,MAAMY,KAsBvD4B,EAtB+E8E,EAuBjG,CACHoP,qBAAsBlU,EAAI5B,IAAI,yBAC9B+V,eAAgBnU,EAAI5B,IAAI,mBACxBiB,gBAAiBW,EAAI5B,IAAI,oBACzBuB,gBAAiBK,EAAI5B,IAAI,oBACzBgW,iBAAkBpU,EAAI5B,IAAI,qBAC1BiW,mBAAoBrU,EAAI5B,IAAI,uBAC5BkW,iBAAkBtU,EAAI5B,IAAI,qBAC1BmW,eAAgBvU,EAAI5B,IAAI,sBA/BqFoW,KAAK,YAAY,GAAMC,WAAW,gCAAgC3M,KAAK,CACpL1H,aAAc,MACdsU,WAAY,QACZC,UAAW,IAmBnB,IAA6B3U,EAhB7B,SAAS4U,EAAU9P,EAAQ5G,GACvB4G,EAAOV,KAAKiG,EAAKwK,MAAO,CACpBjN,IAAK9C,EAAOgD,KAAK,OACjB1H,aAAc0E,EAAOgD,KAAK,gBAC1B4M,WAAY5P,EAAOgD,KAAK,cACxBgN,MAAOhQ,EAAOgD,KAAK,WAEvBhD,EAAOqG,SAASjN,EAAIC,QAAQZ,OAAOuK,KAAK,CACpC1H,aAAc,MACdsU,YAAY,IAEhB,KACK5P,EAAOgD,KAAK,QAAUhD,EAAOgD,KAAK,MAAO,QAC5C,MAAOgL,IACT,OAAOhO,EA2BX,SAASiQ,EAAUvb,GACf,IAAawG,EAGb,OADAA,EADUnH,EAAEoB,SAAST,IAAQX,EAAEkB,UAAUP,GACzBZ,EAAEY,GAAKgU,QAAU,IACtB5K,OAAS5C,EAAM,KAhO9B4T,EAAMhb,EAAEkD,GAAGiY,UACX1J,EAAO,CACHnM,IAAK,SACL2W,MAAO,WACPd,UAAW,gBAEfJ,EAAU,CACNqB,WAAY,SAAoB/W,EAAGoP,GAC/B,IAAInP,EAIJ,OAHAmP,EAAWxU,EAAEa,QAAQ2T,GAAYA,EAAW,GAAGxM,MAAM3F,KAAKuB,UAAW,GAErEyB,EAAMd,GADNa,EAAIA,GAAK,IACGgX,YACLtc,KAAK2B,KAAK4a,GACjB,SAASA,IACL,IAAIpQ,EAAQqQ,EAAUpQ,EAAO4M,EAAOyD,EAAaC,EAAavE,EAAUvT,EAAOE,EAAMsW,EAAWuB,EAChGzc,EAAEyB,KAAK+S,GAAU,SAASkI,GACtBA,EAAEvX,YAAcC,EAAED,aAEtB8G,EAASlM,EAAED,MACXwc,EAAWvc,EAAEsF,EAAII,KAAKhB,SACtByH,EAAQgQ,EAAU9W,EAAET,MACpBmU,EAAQoD,EAAU9W,EAAER,MACpB2X,GAAyB,IAAXnX,EAAET,OAAmBuH,EACnCsQ,GAAyB,IAAXpX,EAAER,OAAmBkU,EACnCyD,IAAgBrQ,EAAQiP,EAAmBlP,EAAQ5G,IACnDmX,IAAgB1D,EAAQ/Y,EAAEsF,EAAII,KAAKb,MAAMW,IAAIF,EAAIE,IAAIX,OACrDsH,GAASA,EAAM9J,IAAI,IACnB6J,EAAS8P,EAAU9P,EAAQ5G,IACvBkX,GAAeC,KACfF,EAAS/W,IAAIF,EAAIE,IAAId,SACrBwH,EAAO1G,IAAIgX,EAAclX,EAAIE,IAAIb,MAAQW,EAAIE,IAAIkB,iBACjDwF,EAAO0Q,KAAKL,GAAUM,SAASvJ,QAAQkJ,EAAcrQ,EAAQ,MAAMqH,OAAOiJ,EAAc1D,EAAQ,OAEpG2D,EAAkBD,EAAcvF,EAAc3C,EAC9C2D,EAAW,IAAIlR,EAAS,CACpBE,GAAIgF,IAERvH,EAAQ,IAAIqH,EAAM,CACdpH,KAAMuH,EACNxH,MAAOuH,GACR5G,GACHT,EAAO,IAAI6X,EAAgB,CACvBvS,KAAM4O,EACNtE,SAAUA,GACXnP,GACH6V,EAAY,IAAI7D,EAAU,CACtB3S,MAAOA,EACPE,KAAMA,EACNqT,SAAUA,EACVC,UAAW9S,EAAE8S,WACd7S,GACH4G,EAAOV,KAAKiG,EAAKnM,IAAKA,GACtB4G,EAAOV,KAAKiG,EAAK0J,UAAWA,KAGpCb,UAAW,WACP,IAAIlC,EAIJ,OAHA6C,EAAOlb,KAAK6U,SAAS,SAASkI,GAC1B1E,EAAU0E,EAAExC,eAETlC,GAEXmC,OAAQ,WAIJ,OAHAU,EAAOlb,MAAM,SAAS+c,GAClBA,EAAEvC,YAECxa,MAEXya,QAAS,WAIL,OAHAS,EAAOlb,MAAM,SAAS+c,GAClBA,EAAEtC,aAECza,MAEXiZ,SAAU,WACN,IAAIX,EAIJ,OAHA4C,EAAOlb,KAAK6U,SAAS,SAASkI,GAC1BzE,EAASyE,EAAE9D,cAERX,GAEXC,SAAU,WAIN,OAHA2C,EAAOlb,MAAM,SAAS+c,GAClBA,EAAExE,cAECvY,MAEX0a,WAAY,WAIR,OAHAQ,EAAOlb,MAAM,SAAS+c,GAClBA,EAAErC,gBAEC1a,MAEXqW,OAAQ,WACJ,IAAIlR,EAIJ,OAHA+V,EAAOlb,KAAK6U,SAAS,SAASkI,GAC1B5X,EAAO4X,EAAE1G,YAENlR,GAEXA,KAAM,WAIF,OAHA+V,EAAOlb,MAAM,SAAS+c,GAClBA,EAAE5X,UAECnF,MAEXuW,MAAO,WAIH,OAHA2E,EAAOlb,MAAM,SAAS+c,GAClBA,EAAExG,WAECvW,MAEXwH,OAAQ,SAAgBL,GACpB,IAAI6V,GAAU,EAAO3V,EAAMpH,EAAEkH,GAI7B,OAHA+T,EAAOlb,KAAK6U,SAAS,SAASkI,GAC1BC,EAAUD,EAAEvV,OAAOH,MAEhB2V,GAEXvV,aAAc,SAAsBN,GAChC,IAAI6V,GAAU,EAAO3V,EAAMpH,EAAEkH,GAI7B,OAHA+T,EAAOlb,KAAK6U,SAAS,SAASkI,GAC1BC,EAAUD,EAAEtV,aAAaJ,MAEtB2V,GAEXlD,WAAY,SAAoBpD,GAC5B,IAAIsG,GAAU,EAId,OAHA9B,EAAOlb,KAAK6U,SAAS,SAASkI,GAC1BC,EAAUD,EAAEjD,WAAWpD,MAEpBsG,GAEX1a,IAAK,SAAa2a,GACd,IAAI5Q,EACJ,OAAKvI,UAAUmG,QAMXiR,EAAOlb,MAAM,SAAS+c,GAClBA,EAAEpC,OAAOsC,MAENjd,OARPkb,EAAOlb,KAAK6U,SAAS,SAASkI,GAC1B1Q,EAAQ0Q,EAAEnC,YAEPvO,IAQf8E,QAAS,WAKL,OAJA+J,EAAOlb,MAAM,SAASob,EAAWjP,IA2DzC,SAAgBA,GACZ,IAAI5G,EAAKiX,EACTjX,EAAM4G,EAAOV,KAAKiG,EAAKnM,KACvBiX,EAAWrQ,EAAO2Q,SAAS7a,OAAOsD,EAAIM,UAAUlB,SAChDzE,EAAEyB,KAAKwK,EAAOV,KAAKiG,EAAKwK,QAAQ,SAAS5Z,EAAKD,GAC1CnC,EAAEiB,YAAYmB,GAAO6J,EAAO2P,WAAWzZ,GAAO8J,EAAOgD,KAAK9M,EAAKC,MAEnE6J,EAAOmP,WAAW5J,EAAK0J,WAAWE,WAAW5J,EAAKnM,KAAK+V,WAAW5J,EAAKvC,MAAMuG,YAAYnQ,EAAIC,QAAQZ,OACjG4X,EAASvS,SACTkC,EAAO+Q,SAAS7P,YAAYmP,GAC5BA,EAASpL,UApEL+L,CAAOhR,GACPiP,EAAUjK,aAEPnR,OAGfC,EAAEkD,GAAGiY,UAAY,SAASzR,GACtB,OAAIqR,EAAQrR,GACDqR,EAAQrR,GAAQ5F,MAAM/D,KAAM,GAAGkI,MAAM3F,KAAKuB,UAAW,IAErDkX,EAAQqB,WAAWtY,MAAM/D,KAAM8D,YAG9C7D,EAAEkD,GAAGiY,UAAUgC,WAAa,WAExB,OADAnd,EAAEkD,GAAGiY,UAAYH,EACVjb,MAvKf", "sourcesContent": ["/*!\r\n * typeahead.js 0.11.1\r\n * https://github.com/twitter/typeahead.js\r\n * Copyright 2013-2015 Twitter, Inc. and other contributors; Licensed MIT\r\n */\r\n\r\n(function(root, factory) {\r\n    if (typeof define === \"function\" && define.amd) {\r\n        define(\"typeahead.js\", [ \"jquery\" ], function(a0) {\r\n            return factory(a0);\r\n        });\r\n    } else if (typeof exports === \"object\") {\r\n        module.exports = factory(require(\"jquery\"));\r\n    } else {\r\n        factory(jQuery);\r\n    }\r\n})(this, function($) {\r\n    var _ = function() {\r\n        \"use strict\";\r\n        return {\r\n            isMsie: function() {\r\n                return /(msie|trident)/i.test(navigator.userAgent) ? navigator.userAgent.match(/(msie |rv:)(\\d+(.\\d+)?)/i)[2] : false;\r\n            },\r\n            isBlankString: function(str) {\r\n                return !str || /^\\s*$/.test(str);\r\n            },\r\n            escapeRegExChars: function(str) {\r\n                return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, \"\\\\$&\");\r\n            },\r\n            isString: function(obj) {\r\n                return typeof obj === \"string\";\r\n            },\r\n            isNumber: function(obj) {\r\n                return typeof obj === \"number\";\r\n            },\r\n            isArray: $.isArray,\r\n            isFunction: $.isFunction,\r\n            isObject: $.isPlainObject,\r\n            isUndefined: function(obj) {\r\n                return typeof obj === \"undefined\";\r\n            },\r\n            isElement: function(obj) {\r\n                return !!(obj && obj.nodeType === 1);\r\n            },\r\n            isJQuery: function(obj) {\r\n                return obj instanceof $;\r\n            },\r\n            toStr: function toStr(s) {\r\n                return _.isUndefined(s) || s === null ? \"\" : s + \"\";\r\n            },\r\n            bind: $.proxy,\r\n            each: function(collection, cb) {\r\n                $.each(collection, reverseArgs);\r\n                function reverseArgs(index, value) {\r\n                    return cb(value, index);\r\n                }\r\n            },\r\n            map: $.map,\r\n            filter: $.grep,\r\n            every: function(obj, test) {\r\n                var result = true;\r\n                if (!obj) {\r\n                    return result;\r\n                }\r\n                $.each(obj, function(key, val) {\r\n                    if (!(result = test.call(null, val, key, obj))) {\r\n                        return false;\r\n                    }\r\n                });\r\n                return !!result;\r\n            },\r\n            some: function(obj, test) {\r\n                var result = false;\r\n                if (!obj) {\r\n                    return result;\r\n                }\r\n                $.each(obj, function(key, val) {\r\n                    if (result = test.call(null, val, key, obj)) {\r\n                        return false;\r\n                    }\r\n                });\r\n                return !!result;\r\n            },\r\n            mixin: $.extend,\r\n            identity: function(x) {\r\n                return x;\r\n            },\r\n            clone: function(obj) {\r\n                return $.extend(true, {}, obj);\r\n            },\r\n            getIdGenerator: function() {\r\n                var counter = 0;\r\n                return function() {\r\n                    return counter++;\r\n                };\r\n            },\r\n            templatify: function templatify(obj) {\r\n                return $.isFunction(obj) ? obj : template;\r\n                function template() {\r\n                    return String(obj);\r\n                }\r\n            },\r\n            defer: function(fn) {\r\n                setTimeout(fn, 0);\r\n            },\r\n            debounce: function(func, wait, immediate) {\r\n                var timeout, result;\r\n                return function() {\r\n                    var context = this, args = arguments, later, callNow;\r\n                    later = function() {\r\n                        timeout = null;\r\n                        if (!immediate) {\r\n                            result = func.apply(context, args);\r\n                        }\r\n                    };\r\n                    callNow = immediate && !timeout;\r\n                    clearTimeout(timeout);\r\n                    timeout = setTimeout(later, wait);\r\n                    if (callNow) {\r\n                        result = func.apply(context, args);\r\n                    }\r\n                    return result;\r\n                };\r\n            },\r\n            throttle: function(func, wait) {\r\n                var context, args, timeout, result, previous, later;\r\n                previous = 0;\r\n                later = function() {\r\n                    previous = new Date();\r\n                    timeout = null;\r\n                    result = func.apply(context, args);\r\n                };\r\n                return function() {\r\n                    var now = new Date(), remaining = wait - (now - previous);\r\n                    context = this;\r\n                    args = arguments;\r\n                    if (remaining <= 0) {\r\n                        clearTimeout(timeout);\r\n                        timeout = null;\r\n                        previous = now;\r\n                        result = func.apply(context, args);\r\n                    } else if (!timeout) {\r\n                        timeout = setTimeout(later, remaining);\r\n                    }\r\n                    return result;\r\n                };\r\n            },\r\n            stringify: function(val) {\r\n                return _.isString(val) ? val : JSON.stringify(val);\r\n            },\r\n            noop: function() {}\r\n        };\r\n    }();\r\n    var WWW = function() {\r\n        \"use strict\";\r\n        var defaultClassNames = {\r\n            wrapper: \"twitter-typeahead\",\r\n            input: \"tt-input\",\r\n            hint: \"tt-hint\",\r\n            menu: \"tt-menu\",\r\n            dataset: \"tt-dataset\",\r\n            suggestion: \"tt-suggestion\",\r\n            selectable: \"tt-selectable\",\r\n            empty: \"tt-empty\",\r\n            open: \"tt-open\",\r\n            cursor: \"tt-cursor\",\r\n            highlight: \"tt-highlight\"\r\n        };\r\n        return build;\r\n        function build(o) {\r\n            var www, classes;\r\n            classes = _.mixin({}, defaultClassNames, o);\r\n            www = {\r\n                css: buildCss(),\r\n                classes: classes,\r\n                html: buildHtml(classes),\r\n                selectors: buildSelectors(classes)\r\n            };\r\n            return {\r\n                css: www.css,\r\n                html: www.html,\r\n                classes: www.classes,\r\n                selectors: www.selectors,\r\n                mixin: function(o) {\r\n                    _.mixin(o, www);\r\n                }\r\n            };\r\n        }\r\n        function buildHtml(c) {\r\n            return {\r\n                wrapper: '<span class=\"' + c.wrapper + '\"></span>',\r\n                menu: '<div class=\"' + c.menu + '\"></div>'\r\n            };\r\n        }\r\n        function buildSelectors(classes) {\r\n            var selectors = {};\r\n            _.each(classes, function(v, k) {\r\n                selectors[k] = \".\" + v;\r\n            });\r\n            return selectors;\r\n        }\r\n        function buildCss() {\r\n            var css = {\r\n                wrapper: {\r\n                    position: \"relative\",\r\n                    display: \"inline-block\"\r\n                },\r\n                hint: {\r\n                    position: \"absolute\",\r\n                    top: \"0\",\r\n                    left: \"0\",\r\n                    borderColor: \"transparent\",\r\n                    boxShadow: \"none\",\r\n                    opacity: \"1\"\r\n                },\r\n                input: {\r\n                    position: \"relative\",\r\n                    verticalAlign: \"top\",\r\n                    backgroundColor: \"transparent\"\r\n                },\r\n                inputWithNoHint: {\r\n                    position: \"relative\",\r\n                    verticalAlign: \"top\"\r\n                },\r\n                menu: {\r\n                    position: \"absolute\",\r\n                    top: \"100%\",\r\n                    left: \"0\",\r\n                    zIndex: \"100\",\r\n                    display: \"none\"\r\n                },\r\n                ltr: {\r\n                    left: \"0\",\r\n                    right: \"auto\"\r\n                },\r\n                rtl: {\r\n                    left: \"auto\",\r\n                    right: \" 0\"\r\n                }\r\n            };\r\n            if (_.isMsie()) {\r\n                _.mixin(css.input, {\r\n                    backgroundImage: \"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\"\r\n                });\r\n            }\r\n            return css;\r\n        }\r\n    }();\r\n    var EventBus = function() {\r\n        \"use strict\";\r\n        var namespace, deprecationMap;\r\n        namespace = \"typeahead:\";\r\n        deprecationMap = {\r\n            render: \"rendered\",\r\n            cursorchange: \"cursorchanged\",\r\n            select: \"selected\",\r\n            autocomplete: \"autocompleted\"\r\n        };\r\n        function EventBus(o) {\r\n            if (!o || !o.el) {\r\n                $.error(\"EventBus initialized without el\");\r\n            }\r\n            this.$el = $(o.el);\r\n        }\r\n        _.mixin(EventBus.prototype, {\r\n            _trigger: function(type, args) {\r\n                var $e;\r\n                $e = $.Event(namespace + type);\r\n                (args = args || []).unshift($e);\r\n                this.$el.trigger.apply(this.$el, args);\r\n                return $e;\r\n            },\r\n            before: function(type) {\r\n                var args, $e;\r\n                args = [].slice.call(arguments, 1);\r\n                $e = this._trigger(\"before\" + type, args);\r\n                return $e.isDefaultPrevented();\r\n            },\r\n            trigger: function(type) {\r\n                var deprecatedType;\r\n                this._trigger(type, [].slice.call(arguments, 1));\r\n                if (deprecatedType = deprecationMap[type]) {\r\n                    this._trigger(deprecatedType, [].slice.call(arguments, 1));\r\n                }\r\n            }\r\n        });\r\n        return EventBus;\r\n    }();\r\n    var EventEmitter = function() {\r\n        \"use strict\";\r\n        var splitter = /\\s+/, nextTick = getNextTick();\r\n        return {\r\n            onSync: onSync,\r\n            onAsync: onAsync,\r\n            off: off,\r\n            trigger: trigger\r\n        };\r\n        function on(method, types, cb, context) {\r\n            var type;\r\n            if (!cb) {\r\n                return this;\r\n            }\r\n            types = types.split(splitter);\r\n            cb = context ? bindContext(cb, context) : cb;\r\n            this._callbacks = this._callbacks || {};\r\n            while (type = types.shift()) {\r\n                this._callbacks[type] = this._callbacks[type] || {\r\n                    sync: [],\r\n                    async: []\r\n                };\r\n                this._callbacks[type][method].push(cb);\r\n            }\r\n            return this;\r\n        }\r\n        function onAsync(types, cb, context) {\r\n            return on.call(this, \"async\", types, cb, context);\r\n        }\r\n        function onSync(types, cb, context) {\r\n            return on.call(this, \"sync\", types, cb, context);\r\n        }\r\n        function off(types) {\r\n            var type;\r\n            if (!this._callbacks) {\r\n                return this;\r\n            }\r\n            types = types.split(splitter);\r\n            while (type = types.shift()) {\r\n                delete this._callbacks[type];\r\n            }\r\n            return this;\r\n        }\r\n        function trigger(types) {\r\n            var type, callbacks, args, syncFlush, asyncFlush;\r\n            if (!this._callbacks) {\r\n                return this;\r\n            }\r\n            types = types.split(splitter);\r\n            args = [].slice.call(arguments, 1);\r\n            while ((type = types.shift()) && (callbacks = this._callbacks[type])) {\r\n                syncFlush = getFlush(callbacks.sync, this, [ type ].concat(args));\r\n                asyncFlush = getFlush(callbacks.async, this, [ type ].concat(args));\r\n                syncFlush() && nextTick(asyncFlush);\r\n            }\r\n            return this;\r\n        }\r\n        function getFlush(callbacks, context, args) {\r\n            return flush;\r\n            function flush() {\r\n                var cancelled;\r\n                for (var i = 0, len = callbacks.length; !cancelled && i < len; i += 1) {\r\n                    cancelled = callbacks[i].apply(context, args) === false;\r\n                }\r\n                return !cancelled;\r\n            }\r\n        }\r\n        function getNextTick() {\r\n            var nextTickFn;\r\n            if (window.setImmediate) {\r\n                nextTickFn = function nextTickSetImmediate(fn) {\r\n                    setImmediate(function() {\r\n                        fn();\r\n                    });\r\n                };\r\n            } else {\r\n                nextTickFn = function nextTickSetTimeout(fn) {\r\n                    setTimeout(function() {\r\n                        fn();\r\n                    }, 0);\r\n                };\r\n            }\r\n            return nextTickFn;\r\n        }\r\n        function bindContext(fn, context) {\r\n            return fn.bind ? fn.bind(context) : function() {\r\n                fn.apply(context, [].slice.call(arguments, 0));\r\n            };\r\n        }\r\n    }();\r\n    var highlight = function(doc) {\r\n        \"use strict\";\r\n        var defaults = {\r\n            node: null,\r\n            pattern: null,\r\n            tagName: \"strong\",\r\n            className: null,\r\n            wordsOnly: false,\r\n            caseSensitive: false\r\n        };\r\n        return function hightlight(o) {\r\n            var regex;\r\n            o = _.mixin({}, defaults, o);\r\n            if (!o.node || !o.pattern) {\r\n                return;\r\n            }\r\n            o.pattern = _.isArray(o.pattern) ? o.pattern : [ o.pattern ];\r\n            regex = getRegex(o.pattern, o.caseSensitive, o.wordsOnly);\r\n            traverse(o.node, hightlightTextNode);\r\n            function hightlightTextNode(textNode) {\r\n                var match, patternNode, wrapperNode;\r\n                if (match = regex.exec(textNode.data)) {\r\n                    wrapperNode = doc.createElement(o.tagName);\r\n                    o.className && (wrapperNode.className = o.className);\r\n                    patternNode = textNode.splitText(match.index);\r\n                    patternNode.splitText(match[0].length);\r\n                    wrapperNode.appendChild(patternNode.cloneNode(true));\r\n                    textNode.parentNode.replaceChild(wrapperNode, patternNode);\r\n                }\r\n                return !!match;\r\n            }\r\n            function traverse(el, hightlightTextNode) {\r\n                var childNode, TEXT_NODE_TYPE = 3;\r\n                for (var i = 0; i < el.childNodes.length; i++) {\r\n                    childNode = el.childNodes[i];\r\n                    if (childNode.nodeType === TEXT_NODE_TYPE) {\r\n                        i += hightlightTextNode(childNode) ? 1 : 0;\r\n                    } else {\r\n                        traverse(childNode, hightlightTextNode);\r\n                    }\r\n                }\r\n            }\r\n        };\r\n        function getRegex(patterns, caseSensitive, wordsOnly) {\r\n            var escapedPatterns = [], regexStr;\r\n            for (var i = 0, len = patterns.length; i < len; i++) {\r\n                escapedPatterns.push(_.escapeRegExChars(patterns[i]));\r\n            }\r\n            regexStr = wordsOnly ? \"\\\\b(\" + escapedPatterns.join(\"|\") + \")\\\\b\" : \"(\" + escapedPatterns.join(\"|\") + \")\";\r\n            return caseSensitive ? new RegExp(regexStr) : new RegExp(regexStr, \"i\");\r\n        }\r\n    }(window.document);\r\n    var Input = function() {\r\n        \"use strict\";\r\n        var specialKeyCodeMap;\r\n        specialKeyCodeMap = {\r\n            9: \"tab\",\r\n            27: \"esc\",\r\n            37: \"left\",\r\n            39: \"right\",\r\n            13: \"enter\",\r\n            38: \"up\",\r\n            40: \"down\"\r\n        };\r\n        function Input(o, www) {\r\n            o = o || {};\r\n            if (!o.input) {\r\n                $.error(\"input is missing\");\r\n            }\r\n            www.mixin(this);\r\n            this.$hint = $(o.hint);\r\n            this.$input = $(o.input);\r\n            this.query = this.$input.val();\r\n            this.queryWhenFocused = this.hasFocus() ? this.query : null;\r\n            this.$overflowHelper = buildOverflowHelper(this.$input);\r\n            this._checkLanguageDirection();\r\n            if (this.$hint.length === 0) {\r\n                this.setHint = this.getHint = this.clearHint = this.clearHintIfInvalid = _.noop;\r\n            }\r\n        }\r\n        Input.normalizeQuery = function(str) {\r\n            return _.toStr(str).replace(/^\\s*/g, \"\").replace(/\\s{2,}/g, \" \");\r\n        };\r\n        _.mixin(Input.prototype, EventEmitter, {\r\n            _onBlur: function onBlur() {\r\n                this.resetInputValue();\r\n                this.trigger(\"blurred\");\r\n            },\r\n            _onFocus: function onFocus() {\r\n                this.queryWhenFocused = this.query;\r\n                this.trigger(\"focused\");\r\n            },\r\n            _onKeydown: function onKeydown($e) {\r\n                var keyName = specialKeyCodeMap[$e.which || $e.keyCode];\r\n                this._managePreventDefault(keyName, $e);\r\n                if (keyName && this._shouldTrigger(keyName, $e)) {\r\n                    this.trigger(keyName + \"Keyed\", $e);\r\n                }\r\n            },\r\n            _onInput: function onInput() {\r\n                this._setQuery(this.getInputValue());\r\n                this.clearHintIfInvalid();\r\n                this._checkLanguageDirection();\r\n            },\r\n            _managePreventDefault: function managePreventDefault(keyName, $e) {\r\n                var preventDefault;\r\n                switch (keyName) {\r\n                  case \"up\":\r\n                  case \"down\":\r\n                    preventDefault = !withModifier($e);\r\n                    break;\r\n\r\n                  default:\r\n                    preventDefault = false;\r\n                }\r\n                preventDefault && $e.preventDefault();\r\n            },\r\n            _shouldTrigger: function shouldTrigger(keyName, $e) {\r\n                var trigger;\r\n                switch (keyName) {\r\n                  case \"tab\":\r\n                    trigger = !withModifier($e);\r\n                    break;\r\n\r\n                  default:\r\n                    trigger = true;\r\n                }\r\n                return trigger;\r\n            },\r\n            _checkLanguageDirection: function checkLanguageDirection() {\r\n                var dir = (this.$input.css(\"direction\") || \"ltr\").toLowerCase();\r\n                if (this.dir !== dir) {\r\n                    this.dir = dir;\r\n                    this.$hint.attr(\"dir\", dir);\r\n                    this.trigger(\"langDirChanged\", dir);\r\n                }\r\n            },\r\n            _setQuery: function setQuery(val, silent) {\r\n                var areEquivalent, hasDifferentWhitespace;\r\n                areEquivalent = areQueriesEquivalent(val, this.query);\r\n                hasDifferentWhitespace = areEquivalent ? this.query.length !== val.length : false;\r\n                this.query = val;\r\n                if (!silent && !areEquivalent) {\r\n                    this.trigger(\"queryChanged\", this.query);\r\n                } else if (!silent && hasDifferentWhitespace) {\r\n                    this.trigger(\"whitespaceChanged\", this.query);\r\n                }\r\n            },\r\n            bind: function() {\r\n                var that = this, onBlur, onFocus, onKeydown, onInput;\r\n                onBlur = _.bind(this._onBlur, this);\r\n                onFocus = _.bind(this._onFocus, this);\r\n                onKeydown = _.bind(this._onKeydown, this);\r\n                onInput = _.bind(this._onInput, this);\r\n                this.$input.on(\"blur.tt\", onBlur).on(\"focus.tt\", onFocus).on(\"keydown.tt\", onKeydown);\r\n                if (!_.isMsie() || _.isMsie() > 9) {\r\n                    this.$input.on(\"input.tt\", onInput);\r\n                } else {\r\n                    this.$input.on(\"keydown.tt keypress.tt cut.tt paste.tt\", function($e) {\r\n                        if (specialKeyCodeMap[$e.which || $e.keyCode]) {\r\n                            return;\r\n                        }\r\n                        _.defer(_.bind(that._onInput, that, $e));\r\n                    });\r\n                }\r\n                return this;\r\n            },\r\n            focus: function focus() {\r\n                this.$input.focus();\r\n            },\r\n            blur: function blur() {\r\n                this.$input.blur();\r\n            },\r\n            getLangDir: function getLangDir() {\r\n                return this.dir;\r\n            },\r\n            getQuery: function getQuery() {\r\n                return this.query || \"\";\r\n            },\r\n            setQuery: function setQuery(val, silent) {\r\n                this.setInputValue(val);\r\n                this._setQuery(val, silent);\r\n            },\r\n            hasQueryChangedSinceLastFocus: function hasQueryChangedSinceLastFocus() {\r\n                return this.query !== this.queryWhenFocused;\r\n            },\r\n            getInputValue: function getInputValue() {\r\n                return this.$input.val();\r\n            },\r\n            setInputValue: function setInputValue(value) {\r\n                this.$input.val(value);\r\n                this.clearHintIfInvalid();\r\n                this._checkLanguageDirection();\r\n            },\r\n            resetInputValue: function resetInputValue() {\r\n                this.setInputValue(this.query);\r\n            },\r\n            getHint: function getHint() {\r\n                return this.$hint.val();\r\n            },\r\n            setHint: function setHint(value) {\r\n                this.$hint.val(value);\r\n            },\r\n            clearHint: function clearHint() {\r\n                this.setHint(\"\");\r\n            },\r\n            clearHintIfInvalid: function clearHintIfInvalid() {\r\n                var val, hint, valIsPrefixOfHint, isValid;\r\n                val = this.getInputValue();\r\n                hint = this.getHint();\r\n                valIsPrefixOfHint = val !== hint && hint.indexOf(val) === 0;\r\n                isValid = val !== \"\" && valIsPrefixOfHint && !this.hasOverflow();\r\n                !isValid && this.clearHint();\r\n            },\r\n            hasFocus: function hasFocus() {\r\n                return this.$input.is(\":focus\");\r\n            },\r\n            hasOverflow: function hasOverflow() {\r\n                var constraint = this.$input.width() - 2;\r\n                this.$overflowHelper.text(this.getInputValue());\r\n                return this.$overflowHelper.width() >= constraint;\r\n            },\r\n            isCursorAtEnd: function() {\r\n                var valueLength, selectionStart, range;\r\n                valueLength = this.$input.val().length;\r\n                selectionStart = this.$input[0].selectionStart;\r\n                if (_.isNumber(selectionStart)) {\r\n                    return selectionStart === valueLength;\r\n                } else if (document.selection) {\r\n                    range = document.selection.createRange();\r\n                    range.moveStart(\"character\", -valueLength);\r\n                    return valueLength === range.text.length;\r\n                }\r\n                return true;\r\n            },\r\n            destroy: function destroy() {\r\n                this.$hint.off(\".tt\");\r\n                this.$input.off(\".tt\");\r\n                this.$overflowHelper.remove();\r\n                this.$hint = this.$input = this.$overflowHelper = $(\"<div>\");\r\n            }\r\n        });\r\n        return Input;\r\n        function buildOverflowHelper($input) {\r\n            return $('<pre aria-hidden=\"true\"></pre>').css({\r\n                position: \"absolute\",\r\n                visibility: \"hidden\",\r\n                whiteSpace: \"pre\",\r\n                fontFamily: $input.css(\"font-family\"),\r\n                fontSize: $input.css(\"font-size\"),\r\n                fontStyle: $input.css(\"font-style\"),\r\n                fontVariant: $input.css(\"font-variant\"),\r\n                fontWeight: $input.css(\"font-weight\"),\r\n                wordSpacing: $input.css(\"word-spacing\"),\r\n                letterSpacing: $input.css(\"letter-spacing\"),\r\n                textIndent: $input.css(\"text-indent\"),\r\n                textRendering: $input.css(\"text-rendering\"),\r\n                textTransform: $input.css(\"text-transform\")\r\n            }).insertAfter($input);\r\n        }\r\n        function areQueriesEquivalent(a, b) {\r\n            return Input.normalizeQuery(a) === Input.normalizeQuery(b);\r\n        }\r\n        function withModifier($e) {\r\n            return $e.altKey || $e.ctrlKey || $e.metaKey || $e.shiftKey;\r\n        }\r\n    }();\r\n    var Dataset = function() {\r\n        \"use strict\";\r\n        var keys, nameGenerator;\r\n        keys = {\r\n            val: \"tt-selectable-display\",\r\n            obj: \"tt-selectable-object\"\r\n        };\r\n        nameGenerator = _.getIdGenerator();\r\n        function Dataset(o, www) {\r\n            o = o || {};\r\n            o.templates = o.templates || {};\r\n            o.templates.notFound = o.templates.notFound || o.templates.empty;\r\n            if (!o.source) {\r\n                $.error(\"missing source\");\r\n            }\r\n            if (!o.node) {\r\n                $.error(\"missing node\");\r\n            }\r\n            if (o.name && !isValidName(o.name)) {\r\n                $.error(\"invalid dataset name: \" + o.name);\r\n            }\r\n            www.mixin(this);\r\n            this.highlight = !!o.highlight;\r\n            this.name = o.name || nameGenerator();\r\n            this.limit = o.limit || 5;\r\n            this.displayFn = getDisplayFn(o.display || o.displayKey);\r\n            this.templates = getTemplates(o.templates, this.displayFn);\r\n            this.source = o.source.__ttAdapter ? o.source.__ttAdapter() : o.source;\r\n            this.async = _.isUndefined(o.async) ? this.source.length > 2 : !!o.async;\r\n            this._resetLastSuggestion();\r\n            this.$el = $(o.node).addClass(this.classes.dataset).addClass(this.classes.dataset + \"-\" + this.name);\r\n        }\r\n        Dataset.extractData = function extractData(el) {\r\n            var $el = $(el);\r\n            if ($el.data(keys.obj)) {\r\n                return {\r\n                    val: $el.data(keys.val) || \"\",\r\n                    obj: $el.data(keys.obj) || null\r\n                };\r\n            }\r\n            return null;\r\n        };\r\n        _.mixin(Dataset.prototype, EventEmitter, {\r\n            _overwrite: function overwrite(query, suggestions) {\r\n                suggestions = suggestions || [];\r\n                if (suggestions.length) {\r\n                    this._renderSuggestions(query, suggestions);\r\n                } else if (this.async && this.templates.pending) {\r\n                    this._renderPending(query);\r\n                } else if (!this.async && this.templates.notFound) {\r\n                    this._renderNotFound(query);\r\n                } else {\r\n                    this._empty();\r\n                }\r\n                this.trigger(\"rendered\", this.name, suggestions, false);\r\n            },\r\n            _append: function append(query, suggestions) {\r\n                suggestions = suggestions || [];\r\n                if (suggestions.length && this.$lastSuggestion.length) {\r\n                    this._appendSuggestions(query, suggestions);\r\n                } else if (suggestions.length) {\r\n                    this._renderSuggestions(query, suggestions);\r\n                } else if (!this.$lastSuggestion.length && this.templates.notFound) {\r\n                    this._renderNotFound(query);\r\n                }\r\n                this.trigger(\"rendered\", this.name, suggestions, true);\r\n            },\r\n            _renderSuggestions: function renderSuggestions(query, suggestions) {\r\n                var $fragment;\r\n                $fragment = this._getSuggestionsFragment(query, suggestions);\r\n                this.$lastSuggestion = $fragment.children().last();\r\n                this.$el.html($fragment).prepend(this._getHeader(query, suggestions)).append(this._getFooter(query, suggestions));\r\n            },\r\n            _appendSuggestions: function appendSuggestions(query, suggestions) {\r\n                var $fragment, $lastSuggestion;\r\n                $fragment = this._getSuggestionsFragment(query, suggestions);\r\n                $lastSuggestion = $fragment.children().last();\r\n                this.$lastSuggestion.after($fragment);\r\n                this.$lastSuggestion = $lastSuggestion;\r\n            },\r\n            _renderPending: function renderPending(query) {\r\n                var template = this.templates.pending;\r\n                this._resetLastSuggestion();\r\n                template && this.$el.html(template({\r\n                    query: query,\r\n                    dataset: this.name\r\n                }));\r\n            },\r\n            _renderNotFound: function renderNotFound(query) {\r\n                var template = this.templates.notFound;\r\n                this._resetLastSuggestion();\r\n                template && this.$el.html(template({\r\n                    query: query,\r\n                    dataset: this.name\r\n                }));\r\n            },\r\n            _empty: function empty() {\r\n                this.$el.empty();\r\n                this._resetLastSuggestion();\r\n            },\r\n            _getSuggestionsFragment: function getSuggestionsFragment(query, suggestions) {\r\n                var that = this, fragment;\r\n                fragment = document.createDocumentFragment();\r\n                _.each(suggestions, function getSuggestionNode(suggestion) {\r\n                    var $el, context;\r\n                    context = that._injectQuery(query, suggestion);\r\n                    $el = $(that.templates.suggestion(context)).data(keys.obj, suggestion).data(keys.val, that.displayFn(suggestion)).addClass(that.classes.suggestion + \" \" + that.classes.selectable);\r\n                    fragment.appendChild($el[0]);\r\n                });\r\n                this.highlight && highlight({\r\n                    className: this.classes.highlight,\r\n                    node: fragment,\r\n                    pattern: query\r\n                });\r\n                return $(fragment);\r\n            },\r\n            _getFooter: function getFooter(query, suggestions) {\r\n                return this.templates.footer ? this.templates.footer({\r\n                    query: query,\r\n                    suggestions: suggestions,\r\n                    dataset: this.name\r\n                }) : null;\r\n            },\r\n            _getHeader: function getHeader(query, suggestions) {\r\n                return this.templates.header ? this.templates.header({\r\n                    query: query,\r\n                    suggestions: suggestions,\r\n                    dataset: this.name\r\n                }) : null;\r\n            },\r\n            _resetLastSuggestion: function resetLastSuggestion() {\r\n                this.$lastSuggestion = $();\r\n            },\r\n            _injectQuery: function injectQuery(query, obj) {\r\n                return _.isObject(obj) ? _.mixin({\r\n                    _query: query\r\n                }, obj) : obj;\r\n            },\r\n            update: function update(query) {\r\n                var that = this, canceled = false, syncCalled = false, rendered = 0;\r\n                this.cancel();\r\n                this.cancel = function cancel() {\r\n                    canceled = true;\r\n                    that.cancel = $.noop;\r\n                    that.async && that.trigger(\"asyncCanceled\", query);\r\n                };\r\n                this.source(query, sync, async);\r\n                !syncCalled && sync([]);\r\n                function sync(suggestions) {\r\n                    if (syncCalled) {\r\n                        return;\r\n                    }\r\n                    syncCalled = true;\r\n                    suggestions = (suggestions || []).slice(0, that.limit);\r\n                    rendered = suggestions.length;\r\n                    that._overwrite(query, suggestions);\r\n                    if (rendered < that.limit && that.async) {\r\n                        that.trigger(\"asyncRequested\", query);\r\n                    }\r\n                }\r\n                function async(suggestions) {\r\n                    suggestions = suggestions || [];\r\n                    if (!canceled && rendered < that.limit) {\r\n                        that.cancel = $.noop;\r\n                        that._append(query, suggestions.slice(0, that.limit - rendered)); //***\r\n                        rendered += suggestions.length; //***\r\n                        that.async && that.trigger(\"asyncReceived\", query);\r\n                    }\r\n                }\r\n            },\r\n            cancel: $.noop,\r\n            clear: function clear() {\r\n                this._empty();\r\n                this.cancel();\r\n                this.trigger(\"cleared\");\r\n            },\r\n            isEmpty: function isEmpty() {\r\n                return this.$el.is(\":empty\");\r\n            },\r\n            destroy: function destroy() {\r\n                this.$el = $(\"<div>\");\r\n            }\r\n        });\r\n        return Dataset;\r\n        function getDisplayFn(display) {\r\n            display = display || _.stringify;\r\n            return _.isFunction(display) ? display : displayFn;\r\n            function displayFn(obj) {\r\n                return obj[display];\r\n            }\r\n        }\r\n        function getTemplates(templates, displayFn) {\r\n            return {\r\n                notFound: templates.notFound && _.templatify(templates.notFound),\r\n                pending: templates.pending && _.templatify(templates.pending),\r\n                header: templates.header && _.templatify(templates.header),\r\n                footer: templates.footer && _.templatify(templates.footer),\r\n                suggestion: templates.suggestion || suggestionTemplate\r\n            };\r\n            function suggestionTemplate(context) {\r\n                return $(\"<div>\").text(displayFn(context));\r\n            }\r\n        }\r\n        function isValidName(str) {\r\n            return /^[_a-zA-Z0-9-]+$/.test(str);\r\n        }\r\n    }();\r\n    var Menu = function() {\r\n        \"use strict\";\r\n        function Menu(o, www) {\r\n            var that = this;\r\n            o = o || {};\r\n            if (!o.node) {\r\n                $.error(\"node is required\");\r\n            }\r\n            www.mixin(this);\r\n            this.$node = $(o.node);\r\n            this.query = null;\r\n            this.datasets = _.map(o.datasets, initializeDataset);\r\n            function initializeDataset(oDataset) {\r\n                var node = that.$node.find(oDataset.node).first();\r\n                oDataset.node = node.length ? node : $(\"<div>\").appendTo(that.$node);\r\n                return new Dataset(oDataset, www);\r\n            }\r\n        }\r\n        _.mixin(Menu.prototype, EventEmitter, {\r\n            _onSelectableClick: function onSelectableClick($e) {\r\n                this.trigger(\"selectableClicked\", $($e.currentTarget));\r\n            },\r\n            _onRendered: function onRendered(type, dataset, suggestions, async) {\r\n                this.$node.toggleClass(this.classes.empty, this._allDatasetsEmpty());\r\n                this.trigger(\"datasetRendered\", dataset, suggestions, async);\r\n            },\r\n            _onCleared: function onCleared() {\r\n                this.$node.toggleClass(this.classes.empty, this._allDatasetsEmpty());\r\n                this.trigger(\"datasetCleared\");\r\n            },\r\n            _propagate: function propagate() {\r\n                this.trigger.apply(this, arguments);\r\n            },\r\n            _allDatasetsEmpty: function allDatasetsEmpty() {\r\n                return _.every(this.datasets, isDatasetEmpty);\r\n                function isDatasetEmpty(dataset) {\r\n                    return dataset.isEmpty();\r\n                }\r\n            },\r\n            _getSelectables: function getSelectables() {\r\n                return this.$node.find(this.selectors.selectable);\r\n            },\r\n            _removeCursor: function _removeCursor() {\r\n                var $selectable = this.getActiveSelectable();\r\n                $selectable && $selectable.removeClass(this.classes.cursor);\r\n            },\r\n            _ensureVisible: function ensureVisible($el) {\r\n                var elTop, elBottom, nodeScrollTop, nodeHeight;\r\n                elTop = $el.position().top;\r\n                elBottom = elTop + $el.outerHeight(true);\r\n                nodeScrollTop = this.$node.scrollTop();\r\n                nodeHeight = this.$node.height() + parseInt(this.$node.css(\"paddingTop\"), 10) + parseInt(this.$node.css(\"paddingBottom\"), 10);\r\n                if (elTop < 0) {\r\n                    this.$node.scrollTop(nodeScrollTop + elTop);\r\n                } else if (nodeHeight < elBottom) {\r\n                    this.$node.scrollTop(nodeScrollTop + (elBottom - nodeHeight));\r\n                }\r\n            },\r\n            bind: function() {\r\n                var that = this, onSelectableClick;\r\n                onSelectableClick = _.bind(this._onSelectableClick, this);\r\n                this.$node.on(\"click.tt\", this.selectors.selectable, onSelectableClick);\r\n                _.each(this.datasets, function(dataset) {\r\n                    dataset.onSync(\"asyncRequested\", that._propagate, that).onSync(\"asyncCanceled\", that._propagate, that).onSync(\"asyncReceived\", that._propagate, that).onSync(\"rendered\", that._onRendered, that).onSync(\"cleared\", that._onCleared, that);\r\n                });\r\n                return this;\r\n            },\r\n            isOpen: function isOpen() {\r\n                return this.$node.hasClass(this.classes.open);\r\n            },\r\n            open: function open() {\r\n                this.$node.addClass(this.classes.open);\r\n            },\r\n            close: function close() {\r\n                this.$node.removeClass(this.classes.open);\r\n                this._removeCursor();\r\n            },\r\n            setLanguageDirection: function setLanguageDirection(dir) {\r\n                this.$node.attr(\"dir\", dir);\r\n            },\r\n            selectableRelativeToCursor: function selectableRelativeToCursor(delta) {\r\n                var $selectables, $oldCursor, oldIndex, newIndex;\r\n                $oldCursor = this.getActiveSelectable();\r\n                $selectables = this._getSelectables();\r\n                oldIndex = $oldCursor ? $selectables.index($oldCursor) : -1;\r\n                newIndex = oldIndex + delta;\r\n                newIndex = (newIndex + 1) % ($selectables.length + 1) - 1;\r\n                newIndex = newIndex < -1 ? $selectables.length - 1 : newIndex;\r\n                return newIndex === -1 ? null : $selectables.eq(newIndex);\r\n            },\r\n            setCursor: function setCursor($selectable) {\r\n                this._removeCursor();\r\n                if ($selectable = $selectable && $selectable.first()) {\r\n                    $selectable.addClass(this.classes.cursor);\r\n                    this._ensureVisible($selectable);\r\n                }\r\n            },\r\n            getSelectableData: function getSelectableData($el) {\r\n                return $el && $el.length ? Dataset.extractData($el) : null;\r\n            },\r\n            getActiveSelectable: function getActiveSelectable() {\r\n                var $selectable = this._getSelectables().filter(this.selectors.cursor).first();\r\n                return $selectable.length ? $selectable : null;\r\n            },\r\n            getTopSelectable: function getTopSelectable() {\r\n                var $selectable = this._getSelectables().first();\r\n                return $selectable.length ? $selectable : null;\r\n            },\r\n            update: function update(query) {\r\n                var isValidUpdate = query !== this.query;\r\n                if (isValidUpdate) {\r\n                    this.query = query;\r\n                    _.each(this.datasets, updateDataset);\r\n                }\r\n                return isValidUpdate;\r\n                function updateDataset(dataset) {\r\n                    dataset.update(query);\r\n                }\r\n            },\r\n            empty: function empty() {\r\n                _.each(this.datasets, clearDataset);\r\n                this.query = null;\r\n                this.$node.addClass(this.classes.empty);\r\n                function clearDataset(dataset) {\r\n                    dataset.clear();\r\n                }\r\n            },\r\n            destroy: function destroy() {\r\n                this.$node.off(\".tt\");\r\n                this.$node = $(\"<div>\");\r\n                _.each(this.datasets, destroyDataset);\r\n                function destroyDataset(dataset) {\r\n                    dataset.destroy();\r\n                }\r\n            }\r\n        });\r\n        return Menu;\r\n    }();\r\n    var DefaultMenu = function() {\r\n        \"use strict\";\r\n        var s = Menu.prototype;\r\n        function DefaultMenu() {\r\n            Menu.apply(this, [].slice.call(arguments, 0));\r\n        }\r\n        _.mixin(DefaultMenu.prototype, Menu.prototype, {\r\n            open: function open() {\r\n                !this._allDatasetsEmpty() && this._show();\r\n                return s.open.apply(this, [].slice.call(arguments, 0));\r\n            },\r\n            close: function close() {\r\n                this._hide();\r\n                return s.close.apply(this, [].slice.call(arguments, 0));\r\n            },\r\n            _onRendered: function onRendered() {\r\n                if (this._allDatasetsEmpty()) {\r\n                    this._hide();\r\n                } else {\r\n                    this.isOpen() && this._show();\r\n                }\r\n                return s._onRendered.apply(this, [].slice.call(arguments, 0));\r\n            },\r\n            _onCleared: function onCleared() {\r\n                if (this._allDatasetsEmpty()) {\r\n                    this._hide();\r\n                } else {\r\n                    this.isOpen() && this._show();\r\n                }\r\n                return s._onCleared.apply(this, [].slice.call(arguments, 0));\r\n            },\r\n            setLanguageDirection: function setLanguageDirection(dir) {\r\n                this.$node.css(dir === \"ltr\" ? this.css.ltr : this.css.rtl);\r\n                return s.setLanguageDirection.apply(this, [].slice.call(arguments, 0));\r\n            },\r\n            _hide: function hide() {\r\n                this.$node.hide();\r\n            },\r\n            _show: function show() {\r\n                this.$node.css(\"display\", \"block\");\r\n            }\r\n        });\r\n        return DefaultMenu;\r\n    }();\r\n    var Typeahead = function() {\r\n        \"use strict\";\r\n        function Typeahead(o, www) {\r\n            var onFocused, onBlurred, onEnterKeyed, onTabKeyed, onEscKeyed, onUpKeyed, onDownKeyed, onLeftKeyed, onRightKeyed, onQueryChanged, onWhitespaceChanged;\r\n            o = o || {};\r\n            if (!o.input) {\r\n                $.error(\"missing input\");\r\n            }\r\n            if (!o.menu) {\r\n                $.error(\"missing menu\");\r\n            }\r\n            if (!o.eventBus) {\r\n                $.error(\"missing event bus\");\r\n            }\r\n            www.mixin(this);\r\n            this.eventBus = o.eventBus;\r\n            this.minLength = _.isNumber(o.minLength) ? o.minLength : 1;\r\n            this.input = o.input;\r\n            this.menu = o.menu;\r\n            this.enabled = true;\r\n            this.active = false;\r\n            this.input.hasFocus() && this.activate();\r\n            this.dir = this.input.getLangDir();\r\n            this._hacks();\r\n            this.menu.bind().onSync(\"selectableClicked\", this._onSelectableClicked, this).onSync(\"asyncRequested\", this._onAsyncRequested, this).onSync(\"asyncCanceled\", this._onAsyncCanceled, this).onSync(\"asyncReceived\", this._onAsyncReceived, this).onSync(\"datasetRendered\", this._onDatasetRendered, this).onSync(\"datasetCleared\", this._onDatasetCleared, this);\r\n            onFocused = c(this, \"activate\", \"open\", \"_onFocused\");\r\n            onBlurred = c(this, \"deactivate\", \"_onBlurred\");\r\n            onEnterKeyed = c(this, \"isActive\", \"isOpen\", \"_onEnterKeyed\");\r\n            onTabKeyed = c(this, \"isActive\", \"isOpen\", \"_onTabKeyed\");\r\n            onEscKeyed = c(this, \"isActive\", \"_onEscKeyed\");\r\n            onUpKeyed = c(this, \"isActive\", \"open\", \"_onUpKeyed\");\r\n            onDownKeyed = c(this, \"isActive\", \"open\", \"_onDownKeyed\");\r\n            onLeftKeyed = c(this, \"isActive\", \"isOpen\", \"_onLeftKeyed\");\r\n            onRightKeyed = c(this, \"isActive\", \"isOpen\", \"_onRightKeyed\");\r\n            onQueryChanged = c(this, \"_openIfActive\", \"_onQueryChanged\");\r\n            onWhitespaceChanged = c(this, \"_openIfActive\", \"_onWhitespaceChanged\");\r\n            this.input.bind().onSync(\"focused\", onFocused, this).onSync(\"blurred\", onBlurred, this).onSync(\"enterKeyed\", onEnterKeyed, this).onSync(\"tabKeyed\", onTabKeyed, this).onSync(\"escKeyed\", onEscKeyed, this).onSync(\"upKeyed\", onUpKeyed, this).onSync(\"downKeyed\", onDownKeyed, this).onSync(\"leftKeyed\", onLeftKeyed, this).onSync(\"rightKeyed\", onRightKeyed, this).onSync(\"queryChanged\", onQueryChanged, this).onSync(\"whitespaceChanged\", onWhitespaceChanged, this).onSync(\"langDirChanged\", this._onLangDirChanged, this);\r\n        }\r\n        _.mixin(Typeahead.prototype, {\r\n            _hacks: function hacks() {\r\n                var $input, $menu;\r\n                $input = this.input.$input || $(\"<div>\");\r\n                $menu = this.menu.$node || $(\"<div>\");\r\n                $input.on(\"blur.tt\", function($e) {\r\n                    var active, isActive, hasActive;\r\n                    active = document.activeElement;\r\n                    isActive = $menu.is(active);\r\n                    hasActive = $menu.has(active).length > 0;\r\n                    if (_.isMsie() && (isActive || hasActive)) {\r\n                        $e.preventDefault();\r\n                        $e.stopImmediatePropagation();\r\n                        _.defer(function() {\r\n                            $input.focus();\r\n                        });\r\n                    }\r\n                });\r\n                $menu.on(\"mousedown.tt\", function($e) {\r\n                    $e.preventDefault();\r\n                });\r\n            },\r\n            _onSelectableClicked: function onSelectableClicked(type, $el) {\r\n                this.select($el);\r\n            },\r\n            _onDatasetCleared: function onDatasetCleared() {\r\n                this._updateHint();\r\n            },\r\n            _onDatasetRendered: function onDatasetRendered(type, dataset, suggestions, async) {\r\n                this._updateHint();\r\n                this.eventBus.trigger(\"render\", suggestions, async, dataset);\r\n            },\r\n            _onAsyncRequested: function onAsyncRequested(type, dataset, query) {\r\n                this.eventBus.trigger(\"asyncrequest\", query, dataset);\r\n            },\r\n            _onAsyncCanceled: function onAsyncCanceled(type, dataset, query) {\r\n                this.eventBus.trigger(\"asynccancel\", query, dataset);\r\n            },\r\n            _onAsyncReceived: function onAsyncReceived(type, dataset, query) {\r\n                this.eventBus.trigger(\"asyncreceive\", query, dataset);\r\n            },\r\n            _onFocused: function onFocused() {\r\n                this._minLengthMet() && this.menu.update(this.input.getQuery());\r\n            },\r\n            _onBlurred: function onBlurred() {\r\n                if (this.input.hasQueryChangedSinceLastFocus()) {\r\n                    this.eventBus.trigger(\"change\", this.input.getQuery());\r\n                }\r\n            },\r\n            _onEnterKeyed: function onEnterKeyed(type, $e) {\r\n                var $selectable;\r\n                if ($selectable = this.menu.getActiveSelectable()) {\r\n                    this.select($selectable) && $e.preventDefault();\r\n                }\r\n            },\r\n            _onTabKeyed: function onTabKeyed(type, $e) {\r\n                var $selectable;\r\n                if ($selectable = this.menu.getActiveSelectable()) {\r\n                    this.select($selectable) && $e.preventDefault();\r\n                } else if ($selectable = this.menu.getTopSelectable()) {\r\n                    this.autocomplete($selectable) && $e.preventDefault();\r\n                }\r\n            },\r\n            _onEscKeyed: function onEscKeyed() {\r\n                this.close();\r\n            },\r\n            _onUpKeyed: function onUpKeyed() {\r\n                this.moveCursor(-1);\r\n            },\r\n            _onDownKeyed: function onDownKeyed() {\r\n                this.moveCursor(+1);\r\n            },\r\n            _onLeftKeyed: function onLeftKeyed() {\r\n                if (this.dir === \"rtl\" && this.input.isCursorAtEnd()) {\r\n                    this.autocomplete(this.menu.getTopSelectable());\r\n                }\r\n            },\r\n            _onRightKeyed: function onRightKeyed() {\r\n                if (this.dir === \"ltr\" && this.input.isCursorAtEnd()) {\r\n                    this.autocomplete(this.menu.getTopSelectable());\r\n                }\r\n            },\r\n            _onQueryChanged: function onQueryChanged(e, query) {\r\n                this._minLengthMet(query) ? this.menu.update(query) : this.menu.empty();\r\n            },\r\n            _onWhitespaceChanged: function onWhitespaceChanged() {\r\n                this._updateHint();\r\n            },\r\n            _onLangDirChanged: function onLangDirChanged(e, dir) {\r\n                if (this.dir !== dir) {\r\n                    this.dir = dir;\r\n                    this.menu.setLanguageDirection(dir);\r\n                }\r\n            },\r\n            _openIfActive: function openIfActive() {\r\n                this.isActive() && this.open();\r\n            },\r\n            _minLengthMet: function minLengthMet(query) {\r\n                query = _.isString(query) ? query : this.input.getQuery() || \"\";\r\n                return query.length >= this.minLength;\r\n            },\r\n            _updateHint: function updateHint() {\r\n                var $selectable, data, val, query, escapedQuery, frontMatchRegEx, match;\r\n                $selectable = this.menu.getTopSelectable();\r\n                data = this.menu.getSelectableData($selectable);\r\n                val = this.input.getInputValue();\r\n                if (data && !_.isBlankString(val) && !this.input.hasOverflow()) {\r\n                    query = Input.normalizeQuery(val);\r\n                    escapedQuery = _.escapeRegExChars(query);\r\n                    frontMatchRegEx = new RegExp(\"^(?:\" + escapedQuery + \")(.+$)\", \"i\");\r\n                    match = frontMatchRegEx.exec(data.val);\r\n                    match && this.input.setHint(val + match[1]);\r\n                } else {\r\n                    this.input.clearHint();\r\n                }\r\n            },\r\n            isEnabled: function isEnabled() {\r\n                return this.enabled;\r\n            },\r\n            enable: function enable() {\r\n                this.enabled = true;\r\n            },\r\n            disable: function disable() {\r\n                this.enabled = false;\r\n            },\r\n            isActive: function isActive() {\r\n                return this.active;\r\n            },\r\n            activate: function activate() {\r\n                if (this.isActive()) {\r\n                    return true;\r\n                } else if (!this.isEnabled() || this.eventBus.before(\"active\")) {\r\n                    return false;\r\n                } else {\r\n                    this.active = true;\r\n                    this.eventBus.trigger(\"active\");\r\n                    return true;\r\n                }\r\n            },\r\n            deactivate: function deactivate() {\r\n                if (!this.isActive()) {\r\n                    return true;\r\n                } else if (this.eventBus.before(\"idle\")) {\r\n                    return false;\r\n                } else {\r\n                    this.active = false;\r\n                    this.close();\r\n                    this.eventBus.trigger(\"idle\");\r\n                    return true;\r\n                }\r\n            },\r\n            isOpen: function isOpen() {\r\n                return this.menu.isOpen();\r\n            },\r\n            open: function open() {\r\n                if (!this.isOpen() && !this.eventBus.before(\"open\")) {\r\n                    this.menu.open();\r\n                    this._updateHint();\r\n                    this.eventBus.trigger(\"open\");\r\n                }\r\n                return this.isOpen();\r\n            },\r\n            close: function close() {\r\n                if (this.isOpen() && !this.eventBus.before(\"close\")) {\r\n                    this.menu.close();\r\n                    this.input.clearHint();\r\n                    this.input.resetInputValue();\r\n                    this.eventBus.trigger(\"close\");\r\n                }\r\n                return !this.isOpen();\r\n            },\r\n            setVal: function setVal(val) {\r\n                this.input.setQuery(_.toStr(val));\r\n            },\r\n            getVal: function getVal() {\r\n                return this.input.getQuery();\r\n            },\r\n            select: function select($selectable) {\r\n                var data = this.menu.getSelectableData($selectable);\r\n                if (data && !this.eventBus.before(\"select\", data.obj)) {\r\n                    this.input.setQuery(data.val, true);\r\n                    this.eventBus.trigger(\"select\", data.obj);\r\n                    this.close();\r\n                    return true;\r\n                }\r\n                return false;\r\n            },\r\n            autocomplete: function autocomplete($selectable) {\r\n                var query, data, isValid;\r\n                query = this.input.getQuery();\r\n                data = this.menu.getSelectableData($selectable);\r\n                isValid = data && query !== data.val;\r\n                if (isValid && !this.eventBus.before(\"autocomplete\", data.obj)) {\r\n                    this.input.setQuery(data.val);\r\n                    this.eventBus.trigger(\"autocomplete\", data.obj);\r\n                    return true;\r\n                }\r\n                return false;\r\n            },\r\n            moveCursor: function moveCursor(delta) {\r\n                var query, $candidate, data, payload, cancelMove;\r\n                query = this.input.getQuery();\r\n                $candidate = this.menu.selectableRelativeToCursor(delta);\r\n                data = this.menu.getSelectableData($candidate);\r\n                payload = data ? data.obj : null;\r\n                cancelMove = this._minLengthMet() && this.menu.update(query);\r\n                if (!cancelMove && !this.eventBus.before(\"cursorchange\", payload)) {\r\n                    this.menu.setCursor($candidate);\r\n                    if (data) {\r\n                        this.input.setInputValue(data.val);\r\n                    } else {\r\n                        this.input.resetInputValue();\r\n                        this._updateHint();\r\n                    }\r\n                    this.eventBus.trigger(\"cursorchange\", payload);\r\n                    return true;\r\n                }\r\n                return false;\r\n            },\r\n            destroy: function destroy() {\r\n                this.input.destroy();\r\n                this.menu.destroy();\r\n            }\r\n        });\r\n        return Typeahead;\r\n        function c(ctx) {\r\n            var methods = [].slice.call(arguments, 1);\r\n            return function() {\r\n                var args = [].slice.call(arguments);\r\n                _.each(methods, function(method) {\r\n                    return ctx[method].apply(ctx, args);\r\n                });\r\n            };\r\n        }\r\n    }();\r\n    (function() {\r\n        \"use strict\";\r\n        var old, keys, methods;\r\n        old = $.fn.typeahead;\r\n        keys = {\r\n            www: \"tt-www\",\r\n            attrs: \"tt-attrs\",\r\n            typeahead: \"tt-typeahead\"\r\n        };\r\n        methods = {\r\n            initialize: function initialize(o, datasets) {\r\n                var www;\r\n                datasets = _.isArray(datasets) ? datasets : [].slice.call(arguments, 1);\r\n                o = o || {};\r\n                www = WWW(o.classNames);\r\n                return this.each(attach);\r\n                function attach() {\r\n                    var $input, $wrapper, $hint, $menu, defaultHint, defaultMenu, eventBus, input, menu, typeahead, MenuConstructor;\r\n                    _.each(datasets, function(d) {\r\n                        d.highlight = !!o.highlight;\r\n                    });\r\n                    $input = $(this);\r\n                    $wrapper = $(www.html.wrapper);\r\n                    $hint = $elOrNull(o.hint);\r\n                    $menu = $elOrNull(o.menu);\r\n                    defaultHint = o.hint !== false && !$hint;\r\n                    defaultMenu = o.menu !== false && !$menu;\r\n                    defaultHint && ($hint = buildHintFromInput($input, www));\r\n                    defaultMenu && ($menu = $(www.html.menu).css(www.css.menu));\r\n                    $hint && $hint.val(\"\");\r\n                    $input = prepInput($input, www);\r\n                    if (defaultHint || defaultMenu) {\r\n                        $wrapper.css(www.css.wrapper);\r\n                        $input.css(defaultHint ? www.css.input : www.css.inputWithNoHint);\r\n                        $input.wrap($wrapper).parent().prepend(defaultHint ? $hint : null).append(defaultMenu ? $menu : null);\r\n                    }\r\n                    MenuConstructor = defaultMenu ? DefaultMenu : Menu;\r\n                    eventBus = new EventBus({\r\n                        el: $input\r\n                    });\r\n                    input = new Input({\r\n                        hint: $hint,\r\n                        input: $input\r\n                    }, www);\r\n                    menu = new MenuConstructor({\r\n                        node: $menu,\r\n                        datasets: datasets\r\n                    }, www);\r\n                    typeahead = new Typeahead({\r\n                        input: input,\r\n                        menu: menu,\r\n                        eventBus: eventBus,\r\n                        minLength: o.minLength\r\n                    }, www);\r\n                    $input.data(keys.www, www);\r\n                    $input.data(keys.typeahead, typeahead);\r\n                }\r\n            },\r\n            isEnabled: function isEnabled() {\r\n                var enabled;\r\n                ttEach(this.first(), function(t) {\r\n                    enabled = t.isEnabled();\r\n                });\r\n                return enabled;\r\n            },\r\n            enable: function enable() {\r\n                ttEach(this, function(t) {\r\n                    t.enable();\r\n                });\r\n                return this;\r\n            },\r\n            disable: function disable() {\r\n                ttEach(this, function(t) {\r\n                    t.disable();\r\n                });\r\n                return this;\r\n            },\r\n            isActive: function isActive() {\r\n                var active;\r\n                ttEach(this.first(), function(t) {\r\n                    active = t.isActive();\r\n                });\r\n                return active;\r\n            },\r\n            activate: function activate() {\r\n                ttEach(this, function(t) {\r\n                    t.activate();\r\n                });\r\n                return this;\r\n            },\r\n            deactivate: function deactivate() {\r\n                ttEach(this, function(t) {\r\n                    t.deactivate();\r\n                });\r\n                return this;\r\n            },\r\n            isOpen: function isOpen() {\r\n                var open;\r\n                ttEach(this.first(), function(t) {\r\n                    open = t.isOpen();\r\n                });\r\n                return open;\r\n            },\r\n            open: function open() {\r\n                ttEach(this, function(t) {\r\n                    t.open();\r\n                });\r\n                return this;\r\n            },\r\n            close: function close() {\r\n                ttEach(this, function(t) {\r\n                    t.close();\r\n                });\r\n                return this;\r\n            },\r\n            select: function select(el) {\r\n                var success = false, $el = $(el);\r\n                ttEach(this.first(), function(t) {\r\n                    success = t.select($el);\r\n                });\r\n                return success;\r\n            },\r\n            autocomplete: function autocomplete(el) {\r\n                var success = false, $el = $(el);\r\n                ttEach(this.first(), function(t) {\r\n                    success = t.autocomplete($el);\r\n                });\r\n                return success;\r\n            },\r\n            moveCursor: function moveCursoe(delta) {\r\n                var success = false;\r\n                ttEach(this.first(), function(t) {\r\n                    success = t.moveCursor(delta);\r\n                });\r\n                return success;\r\n            },\r\n            val: function val(newVal) {\r\n                var query;\r\n                if (!arguments.length) {\r\n                    ttEach(this.first(), function(t) {\r\n                        query = t.getVal();\r\n                    });\r\n                    return query;\r\n                } else {\r\n                    ttEach(this, function(t) {\r\n                        t.setVal(newVal);\r\n                    });\r\n                    return this;\r\n                }\r\n            },\r\n            destroy: function destroy() {\r\n                ttEach(this, function(typeahead, $input) {\r\n                    revert($input);\r\n                    typeahead.destroy();\r\n                });\r\n                return this;\r\n            }\r\n        };\r\n        $.fn.typeahead = function(method) {\r\n            if (methods[method]) {\r\n                return methods[method].apply(this, [].slice.call(arguments, 1));\r\n            } else {\r\n                return methods.initialize.apply(this, arguments);\r\n            }\r\n        };\r\n        $.fn.typeahead.noConflict = function noConflict() {\r\n            $.fn.typeahead = old;\r\n            return this;\r\n        };\r\n        function ttEach($els, fn) {\r\n            $els.each(function() {\r\n                var $input = $(this), typeahead;\r\n                (typeahead = $input.data(keys.typeahead)) && fn(typeahead, $input);\r\n            });\r\n        }\r\n        function buildHintFromInput($input, www) {\r\n            return $input.clone().addClass(www.classes.hint).removeData().css(www.css.hint).css(getBackgroundStyles($input)).prop(\"readonly\", true).removeAttr(\"id name placeholder required\").attr({\r\n                autocomplete: \"off\",\r\n                spellcheck: \"false\",\r\n                tabindex: -1\r\n            });\r\n        }\r\n        function prepInput($input, www) {\r\n            $input.data(keys.attrs, {\r\n                dir: $input.attr(\"dir\"),\r\n                autocomplete: $input.attr(\"autocomplete\"),\r\n                spellcheck: $input.attr(\"spellcheck\"),\r\n                style: $input.attr(\"style\")\r\n            });\r\n            $input.addClass(www.classes.input).attr({\r\n                autocomplete: \"off\",\r\n                spellcheck: false\r\n            });\r\n            try {\r\n                !$input.attr(\"dir\") && $input.attr(\"dir\", \"auto\");\r\n            } catch (e) {}\r\n            return $input;\r\n        }\r\n        function getBackgroundStyles($el) {\r\n            return {\r\n                backgroundAttachment: $el.css(\"background-attachment\"),\r\n                backgroundClip: $el.css(\"background-clip\"),\r\n                backgroundColor: $el.css(\"background-color\"),\r\n                backgroundImage: $el.css(\"background-image\"),\r\n                backgroundOrigin: $el.css(\"background-origin\"),\r\n                backgroundPosition: $el.css(\"background-position\"),\r\n                backgroundRepeat: $el.css(\"background-repeat\"),\r\n                backgroundSize: $el.css(\"background-size\")\r\n            };\r\n        }\r\n        function revert($input) {\r\n            var www, $wrapper;\r\n            www = $input.data(keys.www);\r\n            $wrapper = $input.parent().filter(www.selectors.wrapper);\r\n            _.each($input.data(keys.attrs), function(val, key) {\r\n                _.isUndefined(val) ? $input.removeAttr(key) : $input.attr(key, val);\r\n            });\r\n            $input.removeData(keys.typeahead).removeData(keys.www).removeData(keys.attr).removeClass(www.classes.input);\r\n            if ($wrapper.length) {\r\n                $input.detach().insertAfter($wrapper);\r\n                $wrapper.remove();\r\n            }\r\n        }\r\n        function $elOrNull(obj) {\r\n            var isValid, $el;\r\n            isValid = _.isJQuery(obj) || _.isElement(obj);\r\n            $el = isValid ? $(obj).first() : [];\r\n            return $el.length ? $el : null;\r\n        }\r\n    })();\r\n});"]}