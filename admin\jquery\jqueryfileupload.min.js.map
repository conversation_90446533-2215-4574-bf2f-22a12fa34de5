{"version": 3, "sources": ["..\\..\\..\\jQuery-File-Upload\\js\\canvas-to-blob.js", "..\\..\\..\\jQuery-File-Upload\\js\\jquery.iframe-transport.js", "..\\..\\..\\jQuery-File-Upload\\js\\jquery.fileupload.js", "..\\..\\..\\jQuery-File-Upload\\js\\jquery.fileupload-process.js", "..\\..\\..\\jQuery-File-Upload\\js\\jquery.fileupload-image.js", "..\\..\\..\\jQuery-File-Upload\\js\\jquery.fileupload-audio.js", "..\\..\\..\\jQuery-File-Upload\\js\\jquery.fileupload-video.js", "..\\..\\..\\jQuery-File-Upload\\js\\jquery.fileupload-validate.js", "..\\..\\..\\jQuery-File-Upload\\js\\jquery.fileupload-ui.js"], "names": ["window", "CanvasPrototype", "HTMLCanvasElement", "prototype", "hasBlobConstructor", "Blob", "Boolean", "e", "hasArrayBufferViewSupport", "Uint8Array", "size", "BlobBuilder", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "dataURIPattern", "dataURLtoBlob", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataURI", "matches", "mediaType", "isBase64", "dataString", "byteString", "arrayBuffer", "intArray", "i", "bb", "match", "Error", "slice", "length", "decodeURIComponent", "charCodeAt", "type", "append", "getBlob", "toBlob", "mozGetAsFile", "callback", "quality", "self", "this", "setTimeout", "toDataURL", "msToBlob", "define", "amd", "module", "exports", "factory", "require", "j<PERSON><PERSON><PERSON>", "$", "counter", "jsonAPI", "jsonParse", "JSON", "ajaxTransport", "options", "async", "form", "iframe", "addParamChar", "initialIframeSrc", "send", "_", "completeCallback", "attr", "formAcceptCharset", "test", "url", "on", "fileInputClones", "paramNames", "isArray", "paramName", "off", "response", "contents", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "appendTo", "remove", "prop", "formData", "each", "index", "field", "name", "val", "value", "fileInput", "clone", "after", "removeAttr", "submit", "input", "replaceWith", "document", "body", "abort", "ajaxSetup", "converters", "iframe text", "text", "iframe json", "iframe html", "html", "iframe xml", "xmlDoc", "isXMLDoc", "parseXML", "XMLDocument", "xml", "iframe script", "globalEval", "getDragHandler", "isDragOver", "dataTransfer", "originalEvent", "inArray", "types", "_trigger", "Event", "delegatedEvent", "preventDefault", "dropEffect", "parts", "support", "RegExp", "navigator", "userAgent", "xhrFileUpload", "ProgressEvent", "FileReader", "xhrFormDataFileUpload", "FormData", "blobSlice", "webkitSlice", "mozSlice", "widget", "dropZone", "pasteZone", "replaceFileInput", "singleFileUploads", "limitMultiFileUploads", "limitMultiFileUploadSize", "limitMultiFileUploadSizeOverhead", "sequentialUploads", "limitConcurrentUploads", "forceIframeTransport", "redirect", "redirectParamName", "postMessage", "multipart", "maxChunkSize", "uploadedBytes", "recalculateProgress", "progressInterval", "bitrateInterval", "autoUpload", "uniqueFilenames", "messages", "i18n", "message", "context", "toString", "key", "replace", "serializeArray", "add", "data", "isDefaultPrevented", "fileupload", "process", "done", "processData", "contentType", "cache", "timeout", "_promisePipe", "fn", "j<PERSON>y", "split", "Number", "_specialOptions", "_blobSlice", "apply", "arguments", "_BitrateTimer", "timestamp", "Date", "now", "getTime", "loaded", "bitrate", "getBitrate", "interval", "timeDiff", "_isXHRUpload", "_getFormData", "push", "_getTotal", "files", "total", "file", "_initProgressObject", "obj", "progress", "_progress", "extend", "_initResponseObject", "_response", "Object", "hasOwnProperty", "call", "_onProgress", "lengthComputable", "_time", "Math", "floor", "chunkSize", "_bitrateTimer", "_initProgressListener", "that", "xhr", "ajaxSettings", "upload", "oe", "_deinitProgressListener", "_isInstanceOf", "_getUniqueFilename", "map", "String", "p1", "p2", "_initXHRData", "headers", "contentRange", "blob", "encodeURI", "uploadName", "fileName", "_initIframeSettings", "targetHost", "dataType", "location", "host", "_initDataSettings", "_chunkedUpload", "_getParamName", "_initFormSettings", "href", "toUpperCase", "_getAJAXSettings", "_getDeferredState", "deferred", "state", "isResolved", "isRejected", "_enhancePromise", "promise", "success", "error", "fail", "complete", "always", "_getXHRPromise", "resolveOrReject", "args", "dfd", "Deferred", "resolveWith", "rejectWith", "_addConvenienceMethods", "getPromise", "resolveFunc", "rejectFunc", "_processQueue", "errorThrown", "jqXHR", "_onSend", "processing", "_getUploadedBytes", "range", "getResponseHeader", "upperBytesPos", "parseInt", "testOnly", "fs", "ub", "mcs", "o", "currentLoaded", "ajax", "result", "textStatus", "_beforeSend", "_active", "_onDone", "_onFail", "_onAlways", "jqXHRorResult", "jqXHRorError", "aborted", "slot", "pipe", "_sending", "nextSlot", "_slots", "shift", "resolve", "_sequence", "_onAdd", "paramNameSet", "paramNameSlice", "fileSet", "filesLength", "limit", "limitSize", "overhead", "batchSize", "j", "originalFiles", "element", "newData", "_replaceFileInput", "inputClone", "restoreFocus", "is", "activeElement", "fileInputClone", "reset", "detach", "trigger", "cleanData", "el", "_handleFileTreeEntry", "entry", "path", "<PERSON><PERSON><PERSON><PERSON>", "entries", "<PERSON><PERSON><PERSON><PERSON>", "readEntries", "results", "concat", "_handleFileTreeEntries", "<PERSON><PERSON><PERSON><PERSON>", "isFile", "_file", "relativePath", "isDirectory", "createReader", "when", "Array", "_getDroppedFiles", "items", "webkitGetAsEntry", "getAsEntry", "item", "getAsFile", "makeArray", "_getSingleFileInputFiles", "fileSize", "_getFileInputFiles", "_onChange", "target", "_onPaste", "clipboardData", "_onDrop", "_onDragOver", "_onDragEnter", "_onDragLeave", "_initEventHandlers", "_on", "dragover", "drop", "dragenter", "dragleave", "paste", "change", "_destroyEventHandlers", "_off", "_destroy", "_setOption", "reinit", "_super", "_initSpecialOptions", "find", "_getRegExp", "str", "modifiers", "pop", "join", "_isRegExpOption", "_initDataAttributes", "attributes", "toLowerCase", "char<PERSON>t", "_create", "active", "disabled", "reject", "then", "originalAdd", "blueimp", "processQueue", "$this", "processActions", "_processFile", "originalData", "chain", "settings", "func", "action", "_transformProcessQueue", "prefix", "_processing", "opts", "_processingQueue", "loadImage", "unshift", "maxMetaDataSize", "disableImageHead", "disableMetaDataParsers", "disableExif", "disableExifOffsets", "includeExifTags", "excludeExifTags", "disableIptc", "disableIptcOffsets", "includeIptcTags", "excludeIptcTags", "fileTypes", "maxFileSize", "noRevoke", "max<PERSON><PERSON><PERSON>", "maxHeight", "min<PERSON><PERSON><PERSON>", "minHeight", "crop", "orientation", "forceResize", "thumbnail", "canvas", "loadImageFileTypes", "loadImageMaxFileSize", "imageMaxWidth", "imageMaxHeight", "imageOrientation", "imageCrop", "disableImageResize", "previewMaxWidth", "previewMaxHeight", "previewOrientation", "previewThumbnail", "previewCrop", "previewCanvas", "img", "src", "resizeImage", "thumbnailBlob", "newImg", "width", "height", "getContext", "preview", "exif", "get", "scale", "saveImage", "substr", "imageHead", "loadImageMetaData", "parseMetaData", "saveImageMetaData", "exifOffsets", "writeExifData", "replaceHead", "setImage", "deleteImageReferences", "loadAudioFileTypes", "_audioElement", "createElement", "loadAudio", "audio", "canPlayType", "createObjectURL", "cloneNode", "controls", "setAudio", "loadVideoFileTypes", "_videoElement", "loadVideo", "video", "setVideo", "acceptFileTypes", "minFileSize", "maxNumberOfFiles", "getNumberOfFiles", "noop", "validate", "tmpl", "showElementClass", "uploadTemplateId", "downloadTemplateId", "filesContainer", "prependFiles", "unknownError", "children", "not", "getFilesFromResponse", "_renderUpload", "addClass", "_forceReflow", "_transition", "_formatFileSize", "removeClass", "_renderPreviews", "transition", "first", "css", "template", "_addFinishedDeferreds", "node", "_renderDownload", "replaceAll", "progressall", "globalProgressNode", "extendedProgressNode", "_renderExtendedProgress", "start", "_resetFinishedDeferreds", "stop", "_getFinishedDeferreds", "processstart", "processstop", "destroy", "removeNode", "_finishedUploads", "_enableDragToDesktop", "link", "setData", "ignore", "bytes", "toFixed", "_formatBitrate", "bits", "_formatTime", "seconds", "date", "days", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "_formatPercentage", "floatValue", "_renderTemplate", "formatFileSize", "templatesContainer", "elm", "empty", "uploadTemplate", "downloadTemplate", "end", "_edit<PERSON><PERSON><PERSON>", "edit", "button", "currentTarget", "closest", "_start<PERSON><PERSON>ler", "_cancel<PERSON><PERSON><PERSON>", "_delete<PERSON><PERSON>ler", "offsetWidth", "hasClass", "transitionEndHandler", "toggleClass", "_initButtonBarEventHandlers", "fileUploadButtonBar", "filesList", "click", "_destroyButtonBarEventHandlers", "click .edit", "click .start", "click .cancel", "click .delete", "_enableFileInputButton", "parent", "_disableFileInputButton", "_initTemplates", "_initFilesContainer", "enable", "wasDisabled", "disable"], "mappings": "CAgBC,SAAWA,GACV,aAEA,IAAIC,EACFD,EAAOE,mBAAqBF,EAAOE,kBAAkBC,UACnDC,EACFJ,EAAOK,MACP,WACE,IACE,OAAOC,QAAQ,IAAID,MACnB,MAAOE,GACP,OAAO,GAJX,GAOEC,EACFJ,GACAJ,EAAOS,YACP,WACE,IACE,OAAgD,MAAzC,IAAIJ,KAAK,CAAC,IAAII,WAAW,OAAOC,KACvC,MAAOH,GACP,OAAO,GAJX,GAOEI,EACFX,EAAOW,aACPX,EAAOY,mBACPZ,EAAOa,gBACPb,EAAOc,cACLC,EAAiB,0CACjBC,GACDZ,GAAsBO,IACvBX,EAAOiB,MACPjB,EAAOkB,aACPlB,EAAOS,YACP,SAAUU,GACR,IAAIC,EACFC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAGF,KADAR,EAAUD,EAAQU,MAAMd,IAEtB,MAAM,IAAIe,MAAM,oBAkBlB,IAfAT,EAAYD,EAAQ,GAChBA,EAAQ,GACR,cAAgBA,EAAQ,IAAM,qBAClCE,IAAaF,EAAQ,GACrBG,EAAaJ,EAAQY,MAAMX,EAAQ,GAAGY,QAGpCR,EAFEF,EAEWL,KAAKM,GAGLU,mBAAmBV,GAGlCE,EAAc,IAAIP,YAAYM,EAAWQ,QACzCN,EAAW,IAAIjB,WAAWgB,GACrBE,EAAI,EAAGA,EAAIH,EAAWQ,OAAQL,GAAK,EACtCD,EAASC,GAAKH,EAAWU,WAAWP,GAGtC,OAAIvB,EACK,IAAIC,KAAK,CAACG,EAA4BkB,EAAWD,GAAc,CACpEU,KAAMd,MAGVO,EAAK,IAAIjB,GACNyB,OAAOX,GACHG,EAAGS,QAAQhB,KAElBrB,EAAOE,oBAAsBD,EAAgBqC,SAC3CrC,EAAgBsC,aAClBtC,EAAgBqC,OAAS,SAAUE,EAAUL,EAAMM,GACjD,IAAIC,EAAOC,KACXC,YAAW,WACLH,GAAWxC,EAAgB4C,WAAa7B,EAC1CwB,EAASxB,EAAc0B,EAAKG,UAAUV,EAAMM,KAE5CD,EAASE,EAAKH,aAAa,OAAQJ,QAIhClC,EAAgB4C,WAAa7B,IAClCf,EAAgB6C,SAClB7C,EAAgBqC,OAAS,SAAUE,EAAUL,EAAMM,GACjD,IAAIC,EAAOC,KACXC,YAAW,YAELT,GAAiB,cAATA,GAAyBM,IACnCxC,EAAgB4C,WAChB7B,EAEAwB,EAASxB,EAAc0B,EAAKG,UAAUV,EAAMM,KAE5CD,EAASE,EAAKI,SAASX,QAK7BlC,EAAgBqC,OAAS,SAAUE,EAAUL,EAAMM,GACjD,IAAIC,EAAOC,KACXC,YAAW,WACTJ,EAASxB,EAAc0B,EAAKG,UAAUV,EAAMM,WAMhC,mBAAXM,QAAyBA,OAAOC,IACzCD,QAAO,WACL,OAAO/B,KAEkB,iBAAXiC,QAAuBA,OAAOC,QAC9CD,OAAOC,QAAUlC,EAEjBhB,EAAOgB,cAAgBA,EA5H1B,CA8HEhB,QCjIH,SAAWmD,GACT,aACsB,mBAAXJ,QAAyBA,OAAOC,IAEzCD,OAAO,CAAC,UAAWI,GACS,iBAAZD,QAEhBC,EAAQC,QAAQ,WAGhBD,EAAQnD,OAAOqD,QAVnB,EAYG,SAAUC,GACX,aAGA,IAAIC,EAAU,EACZC,EAAUF,EACVG,EAAY,YAEV,SAAUzD,QAAU,UAAW0D,OACjCF,EAAUE,KACVD,EAAY,SAadH,EAAEK,cAAc,UAAU,SAAUC,GAClC,GAAIA,EAAQC,MAAO,CAIjB,IACEC,EACAC,EACAC,EAHEC,EAAmBL,EAAQK,kBAAoB,oBAInD,MAAO,CACLC,KAAM,SAAUC,EAAGC,IACjBN,EAAOR,EAAE,wCACJe,KAAK,iBAAkBT,EAAQU,mBACpCN,EAAe,KAAKO,KAAKX,EAAQY,KAAO,IAAM,IAEzB,WAAjBZ,EAAQzB,MACVyB,EAAQY,IAAMZ,EAAQY,IAAMR,EAAe,iBAC3CJ,EAAQzB,KAAO,QACW,QAAjByB,EAAQzB,MACjByB,EAAQY,IAAMZ,EAAQY,IAAMR,EAAe,cAC3CJ,EAAQzB,KAAO,QACW,UAAjByB,EAAQzB,OACjByB,EAAQY,IAAMZ,EAAQY,IAAMR,EAAe,gBAC3CJ,EAAQzB,KAAO,QAMjB4B,EAAST,EACP,gBACEW,EACA,6BAJJV,GAAW,GAMP,eACFkB,GAAG,QAAQ,WACX,IAAIC,EACFC,EAAarB,EAAEsB,QAAQhB,EAAQiB,WAC3BjB,EAAQiB,UACR,CAACjB,EAAQiB,WACfd,EAAOe,IAAI,QAAQL,GAAG,QAAQ,WAC5B,IAAIM,EAGJ,IAKE,KAJAA,EAAWhB,EAAOiB,YAIJhD,SAAW+C,EAAS,GAAGE,WACnC,MAAM,IAAInD,MAEZ,MAAOvB,GACPwE,OAAWG,EAIbd,EAAiB,IAAK,UAAW,CAAEL,OAAQgB,IAG3CzB,EAAE,gBAAkBW,EAAmB,eAAekB,SACpDrB,GAEF9D,OAAO4C,YAAW,WAIhBkB,EAAKsB,WACJ,MAELtB,EACGuB,KAAK,SAAUtB,EAAOsB,KAAK,SAC3BA,KAAK,SAAUzB,EAAQY,KACvBa,KAAK,SAAUzB,EAAQzB,MACtByB,EAAQ0B,UACVhC,EAAEiC,KAAK3B,EAAQ0B,UAAU,SAAUE,EAAOC,GACxCnC,EAAE,0BACC+B,KAAK,OAAQI,EAAMC,MACnBC,IAAIF,EAAMG,OACVT,SAASrB,MAIdF,EAAQiC,WACRjC,EAAQiC,UAAU7D,QACD,SAAjB4B,EAAQzB,OAERuC,EAAkBd,EAAQiC,UAAUC,QAEpClC,EAAQiC,UAAUE,OAAM,SAAUP,GAChC,OAAOd,EAAgBc,MAErB5B,EAAQiB,WACVjB,EAAQiC,UAAUN,MAAK,SAAUC,GAC/BlC,EAAEX,MAAM0C,KAAK,OAAQV,EAAWa,IAAU5B,EAAQiB,cAKtDf,EACG1B,OAAOwB,EAAQiC,WACfR,KAAK,UAAW,uBAEhBA,KAAK,WAAY,uBAEpBzB,EAAQiC,UAAUG,WAAW,SAE/BhG,OAAO4C,YAAW,WAKhBkB,EAAKmC,SAGDvB,GAAmBA,EAAgB1C,QACrC4B,EAAQiC,UAAUN,MAAK,SAAUC,EAAOU,GACtC,IAAIJ,EAAQxC,EAAEoB,EAAgBc,IAE9BlC,EAAE4C,GACCb,KAAK,OAAQS,EAAMT,KAAK,SACxBhB,KAAK,OAAQyB,EAAMzB,KAAK,SAC3ByB,EAAMK,YAAYD,QAGrB,MAELpC,EAAK1B,OAAO2B,GAAQoB,SAASiB,SAASC,OAExCC,MAAO,WACDvC,GAGFA,EAAOe,IAAI,QAAQO,KAAK,MAAOpB,GAE7BH,GACFA,EAAKsB,eAiBf9B,EAAEiD,UAAU,CACVC,WAAY,CACVC,cAAe,SAAU1C,GACvB,OAAOA,GAAUT,EAAES,EAAO,GAAGsC,MAAMK,QAErCC,cAAe,SAAU5C,GACvB,OAAOA,GAAUP,EAAQC,GAAWH,EAAES,EAAO,GAAGsC,MAAMK,SAExDE,cAAe,SAAU7C,GACvB,OAAOA,GAAUT,EAAES,EAAO,GAAGsC,MAAMQ,QAErCC,aAAc,SAAU/C,GACtB,IAAIgD,EAAShD,GAAUA,EAAO,GAC9B,OAAOgD,GAAUzD,EAAE0D,SAASD,GACxBA,EACAzD,EAAE2D,SACCF,EAAOG,aAAeH,EAAOG,YAAYC,KACxC7D,EAAEyD,EAAOV,MAAMQ,SAGzBO,gBAAiB,SAAUrD,GACzB,OAAOA,GAAUT,EAAE+D,WAAW/D,EAAES,EAAO,GAAGsC,MAAMK,eChNxD,SAAWvD,GACT,aACsB,mBAAXJ,QAAyBA,OAAOC,IAEzCD,OAAO,CAAC,SAAU,uBAAwBI,GACd,iBAAZD,QAEhBC,EAAQC,QAAQ,UAAWA,QAAQ,8BAGnCD,EAAQnD,OAAOqD,QAVnB,EAYG,SAAUC,GACX,aAqCA,SAASgE,EAAenF,GACtB,IAAIoF,EAAsB,aAATpF,EACjB,OAAO,SAAU5B,GACfA,EAAEiH,aAAejH,EAAEkH,eAAiBlH,EAAEkH,cAAcD,aACpD,IAAIA,EAAejH,EAAEiH,aAEnBA,IAC4C,IAA5ClE,EAAEoE,QAAQ,QAASF,EAAaG,SAC8B,IAA9DhF,KAAKiF,SAASzF,EAAMmB,EAAEuE,MAAM1F,EAAM,CAAE2F,eAAgBvH,OAEpDA,EAAEwH,iBACER,IACFC,EAAaQ,WAAa,UAwOlB,IACRC,EAtRR3E,EAAE4E,QAAQrC,YACR,IAAIsC,OAEF,uJAIA5D,KAAKvE,OAAOoI,UAAUC,YAExB/E,EAAE,wBAAwB+B,KAAK,aAQjC/B,EAAE4E,QAAQI,iBAAmBtI,OAAOuI,gBAAiBvI,OAAOwI,YAC5DlF,EAAE4E,QAAQO,wBAA0BzI,OAAO0I,SAG3CpF,EAAE4E,QAAQS,UACR3I,OAAOK,OACNA,KAAKF,UAAU4B,OACd1B,KAAKF,UAAUyI,aACfvI,KAAKF,UAAU0I,UAkCnBvF,EAAEwF,OAAO,qBAAsB,CAC7BlF,QAAS,CAGPmF,SAAUzF,EAAE8C,UAGZ4C,eAAW9D,EAKXW,eAAWX,EAKX+D,kBAAkB,EAKlBpE,eAAWK,EAIXgE,mBAAmB,EAGnBC,2BAAuBjE,EAIvBkE,8BAA0BlE,EAI1BmE,iCAAkC,IAGlCC,mBAAmB,EAGnBC,4BAAwBrE,EAExBsE,sBAAsB,EAGtBC,cAAUvE,EAGVwE,uBAAmBxE,EAGnByE,iBAAazE,EAIb0E,WAAW,EAKXC,kBAAc3E,EAMd4E,mBAAe5E,EAIf6E,qBAAqB,EAErBC,iBAAkB,IAElBC,gBAAiB,IAEjBC,YAAY,EASZC,qBAAiBjF,EAGjBkF,SAAU,CACRN,cAAe,mCAKjBO,KAAM,SAAUC,EAASC,GASvB,OAPAD,EAAU3H,KAAKyH,SAASE,IAAYA,EAAQE,WACxCD,GACFjH,EAAEiC,KAAKgF,GAAS,SAAUE,EAAK7E,GAE7B0E,EAAUA,EAAQI,QAAQ,IAAMD,EAAM,IAAK7E,MAGxC0E,GAQThF,SAAU,SAAUxB,GAClB,OAAOA,EAAK6G,kBAmBdC,IAAK,SAAUrK,EAAGsK,GAChB,GAAItK,EAAEuK,qBACJ,OAAO,GAGPD,EAAKX,aACgB,IAApBW,EAAKX,YACJ5G,EAAEX,MAAMoI,WAAW,SAAU,gBAE/BF,EAAKG,UAAUC,MAAK,WAClBJ,EAAK5E,aA+DXiF,aAAa,EACbC,aAAa,EACbC,OAAO,EACPC,QAAS,GAQXC,cACMrD,EAAQ3E,EAAEiI,GAAGC,OAAOC,MAAM,KACvBC,OAAOzD,EAAM,IAAM,GAAKyD,OAAOzD,EAAM,IAAM,EAAI,OAAS,QAKjE0D,gBAAiB,CACf,YACA,WACA,YACA,YACA,wBAGFC,WACEtI,EAAE4E,QAAQS,WACV,WACE,IAAI5G,EAAQY,KAAKZ,OAASY,KAAKiG,aAAejG,KAAKkG,SACnD,OAAO9G,EAAM8J,MAAMlJ,KAAMmJ,YAG7BC,cAAe,WACbpJ,KAAKqJ,UAAYC,KAAKC,IAAMD,KAAKC,OAAQ,IAAID,MAAOE,UACpDxJ,KAAKyJ,OAAS,EACdzJ,KAAK0J,QAAU,EACf1J,KAAK2J,WAAa,SAAUJ,EAAKE,EAAQG,GACvC,IAAIC,EAAWN,EAAMvJ,KAAKqJ,UAM1B,QALKrJ,KAAK0J,UAAYE,GAAYC,EAAWD,KAC3C5J,KAAK0J,SAAWD,EAASzJ,KAAKyJ,SAAW,IAAOI,GAAY,EAC5D7J,KAAKyJ,OAASA,EACdzJ,KAAKqJ,UAAYE,GAEZvJ,KAAK0J,UAIhBI,aAAc,SAAU7I,GACtB,OACGA,EAAQ4F,wBACN5F,EAAQgG,WAAatG,EAAE4E,QAAQI,eAChChF,EAAE4E,QAAQO,wBAIhBiE,aAAc,SAAU9I,GACtB,IAAI0B,EACJ,MAAiC,aAA7BhC,EAAEnB,KAAKyB,EAAQ0B,UACV1B,EAAQ0B,SAAS1B,EAAQE,MAE9BR,EAAEsB,QAAQhB,EAAQ0B,UACb1B,EAAQ0B,SAEgB,WAA7BhC,EAAEnB,KAAKyB,EAAQ0B,WACjBA,EAAW,GACXhC,EAAEiC,KAAK3B,EAAQ0B,UAAU,SAAUI,EAAME,GACvCN,EAASqH,KAAK,CAAEjH,KAAMA,EAAME,MAAOA,OAE9BN,GAEF,IAGTsH,UAAW,SAAUC,GACnB,IAAIC,EAAQ,EAIZ,OAHAxJ,EAAEiC,KAAKsH,GAAO,SAAUrH,EAAOuH,GAC7BD,GAASC,EAAKrM,MAAQ,KAEjBoM,GAGTE,oBAAqB,SAAUC,GAC7B,IAAIC,EAAW,CACbd,OAAQ,EACRU,MAAO,EACPT,QAAS,GAEPY,EAAIE,UACN7J,EAAE8J,OAAOH,EAAIE,UAAWD,GAExBD,EAAIE,UAAYD,GAIpBG,oBAAqB,SAAUJ,GAC7B,IAAI5H,EACJ,GAAI4H,EAAIK,UACN,IAAKjI,KAAQ4H,EAAIK,UACXC,OAAOpN,UAAUqN,eAAeC,KAAKR,EAAIK,UAAWjI,WAC/C4H,EAAIK,UAAUjI,QAIzB4H,EAAIK,UAAY,IAIpBI,YAAa,SAAUnN,EAAGsK,GACxB,GAAItK,EAAEoN,iBAAkB,CACtB,IACEvB,EADEF,EAAMD,KAAKC,IAAMD,KAAKC,OAAQ,IAAID,MAAOE,UAE7C,GACEtB,EAAK+C,OACL/C,EAAKb,kBACLkC,EAAMrB,EAAK+C,MAAQ/C,EAAKb,kBACxBzJ,EAAE6L,SAAW7L,EAAEuM,MAEf,OAEFjC,EAAK+C,MAAQ1B,EACbE,EACEyB,KAAKC,MACFvN,EAAE6L,OAAS7L,EAAEuM,OAAUjC,EAAKkD,WAAalD,EAAKsC,UAAUL,SACtDjC,EAAKf,eAAiB,GAG7BnH,KAAKwK,UAAUf,QAAUA,EAASvB,EAAKsC,UAAUf,OACjDzJ,KAAKwK,UAAUd,QAAU1J,KAAKqL,cAAc1B,WAC1CJ,EACAvJ,KAAKwK,UAAUf,OACfvB,EAAKZ,iBAEPY,EAAKsC,UAAUf,OAASvB,EAAKuB,OAASA,EACtCvB,EAAKsC,UAAUd,QAAUxB,EAAKwB,QAAUxB,EAAKmD,cAAc1B,WACzDJ,EACAE,EACAvB,EAAKZ,iBAKPtH,KAAKiF,SACH,WACAtE,EAAEuE,MAAM,WAAY,CAAEC,eAAgBvH,IACtCsK,GAIFlI,KAAKiF,SACH,cACAtE,EAAEuE,MAAM,cAAe,CAAEC,eAAgBvH,IACzCoC,KAAKwK,aAKXc,sBAAuB,SAAUrK,GAC/B,IAAIsK,EAAOvL,KACTwL,EAAMvK,EAAQuK,IAAMvK,EAAQuK,MAAQ7K,EAAE8K,aAAaD,MAGjDA,EAAIE,SACN/K,EAAE6K,EAAIE,QAAQ5J,GAAG,YAAY,SAAUlE,GACrC,IAAI+N,EAAK/N,EAAEkH,cAEXlH,EAAEoN,iBAAmBW,EAAGX,iBACxBpN,EAAE6L,OAASkC,EAAGlC,OACd7L,EAAEuM,MAAQwB,EAAGxB,MACboB,EAAKR,YAAYnN,EAAGqD,MAEtBA,EAAQuK,IAAM,WACZ,OAAOA,KAKbI,wBAAyB,SAAU3K,GACjC,IAAIuK,EAAMvK,EAAQuK,IAAMvK,EAAQuK,MAAQ7K,EAAE8K,aAAaD,MACnDA,EAAIE,QACN/K,EAAE6K,EAAIE,QAAQvJ,IAAI,aAItB0J,cAAe,SAAUrM,EAAM8K,GAE7B,OAAOM,OAAOpN,UAAUqK,SAASiD,KAAKR,KAAS,WAAa9K,EAAO,KAGrEsM,mBAAoB,SAAU/I,EAAMgJ,GAGlC,OAAIA,EADJhJ,EAAOiJ,OAAOjJ,KAGZA,EAAOA,EAAKgF,QAAQ,gCAAgC,SAClDvG,EACAyK,EACAC,GAIA,MAAO,MAFKD,EAAKlD,OAAOkD,GAAM,EAAI,GAEZ,KADZC,GAAM,OAGXlM,KAAK8L,mBAAmB/I,EAAMgJ,KAEvCA,EAAIhJ,IAAQ,EACLA,IAGToJ,aAAc,SAAUlL,GACtB,IACE0B,EADE4I,EAAOvL,KAEToK,EAAOnJ,EAAQiJ,MAAM,GAErBjD,EAAYhG,EAAQgG,YAActG,EAAE4E,QAAQI,cAC5CzD,EACgC,UAA9BvB,EAAEnB,KAAKyB,EAAQiB,WACXjB,EAAQiB,UAAU,GAClBjB,EAAQiB,UAChBjB,EAAQmL,QAAUzL,EAAE8J,OAAO,GAAIxJ,EAAQmL,SACnCnL,EAAQoL,eACVpL,EAAQmL,QAAQ,iBAAmBnL,EAAQoL,cAExCpF,IAAahG,EAAQqL,MAAStM,KAAK6L,cAAc,OAAQzB,KAC5DnJ,EAAQmL,QAAQ,uBACd,yBACAG,UAAUnC,EAAKoC,YAAcpC,EAAKrH,MAClC,KAECkE,EAGMtG,EAAE4E,QAAQO,wBACf7E,EAAQ+F,aAKVrE,EAAW3C,KAAK+J,aAAa9I,GACzBA,EAAQqL,KACV3J,EAASqH,KAAK,CACZjH,KAAMb,EACNe,MAAOhC,EAAQqL,OAGjB3L,EAAEiC,KAAK3B,EAAQiJ,OAAO,SAAUrH,EAAOuH,GACrCzH,EAASqH,KAAK,CACZjH,KACiC,UAA9BpC,EAAEnB,KAAKyB,EAAQiB,YACdjB,EAAQiB,UAAUW,IACpBX,EACFe,MAAOmH,SAKTmB,EAAKM,cAAc,WAAY5K,EAAQ0B,UACzCA,EAAW1B,EAAQ0B,UAEnBA,EAAW,IAAIoD,SACfpF,EAAEiC,KAAK5C,KAAK+J,aAAa9I,IAAU,SAAU4B,EAAOC,GAClDH,EAASlD,OAAOqD,EAAMC,KAAMD,EAAMG,WAGlChC,EAAQqL,KACV3J,EAASlD,OACPyC,EACAjB,EAAQqL,KACRlC,EAAKoC,YAAcpC,EAAKrH,MAG1BpC,EAAEiC,KAAK3B,EAAQiJ,OAAO,SAAUrH,EAAOuH,GAGrC,GACEmB,EAAKM,cAAc,OAAQzB,IAC3BmB,EAAKM,cAAc,OAAQzB,GAC3B,CACA,IAAIqC,EAAWrC,EAAKoC,YAAcpC,EAAKrH,KACnC9B,EAAQuG,kBACViF,EAAWlB,EAAKO,mBACdW,EACAxL,EAAQuG,kBAGZ7E,EAASlD,OACwB,UAA9BkB,EAAEnB,KAAKyB,EAAQiB,YACdjB,EAAQiB,UAAUW,IAClBX,EACFkI,EACAqC,QAMVxL,EAAQiH,KAAOvF,IAlEf1B,EAAQuH,YAAc4B,EAAK5K,MAAQ,2BACnCyB,EAAQiH,KAAOjH,EAAQqL,MAAQlC,GAoEjCnJ,EAAQqL,KAAO,MAGjBI,oBAAqB,SAAUzL,GAC7B,IAAI0L,EAAahM,EAAE,WAAW+B,KAAK,OAAQzB,EAAQY,KAAKa,KAAK,QAE7DzB,EAAQ2L,SAAW,WAAa3L,EAAQ2L,UAAY,IAEpD3L,EAAQ0B,SAAW3C,KAAK+J,aAAa9I,GAEjCA,EAAQ6F,UAAY6F,GAAcA,IAAeE,SAASC,MAC5D7L,EAAQ0B,SAASqH,KAAK,CACpBjH,KAAM9B,EAAQ8F,mBAAqB,WACnC9D,MAAOhC,EAAQ6F,YAKrBiG,kBAAmB,SAAU9L,GACvBjB,KAAK8J,aAAa7I,IACfjB,KAAKgN,eAAe/L,GAAS,KAC3BA,EAAQiH,MACXlI,KAAKmM,aAAalL,GAEpBjB,KAAKsL,sBAAsBrK,IAEzBA,EAAQ+F,cAGV/F,EAAQ2L,SAAW,gBAAkB3L,EAAQ2L,UAAY,MAG3D5M,KAAK0M,oBAAoBzL,IAI7BgM,cAAe,SAAUhM,GACvB,IAAIiC,EAAYvC,EAAEM,EAAQiC,WACxBhB,EAAYjB,EAAQiB,UAkBtB,OAjBKA,EAcOvB,EAAEsB,QAAQC,KACpBA,EAAY,CAACA,KAdbA,EAAY,GACZgB,EAAUN,MAAK,WAIb,IAHA,IAAIW,EAAQ5C,EAAEX,MACZ+C,EAAOQ,EAAMb,KAAK,SAAW,UAC7B1D,GAAKuE,EAAMb,KAAK,UAAY,CAAC,IAAIrD,OAC5BL,GACLkD,EAAU8H,KAAKjH,GACf/D,GAAK,KAGJkD,EAAU7C,SACb6C,EAAY,CAACgB,EAAUR,KAAK,SAAW,aAKpCR,GAGTgL,kBAAmB,SAAUjM,GAGtBA,EAAQE,MAASF,EAAQE,KAAK9B,SACjC4B,EAAQE,KAAOR,EAAEM,EAAQiC,UAAUR,KAAK,SAGnCzB,EAAQE,KAAK9B,SAChB4B,EAAQE,KAAOR,EAAEX,KAAKiB,QAAQiC,UAAUR,KAAK,WAGjDzB,EAAQiB,UAAYlC,KAAKiN,cAAchM,GAClCA,EAAQY,MACXZ,EAAQY,IAAMZ,EAAQE,KAAKuB,KAAK,WAAamK,SAASM,MAGxDlM,EAAQzB,MACNyB,EAAQzB,MACiC,WAAxCmB,EAAEnB,KAAKyB,EAAQE,KAAKuB,KAAK,YACxBzB,EAAQE,KAAKuB,KAAK,WACpB,IACA0K,cAEiB,SAAjBnM,EAAQzB,MACS,QAAjByB,EAAQzB,MACS,UAAjByB,EAAQzB,OAERyB,EAAQzB,KAAO,QAEZyB,EAAQU,oBACXV,EAAQU,kBAAoBV,EAAQE,KAAKO,KAAK,oBAIlD2L,iBAAkB,SAAUnF,GAC1B,IAAIjH,EAAUN,EAAE8J,OAAO,GAAIzK,KAAKiB,QAASiH,GAGzC,OAFAlI,KAAKkN,kBAAkBjM,GACvBjB,KAAK+M,kBAAkB9L,GAChBA,GAKTqM,kBAAmB,SAAUC,GAC3B,OAAIA,EAASC,MACJD,EAASC,QAEdD,EAASE,aACJ,WAELF,EAASG,aACJ,WAEF,WAKTC,gBAAiB,SAAUC,GAIzB,OAHAA,EAAQC,QAAUD,EAAQtF,KAC1BsF,EAAQE,MAAQF,EAAQG,KACxBH,EAAQI,SAAWJ,EAAQK,OACpBL,GAKTM,eAAgB,SAAUC,EAAiBvG,EAASwG,GAClD,IAAIC,EAAM1N,EAAE2N,WACVV,EAAUS,EAAIT,UAShB,OAPAhG,EAAUA,GAAW5H,KAAKiB,QAAQ2G,SAAWgG,GACrB,IAApBO,EACFE,EAAIE,YAAY3G,EAASwG,IACI,IAApBD,GACTE,EAAIG,WAAW5G,EAASwG,GAE1BR,EAAQjK,MAAQ0K,EAAIT,QACb5N,KAAK2N,gBAAgBC,IAI9Ba,uBAAwB,SAAU7Q,EAAGsK,GACnC,IAAIqD,EAAOvL,KACT0O,EAAa,SAAUN,GACrB,OAAOzN,EAAE2N,WAAWC,YAAYhD,EAAM6C,GAAMR,WAEhD1F,EAAKG,QAAU,SAAUsG,EAAaC,GAYpC,OAXID,GAAeC,KACjB1G,EAAK2G,cAAgB7O,KAAK6O,eAAiB7O,KAAK6O,eAC9CH,EAAW,CAAC1O,QACXuL,EAAK5C,eAAc,WAClB,OAAIT,EAAK4G,YACAnO,EAAE2N,WAAWE,WAAWjD,EAAM,CAACrD,IAAO0F,UAExCc,EAAWvF,cAEnBoC,EAAK5C,cAAcgG,EAAaC,IAE9B5O,KAAK6O,eAAiBH,EAAW,CAAC1O,QAE3CkI,EAAK5E,OAAS,WASZ,MARqB,YAAjBtD,KAAKwN,UACPtF,EAAK6G,MAAQ/O,KAAK+O,OAKV,IAJNxD,EAAKtG,SACH,SACAtE,EAAEuE,MAAM,SAAU,CAAEC,eAAgBvH,IACpCoC,OACauL,EAAKyD,QAAQpR,EAAGoC,OAE5BA,KAAK+O,OAASxD,EAAK2C,kBAE5BhG,EAAKvE,MAAQ,WACX,OAAI3D,KAAK+O,MACA/O,KAAK+O,MAAMpL,SAEpB3D,KAAK8O,YAAc,QACnBvD,EAAKtG,SAAS,OAAQ,KAAMjF,MACrBuL,EAAK2C,gBAAe,KAE7BhG,EAAKsF,MAAQ,WACX,OAAIxN,KAAK+O,MACAxD,EAAK+B,kBAAkBtN,KAAK+O,OAEjC/O,KAAK6O,cACAtD,EAAK+B,kBAAkBtN,KAAK6O,oBADrC,GAIF3G,EAAK+G,WAAa,WAChB,OACGjP,KAAK+O,OACN/O,KAAK6O,eAC0C,YAA/CtD,EAAK+B,kBAAkBtN,KAAK6O,gBAGhC3G,EAAKqC,SAAW,WACd,OAAOvK,KAAKwK,WAEdtC,EAAK9F,SAAW,WACd,OAAOpC,KAAK2K,YAMhBuE,kBAAmB,SAAUH,GAC3B,IAAII,EAAQJ,EAAMK,kBAAkB,SAClC9J,EAAQ6J,GAASA,EAAMrG,MAAM,KAC7BuG,EAAgB/J,GAASA,EAAMjG,OAAS,GAAKiQ,SAAShK,EAAM,GAAI,IAClE,OAAO+J,GAAiBA,EAAgB,GAQ1CrC,eAAgB,SAAU/L,EAASsO,GACjCtO,EAAQkG,cAAgBlG,EAAQkG,eAAiB,EACjD,IAQE4H,EACArD,EATEH,EAAOvL,KACToK,EAAOnJ,EAAQiJ,MAAM,GACrBsF,EAAKpF,EAAKrM,KACV0R,EAAKxO,EAAQkG,cACbuI,EAAMzO,EAAQiG,cAAgBsI,EAC9BpQ,EAAQY,KAAKiJ,WACboF,EAAM1N,EAAE2N,WACRV,EAAUS,EAAIT,UAGhB,UAEI5N,KAAK8J,aAAa7I,IAClB7B,IACCqQ,IAAuB,aAAhB9O,EAAEnB,KAAKkQ,GAAsBA,EAAIzO,GAAWyO,GAAOF,KAE7DvO,EAAQiH,UAINqH,IAGAE,GAAMD,GACRpF,EAAK0D,MAAQ7M,EAAQyG,KAAK,iBACnB1H,KAAKkO,gBAAe,EAAOjN,EAAQ2G,QAAS,CACjD,KACA,QACAwC,EAAK0D,UAITpC,EAAS,WAEP,IAAIiE,EAAIhP,EAAE8J,OAAO,GAAIxJ,GACnB2O,EAAgBD,EAAEnF,UAAUf,OAC9BkG,EAAErD,KAAOlN,EAAM0L,KACbV,EACAqF,EACAA,GAAsB,aAAhB9O,EAAEnB,KAAKkQ,GAAsBA,EAAIC,GAAKD,GAC5CtF,EAAK5K,MAIPmQ,EAAEvE,UAAYuE,EAAErD,KAAKvO,KAErB4R,EAAEtD,aACA,SAAWoD,EAAK,KAAOA,EAAKE,EAAEvE,UAAY,GAAK,IAAMoE,EAEvDjE,EAAKtG,SAAS,kBAAmB,KAAM0K,GAEvCpE,EAAKY,aAAawD,GAElBpE,EAAKD,sBAAsBqE,GAC3BZ,IAC2C,IAAxCxD,EAAKtG,SAAS,YAAa,KAAM0K,IAAgBhP,EAAEkP,KAAKF,IACzDpE,EAAK2C,gBAAe,EAAOyB,EAAE/H,UAE5BU,MAAK,SAAUwH,EAAQC,EAAYhB,GAClCU,EAAKlE,EAAK2D,kBAAkBH,IAAUU,EAAKE,EAAEvE,UAIzCwE,EAAgBD,EAAEvE,UAAYuE,EAAEnF,UAAUf,QAC5C8B,EAAKR,YACHpK,EAAEuE,MAAM,WAAY,CAClB8F,kBAAkB,EAClBvB,OAAQgG,EAAKE,EAAExI,cACfgD,MAAOsF,EAAKE,EAAExI,gBAEhBwI,GAGJ1O,EAAQkG,cAAgBwI,EAAExI,cAAgBsI,EAC1CE,EAAEG,OAASA,EACXH,EAAEI,WAAaA,EACfJ,EAAEZ,MAAQA,EACVxD,EAAKtG,SAAS,YAAa,KAAM0K,GACjCpE,EAAKtG,SAAS,cAAe,KAAM0K,GAC/BF,EAAKD,EAGP9D,IAEA2C,EAAIE,YAAYoB,EAAE/H,QAAS,CAACkI,EAAQC,EAAYhB,OAGnDhB,MAAK,SAAUgB,EAAOgB,EAAYjB,GACjCa,EAAEZ,MAAQA,EACVY,EAAEI,WAAaA,EACfJ,EAAEb,YAAcA,EAChBvD,EAAKtG,SAAS,YAAa,KAAM0K,GACjCpE,EAAKtG,SAAS,cAAe,KAAM0K,GACnCtB,EAAIG,WAAWmB,EAAE/H,QAAS,CAACmH,EAAOgB,EAAYjB,OAE/Cb,QAAO,WACN1C,EAAKK,wBAAwB+D,OAGnC3P,KAAK2N,gBAAgBC,GACrBA,EAAQjK,MAAQ,WACd,OAAOoL,EAAMpL,SAEf+H,IACOkC,MAGToC,YAAa,SAAUpS,EAAGsK,GACH,IAAjBlI,KAAKiQ,UAIPjQ,KAAKiF,SAAS,SAEdjF,KAAKqL,cAAgB,IAAIrL,KAAKoJ,cAE9BpJ,KAAKwK,UAAUf,OAASzJ,KAAKwK,UAAUL,MAAQ,EAC/CnK,KAAKwK,UAAUd,QAAU,GAK3B1J,KAAK0K,oBAAoBxC,GACzBlI,KAAKqK,oBAAoBnC,GACzBA,EAAKsC,UAAUf,OAASvB,EAAKuB,OAASvB,EAAKf,eAAiB,EAC5De,EAAKsC,UAAUL,MAAQjC,EAAKiC,MAAQnK,KAAKiK,UAAU/B,EAAKgC,QAAU,EAClEhC,EAAKsC,UAAUd,QAAUxB,EAAKwB,QAAU,EACxC1J,KAAKiQ,SAAW,EAEhBjQ,KAAKwK,UAAUf,QAAUvB,EAAKuB,OAC9BzJ,KAAKwK,UAAUL,OAASjC,EAAKiC,OAG/B+F,QAAS,SAAUJ,EAAQC,EAAYhB,EAAO9N,GAC5C,IAAIkJ,EAAQlJ,EAAQuJ,UAAUL,MAC5B/H,EAAWnB,EAAQ0J,UACjB1J,EAAQuJ,UAAUf,OAASU,GAG7BnK,KAAK+K,YACHpK,EAAEuE,MAAM,WAAY,CAClB8F,kBAAkB,EAClBvB,OAAQU,EACRA,MAAOA,IAETlJ,GAGJmB,EAAS0N,OAAS7O,EAAQ6O,OAASA,EACnC1N,EAAS2N,WAAa9O,EAAQ8O,WAAaA,EAC3C3N,EAAS2M,MAAQ9N,EAAQ8N,MAAQA,EACjC/O,KAAKiF,SAAS,OAAQ,KAAMhE,IAG9BkP,QAAS,SAAUpB,EAAOgB,EAAYjB,EAAa7N,GACjD,IAAImB,EAAWnB,EAAQ0J,UACnB1J,EAAQmG,sBAGVpH,KAAKwK,UAAUf,QAAUxI,EAAQuJ,UAAUf,OAC3CzJ,KAAKwK,UAAUL,OAASlJ,EAAQuJ,UAAUL,OAE5C/H,EAAS2M,MAAQ9N,EAAQ8N,MAAQA,EACjC3M,EAAS2N,WAAa9O,EAAQ8O,WAAaA,EAC3C3N,EAAS0M,YAAc7N,EAAQ6N,YAAcA,EAC7C9O,KAAKiF,SAAS,OAAQ,KAAMhE,IAG9BmP,UAAW,SAAUC,EAAeN,EAAYO,EAAcrP,GAG5DjB,KAAKiF,SAAS,SAAU,KAAMhE,IAGhC+N,QAAS,SAAUpR,EAAGsK,GACfA,EAAK5E,QACRtD,KAAKyO,uBAAuB7Q,EAAGsK,GAEjC,IACE6G,EACAwB,EACAC,EACAC,EAJElF,EAAOvL,KAKTiB,EAAUsK,EAAK8B,iBAAiBnF,GAChC3G,EAAO,WAsDL,OArDAgK,EAAKmF,UAAY,EAEjBzP,EAAQoK,cAAgB,IAAIE,EAAKnC,cACjC2F,EACEA,KAEIwB,IAKM,IAJNhF,EAAKtG,SACH,OACAtE,EAAEuE,MAAM,OAAQ,CAAEC,eAAgBvH,IAClCqD,KAEFsK,EAAK2C,gBAAe,EAAOjN,EAAQ2G,QAAS2I,IAC9ChF,EAAKyB,eAAe/L,IACpBN,EAAEkP,KAAK5O,IAENqH,MAAK,SAAUwH,EAAQC,EAAYhB,GAClCxD,EAAK2E,QAAQJ,EAAQC,EAAYhB,EAAO9N,MAEzC8M,MAAK,SAAUgB,EAAOgB,EAAYjB,GACjCvD,EAAK4E,QAAQpB,EAAOgB,EAAYjB,EAAa7N,MAE9CgN,QAAO,SAAUoC,EAAeN,EAAYO,GAU3C,GATA/E,EAAKK,wBAAwB3K,GAC7BsK,EAAK6E,UACHC,EACAN,EACAO,EACArP,GAEFsK,EAAKmF,UAAY,EACjBnF,EAAK0E,SAAW,EAEdhP,EAAQ2F,wBACR3F,EAAQ2F,uBAAyB2E,EAAKmF,SAKtC,IADA,IAAIC,EAAWpF,EAAKqF,OAAOC,QACpBF,GAAU,CACf,GAAyC,YAArCpF,EAAK+B,kBAAkBqD,GAAyB,CAClDA,EAASG,UACT,MAEFH,EAAWpF,EAAKqF,OAAOC,QAGN,IAAjBtF,EAAK0E,SAGP1E,EAAKtG,SAAS,YAM1B,OADAjF,KAAKgQ,YAAYpS,EAAGqD,GAElBjB,KAAKiB,QAAQ0F,mBACZ3G,KAAKiB,QAAQ2F,wBACZ5G,KAAKiB,QAAQ2F,wBAA0B5G,KAAK0Q,UAE1C1Q,KAAKiB,QAAQ2F,uBAAyB,GACxC4J,EAAO7P,EAAE2N,WACTtO,KAAK4Q,OAAO5G,KAAKwG,GACjBC,EAAOD,EAAKjF,EAAK5C,cAAcpH,KAE/BvB,KAAK+Q,UAAY/Q,KAAK+Q,UAAUxF,EAAK5C,cAAcpH,EAAMA,GACzDkP,EAAOzQ,KAAK+Q,WAKdN,EAAK9M,MAAQ,WAEX,OADA4M,EAAU,MAAChO,EAAW,QAAS,SAC1BwM,EAMEA,EAAMpL,SALP6M,GACFA,EAAKhC,WAAWvN,EAAQ2G,QAAS2I,GAE5BhP,MAIJvB,KAAK2N,gBAAgB8C,IAEvBlP,KAGTyP,OAAQ,SAAUpT,EAAGsK,GACnB,IAUE+I,EACAC,EACAC,EACAnS,EAbEuM,EAAOvL,KACT8P,GAAS,EACT7O,EAAUN,EAAE8J,OAAO,GAAIzK,KAAKiB,QAASiH,GACrCgC,EAAQhC,EAAKgC,MACbkH,EAAclH,EAAM7K,OACpBgS,EAAQpQ,EAAQuF,sBAChB8K,EAAYrQ,EAAQwF,yBACpB8K,EAAWtQ,EAAQyF,iCACnB8K,EAAY,EACZtP,EAAYlC,KAAKiN,cAAchM,GAK/BwQ,EAAI,EACN,IAAKL,EACH,OAAO,EAKT,GAHIE,QAA+B/O,IAAlB2H,EAAM,GAAGnM,OACxBuT,OAAY/O,IAGVtB,EAAQsF,mBAAqB8K,GAASC,IACvCtR,KAAK8J,aAAa7I,GAId,GAAMA,EAAQsF,mBAAqB+K,IAAcD,EAWjD,IAAKpQ,EAAQsF,mBAAqB+K,EAGvC,IAFAH,EAAU,GACVF,EAAe,GACVjS,EAAI,EAAGA,EAAIoS,EAAapS,GAAQ,EACnCwS,GAAatH,EAAMlL,GAAGjB,KAAOwT,GAE3BvS,EAAI,IAAMoS,GACVI,EAAYtH,EAAMlL,EAAI,GAAGjB,KAAOwT,EAAWD,GAC1CD,GAASrS,EAAI,EAAIyS,GAAKJ,KAEvBF,EAAQnH,KAAKE,EAAM9K,MAAMqS,EAAGzS,EAAI,KAChCkS,EAAiBhP,EAAU9C,MAAMqS,EAAGzS,EAAI,IACpBK,SAClB6R,EAAiBhP,GAEnB+O,EAAajH,KAAKkH,GAClBO,EAAIzS,EAAI,EACRwS,EAAY,QAIhBP,EAAe/O,OA7Bf,IAFAiP,EAAU,GACVF,EAAe,GACVjS,EAAI,EAAGA,EAAIoS,EAAapS,GAAKqS,EAChCF,EAAQnH,KAAKE,EAAM9K,MAAMJ,EAAGA,EAAIqS,KAChCH,EAAiBhP,EAAU9C,MAAMJ,EAAGA,EAAIqS,IACpBhS,SAClB6R,EAAiBhP,GAEnB+O,EAAajH,KAAKkH,QAXpBC,EAAU,CAACjH,GACX+G,EAAe,CAAC/O,GAkDlB,OAfAgG,EAAKwJ,cAAgBxH,EACrBvJ,EAAEiC,KAAKuO,GAAWjH,GAAO,SAAUrH,EAAO8O,GACxC,IAAIC,EAAUjR,EAAE8J,OAAO,GAAIvC,GAW3B,OAVA0J,EAAQ1H,MAAQiH,EAAUQ,EAAU,CAACA,GACrCC,EAAQ1P,UAAY+O,EAAapO,GACjC0I,EAAKb,oBAAoBkH,GACzBrG,EAAKlB,oBAAoBuH,GACzBrG,EAAKkD,uBAAuB7Q,EAAGgU,GAC/B9B,EAASvE,EAAKtG,SACZ,MACAtE,EAAEuE,MAAM,MAAO,CAAEC,eAAgBvH,IACjCgU,MAIG9B,GAGT+B,kBAAmB,SAAU3J,GAC3B,IAAI3E,EAAQ2E,EAAKhF,UACf4O,EAAavO,EAAMJ,OAAM,GACzB4O,EAAexO,EAAMyO,GAAGvO,SAASwO,eAEnC/J,EAAKgK,eAAiBJ,EACtBnR,EAAE,iBAAiBlB,OAAOqS,GAAY,GAAGK,QAGzC5O,EAAMH,MAAM0O,GAAYM,SAGpBL,GACFD,EAAWO,QAAQ,SAGrB1R,EAAE2R,UAAU/O,EAAMpB,IAAI,WAItBnC,KAAKiB,QAAQiC,UAAYlD,KAAKiB,QAAQiC,UAAU6I,KAAI,SAAU/M,EAAGuT,GAC/D,OAAIA,IAAOhP,EAAM,GACRuO,EAAW,GAEbS,KAILhP,EAAM,KAAOvD,KAAK2R,QAAQ,KAC5B3R,KAAK2R,QAAUG,IAInBU,qBAAsB,SAAUC,EAAOC,GACrC,IAGEC,EAHEpH,EAAOvL,KACTqO,EAAM1N,EAAE2N,WACRsE,EAAU,GAEVC,EAAe,SAAUjV,GACnBA,IAAMA,EAAE6U,QACV7U,EAAE6U,MAAQA,GAMZpE,EAAIyC,QAAQ,CAAClT,KAUfkV,EAAc,WACZH,EAAUG,aAAY,SAAUC,GACzBA,EAAQ1T,QAGXuT,EAAUA,EAAQI,OAAOD,GACzBD,KAdW,SAAUF,GACzBrH,EACG0H,uBAAuBL,EAASF,EAAOD,EAAM1P,KAAO,KACpDuF,MAAK,SAAU4B,GACdmE,EAAIyC,QAAQ5G,MAEb6D,KAAK8E,GAKJK,CAAeN,KAKhBC,IAuBP,OApBAH,EAAOA,GAAQ,GACXD,EAAMU,OACJV,EAAMW,OAERX,EAAMW,MAAMC,aAAeX,EAC3BrE,EAAIyC,QAAQ2B,EAAMW,QAElBX,EAAMrI,MAAK,SAAUA,GACnBA,EAAKiJ,aAAeX,EACpBrE,EAAIyC,QAAQ1G,KACXyI,GAEIJ,EAAMa,aACfX,EAAYF,EAAMc,eAClBT,KAIAzE,EAAIyC,QAAQ,IAEPzC,EAAIT,WAGbqF,uBAAwB,SAAUL,EAASF,GACzC,IAAInH,EAAOvL,KACX,OAAOW,EAAE6S,KACNtK,MACCvI,EACAA,EAAEoL,IAAI6G,GAAS,SAAUH,GACvB,OAAOlH,EAAKiH,qBAAqBC,EAAOC,OAG3C1S,KAAK2I,eAAc,WAClB,OAAO8K,MAAMjW,UAAUwV,OAAO9J,MAAM,GAAIC,eAI9CuK,iBAAkB,SAAU7O,GAG1B,IAAI8O,GADJ9O,EAAeA,GAAgB,IACN8O,MACzB,OACEA,GACAA,EAAMtU,SACLsU,EAAM,GAAGC,kBAAoBD,EAAM,GAAGE,YAEhC7T,KAAKiT,uBACVtS,EAAEoL,IAAI4H,GAAO,SAAUG,GACrB,IAAIrB,EACJ,OAAIqB,EAAKF,mBACPnB,EAAQqB,EAAKF,sBAGXnB,EAAMW,MAAQU,EAAKC,aAEdtB,GAEFqB,EAAKD,iBAIXlT,EAAE2N,WAAWwC,QAAQnQ,EAAEqT,UAAUnP,EAAaqF,QAAQ0D,WAG/DqG,yBAA0B,SAAU/Q,GAGlC,IAEEgH,EACAjH,EAHE2P,GADJ1P,EAAYvC,EAAEuC,IAEAR,KAAK,kBAAoBQ,EAAUR,KAAK,WAGtD,GAAIkQ,GAAWA,EAAQvT,OACrB,OAAOW,KAAKiT,uBAAuBL,GAGrC,IADA1I,EAAQvJ,EAAEqT,UAAU9Q,EAAUR,KAAK,WACxBrD,YASkBkD,IAAlB2H,EAAM,GAAGnH,MAAsBmH,EAAM,GAAGuC,UAEjD9L,EAAEiC,KAAKsH,GAAO,SAAUrH,EAAOuH,GAC7BA,EAAKrH,KAAOqH,EAAKqC,SACjBrC,EAAKrM,KAAOqM,EAAK8J,gBAbF,CAEjB,KADAjR,EAAQC,EAAUR,KAAK,UAErB,OAAO/B,EAAE2N,WAAWwC,QAAQ,IAAIlD,UAKlC1D,EAAQ,CAAC,CAAEnH,KAAME,EAAM8E,QAAQ,QAAS,MAQ1C,OAAOpH,EAAE2N,WAAWwC,QAAQ5G,GAAO0D,WAGrCuG,mBAAoB,SAAUjR,GAC5B,OAAMA,aAAqBvC,GAA2B,IAArBuC,EAAU7D,OAGpCsB,EAAE6S,KACNtK,MAAMvI,EAAGA,EAAEoL,IAAI7I,EAAWlD,KAAKiU,2BAC/BjU,KAAK2I,eAAc,WAClB,OAAO8K,MAAMjW,UAAUwV,OAAO9J,MAAM,GAAIC,cALnCnJ,KAAKiU,yBAAyB/Q,IASzCkR,UAAW,SAAUxW,GACnB,IAAI2N,EAAOvL,KACTkI,EAAO,CACLhF,UAAWvC,EAAE/C,EAAEyW,QACflT,KAAMR,EAAE/C,EAAEyW,OAAOlT,OAErBnB,KAAKmU,mBAAmBjM,EAAKhF,WAAW+K,QAAO,SAAU/D,GACvDhC,EAAKgC,MAAQA,EACTqB,EAAKtK,QAAQqF,kBACfiF,EAAKsG,kBAAkB3J,IAOjB,IAJNqD,EAAKtG,SACH,SACAtE,EAAEuE,MAAM,SAAU,CAAEC,eAAgBvH,IACpCsK,IAGFqD,EAAKyF,OAAOpT,EAAGsK,OAKrBoM,SAAU,SAAU1W,GAClB,IAAI+V,EACA/V,EAAEkH,eACFlH,EAAEkH,cAAcyP,eAChB3W,EAAEkH,cAAcyP,cAAcZ,MAChCzL,EAAO,CAAEgC,MAAO,IACdyJ,GAASA,EAAMtU,SACjBsB,EAAEiC,KAAK+Q,GAAO,SAAU9Q,EAAOiR,GAC7B,IAAI1J,EAAO0J,EAAKC,WAAaD,EAAKC,YAC9B3J,GACFlC,EAAKgC,MAAMF,KAAKI,OAQZ,IAJNpK,KAAKiF,SACH,QACAtE,EAAEuE,MAAM,QAAS,CAAEC,eAAgBvH,IACnCsK,IAGFlI,KAAKgR,OAAOpT,EAAGsK,KAKrBsM,QAAS,SAAU5W,GACjBA,EAAEiH,aAAejH,EAAEkH,eAAiBlH,EAAEkH,cAAcD,aACpD,IAAI0G,EAAOvL,KACT6E,EAAejH,EAAEiH,aACjBqD,EAAO,GACLrD,GAAgBA,EAAaqF,OAASrF,EAAaqF,MAAM7K,SAC3DzB,EAAEwH,iBACFpF,KAAK0T,iBAAiB7O,GAAcoJ,QAAO,SAAU/D,GACnDhC,EAAKgC,MAAQA,GAML,IAJNqB,EAAKtG,SACH,OACAtE,EAAEuE,MAAM,OAAQ,CAAEC,eAAgBvH,IAClCsK,IAGFqD,EAAKyF,OAAOpT,EAAGsK,QAMvBuM,YAAa9P,EAAe,YAE5B+P,aAAc/P,EAAe,aAE7BgQ,aAAchQ,EAAe,aAE7BiQ,mBAAoB,WACd5U,KAAK8J,aAAa9J,KAAKiB,WACzBjB,KAAK6U,IAAI7U,KAAKiB,QAAQmF,SAAU,CAC9B0O,SAAU9U,KAAKyU,YACfM,KAAM/U,KAAKwU,QAEXQ,UAAWhV,KAAK0U,aAEhBO,UAAWjV,KAAK2U,eAElB3U,KAAK6U,IAAI7U,KAAKiB,QAAQoF,UAAW,CAC/B6O,MAAOlV,KAAKsU,YAGZ3T,EAAE4E,QAAQrC,WACZlD,KAAK6U,IAAI7U,KAAKiB,QAAQiC,UAAW,CAC/BiS,OAAQnV,KAAKoU,aAKnBgB,sBAAuB,WACrBpV,KAAKqV,KAAKrV,KAAKiB,QAAQmF,SAAU,qCACjCpG,KAAKqV,KAAKrV,KAAKiB,QAAQoF,UAAW,SAClCrG,KAAKqV,KAAKrV,KAAKiB,QAAQiC,UAAW,WAGpCoS,SAAU,WACRtV,KAAKoV,yBAGPG,WAAY,SAAUzN,EAAK7E,GACzB,IAAIuS,GAAmD,IAA1C7U,EAAEoE,QAAQ+C,EAAK9H,KAAKgJ,iBAC7BwM,GACFxV,KAAKoV,wBAEPpV,KAAKyV,OAAO3N,EAAK7E,GACbuS,IACFxV,KAAK0V,sBACL1V,KAAK4U,uBAITc,oBAAqB,WACnB,IAAIzU,EAAUjB,KAAKiB,aACOsB,IAAtBtB,EAAQiC,UACVjC,EAAQiC,UAAYlD,KAAK2R,QAAQK,GAAG,sBAChChS,KAAK2R,QACL3R,KAAK2R,QAAQgE,KAAK,sBACX1U,EAAQiC,qBAAqBvC,IACxCM,EAAQiC,UAAYvC,EAAEM,EAAQiC,YAE1BjC,EAAQmF,oBAAoBzF,IAChCM,EAAQmF,SAAWzF,EAAEM,EAAQmF,WAEzBnF,EAAQoF,qBAAqB1F,IACjCM,EAAQoF,UAAY1F,EAAEM,EAAQoF,aAIlCuP,WAAY,SAAUC,GACpB,IAAIvQ,EAAQuQ,EAAI/M,MAAM,KACpBgN,EAAYxQ,EAAMyQ,MAEpB,OADAzQ,EAAMuL,QACC,IAAIrL,OAAOF,EAAM0Q,KAAK,KAAMF,IAGrCG,gBAAiB,SAAUnO,EAAK7E,GAC9B,MACU,QAAR6E,GACkB,WAAlBnH,EAAEnB,KAAKyD,IACP,qBAAqBrB,KAAKqB,IAI9BiT,oBAAqB,WACnB,IAAI3K,EAAOvL,KACTiB,EAAUjB,KAAKiB,QACfiH,EAAOlI,KAAK2R,QAAQzJ,OAEtBvH,EAAEiC,KAAK5C,KAAK2R,QAAQ,GAAGwE,YAAY,SAAUtT,EAAOnB,GAClD,IACEuB,EADE6E,EAAMpG,EAAKqB,KAAKqT,cAEhB,SAASxU,KAAKkG,KAEhBA,EAAMA,EAAI1I,MAAM,GAAG2I,QAAQ,WAAW,SAAU8N,GAC9C,OAAOA,EAAIQ,OAAO,GAAGjJ,iBAEvBnK,EAAQiF,EAAKJ,GACTyD,EAAK0K,gBAAgBnO,EAAK7E,KAC5BA,EAAQsI,EAAKqK,WAAW3S,IAE1BhC,EAAQ6G,GAAO7E,OAKrBqT,QAAS,WACPtW,KAAKkW,sBACLlW,KAAK0V,sBACL1V,KAAK4Q,OAAS,GACd5Q,KAAK+Q,UAAY/Q,KAAKkO,gBAAe,GACrClO,KAAK0Q,SAAW1Q,KAAKiQ,QAAU,EAC/BjQ,KAAKqK,oBAAoBrK,MACzBA,KAAK4U,sBAKP2B,OAAQ,WACN,OAAOvW,KAAKiQ,SAOd1F,SAAU,WACR,OAAOvK,KAAKwK,WAOdvC,IAAK,SAAUC,GACb,IAAIqD,EAAOvL,KACNkI,IAAQlI,KAAKiB,QAAQuV,WAGtBtO,EAAKhF,YAAcgF,EAAKgC,MAC1BlK,KAAKmU,mBAAmBjM,EAAKhF,WAAW+K,QAAO,SAAU/D,GACvDhC,EAAKgC,MAAQA,EACbqB,EAAKyF,OAAO,KAAM9I,OAGpBA,EAAKgC,MAAQvJ,EAAEqT,UAAU9L,EAAKgC,OAC9BlK,KAAKgR,OAAO,KAAM9I,MAStB3G,KAAM,SAAU2G,GACd,GAAIA,IAASlI,KAAKiB,QAAQuV,SAAU,CAClC,GAAItO,EAAKhF,YAAcgF,EAAKgC,MAAO,CACjC,IAGE6E,EACAwB,EAJEhF,EAAOvL,KACTqO,EAAM1N,EAAE2N,WACRV,EAAUS,EAAIT,UA8BhB,OA3BAA,EAAQjK,MAAQ,WAEd,OADA4M,GAAU,EACNxB,EACKA,EAAMpL,SAEf0K,EAAIoI,OAAO,KAAM,QAAS,SACnB7I,IAET5N,KAAKmU,mBAAmBjM,EAAKhF,WAAW+K,QAAO,SAAU/D,GACnDqG,IAGCrG,EAAM7K,QAIX6I,EAAKgC,MAAQA,GACb6E,EAAQxD,EAAKyD,QAAQ,KAAM9G,IACrBwO,MACJ,SAAU5G,EAAQC,EAAYhB,GAC5BV,EAAIyC,QAAQhB,EAAQC,EAAYhB,MAElC,SAAUA,EAAOgB,EAAYjB,GAC3BT,EAAIoI,OAAO1H,EAAOgB,EAAYjB,OAVhCT,EAAIoI,aAcDzW,KAAK2N,gBAAgBC,GAG9B,GADA1F,EAAKgC,MAAQvJ,EAAEqT,UAAU9L,EAAKgC,OAC1BhC,EAAKgC,MAAM7K,OACb,OAAOW,KAAKgP,QAAQ,KAAM9G,GAG9B,OAAOlI,KAAKkO,gBAAe,EAAOhG,GAAQA,EAAKN,eCrjDrD,SAAWpH,GACT,aACsB,mBAAXJ,QAAyBA,OAAOC,IAEzCD,OAAO,CAAC,SAAU,uBAAwBI,GACd,iBAAZD,QAEhBC,EAAQC,QAAQ,UAAWA,QAAQ,wBAGnCD,EAAQnD,OAAOqD,QAVnB,EAYG,SAAUC,GACX,aAEA,IAAIgW,EAAchW,EAAEiW,QAAQxO,WAAW5K,UAAUyD,QAAQgH,IAIzDtH,EAAEwF,OAAO,qBAAsBxF,EAAEiW,QAAQxO,WAAY,CACnDnH,QAAS,CAEP4V,aAAc,GAQd5O,IAAK,SAAUrK,EAAGsK,GAChB,IAAI4O,EAAQnW,EAAEX,MACdkI,EAAKG,SAAQ,WACX,OAAOyO,EAAM1O,WAAW,UAAWF,MAErCyO,EAAY7L,KAAK9K,KAAMpC,EAAGsK,KAI9B6O,eAAgB,GAUhBC,aAAc,SAAU9O,EAAM+O,GAC5B,IAAI1L,EAAOvL,KAGTkX,EADMvW,EAAE2N,WAAWC,YAAYhD,EAAM,CAACrD,IAC1B0F,UAyBd,OAxBA5N,KAAKiF,SAAS,UAAW,KAAMiD,GAC/BvH,EAAEiC,KAAKsF,EAAK2O,cAAc,SAAU7X,EAAGmY,GACrC,IAAIC,EAAO,SAAUlP,GACnB,OAAI+O,EAAanI,YAERnO,EAAE2N,WAAWE,WAAWjD,EAAM,CAAC0L,IAAerJ,UAEhDrC,EAAKwL,eAAeI,EAASE,QAAQvM,KAC1CS,EACArD,EACAiP,IAGJD,EAAQA,EAAM3L,EAAK5C,cAAcyO,EAAMD,EAASlJ,QAAUmJ,MAE5DF,EACG5O,MAAK,WACJiD,EAAKtG,SAAS,cAAe,KAAMiD,GACnCqD,EAAKtG,SAAS,gBAAiB,KAAMiD,MAEtC6F,MAAK,WACJxC,EAAKtG,SAAS,cAAe,KAAMiD,GACnCqD,EAAKtG,SAAS,gBAAiB,KAAMiD,MAElCgP,GAOTI,uBAAwB,SAAUrW,GAChC,IAAI4V,EAAe,GACnBlW,EAAEiC,KAAK3B,EAAQ4V,cAAc,WAC3B,IAAIM,EAAW,GACbE,EAASrX,KAAKqX,OACdE,GAAyB,IAAhBvX,KAAKuX,OAAkBF,EAASrX,KAAKuX,OAChD5W,EAAEiC,KAAK5C,MAAM,SAAU8H,EAAK7E,GACJ,WAAlBtC,EAAEnB,KAAKyD,IAA2C,MAApBA,EAAMoT,OAAO,GAC7Cc,EAASrP,GACP7G,EACEgC,EAAM7D,MAAM,KACTmY,EACGA,EAASzP,EAAIuO,OAAO,GAAGjJ,cAAgBtF,EAAI1I,MAAM,GACjD0I,IAGVqP,EAASrP,GAAO7E,KAGpB4T,EAAa7M,KAAKmN,MAEpBlW,EAAQ4V,aAAeA,GAIzB5H,WAAY,WACV,OAAOjP,KAAKwX,aAKdnP,QAAS,SAAUH,GACjB,IAAIqD,EAAOvL,KACTiB,EAAUN,EAAE8J,OAAO,GAAIzK,KAAKiB,QAASiH,GA4BvC,OA3BIjH,EAAQ4V,cAAgB5V,EAAQ4V,aAAaxX,SAC/CW,KAAKsX,uBAAuBrW,GACH,IAArBjB,KAAKwX,aACPxX,KAAKiF,SAAS,gBAEhBtE,EAAEiC,KAAKsF,EAAKgC,OAAO,SAAUrH,GAC3B,IAAI4U,EAAO5U,EAAQlC,EAAE8J,OAAO,GAAIxJ,GAAWA,EACzCmW,EAAO,WACL,OAAIlP,EAAK4G,YAEAnO,EAAE2N,WAAWE,WAAWjD,EAAM,CAACrD,IAAO0F,UAExCrC,EAAKyL,aAAaS,EAAMvP,IAEnCuP,EAAK5U,MAAQA,EACb0I,EAAKiM,aAAe,EACpBjM,EAAKmM,iBAAmBnM,EAAKmM,iBAAiBnM,EAAK5C,cACjDyO,EACAA,GACAnJ,QAAO,WACP1C,EAAKiM,aAAe,EACK,IAArBjM,EAAKiM,aACPjM,EAAKtG,SAAS,sBAKfjF,KAAK0X,kBAGdpB,QAAS,WACPtW,KAAKyV,SACLzV,KAAKwX,YAAc,EAEnBxX,KAAK0X,iBAAmB/W,EAAE2N,WAAWC,YAAYvO,MAAM4N,gBCzJ7D,SAAWpN,GACT,aACsB,mBAAXJ,QAAyBA,OAAOC,IAEzCD,OAAO,CACL,SACA,aACA,kBACA,mBACA,kBACA,yBACA,iBACA,+BACCI,GACyB,iBAAZD,QAEhBC,EACEC,QAAQ,UACRA,QAAQ,oCACRA,QAAQ,yCACRA,QAAQ,0CACRA,QAAQ,yCACRA,QAAQ,gDACRA,QAAQ,0BACRA,QAAQ,gCAIVD,EAAQnD,OAAOqD,OAAQrD,OAAOsa,WA5BlC,EA8BG,SAAUhX,EAAGgX,GACd,aAGAhX,EAAEiW,QAAQxO,WAAW5K,UAAUyD,QAAQ4V,aAAae,QAClD,CACEP,OAAQ,oBACRQ,gBAAiB,IACjBC,iBAAkB,IAClBC,uBAAwB,IACxBC,YAAa,IACbC,mBAAoB,IACpBC,gBAAiB,IACjBC,gBAAiB,IACjBC,YAAa,IACbC,mBAAoB,IACpBC,gBAAiB,IACjBC,gBAAiB,IACjB/B,SAAU,6BAEZ,CACEa,OAAQ,YAERE,QAAQ,EACRiB,UAAW,IACXC,YAAa,IACbC,SAAU,IACVlC,SAAU,qBAEZ,CACEa,OAAQ,cAERE,OAAQ,QACRoB,SAAU,IACVC,UAAW,IACXC,SAAU,IACVC,UAAW,IACXC,KAAM,IACNC,YAAa,IACbC,YAAa,IACbzC,SAAU,uBAEZ,CACEa,OAAQ,YACRvX,QAAS,gBACTN,KAAM,aACNgX,SAAU,uBAEZ,CACEa,OAAQ,oBACRb,SAAU,6BAEZ,CACEa,OAAQ,cAERE,OAAQ,UACRoB,SAAU,IACVC,UAAW,IACXC,SAAU,IACVC,UAAW,IACXC,KAAM,IACNC,YAAa,IACbE,UAAW,IACXC,OAAQ,IACR3C,SAAU,wBAEZ,CACEa,OAAQ,WACRtU,KAAM,oBACNyT,SAAU,wBAEZ,CACEa,OAAQ,wBACRb,SAAU,oCAMd7V,EAAEwF,OAAO,qBAAsBxF,EAAEiW,QAAQxO,WAAY,CACnDnH,QAAS,CAGPmY,mBAAoB,mCAEpBC,qBAAsB,IAEtBC,cAAe,KAEfC,eAAgB,KAGhBC,kBAAkB,EAElBC,WAAW,EAEXC,oBAAoB,EAEpBC,gBAAiB,GAEjBC,iBAAkB,GAGlBC,oBAAoB,EAEpBC,kBAAkB,EAElBC,aAAa,EAEbC,eAAe,GAGjBjD,eAAgB,CAKdY,UAAW,SAAUzP,EAAMjH,GACzB,GAAIA,EAAQuV,SACV,OAAOtO,EAET,IAAIqD,EAAOvL,KACToK,EAAOlC,EAAKgC,MAAMhC,EAAKrF,OAEvBwL,EAAM1N,EAAE2N,WACV,MACmC,WAAhC3N,EAAEnB,KAAKyB,EAAQwX,cACdrO,EAAKrM,KAAOkD,EAAQwX,aACrBxX,EAAQuX,YAAcvX,EAAQuX,UAAU5W,KAAKwI,EAAK5K,QAClDmY,EACCvN,GACA,SAAU6P,GACJA,EAAIC,MACNhS,EAAK+R,IAAMA,GAEb5L,EAAIE,YAAYhD,EAAM,CAACrD,MAEzBjH,GAGKiH,EAEFmG,EAAIT,WAQbuM,YAAa,SAAUjS,EAAMjH,GAC3B,GAAIA,EAAQuV,WAActO,EAAKiR,SAAUjR,EAAK+R,IAC5C,OAAO/R,EAGTjH,EAAUN,EAAE8J,OAAO,CAAE0O,QAAQ,GAAQlY,GACrC,IAgBEiY,EACAkB,EAjBE7O,EAAOvL,KAETqO,EAAM1N,EAAE2N,WACR2L,EAAOhZ,EAAQkY,QAAUjR,EAAKiR,QAAWjR,EAAK+R,IAC9CnJ,EAAU,SAAUuJ,GAEhBA,IACCA,EAAOC,QAAUL,EAAIK,OACpBD,EAAOE,SAAWN,EAAIM,QACtBtZ,EAAQgY,eAEV/Q,EAAKmS,EAAOG,WAAa,SAAW,OAASH,GAE/CnS,EAAKuS,QAAUJ,EACfhM,EAAIE,YAAYhD,EAAM,CAACrD,KAI3B,OAAIA,EAAKwS,MAAQzZ,EAAQiY,YAEvBkB,GADAlB,EAAYhR,EAAKwS,KAAKC,IAAI,eACGzB,EAAUyB,IAAI,UAEzC1Z,EAAQ+X,YAAc9Q,EAAKwS,KAAKC,IAAI,eACpChD,EAAUyC,EAAetJ,EAAS7P,GAC3BoN,EAAIT,YAGX1F,EAAK8Q,mBAEA/X,EAAQ+X,YAEf9Q,EAAK8Q,YAAc/X,EAAQ+X,aAAerB,EAAUqB,YAElDiB,GACFnJ,EAAQ6G,EAAUiD,MAAMX,EAAKhZ,EAASiH,IAC/BmG,EAAIT,WAEN1F,IAKT2S,UAAW,SAAU3S,EAAMjH,GACzB,IAAKiH,EAAKiR,QAAUlY,EAAQuV,SAC1B,OAAOtO,EAET,IAAIqD,EAAOvL,KACToK,EAAOlC,EAAKgC,MAAMhC,EAAKrF,OAEvBwL,EAAM1N,EAAE2N,WACV,OAAIpG,EAAKiR,OAAOxZ,QACduI,EAAKiR,OAAOxZ,QACV,SAAU2M,GACHA,EAAKvJ,OACJqH,EAAK5K,OAAS8M,EAAK9M,KACrB8M,EAAKvJ,KAAOqH,EAAKrH,KACRqH,EAAKrH,OACduJ,EAAKvJ,KAAOqH,EAAKrH,KAAKgF,QACpB,SACA,IAAMuE,EAAK9M,KAAKsb,OAAO,MAKzB1Q,EAAK5K,OAAS8M,EAAK9M,aACd0I,EAAK6S,UAId7S,EAAKgC,MAAMhC,EAAKrF,OAASyJ,EACzB+B,EAAIE,YAAYhD,EAAM,CAACrD,MAEzBjH,EAAQzB,MAAQ4K,EAAK5K,KACrByB,EAAQnB,SAKLuO,EAAIT,WAFF1F,GAKX8S,kBAAmB,SAAU9S,EAAMjH,GACjC,GAAIA,EAAQuV,SACV,OAAOtO,EAET,IAAIqD,EAAOvL,KAETqO,EAAM1N,EAAE2N,WASV,OARAqJ,EAAUsD,cACR/S,EAAKgC,MAAMhC,EAAKrF,QAChB,SAAUiN,GACRnP,EAAE8J,OAAOvC,EAAM4H,GACfzB,EAAIE,YAAYhD,EAAM,CAACrD,MAEzBjH,GAEKoN,EAAIT,WAGbsN,kBAAmB,SAAUhT,EAAMjH,GACjC,KAEIiH,EAAK6S,WACL7S,EAAKiR,QACLjR,EAAKiR,OAAOxZ,SACXsB,EAAQuV,SAGX,OAAOtO,EAET,IAAIqD,EAAOvL,KACToK,EAAOlC,EAAKgC,MAAMhC,EAAKrF,OAEvBwL,EAAM1N,EAAE2N,WAUV,OATyB,IAArBpG,EAAK8Q,aAAwB9Q,EAAKiT,aAEpCxD,EAAUyD,cAAclT,EAAK6S,UAAW7S,EAAM,cAAe,GAE/DyP,EAAU0D,YAAYjR,EAAMlC,EAAK6S,WAAW,SAAUzO,GACpDA,EAAKvJ,KAAOqH,EAAKrH,KACjBmF,EAAKgC,MAAMhC,EAAKrF,OAASyJ,EACzB+B,EAAIE,YAAYhD,EAAM,CAACrD,OAElBmG,EAAIT,WAKb0N,SAAU,SAAUpT,EAAMjH,GAIxB,OAHIiH,EAAKuS,UAAYxZ,EAAQuV,WAC3BtO,EAAKgC,MAAMhC,EAAKrF,OAAO5B,EAAQ8B,MAAQ,WAAamF,EAAKuS,SAEpDvS,GAGTqT,sBAAuB,SAAUrT,EAAMjH,GAOrC,OANKA,EAAQuV,kBACJtO,EAAK+R,WACL/R,EAAKiR,cACLjR,EAAKuS,eACLvS,EAAK6S,WAEP7S,SCxUf,SAAW1H,GACT,aACsB,mBAAXJ,QAAyBA,OAAOC,IAEzCD,OAAO,CAAC,SAAU,aAAc,+BAAgCI,GACpC,iBAAZD,QAEhBC,EACEC,QAAQ,UACRA,QAAQ,oCACRA,QAAQ,gCAIVD,EAAQnD,OAAOqD,OAAQrD,OAAOsa,WAdlC,EAgBG,SAAUhX,EAAGgX,GACd,aAGAhX,EAAEiW,QAAQxO,WAAW5K,UAAUyD,QAAQ4V,aAAae,QAClD,CACEP,OAAQ,YAERE,QAAQ,EACRiB,UAAW,IACXC,YAAa,IACbjC,SAAU,wBAEZ,CACEa,OAAQ,WACRtU,KAAM,oBACNyT,SAAU,yBAMd7V,EAAEwF,OAAO,qBAAsBxF,EAAEiW,QAAQxO,WAAY,CACnDnH,QAAS,CAGPua,mBAAoB,eAGtBC,cAAehY,SAASiY,cAAc,SAEtC3E,eAAgB,CAKd4E,UAAW,SAAUzT,EAAMjH,GACzB,GAAIA,EAAQuV,SACV,OAAOtO,EAET,IACErG,EACA+Z,EAFExR,EAAOlC,EAAKgC,MAAMhC,EAAKrF,OAG3B,OACE7C,KAAKyb,cAAcI,aACnB7b,KAAKyb,cAAcI,YAAYzR,EAAK5K,QACH,WAAhCmB,EAAEnB,KAAKyB,EAAQwX,cACdrO,EAAKrM,MAAQkD,EAAQwX,gBACrBxX,EAAQuX,WAAavX,EAAQuX,UAAU5W,KAAKwI,EAAK5K,SAEnDqC,EAAM8V,EAAUmE,gBAAgB1R,MAE9BwR,EAAQ5b,KAAKyb,cAAcM,WAAU,IAC/B7B,IAAMrY,EACZ+Z,EAAMI,UAAW,EACjB9T,EAAK0T,MAAQA,EACN1T,GAGJA,GAIT+T,SAAU,SAAU/T,EAAMjH,GAIxB,OAHIiH,EAAK0T,QAAU3a,EAAQuV,WACzBtO,EAAKgC,MAAMhC,EAAKrF,OAAO5B,EAAQ8B,MAAQ,WAAamF,EAAK0T,OAEpD1T,SCnFf,SAAW1H,GACT,aACsB,mBAAXJ,QAAyBA,OAAOC,IAEzCD,OAAO,CAAC,SAAU,aAAc,+BAAgCI,GACpC,iBAAZD,QAEhBC,EACEC,QAAQ,UACRA,QAAQ,oCACRA,QAAQ,gCAIVD,EAAQnD,OAAOqD,OAAQrD,OAAOsa,WAdlC,EAgBG,SAAUhX,EAAGgX,GACd,aAGAhX,EAAEiW,QAAQxO,WAAW5K,UAAUyD,QAAQ4V,aAAae,QAClD,CACEP,OAAQ,YAERE,QAAQ,EACRiB,UAAW,IACXC,YAAa,IACbjC,SAAU,wBAEZ,CACEa,OAAQ,WACRtU,KAAM,oBACNyT,SAAU,yBAMd7V,EAAEwF,OAAO,qBAAsBxF,EAAEiW,QAAQxO,WAAY,CACnDnH,QAAS,CAGPib,mBAAoB,eAGtBC,cAAe1Y,SAASiY,cAAc,SAEtC3E,eAAgB,CAKdqF,UAAW,SAAUlU,EAAMjH,GACzB,GAAIA,EAAQuV,SACV,OAAOtO,EAET,IACErG,EACAwa,EAFEjS,EAAOlC,EAAKgC,MAAMhC,EAAKrF,OAG3B,OACE7C,KAAKmc,cAAcN,aACnB7b,KAAKmc,cAAcN,YAAYzR,EAAK5K,QACH,WAAhCmB,EAAEnB,KAAKyB,EAAQwX,cACdrO,EAAKrM,MAAQkD,EAAQwX,gBACrBxX,EAAQuX,WAAavX,EAAQuX,UAAU5W,KAAKwI,EAAK5K,SAEnDqC,EAAM8V,EAAUmE,gBAAgB1R,MAE9BiS,EAAQrc,KAAKmc,cAAcJ,WAAU,IAC/B7B,IAAMrY,EACZwa,EAAML,UAAW,EACjB9T,EAAKmU,MAAQA,EACNnU,GAGJA,GAIToU,SAAU,SAAUpU,EAAMjH,GAIxB,OAHIiH,EAAKmU,QAAUpb,EAAQuV,WACzBtO,EAAKgC,MAAMhC,EAAKrF,OAAO5B,EAAQ8B,MAAQ,WAAamF,EAAKmU,OAEpDnU,SCnFf,SAAW1H,GACT,aACsB,mBAAXJ,QAAyBA,OAAOC,IAEzCD,OAAO,CAAC,SAAU,+BAAgCI,GACtB,iBAAZD,QAEhBC,EAAQC,QAAQ,UAAWA,QAAQ,gCAGnCD,EAAQnD,OAAOqD,QAVnB,EAYG,SAAUC,GACX,aAGAA,EAAEiW,QAAQxO,WAAW5K,UAAUyD,QAAQ4V,aAAa7M,KAAK,CACvDqN,OAAQ,WAGRpJ,QAAQ,EAERsO,gBAAiB,IACjB9D,YAAa,IACb+D,YAAa,IACbC,iBAAkB,IAClBjG,SAAU,uBAKZ7V,EAAEwF,OAAO,qBAAsBxF,EAAEiW,QAAQxO,WAAY,CACnDnH,QAAS,CAePyb,iBAAkB/b,EAAEgc,KAGpBlV,SAAU,CACRgV,iBAAkB,mCAClBF,gBAAiB,wBACjB9D,YAAa,oBACb+D,YAAa,sBAIjBzF,eAAgB,CACd6F,SAAU,SAAU1U,EAAMjH,GACxB,GAAIA,EAAQuV,SACV,OAAOtO,EAGT,IAGEgM,EAHE7F,EAAM1N,EAAE2N,WACV6I,EAAWnX,KAAKiB,QAChBmJ,EAAOlC,EAAKgC,MAAMhC,EAAKrF,OAmCzB,OAjCI5B,EAAQub,aAAevb,EAAQwX,eACjCvE,EAAW9J,EAAKrM,MAGqB,WAArC4C,EAAEnB,KAAKyB,EAAQwb,oBACdtF,EAASuF,oBAAsB,GAAKxU,EAAKgC,MAAM7K,OAC9C4B,EAAQwb,iBAEVrS,EAAK0D,MAAQqJ,EAASzP,KAAK,qBAE3BzG,EAAQsb,iBAENtb,EAAQsb,gBAAgB3a,KAAKwI,EAAK5K,OAClCyB,EAAQsb,gBAAgB3a,KAAKwI,EAAKrH,MAI3BmR,EAAWjT,EAAQwX,YAC5BrO,EAAK0D,MAAQqJ,EAASzP,KAAK,eAEN,WAArB/G,EAAEnB,KAAK0U,IACPA,EAAWjT,EAAQub,YAEnBpS,EAAK0D,MAAQqJ,EAASzP,KAAK,sBAEpB0C,EAAK0D,MATZ1D,EAAK0D,MAAQqJ,EAASzP,KAAK,mBAWzB0C,EAAK0D,OAAS5F,EAAKgC,MAAM4D,OAC3B5F,EAAKgC,MAAM4D,OAAQ,EACnBO,EAAIG,WAAWxO,KAAM,CAACkI,KAEtBmG,EAAIE,YAAYvO,KAAM,CAACkI,IAElBmG,EAAIT,iBCrGnB,SAAWpN,GACT,aACsB,mBAAXJ,QAAyBA,OAAOC,IAEzCD,OAAO,CACL,SACA,eACA,4BACA,4BACA,4BACA,gCACCI,GACyB,iBAAZD,QAEhBC,EACEC,QAAQ,UACRA,QAAQ,gBACRA,QAAQ,6BACRA,QAAQ,6BACRA,QAAQ,6BACRA,QAAQ,iCAIVD,EAAQnD,OAAOqD,OAAQrD,OAAOwf,MAxBlC,EA0BG,SAAUlc,EAAGkc,GACd,aAEAlc,EAAEiW,QAAQxO,WAAW5K,UAAUwL,gBAAgBgB,KAC7C,iBACA,mBACA,sBAKFrJ,EAAEwF,OAAO,qBAAsBxF,EAAEiW,QAAQxO,WAAY,CACnDnH,QAAS,CAIPsG,YAAY,EAEZuV,iBAAkB,KAElBC,iBAAkB,kBAElBC,mBAAoB,oBAGpBC,oBAAgB1a,EAGhB2a,cAAc,EAGdtQ,SAAU,OAGVnF,SAAU,CACR0V,aAAc,iBAKhBT,iBAAkB,WAChB,OAAO1c,KAAKid,eAAeG,WAAWC,IAAI,eAAehe,QAI3Die,qBAAsB,SAAUpV,GAC9B,OAAIA,EAAK4H,QAAUnP,EAAEsB,QAAQiG,EAAK4H,OAAO5F,OAChChC,EAAK4H,OAAO5F,MAEd,IAMTjC,IAAK,SAAUrK,EAAGsK,GAChB,GAAItK,EAAEuK,qBACJ,OAAO,EAET,IAAI2O,EAAQnW,EAAEX,MACZuL,EAAOuL,EAAM5O,KAAK,uBAAyB4O,EAAM5O,KAAK,cACtDjH,EAAUsK,EAAKtK,QACjBiH,EAAKN,QAAU2D,EACZgS,cAAcrV,EAAKgC,OACnBhC,KAAK,OAAQA,GACbsV,SAAS,cACZvc,EAAQgc,eAAehc,EAAQic,aAAe,UAAY,UACxDhV,EAAKN,SAEP2D,EAAKkS,aAAavV,EAAKN,SACvB2D,EAAKmS,YAAYxV,EAAKN,SACtBM,EACGG,SAAQ,WACP,OAAOyO,EAAM1O,WAAW,UAAWF,MAEpC+F,QAAO,WACN/F,EAAKN,QACFhF,MAAK,SAAUC,GACdlC,EAAEX,MACC2V,KAAK,SACL5R,KAAKwH,EAAKoS,gBAAgBzV,EAAKgC,MAAMrH,GAAO9E,UAEhD6f,YAAY,cACfrS,EAAKsS,gBAAgB3V,MAEtBI,MAAK,WACJJ,EAAKN,QAAQ+N,KAAK,gBAAgBjT,KAAK,YAAY,IAEb,IAApC6I,EAAKtG,SAAS,QAASrH,EAAGsK,KACzBjH,EAAQsG,YAAcW,EAAKX,cACR,IAApBW,EAAKX,YAELW,EAAK5E,YAGRyK,MAAK,WACA7F,EAAKgC,MAAM4D,OACb5F,EAAKN,QAAQhF,MAAK,SAAUC,GAC1B,IAAIiL,EAAQ5F,EAAKgC,MAAMrH,GAAOiL,MAC1BA,GACFnN,EAAEX,MAAM2V,KAAK,UAAU5R,KAAK+J,UAOxCvM,KAAM,SAAU3D,EAAGsK,GACjB,GAAItK,EAAEuK,qBACJ,OAAO,EAET,IAAIoD,EACF5K,EAAEX,MAAMkI,KAAK,uBAAyBvH,EAAEX,MAAMkI,KAAK,cAiBrD,OAfEA,EAAKN,SACLM,EAAK0E,UAC0B,WAA/B1E,EAAK0E,SAASkO,OAAO,EAAG,IAKxB5S,EAAKN,QACF+N,KAAK,aACL6H,UAAU7c,EAAE4E,QAAQuY,YAAc,qBAClCpc,KAAK,gBAAiB,KACtB0b,WACAW,QACAC,IAAI,QAAS,QAEXzS,EAAKtG,SAAS,OAAQrH,EAAGsK,IAGlCI,KAAM,SAAU1K,EAAGsK,GACjB,GAAItK,EAAEuK,qBACJ,OAAO,EAET,IAKE8V,EACA1Q,EANEhC,EACA5K,EAAEX,MAAMkI,KAAK,uBAAyBvH,EAAEX,MAAMkI,KAAK,cAGrDgC,GADEhC,EAAKoV,sBAAwB/R,EAAKtK,QAAQqc,sBACfpV,GAG3BA,EAAKN,QACPM,EAAKN,QAAQhF,MAAK,SAAUC,GAC1B,IAAIuH,EAAOF,EAAMrH,IAAU,CAAEiL,MAAO,4BACpCP,EAAWhC,EAAK2S,wBAChB3S,EAAKmS,YAAY/c,EAAEX,OAAOsI,MAAK,WAC7B,IAAI6V,EAAOxd,EAAEX,MACbie,EAAW1S,EAAK6S,gBAAgB,CAAChU,IAAOiU,WAAWF,GACnD5S,EAAKkS,aAAaQ,GAClB1S,EAAKmS,YAAYO,GAAU3V,MAAK,WAC9BJ,EAAKN,QAAUjH,EAAEX,MACjBuL,EAAKtG,SAAS,YAAarH,EAAGsK,GAC9BqD,EAAKtG,SAAS,WAAYrH,EAAGsK,GAC7BqF,EAASuD,oBAKfmN,EAAW1S,EACR6S,gBAAgBlU,GAChBqB,EAAKtK,QAAQic,aAAe,YAAc,YACzC3R,EAAKtK,QAAQgc,gBAEjB1R,EAAKkS,aAAaQ,GAClB1Q,EAAWhC,EAAK2S,wBAChB3S,EAAKmS,YAAYO,GAAU3V,MAAK,WAC9BJ,EAAKN,QAAUjH,EAAEX,MACjBuL,EAAKtG,SAAS,YAAarH,EAAGsK,GAC9BqD,EAAKtG,SAAS,WAAYrH,EAAGsK,GAC7BqF,EAASuD,eAKf/C,KAAM,SAAUnQ,EAAGsK,GACjB,GAAItK,EAAEuK,qBACJ,OAAO,EAET,IAEE8V,EACA1Q,EAHEhC,EACA5K,EAAEX,MAAMkI,KAAK,uBAAyBvH,EAAEX,MAAMkI,KAAK,cAGnDA,EAAKN,QACPM,EAAKN,QAAQhF,MAAK,SAAUC,GAC1B,GAAyB,UAArBqF,EAAK4G,YAAyB,CAChC,IAAI1E,EAAOlC,EAAKgC,MAAMrH,GACtBuH,EAAK0D,MACH1D,EAAK0D,OAAS5F,EAAK4G,aAAe5G,EAAKR,KAAK,gBAC9C6F,EAAWhC,EAAK2S,wBAChB3S,EAAKmS,YAAY/c,EAAEX,OAAOsI,MAAK,WAC7B,IAAI6V,EAAOxd,EAAEX,MACbie,EAAW1S,EAAK6S,gBAAgB,CAAChU,IAAOiU,WAAWF,GACnD5S,EAAKkS,aAAaQ,GAClB1S,EAAKmS,YAAYO,GAAU3V,MAAK,WAC9BJ,EAAKN,QAAUjH,EAAEX,MACjBuL,EAAKtG,SAAS,SAAUrH,EAAGsK,GAC3BqD,EAAKtG,SAAS,WAAYrH,EAAGsK,GAC7BqF,EAASuD,qBAIbvD,EAAWhC,EAAK2S,wBAChB3S,EAAKmS,YAAY/c,EAAEX,OAAOsI,MAAK,WAC7B3H,EAAEX,MAAMyC,SACR8I,EAAKtG,SAAS,SAAUrH,EAAGsK,GAC3BqD,EAAKtG,SAAS,WAAYrH,EAAGsK,GAC7BqF,EAASuD,gBAIe,UAArB5I,EAAK4G,aACd5G,EAAKN,QAAU2D,EACZgS,cAAcrV,EAAKgC,OACnBqB,EAAKtK,QAAQic,aAAe,YAAc,YACzC3R,EAAKtK,QAAQgc,gBAEd/U,KAAK,OAAQA,GAChBqD,EAAKkS,aAAavV,EAAKN,SACvB2F,EAAWhC,EAAK2S,wBAChB3S,EAAKmS,YAAYxV,EAAKN,SAASU,MAAK,WAClCJ,EAAKN,QAAUjH,EAAEX,MACjBuL,EAAKtG,SAAS,SAAUrH,EAAGsK,GAC3BqD,EAAKtG,SAAS,WAAYrH,EAAGsK,GAC7BqF,EAASuD,eAGXvF,EAAKtG,SAAS,SAAUrH,EAAGsK,GAC3BqD,EAAKtG,SAAS,WAAYrH,EAAGsK,GAC7BqD,EAAK2S,wBAAwBpN,YAIjCvG,SAAU,SAAU3M,EAAGsK,GACrB,GAAItK,EAAEuK,qBACJ,OAAO,EAET,IAAIoC,EAAWW,KAAKC,MAAOjD,EAAKuB,OAASvB,EAAKiC,MAAS,KACnDjC,EAAKN,SACPM,EAAKN,QAAQhF,MAAK,WAChBjC,EAAEX,MACC2V,KAAK,aACLjU,KAAK,gBAAiB6I,GACtB6S,WACAW,QACAC,IAAI,QAASzT,EAAW,SAKjC+T,YAAa,SAAU1gB,EAAGsK,GACxB,GAAItK,EAAEuK,qBACJ,OAAO,EAET,IAAI2O,EAAQnW,EAAEX,MACZuK,EAAWW,KAAKC,MAAOjD,EAAKuB,OAASvB,EAAKiC,MAAS,KACnDoU,EAAqBzH,EAAMnB,KAAK,wBAChC6I,EAAuBD,EAAmB5I,KAAK,sBAC7C6I,EAAqBnf,QACvBmf,EAAqBta,MAEjB4S,EAAM5O,KAAK,uBAAyB4O,EAAM5O,KAAK,eAC/CuW,wBAAwBvW,IAG9BqW,EACG5I,KAAK,aACLjU,KAAK,gBAAiB6I,GACtB6S,WACAW,QACAC,IAAI,QAASzT,EAAW,MAG7BmU,MAAO,SAAU9gB,GACf,GAAIA,EAAEuK,qBACJ,OAAO,EAET,IAAIoD,EACF5K,EAAEX,MAAMkI,KAAK,uBAAyBvH,EAAEX,MAAMkI,KAAK,cACrDqD,EAAKoT,0BACLpT,EACGmS,YAAY/c,EAAEX,MAAM2V,KAAK,yBACzBrN,MAAK,WACJiD,EAAKtG,SAAS,UAAWrH,OAI/BghB,KAAM,SAAUhhB,GACd,GAAIA,EAAEuK,qBACJ,OAAO,EAET,IAAIoD,EACA5K,EAAEX,MAAMkI,KAAK,uBAAyBvH,EAAEX,MAAMkI,KAAK,cACrDqF,EAAWhC,EAAK2S,wBAClBvd,EAAE6S,KAAKtK,MAAMvI,EAAG4K,EAAKsT,yBAAyBvW,MAAK,WACjDiD,EAAKtG,SAAS,UAAWrH,MAE3B2N,EACGmS,YAAY/c,EAAEX,MAAM2V,KAAK,yBACzBrN,MAAK,WACJ3H,EAAEX,MACC2V,KAAK,aACLjU,KAAK,gBAAiB,KACtB0b,WACAW,QACAC,IAAI,QAAS,MAChBrd,EAAEX,MAAM2V,KAAK,sBAAsBzR,KAAK,UACxCqJ,EAASuD,cAGfgO,aAAc,SAAUlhB,GACtB,GAAIA,EAAEuK,qBACJ,OAAO,EAETxH,EAAEX,MAAMwd,SAAS,0BAEnBuB,YAAa,SAAUnhB,GACrB,GAAIA,EAAEuK,qBACJ,OAAO,EAETxH,EAAEX,MAAM4d,YAAY,0BAGtBoB,QAAS,SAAUphB,EAAGsK,GACpB,GAAItK,EAAEuK,qBACJ,OAAO,EAET,IAAIoD,EACA5K,EAAEX,MAAMkI,KAAK,uBAAyBvH,EAAEX,MAAMkI,KAAK,cACrD+W,EAAa,WACX1T,EAAKmS,YAAYxV,EAAKN,SAASU,MAAK,WAClC3H,EAAEX,MAAMyC,SACR8I,EAAKtG,SAAS,YAAarH,EAAGsK,OAGhCA,EAAKrG,KACPqG,EAAK0E,SAAW1E,EAAK0E,UAAYrB,EAAKtK,QAAQ2L,SAC9CjM,EAAEkP,KAAK3H,GACJI,KAAK2W,GACLlR,MAAK,WACJxC,EAAKtG,SAAS,gBAAiBrH,EAAGsK,OAGtC+W,MAKNN,wBAAyB,WACvB3e,KAAKkf,iBAAmB,IAG1BhB,sBAAuB,SAAU3Q,GAE/B,IAAIK,EAAUL,GAAY5M,EAAE2N,WAE5B,OADAtO,KAAKkf,iBAAiBlV,KAAK4D,GACpBA,GAGTiR,sBAAuB,WACrB,OAAO7e,KAAKkf,kBAKdC,qBAAsB,WACpB,IAAIC,EAAOze,EAAEX,MACX6B,EAAMud,EAAK1c,KAAK,QAChBK,EAAOqc,EAAK1c,KAAK,YACjBlD,EAAO,2BACT4f,EAAKtd,GAAG,aAAa,SAAUlE,GAC7B,IACEA,EAAEkH,cAAcD,aAAawa,QAC3B,cACA,CAAC7f,EAAMuD,EAAMlB,GAAKmU,KAAK,MAEzB,MAAOsJ,SAMb3B,gBAAiB,SAAU4B,GACzB,MAAqB,iBAAVA,EACF,GAELA,GAAS,KACHA,EAAQ,KAAYC,QAAQ,GAAK,MAEvCD,GAAS,KACHA,EAAQ,KAASC,QAAQ,GAAK,OAEhCD,EAAQ,KAAMC,QAAQ,GAAK,OAGrCC,eAAgB,SAAUC,GACxB,MAAoB,iBAATA,EACF,GAELA,GAAQ,KACFA,EAAO,KAAYF,QAAQ,GAAK,UAEtCE,GAAQ,KACFA,EAAO,KAASF,QAAQ,GAAK,UAEnCE,GAAQ,KACFA,EAAO,KAAMF,QAAQ,GAAK,UAE7BE,EAAKF,QAAQ,GAAK,UAG3BG,YAAa,SAAUC,GACrB,IAAIC,EAAO,IAAIvW,KAAe,IAAVsW,GAClBE,EAAO5U,KAAKC,MAAMyU,EAAU,OAE9B,OADAE,EAAOA,EAAOA,EAAO,KAAO,KAGzB,IAAMD,EAAKE,eAAe3gB,OAAO,GAClC,KACC,IAAMygB,EAAKG,iBAAiB5gB,OAAO,GACpC,KACC,IAAMygB,EAAKI,iBAAiB7gB,OAAO,IAIxC8gB,kBAAmB,SAAUC,GAC3B,OAAqB,IAAbA,GAAkBX,QAAQ,GAAK,MAGzCf,wBAAyB,SAAUvW,GACjC,OACElI,KAAKyf,eAAevX,EAAKwB,SACzB,MACA1J,KAAK2f,YAA0C,GAA5BzX,EAAKiC,MAAQjC,EAAKuB,QAAevB,EAAKwB,SACzD,MACA1J,KAAKkgB,kBAAkBhY,EAAKuB,OAASvB,EAAKiC,OAC1C,MACAnK,KAAK2d,gBAAgBzV,EAAKuB,QAC1B,MACAzJ,KAAK2d,gBAAgBzV,EAAKiC,QAI9BiW,gBAAiB,SAAUhJ,EAAMlN,GAC/B,IAAKkN,EACH,OAAOzW,IAET,IAAImP,EAASsH,EAAK,CAChBlN,MAAOA,EACPmW,eAAgBrgB,KAAK2d,gBACrB1c,QAASjB,KAAKiB,UAEhB,OAAI6O,aAAkBnP,EACbmP,EAEFnP,EAAEX,KAAKiB,QAAQqf,oBAAoBpc,KAAK4L,GAAQsN,YAGzDS,gBAAiB,SAAU3V,GACzBA,EAAKN,QAAQ+N,KAAK,YAAY/S,MAAK,SAAUC,EAAO0d,GAClD5f,EAAE4f,GAAKC,QAAQ/gB,OAAOyI,EAAKgC,MAAMrH,GAAO4X,aAI5C8C,cAAe,SAAUrT,GACvB,OAAOlK,KAAKogB,gBAAgBpgB,KAAKiB,QAAQwf,eAAgBvW,IAG3DkU,gBAAiB,SAAUlU,GACzB,OAAOlK,KAAKogB,gBAAgBpgB,KAAKiB,QAAQyf,iBAAkBxW,GACxDyL,KAAK,eACL/S,KAAK5C,KAAKmf,sBACVwB,OAGLC,aAAc,SAAUhjB,GAEtB,GADAA,EAAEwH,iBACGpF,KAAKiB,QAAQ4f,KAAlB,CACA,IAAItV,EAAOvL,KACT8gB,EAASngB,EAAE/C,EAAEmjB,eACb9C,EAAW6C,EAAOE,QAAQ,oBAC1B9Y,EAAO+V,EAAS/V,KAAK,QACrBrF,EAAQie,EAAO5Y,OAAOrF,MACxB7C,KAAKiB,QAAQ4f,KAAK3Y,EAAKgC,MAAMrH,IAAQ6T,MAAK,SAAUtM,GAC7CA,IACLlC,EAAKgC,MAAMrH,GAASuH,EACpBlC,EAAKN,QAAQ4V,SAAS,cACtBS,EAAStI,KAAK,gBAAgBjT,KAAK,YAAY,GAC/C/B,EAAE4K,EAAKoG,SACJvJ,WAAW,UAAWF,GACtB+F,QAAO,WACNgQ,EACGtI,KAAK,SACL5R,KAAKwH,EAAKoS,gBAAgBzV,EAAKgC,MAAMrH,GAAO9E,OAC/CmK,EAAKN,QAAQgW,YAAY,cACzBrS,EAAKsS,gBAAgB3V,MAEtBI,MAAK,WACJ2V,EAAStI,KAAK,gBAAgBjT,KAAK,YAAY,MAEhDqL,MAAK,WACJkQ,EAAStI,KAAK,SAASjT,KAAK,YAAY,GACxC,IAAIoL,EAAQ5F,EAAKgC,MAAMrH,GAAOiL,MAC1BA,GACFmQ,EAAStI,KAAK,UAAU5R,KAAK+J,YAMvCmT,cAAe,SAAUrjB,GACvBA,EAAEwH,iBACF,IAAI0b,EAASngB,EAAE/C,EAAEmjB,eAEf7Y,EADW4Y,EAAOE,QAAQ,oBACV9Y,KAAK,QACvB4Y,EAAOpe,KAAK,YAAY,GACpBwF,GAAQA,EAAK5E,QACf4E,EAAK5E,UAIT4d,eAAgB,SAAUtjB,GACxBA,EAAEwH,iBACF,IAAI6Y,EAAWtd,EAAE/C,EAAEmjB,eAAeC,QAC9B,uCAEF9Y,EAAO+V,EAAS/V,KAAK,SAAW,GAClCA,EAAKN,QAAUM,EAAKN,SAAWqW,EAC3B/V,EAAKvE,MACPuE,EAAKvE,SAELuE,EAAK4G,YAAc,QACnB9O,KAAKiF,SAAS,OAAQrH,EAAGsK,KAI7BiZ,eAAgB,SAAUvjB,GACxBA,EAAEwH,iBACF,IAAI0b,EAASngB,EAAE/C,EAAEmjB,eACjB/gB,KAAKiF,SACH,UACArH,EACA+C,EAAE8J,OACA,CACE7C,QAASkZ,EAAOE,QAAQ,sBACxBxhB,KAAM,UAERshB,EAAO5Y,UAKbuV,aAAc,SAAUU,GACtB,OAAOxd,EAAE4E,QAAQuY,YAAcK,EAAK9e,QAAU8e,EAAK,GAAGiD,aAGxD1D,YAAa,SAAUS,GAErB,IAAI9P,EAAM1N,EAAE2N,WACZ,GACE3N,EAAE4E,QAAQuY,YACVK,EAAKkD,SAAS,SACdlD,EAAKnM,GAAG,YACR,CACA,IAAIsP,EAAuB,SAAU1jB,GAG/BA,EAAEyW,SAAW8J,EAAK,KACpBA,EAAKhc,IAAIxB,EAAE4E,QAAQuY,WAAW6C,IAAKW,GACnCjT,EAAIE,YAAY4P,KAGpBA,EACGrc,GAAGnB,EAAE4E,QAAQuY,WAAW6C,IAAKW,GAC7BC,YAAYvhB,KAAKiB,QAAQ6b,uBAE5BqB,EAAKoD,YAAYvhB,KAAKiB,QAAQ6b,kBAC9BzO,EAAIE,YAAY4P,GAElB,OAAO9P,GAGTmT,4BAA6B,WAC3B,IAAIC,EAAsBzhB,KAAK2R,QAAQgE,KAAK,yBAC1C+L,EAAY1hB,KAAKiB,QAAQgc,eAC3Bjd,KAAK6U,IAAI4M,EAAoB9L,KAAK,UAAW,CAC3CgM,MAAO,SAAU/jB,GACfA,EAAEwH,iBACFsc,EAAU/L,KAAK,UAAUtD,QAAQ,YAGrCrS,KAAK6U,IAAI4M,EAAoB9L,KAAK,WAAY,CAC5CgM,MAAO,SAAU/jB,GACfA,EAAEwH,iBACFsc,EAAU/L,KAAK,WAAWtD,QAAQ,YAGtCrS,KAAK6U,IAAI4M,EAAoB9L,KAAK,WAAY,CAC5CgM,MAAO,SAAU/jB,GACfA,EAAEwH,iBACFsc,EACG/L,KAAK,mBACLqL,QAAQ,sBACRrL,KAAK,WACLtD,QAAQ,SACXoP,EAAoB9L,KAAK,WAAWjT,KAAK,WAAW,MAGxD1C,KAAK6U,IAAI4M,EAAoB9L,KAAK,WAAY,CAC5CR,OAAQ,SAAUvX,GAChB8jB,EACG/L,KAAK,WACLjT,KAAK,UAAW/B,EAAE/C,EAAEmjB,eAAe/O,GAAG,iBAK/C4P,+BAAgC,WAC9B5hB,KAAKqV,KACHrV,KAAK2R,QACFgE,KAAK,yBACLA,KAAK,4BACR,SAEF3V,KAAKqV,KAAKrV,KAAK2R,QAAQgE,KAAK,iCAAkC,YAGhEf,mBAAoB,WAClB5U,KAAKyV,SACLzV,KAAK6U,IAAI7U,KAAKiB,QAAQgc,eAAgB,CACpC4E,cAAe7hB,KAAK4gB,aACpBkB,eAAgB9hB,KAAKihB,cACrBc,gBAAiB/hB,KAAKkhB,eACtBc,gBAAiBhiB,KAAKmhB,iBAExBnhB,KAAKwhB,+BAGPpM,sBAAuB,WACrBpV,KAAK4hB,iCACL5hB,KAAKqV,KAAKrV,KAAKiB,QAAQgc,eAAgB,SACvCjd,KAAKyV,UAGPwM,uBAAwB,WACtBjiB,KAAK2R,QACFgE,KAAK,2BACLjT,KAAK,YAAY,GACjBwf,SACAtE,YAAY,aAGjBuE,wBAAyB,WACvBniB,KAAK2R,QACFgE,KAAK,2BACLjT,KAAK,YAAY,GACjBwf,SACA1E,SAAS,aAGd4E,eAAgB,WACd,IAAInhB,EAAUjB,KAAKiB,QACnBA,EAAQqf,mBAAqBtgB,KAAKyD,SAAS,GAAGiY,cAC5Cza,EAAQgc,eAAeva,KAAK,aAE1Bma,IACE5b,EAAQ8b,mBACV9b,EAAQwf,eAAiB5D,EAAK5b,EAAQ8b,mBAEpC9b,EAAQ+b,qBACV/b,EAAQyf,iBAAmB7D,EAAK5b,EAAQ+b,uBAK9CqF,oBAAqB,WACnB,IAAIphB,EAAUjB,KAAKiB,aACYsB,IAA3BtB,EAAQgc,eACVhc,EAAQgc,eAAiBjd,KAAK2R,QAAQgE,KAAK,UAChC1U,EAAQgc,0BAA0Btc,IAC7CM,EAAQgc,eAAiBtc,EAAEM,EAAQgc,kBAIvCvH,oBAAqB,WACnB1V,KAAKyV,SACLzV,KAAKqiB,sBACLriB,KAAKoiB,kBAGP9L,QAAS,WACPtW,KAAKyV,SACLzV,KAAK2e,0BACAhe,EAAE4E,QAAQrC,WACblD,KAAKmiB,2BAITG,OAAQ,WACN,IAAIC,GAAc,EACdviB,KAAKiB,QAAQuV,WACf+L,GAAc,GAEhBviB,KAAKyV,SACD8M,IACFviB,KAAK2R,QAAQgE,KAAK,iBAAiBjT,KAAK,YAAY,GACpD1C,KAAKiiB,2BAITO,QAAS,WACFxiB,KAAKiB,QAAQuV,WAChBxW,KAAK2R,QAAQgE,KAAK,iBAAiBjT,KAAK,YAAY,GACpD1C,KAAKmiB,2BAEPniB,KAAKyV", "sourcesContent": ["/*\n * JavaScript Canvas to Blob\n * https://github.com/blueimp/JavaScript-Canvas-to-Blob\n *\n * Copyright 2012, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n *\n * Based on stackoverflow user Stoive's code snippet:\n * http://stackoverflow.com/q/4998908\n */\n\n/* global define, Uint8Array, ArrayBuffer, module */\n\n;(function (window) {\n  'use strict'\n\n  var CanvasPrototype =\n    window.HTMLCanvasElement && window.HTMLCanvasElement.prototype\n  var hasBlobConstructor =\n    window.Blob &&\n    (function () {\n      try {\n        return Boolean(new Blob())\n      } catch (e) {\n        return false\n      }\n    })()\n  var hasArrayBufferViewSupport =\n    hasBlobConstructor &&\n    window.Uint8Array &&\n    (function () {\n      try {\n        return new Blob([new Uint8Array(100)]).size === 100\n      } catch (e) {\n        return false\n      }\n    })()\n  var BlobBuilder =\n    window.BlobBuilder ||\n    window.WebKitBlobBuilder ||\n    window.MozBlobBuilder ||\n    window.MSBlobBuilder\n  var dataURIPattern = /^data:((.*?)(;charset=.*?)?)(;base64)?,/\n  var dataURLtoBlob =\n    (hasBlobConstructor || BlobBuilder) &&\n    window.atob &&\n    window.ArrayBuffer &&\n    window.Uint8Array &&\n    function (dataURI) {\n      var matches,\n        mediaType,\n        isBase64,\n        dataString,\n        byteString,\n        arrayBuffer,\n        intArray,\n        i,\n        bb\n      // Parse the dataURI components as per RFC 2397\n      matches = dataURI.match(dataURIPattern)\n      if (!matches) {\n        throw new Error('invalid data URI')\n      }\n      // Default to text/plain;charset=US-ASCII\n      mediaType = matches[2]\n        ? matches[1]\n        : 'text/plain' + (matches[3] || ';charset=US-ASCII')\n      isBase64 = !!matches[4]\n      dataString = dataURI.slice(matches[0].length)\n      if (isBase64) {\n        // Convert base64 to raw binary data held in a string:\n        byteString = atob(dataString)\n      } else {\n        // Convert base64/URLEncoded data component to raw binary:\n        byteString = decodeURIComponent(dataString)\n      }\n      // Write the bytes of the string to an ArrayBuffer:\n      arrayBuffer = new ArrayBuffer(byteString.length)\n      intArray = new Uint8Array(arrayBuffer)\n      for (i = 0; i < byteString.length; i += 1) {\n        intArray[i] = byteString.charCodeAt(i)\n      }\n      // Write the ArrayBuffer (or ArrayBufferView) to a blob:\n      if (hasBlobConstructor) {\n        return new Blob([hasArrayBufferViewSupport ? intArray : arrayBuffer], {\n          type: mediaType\n        })\n      }\n      bb = new BlobBuilder()\n      bb.append(arrayBuffer)\n      return bb.getBlob(mediaType)\n    }\n  if (window.HTMLCanvasElement && !CanvasPrototype.toBlob) {\n    if (CanvasPrototype.mozGetAsFile) {\n      CanvasPrototype.toBlob = function (callback, type, quality) {\n        var self = this\n        setTimeout(function () {\n          if (quality && CanvasPrototype.toDataURL && dataURLtoBlob) {\n            callback(dataURLtoBlob(self.toDataURL(type, quality)))\n          } else {\n            callback(self.mozGetAsFile('blob', type))\n          }\n        })\n      }\n    } else if (CanvasPrototype.toDataURL && dataURLtoBlob) {\n      if (CanvasPrototype.msToBlob) {\n        CanvasPrototype.toBlob = function (callback, type, quality) {\n          var self = this\n          setTimeout(function () {\n            if (\n              ((type && type !== 'image/png') || quality) &&\n              CanvasPrototype.toDataURL &&\n              dataURLtoBlob\n            ) {\n              callback(dataURLtoBlob(self.toDataURL(type, quality)))\n            } else {\n              callback(self.msToBlob(type))\n            }\n          })\n        }\n      } else {\n        CanvasPrototype.toBlob = function (callback, type, quality) {\n          var self = this\n          setTimeout(function () {\n            callback(dataURLtoBlob(self.toDataURL(type, quality)))\n          })\n        }\n      }\n    }\n  }\n  if (typeof define === 'function' && define.amd) {\n    define(function () {\n      return dataURLtoBlob\n    })\n  } else if (typeof module === 'object' && module.exports) {\n    module.exports = dataURLtoBlob\n  } else {\n    window.dataURLtoBlob = dataURLtoBlob\n  }\n})(window)\n", "/*\n * jQuery Iframe Transport Plugin\n * https://github.com/blueimp/jQuery-File-Upload\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, require */\n\n(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['jquery'], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS:\n    factory(require('jquery'));\n  } else {\n    // Browser globals:\n    factory(window.jQuery);\n  }\n})(function ($) {\n  'use strict';\n\n  // Helper variable to create unique names for the transport iframes:\n  var counter = 0,\n    jsonAPI = $,\n    jsonParse = 'parseJSON';\n\n  if ('JSON' in window && 'parse' in JSON) {\n    jsonAPI = JSON;\n    jsonParse = 'parse';\n  }\n\n  // The iframe transport accepts four additional options:\n  // options.fileInput: a jQuery collection of file input fields\n  // options.paramName: the parameter name for the file form data,\n  //  overrides the name property of the file input field(s),\n  //  can be a string or an array of strings.\n  // options.formData: an array of objects with name and value properties,\n  //  equivalent to the return data of .serializeArray(), e.g.:\n  //  [{name: 'a', value: 1}, {name: 'b', value: 2}]\n  // options.initialIframeSrc: the URL of the initial iframe src,\n  //  by default set to \"javascript:false;\"\n  $.ajaxTransport('iframe', function (options) {\n    if (options.async) {\n      // javascript:false as initial iframe src\n      // prevents warning popups on HTTPS in IE6:\n      // eslint-disable-next-line no-script-url\n      var initialIframeSrc = options.initialIframeSrc || 'javascript:false;',\n        form,\n        iframe,\n        addParamChar;\n      return {\n        send: function (_, completeCallback) {\n          form = $('<form style=\"display:none;\"></form>');\n          form.attr('accept-charset', options.formAcceptCharset);\n          addParamChar = /\\?/.test(options.url) ? '&' : '?';\n          // XDomainRequest only supports GET and POST:\n          if (options.type === 'DELETE') {\n            options.url = options.url + addParamChar + '_method=DELETE';\n            options.type = 'POST';\n          } else if (options.type === 'PUT') {\n            options.url = options.url + addParamChar + '_method=PUT';\n            options.type = 'POST';\n          } else if (options.type === 'PATCH') {\n            options.url = options.url + addParamChar + '_method=PATCH';\n            options.type = 'POST';\n          }\n          // IE versions below IE8 cannot set the name property of\n          // elements that have already been added to the DOM,\n          // so we set the name along with the iframe HTML markup:\n          counter += 1;\n          iframe = $(\n            '<iframe src=\"' +\n              initialIframeSrc +\n              '\" name=\"iframe-transport-' +\n              counter +\n              '\"></iframe>'\n          ).on('load', function () {\n            var fileInputClones,\n              paramNames = $.isArray(options.paramName)\n                ? options.paramName\n                : [options.paramName];\n            iframe.off('load').on('load', function () {\n              var response;\n              // Wrap in a try/catch block to catch exceptions thrown\n              // when trying to access cross-domain iframe contents:\n              try {\n                response = iframe.contents();\n                // Google Chrome and Firefox do not throw an\n                // exception when calling iframe.contents() on\n                // cross-domain requests, so we unify the response:\n                if (!response.length || !response[0].firstChild) {\n                  throw new Error();\n                }\n              } catch (e) {\n                response = undefined;\n              }\n              // The complete callback returns the\n              // iframe content document as response object:\n              completeCallback(200, 'success', { iframe: response });\n              // Fix for IE endless progress bar activity bug\n              // (happens on form submits to iframe targets):\n              $('<iframe src=\"' + initialIframeSrc + '\"></iframe>').appendTo(\n                form\n              );\n              window.setTimeout(function () {\n                // Removing the form in a setTimeout call\n                // allows Chrome's developer tools to display\n                // the response result\n                form.remove();\n              }, 0);\n            });\n            form\n              .prop('target', iframe.prop('name'))\n              .prop('action', options.url)\n              .prop('method', options.type);\n            if (options.formData) {\n              $.each(options.formData, function (index, field) {\n                $('<input type=\"hidden\"/>')\n                  .prop('name', field.name)\n                  .val(field.value)\n                  .appendTo(form);\n              });\n            }\n            if (\n              options.fileInput &&\n              options.fileInput.length &&\n              options.type === 'POST'\n            ) {\n              fileInputClones = options.fileInput.clone();\n              // Insert a clone for each file input field:\n              options.fileInput.after(function (index) {\n                return fileInputClones[index];\n              });\n              if (options.paramName) {\n                options.fileInput.each(function (index) {\n                  $(this).prop('name', paramNames[index] || options.paramName);\n                });\n              }\n              // Appending the file input fields to the hidden form\n              // removes them from their original location:\n              form\n                .append(options.fileInput)\n                .prop('enctype', 'multipart/form-data')\n                // enctype must be set as encoding for IE:\n                .prop('encoding', 'multipart/form-data');\n              // Remove the HTML5 form attribute from the input(s):\n              options.fileInput.removeAttr('form');\n            }\n            window.setTimeout(function () {\n              // Submitting the form in a setTimeout call fixes an issue with\n              // Safari 13 not triggering the iframe load event after resetting\n              // the load event handler, see also:\n              // https://github.com/blueimp/jQuery-File-Upload/issues/3633\n              form.submit();\n              // Insert the file input fields at their original location\n              // by replacing the clones with the originals:\n              if (fileInputClones && fileInputClones.length) {\n                options.fileInput.each(function (index, input) {\n                  var clone = $(fileInputClones[index]);\n                  // Restore the original name and form properties:\n                  $(input)\n                    .prop('name', clone.prop('name'))\n                    .attr('form', clone.attr('form'));\n                  clone.replaceWith(input);\n                });\n              }\n            }, 0);\n          });\n          form.append(iframe).appendTo(document.body);\n        },\n        abort: function () {\n          if (iframe) {\n            // javascript:false as iframe src aborts the request\n            // and prevents warning popups on HTTPS in IE6.\n            iframe.off('load').prop('src', initialIframeSrc);\n          }\n          if (form) {\n            form.remove();\n          }\n        }\n      };\n    }\n  });\n\n  // The iframe transport returns the iframe content document as response.\n  // The following adds converters from iframe to text, json, html, xml\n  // and script.\n  // Please note that the Content-Type for JSON responses has to be text/plain\n  // or text/html, if the browser doesn't include application/json in the\n  // Accept header, else IE will show a download dialog.\n  // The Content-Type for XML responses on the other hand has to be always\n  // application/xml or text/xml, so IE properly parses the XML response.\n  // See also\n  // https://github.com/blueimp/jQuery-File-Upload/wiki/Setup#content-type-negotiation\n  $.ajaxSetup({\n    converters: {\n      'iframe text': function (iframe) {\n        return iframe && $(iframe[0].body).text();\n      },\n      'iframe json': function (iframe) {\n        return iframe && jsonAPI[jsonParse]($(iframe[0].body).text());\n      },\n      'iframe html': function (iframe) {\n        return iframe && $(iframe[0].body).html();\n      },\n      'iframe xml': function (iframe) {\n        var xmlDoc = iframe && iframe[0];\n        return xmlDoc && $.isXMLDoc(xmlDoc)\n          ? xmlDoc\n          : $.parseXML(\n              (xmlDoc.XMLDocument && xmlDoc.XMLDocument.xml) ||\n                $(xmlDoc.body).html()\n            );\n      },\n      'iframe script': function (iframe) {\n        return iframe && $.globalEval($(iframe[0].body).text());\n      }\n    }\n  });\n});\n", "/*\n * jQuery File Upload Plugin\n * https://github.com/blueimp/jQuery-File-Upload\n *\n * Copyright 2010, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, require */\n/* eslint-disable new-cap */\n\n(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['jquery', 'jquery-ui/ui/widget'], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS:\n    factory(require('jquery'), require('./vendor/jquery.ui.widget'));\n  } else {\n    // Browser globals:\n    factory(window.jQuery);\n  }\n})(function ($) {\n  'use strict';\n\n  // Detect file input support, based on\n  // https://viljamis.com/2012/file-upload-support-on-mobile/\n  $.support.fileInput = !(\n    new RegExp(\n      // Handle devices which give false positives for the feature detection:\n      '(Android (1\\\\.[0156]|2\\\\.[01]))' +\n        '|(Windows Phone (OS 7|8\\\\.0))|(XBLWP)|(ZuneWP)|(WPDesktop)' +\n        '|(w(eb)?OSBrowser)|(webOS)' +\n        '|(Kindle/(1\\\\.0|2\\\\.[05]|3\\\\.0))'\n    ).test(window.navigator.userAgent) ||\n    // Feature detection for all other devices:\n    $('<input type=\"file\"/>').prop('disabled')\n  );\n\n  // The FileReader API is not actually used, but works as feature detection,\n  // as some Safari versions (5?) support XHR file uploads via the FormData API,\n  // but not non-multipart XHR file uploads.\n  // window.XMLHttpRequestUpload is not available on IE10, so we check for\n  // window.ProgressEvent instead to detect XHR2 file upload capability:\n  $.support.xhrFileUpload = !!(window.ProgressEvent && window.FileReader);\n  $.support.xhrFormDataFileUpload = !!window.FormData;\n\n  // Detect support for Blob slicing (required for chunked uploads):\n  $.support.blobSlice =\n    window.Blob &&\n    (Blob.prototype.slice ||\n      Blob.prototype.webkitSlice ||\n      Blob.prototype.mozSlice);\n\n  /**\n   * Helper function to create drag handlers for dragover/dragenter/dragleave\n   *\n   * @param {string} type Event type\n   * @returns {Function} Drag handler\n   */\n  function getDragHandler(type) {\n    var isDragOver = type === 'dragover';\n    return function (e) {\n      e.dataTransfer = e.originalEvent && e.originalEvent.dataTransfer;\n      var dataTransfer = e.dataTransfer;\n      if (\n        dataTransfer &&\n        $.inArray('Files', dataTransfer.types) !== -1 &&\n        this._trigger(type, $.Event(type, { delegatedEvent: e })) !== false\n      ) {\n        e.preventDefault();\n        if (isDragOver) {\n          dataTransfer.dropEffect = 'copy';\n        }\n      }\n    };\n  }\n\n  // The fileupload widget listens for change events on file input fields defined\n  // via fileInput setting and paste or drop events of the given dropZone.\n  // In addition to the default jQuery Widget methods, the fileupload widget\n  // exposes the \"add\" and \"send\" methods, to add or directly send files using\n  // the fileupload API.\n  // By default, files added via file input selection, paste, drag & drop or\n  // \"add\" method are uploaded immediately, but it is possible to override\n  // the \"add\" callback option to queue file uploads.\n  $.widget('blueimp.fileupload', {\n    options: {\n      // The drop target element(s), by the default the complete document.\n      // Set to null to disable drag & drop support:\n      dropZone: $(document),\n      // The paste target element(s), by the default undefined.\n      // Set to a DOM node or jQuery object to enable file pasting:\n      pasteZone: undefined,\n      // The file input field(s), that are listened to for change events.\n      // If undefined, it is set to the file input fields inside\n      // of the widget element on plugin initialization.\n      // Set to null to disable the change listener.\n      fileInput: undefined,\n      // By default, the file input field is replaced with a clone after\n      // each input field change event. This is required for iframe transport\n      // queues and allows change events to be fired for the same file\n      // selection, but can be disabled by setting the following option to false:\n      replaceFileInput: true,\n      // The parameter name for the file form data (the request argument name).\n      // If undefined or empty, the name property of the file input field is\n      // used, or \"files[]\" if the file input name property is also empty,\n      // can be a string or an array of strings:\n      paramName: undefined,\n      // By default, each file of a selection is uploaded using an individual\n      // request for XHR type uploads. Set to false to upload file\n      // selections in one request each:\n      singleFileUploads: true,\n      // To limit the number of files uploaded with one XHR request,\n      // set the following option to an integer greater than 0:\n      limitMultiFileUploads: undefined,\n      // The following option limits the number of files uploaded with one\n      // XHR request to keep the request size under or equal to the defined\n      // limit in bytes:\n      limitMultiFileUploadSize: undefined,\n      // Multipart file uploads add a number of bytes to each uploaded file,\n      // therefore the following option adds an overhead for each file used\n      // in the limitMultiFileUploadSize configuration:\n      limitMultiFileUploadSizeOverhead: 512,\n      // Set the following option to true to issue all file upload requests\n      // in a sequential order:\n      sequentialUploads: false,\n      // To limit the number of concurrent uploads,\n      // set the following option to an integer greater than 0:\n      limitConcurrentUploads: undefined,\n      // Set the following option to true to force iframe transport uploads:\n      forceIframeTransport: false,\n      // Set the following option to the location of a redirect url on the\n      // origin server, for cross-domain iframe transport uploads:\n      redirect: undefined,\n      // The parameter name for the redirect url, sent as part of the form\n      // data and set to 'redirect' if this option is empty:\n      redirectParamName: undefined,\n      // Set the following option to the location of a postMessage window,\n      // to enable postMessage transport uploads:\n      postMessage: undefined,\n      // By default, XHR file uploads are sent as multipart/form-data.\n      // The iframe transport is always using multipart/form-data.\n      // Set to false to enable non-multipart XHR uploads:\n      multipart: true,\n      // To upload large files in smaller chunks, set the following option\n      // to a preferred maximum chunk size. If set to 0, null or undefined,\n      // or the browser does not support the required Blob API, files will\n      // be uploaded as a whole.\n      maxChunkSize: undefined,\n      // When a non-multipart upload or a chunked multipart upload has been\n      // aborted, this option can be used to resume the upload by setting\n      // it to the size of the already uploaded bytes. This option is most\n      // useful when modifying the options object inside of the \"add\" or\n      // \"send\" callbacks, as the options are cloned for each file upload.\n      uploadedBytes: undefined,\n      // By default, failed (abort or error) file uploads are removed from the\n      // global progress calculation. Set the following option to false to\n      // prevent recalculating the global progress data:\n      recalculateProgress: true,\n      // Interval in milliseconds to calculate and trigger progress events:\n      progressInterval: 100,\n      // Interval in milliseconds to calculate progress bitrate:\n      bitrateInterval: 500,\n      // By default, uploads are started automatically when adding files:\n      autoUpload: true,\n      // By default, duplicate file names are expected to be handled on\n      // the server-side. If this is not possible (e.g. when uploading\n      // files directly to Amazon S3), the following option can be set to\n      // an empty object or an object mapping existing filenames, e.g.:\n      // { \"image.jpg\": true, \"image (1).jpg\": true }\n      // If it is set, all files will be uploaded with unique filenames,\n      // adding increasing number suffixes if necessary, e.g.:\n      // \"image (2).jpg\"\n      uniqueFilenames: undefined,\n\n      // Error and info messages:\n      messages: {\n        uploadedBytes: 'Uploaded bytes exceed file size'\n      },\n\n      // Translation function, gets the message key to be translated\n      // and an object with context specific data as arguments:\n      i18n: function (message, context) {\n        // eslint-disable-next-line no-param-reassign\n        message = this.messages[message] || message.toString();\n        if (context) {\n          $.each(context, function (key, value) {\n            // eslint-disable-next-line no-param-reassign\n            message = message.replace('{' + key + '}', value);\n          });\n        }\n        return message;\n      },\n\n      // Additional form data to be sent along with the file uploads can be set\n      // using this option, which accepts an array of objects with name and\n      // value properties, a function returning such an array, a FormData\n      // object (for XHR file uploads), or a simple object.\n      // The form of the first fileInput is given as parameter to the function:\n      formData: function (form) {\n        return form.serializeArray();\n      },\n\n      // The add callback is invoked as soon as files are added to the fileupload\n      // widget (via file input selection, drag & drop, paste or add API call).\n      // If the singleFileUploads option is enabled, this callback will be\n      // called once for each file in the selection for XHR file uploads, else\n      // once for each file selection.\n      //\n      // The upload starts when the submit method is invoked on the data parameter.\n      // The data object contains a files property holding the added files\n      // and allows you to override plugin options as well as define ajax settings.\n      //\n      // Listeners for this callback can also be bound the following way:\n      // .on('fileuploadadd', func);\n      //\n      // data.submit() returns a Promise object and allows to attach additional\n      // handlers using jQuery's Deferred callbacks:\n      // data.submit().done(func).fail(func).always(func);\n      add: function (e, data) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        if (\n          data.autoUpload ||\n          (data.autoUpload !== false &&\n            $(this).fileupload('option', 'autoUpload'))\n        ) {\n          data.process().done(function () {\n            data.submit();\n          });\n        }\n      },\n\n      // Other callbacks:\n\n      // Callback for the submit event of each file upload:\n      // submit: function (e, data) {}, // .on('fileuploadsubmit', func);\n\n      // Callback for the start of each file upload request:\n      // send: function (e, data) {}, // .on('fileuploadsend', func);\n\n      // Callback for successful uploads:\n      // done: function (e, data) {}, // .on('fileuploaddone', func);\n\n      // Callback for failed (abort or error) uploads:\n      // fail: function (e, data) {}, // .on('fileuploadfail', func);\n\n      // Callback for completed (success, abort or error) requests:\n      // always: function (e, data) {}, // .on('fileuploadalways', func);\n\n      // Callback for upload progress events:\n      // progress: function (e, data) {}, // .on('fileuploadprogress', func);\n\n      // Callback for global upload progress events:\n      // progressall: function (e, data) {}, // .on('fileuploadprogressall', func);\n\n      // Callback for uploads start, equivalent to the global ajaxStart event:\n      // start: function (e) {}, // .on('fileuploadstart', func);\n\n      // Callback for uploads stop, equivalent to the global ajaxStop event:\n      // stop: function (e) {}, // .on('fileuploadstop', func);\n\n      // Callback for change events of the fileInput(s):\n      // change: function (e, data) {}, // .on('fileuploadchange', func);\n\n      // Callback for paste events to the pasteZone(s):\n      // paste: function (e, data) {}, // .on('fileuploadpaste', func);\n\n      // Callback for drop events of the dropZone(s):\n      // drop: function (e, data) {}, // .on('fileuploaddrop', func);\n\n      // Callback for dragover events of the dropZone(s):\n      // dragover: function (e) {}, // .on('fileuploaddragover', func);\n\n      // Callback before the start of each chunk upload request (before form data initialization):\n      // chunkbeforesend: function (e, data) {}, // .on('fileuploadchunkbeforesend', func);\n\n      // Callback for the start of each chunk upload request:\n      // chunksend: function (e, data) {}, // .on('fileuploadchunksend', func);\n\n      // Callback for successful chunk uploads:\n      // chunkdone: function (e, data) {}, // .on('fileuploadchunkdone', func);\n\n      // Callback for failed (abort or error) chunk uploads:\n      // chunkfail: function (e, data) {}, // .on('fileuploadchunkfail', func);\n\n      // Callback for completed (success, abort or error) chunk upload requests:\n      // chunkalways: function (e, data) {}, // .on('fileuploadchunkalways', func);\n\n      // The plugin options are used as settings object for the ajax calls.\n      // The following are jQuery ajax settings required for the file uploads:\n      processData: false,\n      contentType: false,\n      cache: false,\n      timeout: 0\n    },\n\n    // jQuery versions before 1.8 require promise.pipe if the return value is\n    // used, as promise.then in older versions has a different behavior, see:\n    // https://blog.jquery.com/2012/08/09/jquery-1-8-released/\n    // https://bugs.jquery.com/ticket/11010\n    // https://github.com/blueimp/jQuery-File-Upload/pull/3435\n    _promisePipe: (function () {\n      var parts = $.fn.jquery.split('.');\n      return Number(parts[0]) > 1 || Number(parts[1]) > 7 ? 'then' : 'pipe';\n    })(),\n\n    // A list of options that require reinitializing event listeners and/or\n    // special initialization code:\n    _specialOptions: [\n      'fileInput',\n      'dropZone',\n      'pasteZone',\n      'multipart',\n      'forceIframeTransport'\n    ],\n\n    _blobSlice:\n      $.support.blobSlice &&\n      function () {\n        var slice = this.slice || this.webkitSlice || this.mozSlice;\n        return slice.apply(this, arguments);\n      },\n\n    _BitrateTimer: function () {\n      this.timestamp = Date.now ? Date.now() : new Date().getTime();\n      this.loaded = 0;\n      this.bitrate = 0;\n      this.getBitrate = function (now, loaded, interval) {\n        var timeDiff = now - this.timestamp;\n        if (!this.bitrate || !interval || timeDiff > interval) {\n          this.bitrate = (loaded - this.loaded) * (1000 / timeDiff) * 8;\n          this.loaded = loaded;\n          this.timestamp = now;\n        }\n        return this.bitrate;\n      };\n    },\n\n    _isXHRUpload: function (options) {\n      return (\n        !options.forceIframeTransport &&\n        ((!options.multipart && $.support.xhrFileUpload) ||\n          $.support.xhrFormDataFileUpload)\n      );\n    },\n\n    _getFormData: function (options) {\n      var formData;\n      if ($.type(options.formData) === 'function') {\n        return options.formData(options.form);\n      }\n      if ($.isArray(options.formData)) {\n        return options.formData;\n      }\n      if ($.type(options.formData) === 'object') {\n        formData = [];\n        $.each(options.formData, function (name, value) {\n          formData.push({ name: name, value: value });\n        });\n        return formData;\n      }\n      return [];\n    },\n\n    _getTotal: function (files) {\n      var total = 0;\n      $.each(files, function (index, file) {\n        total += file.size || 1;\n      });\n      return total;\n    },\n\n    _initProgressObject: function (obj) {\n      var progress = {\n        loaded: 0,\n        total: 0,\n        bitrate: 0\n      };\n      if (obj._progress) {\n        $.extend(obj._progress, progress);\n      } else {\n        obj._progress = progress;\n      }\n    },\n\n    _initResponseObject: function (obj) {\n      var prop;\n      if (obj._response) {\n        for (prop in obj._response) {\n          if (Object.prototype.hasOwnProperty.call(obj._response, prop)) {\n            delete obj._response[prop];\n          }\n        }\n      } else {\n        obj._response = {};\n      }\n    },\n\n    _onProgress: function (e, data) {\n      if (e.lengthComputable) {\n        var now = Date.now ? Date.now() : new Date().getTime(),\n          loaded;\n        if (\n          data._time &&\n          data.progressInterval &&\n          now - data._time < data.progressInterval &&\n          e.loaded !== e.total\n        ) {\n          return;\n        }\n        data._time = now;\n        loaded =\n          Math.floor(\n            (e.loaded / e.total) * (data.chunkSize || data._progress.total)\n          ) + (data.uploadedBytes || 0);\n        // Add the difference from the previously loaded state\n        // to the global loaded counter:\n        this._progress.loaded += loaded - data._progress.loaded;\n        this._progress.bitrate = this._bitrateTimer.getBitrate(\n          now,\n          this._progress.loaded,\n          data.bitrateInterval\n        );\n        data._progress.loaded = data.loaded = loaded;\n        data._progress.bitrate = data.bitrate = data._bitrateTimer.getBitrate(\n          now,\n          loaded,\n          data.bitrateInterval\n        );\n        // Trigger a custom progress event with a total data property set\n        // to the file size(s) of the current upload and a loaded data\n        // property calculated accordingly:\n        this._trigger(\n          'progress',\n          $.Event('progress', { delegatedEvent: e }),\n          data\n        );\n        // Trigger a global progress event for all current file uploads,\n        // including ajax calls queued for sequential file uploads:\n        this._trigger(\n          'progressall',\n          $.Event('progressall', { delegatedEvent: e }),\n          this._progress\n        );\n      }\n    },\n\n    _initProgressListener: function (options) {\n      var that = this,\n        xhr = options.xhr ? options.xhr() : $.ajaxSettings.xhr();\n      // Accesss to the native XHR object is required to add event listeners\n      // for the upload progress event:\n      if (xhr.upload) {\n        $(xhr.upload).on('progress', function (e) {\n          var oe = e.originalEvent;\n          // Make sure the progress event properties get copied over:\n          e.lengthComputable = oe.lengthComputable;\n          e.loaded = oe.loaded;\n          e.total = oe.total;\n          that._onProgress(e, options);\n        });\n        options.xhr = function () {\n          return xhr;\n        };\n      }\n    },\n\n    _deinitProgressListener: function (options) {\n      var xhr = options.xhr ? options.xhr() : $.ajaxSettings.xhr();\n      if (xhr.upload) {\n        $(xhr.upload).off('progress');\n      }\n    },\n\n    _isInstanceOf: function (type, obj) {\n      // Cross-frame instanceof check\n      return Object.prototype.toString.call(obj) === '[object ' + type + ']';\n    },\n\n    _getUniqueFilename: function (name, map) {\n      // eslint-disable-next-line no-param-reassign\n      name = String(name);\n      if (map[name]) {\n        // eslint-disable-next-line no-param-reassign\n        name = name.replace(/(?: \\(([\\d]+)\\))?(\\.[^.]+)?$/, function (\n          _,\n          p1,\n          p2\n        ) {\n          var index = p1 ? Number(p1) + 1 : 1;\n          var ext = p2 || '';\n          return ' (' + index + ')' + ext;\n        });\n        return this._getUniqueFilename(name, map);\n      }\n      map[name] = true;\n      return name;\n    },\n\n    _initXHRData: function (options) {\n      var that = this,\n        formData,\n        file = options.files[0],\n        // Ignore non-multipart setting if not supported:\n        multipart = options.multipart || !$.support.xhrFileUpload,\n        paramName =\n          $.type(options.paramName) === 'array'\n            ? options.paramName[0]\n            : options.paramName;\n      options.headers = $.extend({}, options.headers);\n      if (options.contentRange) {\n        options.headers['Content-Range'] = options.contentRange;\n      }\n      if (!multipart || options.blob || !this._isInstanceOf('File', file)) {\n        options.headers['Content-Disposition'] =\n          'attachment; filename=\"' +\n          encodeURI(file.uploadName || file.name) +\n          '\"';\n      }\n      if (!multipart) {\n        options.contentType = file.type || 'application/octet-stream';\n        options.data = options.blob || file;\n      } else if ($.support.xhrFormDataFileUpload) {\n        if (options.postMessage) {\n          // window.postMessage does not allow sending FormData\n          // objects, so we just add the File/Blob objects to\n          // the formData array and let the postMessage window\n          // create the FormData object out of this array:\n          formData = this._getFormData(options);\n          if (options.blob) {\n            formData.push({\n              name: paramName,\n              value: options.blob\n            });\n          } else {\n            $.each(options.files, function (index, file) {\n              formData.push({\n                name:\n                  ($.type(options.paramName) === 'array' &&\n                    options.paramName[index]) ||\n                  paramName,\n                value: file\n              });\n            });\n          }\n        } else {\n          if (that._isInstanceOf('FormData', options.formData)) {\n            formData = options.formData;\n          } else {\n            formData = new FormData();\n            $.each(this._getFormData(options), function (index, field) {\n              formData.append(field.name, field.value);\n            });\n          }\n          if (options.blob) {\n            formData.append(\n              paramName,\n              options.blob,\n              file.uploadName || file.name\n            );\n          } else {\n            $.each(options.files, function (index, file) {\n              // This check allows the tests to run with\n              // dummy objects:\n              if (\n                that._isInstanceOf('File', file) ||\n                that._isInstanceOf('Blob', file)\n              ) {\n                var fileName = file.uploadName || file.name;\n                if (options.uniqueFilenames) {\n                  fileName = that._getUniqueFilename(\n                    fileName,\n                    options.uniqueFilenames\n                  );\n                }\n                formData.append(\n                  ($.type(options.paramName) === 'array' &&\n                    options.paramName[index]) ||\n                    paramName,\n                  file,\n                  fileName\n                );\n              }\n            });\n          }\n        }\n        options.data = formData;\n      }\n      // Blob reference is not needed anymore, free memory:\n      options.blob = null;\n    },\n\n    _initIframeSettings: function (options) {\n      var targetHost = $('<a></a>').prop('href', options.url).prop('host');\n      // Setting the dataType to iframe enables the iframe transport:\n      options.dataType = 'iframe ' + (options.dataType || '');\n      // The iframe transport accepts a serialized array as form data:\n      options.formData = this._getFormData(options);\n      // Add redirect url to form data on cross-domain uploads:\n      if (options.redirect && targetHost && targetHost !== location.host) {\n        options.formData.push({\n          name: options.redirectParamName || 'redirect',\n          value: options.redirect\n        });\n      }\n    },\n\n    _initDataSettings: function (options) {\n      if (this._isXHRUpload(options)) {\n        if (!this._chunkedUpload(options, true)) {\n          if (!options.data) {\n            this._initXHRData(options);\n          }\n          this._initProgressListener(options);\n        }\n        if (options.postMessage) {\n          // Setting the dataType to postmessage enables the\n          // postMessage transport:\n          options.dataType = 'postmessage ' + (options.dataType || '');\n        }\n      } else {\n        this._initIframeSettings(options);\n      }\n    },\n\n    _getParamName: function (options) {\n      var fileInput = $(options.fileInput),\n        paramName = options.paramName;\n      if (!paramName) {\n        paramName = [];\n        fileInput.each(function () {\n          var input = $(this),\n            name = input.prop('name') || 'files[]',\n            i = (input.prop('files') || [1]).length;\n          while (i) {\n            paramName.push(name);\n            i -= 1;\n          }\n        });\n        if (!paramName.length) {\n          paramName = [fileInput.prop('name') || 'files[]'];\n        }\n      } else if (!$.isArray(paramName)) {\n        paramName = [paramName];\n      }\n      return paramName;\n    },\n\n    _initFormSettings: function (options) {\n      // Retrieve missing options from the input field and the\n      // associated form, if available:\n      if (!options.form || !options.form.length) {\n        options.form = $(options.fileInput.prop('form'));\n        // If the given file input doesn't have an associated form,\n        // use the default widget file input's form:\n        if (!options.form.length) {\n          options.form = $(this.options.fileInput.prop('form'));\n        }\n      }\n      options.paramName = this._getParamName(options);\n      if (!options.url) {\n        options.url = options.form.prop('action') || location.href;\n      }\n      // The HTTP request method must be \"POST\" or \"PUT\":\n      options.type = (\n        options.type ||\n        ($.type(options.form.prop('method')) === 'string' &&\n          options.form.prop('method')) ||\n        ''\n      ).toUpperCase();\n      if (\n        options.type !== 'POST' &&\n        options.type !== 'PUT' &&\n        options.type !== 'PATCH'\n      ) {\n        options.type = 'POST';\n      }\n      if (!options.formAcceptCharset) {\n        options.formAcceptCharset = options.form.attr('accept-charset');\n      }\n    },\n\n    _getAJAXSettings: function (data) {\n      var options = $.extend({}, this.options, data);\n      this._initFormSettings(options);\n      this._initDataSettings(options);\n      return options;\n    },\n\n    // jQuery 1.6 doesn't provide .state(),\n    // while jQuery 1.8+ removed .isRejected() and .isResolved():\n    _getDeferredState: function (deferred) {\n      if (deferred.state) {\n        return deferred.state();\n      }\n      if (deferred.isResolved()) {\n        return 'resolved';\n      }\n      if (deferred.isRejected()) {\n        return 'rejected';\n      }\n      return 'pending';\n    },\n\n    // Maps jqXHR callbacks to the equivalent\n    // methods of the given Promise object:\n    _enhancePromise: function (promise) {\n      promise.success = promise.done;\n      promise.error = promise.fail;\n      promise.complete = promise.always;\n      return promise;\n    },\n\n    // Creates and returns a Promise object enhanced with\n    // the jqXHR methods abort, success, error and complete:\n    _getXHRPromise: function (resolveOrReject, context, args) {\n      var dfd = $.Deferred(),\n        promise = dfd.promise();\n      // eslint-disable-next-line no-param-reassign\n      context = context || this.options.context || promise;\n      if (resolveOrReject === true) {\n        dfd.resolveWith(context, args);\n      } else if (resolveOrReject === false) {\n        dfd.rejectWith(context, args);\n      }\n      promise.abort = dfd.promise;\n      return this._enhancePromise(promise);\n    },\n\n    // Adds convenience methods to the data callback argument:\n    _addConvenienceMethods: function (e, data) {\n      var that = this,\n        getPromise = function (args) {\n          return $.Deferred().resolveWith(that, args).promise();\n        };\n      data.process = function (resolveFunc, rejectFunc) {\n        if (resolveFunc || rejectFunc) {\n          data._processQueue = this._processQueue = (this._processQueue ||\n            getPromise([this]))\n            [that._promisePipe](function () {\n              if (data.errorThrown) {\n                return $.Deferred().rejectWith(that, [data]).promise();\n              }\n              return getPromise(arguments);\n            })\n            [that._promisePipe](resolveFunc, rejectFunc);\n        }\n        return this._processQueue || getPromise([this]);\n      };\n      data.submit = function () {\n        if (this.state() !== 'pending') {\n          data.jqXHR = this.jqXHR =\n            that._trigger(\n              'submit',\n              $.Event('submit', { delegatedEvent: e }),\n              this\n            ) !== false && that._onSend(e, this);\n        }\n        return this.jqXHR || that._getXHRPromise();\n      };\n      data.abort = function () {\n        if (this.jqXHR) {\n          return this.jqXHR.abort();\n        }\n        this.errorThrown = 'abort';\n        that._trigger('fail', null, this);\n        return that._getXHRPromise(false);\n      };\n      data.state = function () {\n        if (this.jqXHR) {\n          return that._getDeferredState(this.jqXHR);\n        }\n        if (this._processQueue) {\n          return that._getDeferredState(this._processQueue);\n        }\n      };\n      data.processing = function () {\n        return (\n          !this.jqXHR &&\n          this._processQueue &&\n          that._getDeferredState(this._processQueue) === 'pending'\n        );\n      };\n      data.progress = function () {\n        return this._progress;\n      };\n      data.response = function () {\n        return this._response;\n      };\n    },\n\n    // Parses the Range header from the server response\n    // and returns the uploaded bytes:\n    _getUploadedBytes: function (jqXHR) {\n      var range = jqXHR.getResponseHeader('Range'),\n        parts = range && range.split('-'),\n        upperBytesPos = parts && parts.length > 1 && parseInt(parts[1], 10);\n      return upperBytesPos && upperBytesPos + 1;\n    },\n\n    // Uploads a file in multiple, sequential requests\n    // by splitting the file up in multiple blob chunks.\n    // If the second parameter is true, only tests if the file\n    // should be uploaded in chunks, but does not invoke any\n    // upload requests:\n    _chunkedUpload: function (options, testOnly) {\n      options.uploadedBytes = options.uploadedBytes || 0;\n      var that = this,\n        file = options.files[0],\n        fs = file.size,\n        ub = options.uploadedBytes,\n        mcs = options.maxChunkSize || fs,\n        slice = this._blobSlice,\n        dfd = $.Deferred(),\n        promise = dfd.promise(),\n        jqXHR,\n        upload;\n      if (\n        !(\n          this._isXHRUpload(options) &&\n          slice &&\n          (ub || ($.type(mcs) === 'function' ? mcs(options) : mcs) < fs)\n        ) ||\n        options.data\n      ) {\n        return false;\n      }\n      if (testOnly) {\n        return true;\n      }\n      if (ub >= fs) {\n        file.error = options.i18n('uploadedBytes');\n        return this._getXHRPromise(false, options.context, [\n          null,\n          'error',\n          file.error\n        ]);\n      }\n      // The chunk upload method:\n      upload = function () {\n        // Clone the options object for each chunk upload:\n        var o = $.extend({}, options),\n          currentLoaded = o._progress.loaded;\n        o.blob = slice.call(\n          file,\n          ub,\n          ub + ($.type(mcs) === 'function' ? mcs(o) : mcs),\n          file.type\n        );\n        // Store the current chunk size, as the blob itself\n        // will be dereferenced after data processing:\n        o.chunkSize = o.blob.size;\n        // Expose the chunk bytes position range:\n        o.contentRange =\n          'bytes ' + ub + '-' + (ub + o.chunkSize - 1) + '/' + fs;\n        // Trigger chunkbeforesend to allow form data to be updated for this chunk\n        that._trigger('chunkbeforesend', null, o);\n        // Process the upload data (the blob and potential form data):\n        that._initXHRData(o);\n        // Add progress listeners for this chunk upload:\n        that._initProgressListener(o);\n        jqXHR = (\n          (that._trigger('chunksend', null, o) !== false && $.ajax(o)) ||\n          that._getXHRPromise(false, o.context)\n        )\n          .done(function (result, textStatus, jqXHR) {\n            ub = that._getUploadedBytes(jqXHR) || ub + o.chunkSize;\n            // Create a progress event if no final progress event\n            // with loaded equaling total has been triggered\n            // for this chunk:\n            if (currentLoaded + o.chunkSize - o._progress.loaded) {\n              that._onProgress(\n                $.Event('progress', {\n                  lengthComputable: true,\n                  loaded: ub - o.uploadedBytes,\n                  total: ub - o.uploadedBytes\n                }),\n                o\n              );\n            }\n            options.uploadedBytes = o.uploadedBytes = ub;\n            o.result = result;\n            o.textStatus = textStatus;\n            o.jqXHR = jqXHR;\n            that._trigger('chunkdone', null, o);\n            that._trigger('chunkalways', null, o);\n            if (ub < fs) {\n              // File upload not yet complete,\n              // continue with the next chunk:\n              upload();\n            } else {\n              dfd.resolveWith(o.context, [result, textStatus, jqXHR]);\n            }\n          })\n          .fail(function (jqXHR, textStatus, errorThrown) {\n            o.jqXHR = jqXHR;\n            o.textStatus = textStatus;\n            o.errorThrown = errorThrown;\n            that._trigger('chunkfail', null, o);\n            that._trigger('chunkalways', null, o);\n            dfd.rejectWith(o.context, [jqXHR, textStatus, errorThrown]);\n          })\n          .always(function () {\n            that._deinitProgressListener(o);\n          });\n      };\n      this._enhancePromise(promise);\n      promise.abort = function () {\n        return jqXHR.abort();\n      };\n      upload();\n      return promise;\n    },\n\n    _beforeSend: function (e, data) {\n      if (this._active === 0) {\n        // the start callback is triggered when an upload starts\n        // and no other uploads are currently running,\n        // equivalent to the global ajaxStart event:\n        this._trigger('start');\n        // Set timer for global bitrate progress calculation:\n        this._bitrateTimer = new this._BitrateTimer();\n        // Reset the global progress values:\n        this._progress.loaded = this._progress.total = 0;\n        this._progress.bitrate = 0;\n      }\n      // Make sure the container objects for the .response() and\n      // .progress() methods on the data object are available\n      // and reset to their initial state:\n      this._initResponseObject(data);\n      this._initProgressObject(data);\n      data._progress.loaded = data.loaded = data.uploadedBytes || 0;\n      data._progress.total = data.total = this._getTotal(data.files) || 1;\n      data._progress.bitrate = data.bitrate = 0;\n      this._active += 1;\n      // Initialize the global progress values:\n      this._progress.loaded += data.loaded;\n      this._progress.total += data.total;\n    },\n\n    _onDone: function (result, textStatus, jqXHR, options) {\n      var total = options._progress.total,\n        response = options._response;\n      if (options._progress.loaded < total) {\n        // Create a progress event if no final progress event\n        // with loaded equaling total has been triggered:\n        this._onProgress(\n          $.Event('progress', {\n            lengthComputable: true,\n            loaded: total,\n            total: total\n          }),\n          options\n        );\n      }\n      response.result = options.result = result;\n      response.textStatus = options.textStatus = textStatus;\n      response.jqXHR = options.jqXHR = jqXHR;\n      this._trigger('done', null, options);\n    },\n\n    _onFail: function (jqXHR, textStatus, errorThrown, options) {\n      var response = options._response;\n      if (options.recalculateProgress) {\n        // Remove the failed (error or abort) file upload from\n        // the global progress calculation:\n        this._progress.loaded -= options._progress.loaded;\n        this._progress.total -= options._progress.total;\n      }\n      response.jqXHR = options.jqXHR = jqXHR;\n      response.textStatus = options.textStatus = textStatus;\n      response.errorThrown = options.errorThrown = errorThrown;\n      this._trigger('fail', null, options);\n    },\n\n    _onAlways: function (jqXHRorResult, textStatus, jqXHRorError, options) {\n      // jqXHRorResult, textStatus and jqXHRorError are added to the\n      // options object via done and fail callbacks\n      this._trigger('always', null, options);\n    },\n\n    _onSend: function (e, data) {\n      if (!data.submit) {\n        this._addConvenienceMethods(e, data);\n      }\n      var that = this,\n        jqXHR,\n        aborted,\n        slot,\n        pipe,\n        options = that._getAJAXSettings(data),\n        send = function () {\n          that._sending += 1;\n          // Set timer for bitrate progress calculation:\n          options._bitrateTimer = new that._BitrateTimer();\n          jqXHR =\n            jqXHR ||\n            (\n              ((aborted ||\n                that._trigger(\n                  'send',\n                  $.Event('send', { delegatedEvent: e }),\n                  options\n                ) === false) &&\n                that._getXHRPromise(false, options.context, aborted)) ||\n              that._chunkedUpload(options) ||\n              $.ajax(options)\n            )\n              .done(function (result, textStatus, jqXHR) {\n                that._onDone(result, textStatus, jqXHR, options);\n              })\n              .fail(function (jqXHR, textStatus, errorThrown) {\n                that._onFail(jqXHR, textStatus, errorThrown, options);\n              })\n              .always(function (jqXHRorResult, textStatus, jqXHRorError) {\n                that._deinitProgressListener(options);\n                that._onAlways(\n                  jqXHRorResult,\n                  textStatus,\n                  jqXHRorError,\n                  options\n                );\n                that._sending -= 1;\n                that._active -= 1;\n                if (\n                  options.limitConcurrentUploads &&\n                  options.limitConcurrentUploads > that._sending\n                ) {\n                  // Start the next queued upload,\n                  // that has not been aborted:\n                  var nextSlot = that._slots.shift();\n                  while (nextSlot) {\n                    if (that._getDeferredState(nextSlot) === 'pending') {\n                      nextSlot.resolve();\n                      break;\n                    }\n                    nextSlot = that._slots.shift();\n                  }\n                }\n                if (that._active === 0) {\n                  // The stop callback is triggered when all uploads have\n                  // been completed, equivalent to the global ajaxStop event:\n                  that._trigger('stop');\n                }\n              });\n          return jqXHR;\n        };\n      this._beforeSend(e, options);\n      if (\n        this.options.sequentialUploads ||\n        (this.options.limitConcurrentUploads &&\n          this.options.limitConcurrentUploads <= this._sending)\n      ) {\n        if (this.options.limitConcurrentUploads > 1) {\n          slot = $.Deferred();\n          this._slots.push(slot);\n          pipe = slot[that._promisePipe](send);\n        } else {\n          this._sequence = this._sequence[that._promisePipe](send, send);\n          pipe = this._sequence;\n        }\n        // Return the piped Promise object, enhanced with an abort method,\n        // which is delegated to the jqXHR object of the current upload,\n        // and jqXHR callbacks mapped to the equivalent Promise methods:\n        pipe.abort = function () {\n          aborted = [undefined, 'abort', 'abort'];\n          if (!jqXHR) {\n            if (slot) {\n              slot.rejectWith(options.context, aborted);\n            }\n            return send();\n          }\n          return jqXHR.abort();\n        };\n        return this._enhancePromise(pipe);\n      }\n      return send();\n    },\n\n    _onAdd: function (e, data) {\n      var that = this,\n        result = true,\n        options = $.extend({}, this.options, data),\n        files = data.files,\n        filesLength = files.length,\n        limit = options.limitMultiFileUploads,\n        limitSize = options.limitMultiFileUploadSize,\n        overhead = options.limitMultiFileUploadSizeOverhead,\n        batchSize = 0,\n        paramName = this._getParamName(options),\n        paramNameSet,\n        paramNameSlice,\n        fileSet,\n        i,\n        j = 0;\n      if (!filesLength) {\n        return false;\n      }\n      if (limitSize && files[0].size === undefined) {\n        limitSize = undefined;\n      }\n      if (\n        !(options.singleFileUploads || limit || limitSize) ||\n        !this._isXHRUpload(options)\n      ) {\n        fileSet = [files];\n        paramNameSet = [paramName];\n      } else if (!(options.singleFileUploads || limitSize) && limit) {\n        fileSet = [];\n        paramNameSet = [];\n        for (i = 0; i < filesLength; i += limit) {\n          fileSet.push(files.slice(i, i + limit));\n          paramNameSlice = paramName.slice(i, i + limit);\n          if (!paramNameSlice.length) {\n            paramNameSlice = paramName;\n          }\n          paramNameSet.push(paramNameSlice);\n        }\n      } else if (!options.singleFileUploads && limitSize) {\n        fileSet = [];\n        paramNameSet = [];\n        for (i = 0; i < filesLength; i = i + 1) {\n          batchSize += files[i].size + overhead;\n          if (\n            i + 1 === filesLength ||\n            batchSize + files[i + 1].size + overhead > limitSize ||\n            (limit && i + 1 - j >= limit)\n          ) {\n            fileSet.push(files.slice(j, i + 1));\n            paramNameSlice = paramName.slice(j, i + 1);\n            if (!paramNameSlice.length) {\n              paramNameSlice = paramName;\n            }\n            paramNameSet.push(paramNameSlice);\n            j = i + 1;\n            batchSize = 0;\n          }\n        }\n      } else {\n        paramNameSet = paramName;\n      }\n      data.originalFiles = files;\n      $.each(fileSet || files, function (index, element) {\n        var newData = $.extend({}, data);\n        newData.files = fileSet ? element : [element];\n        newData.paramName = paramNameSet[index];\n        that._initResponseObject(newData);\n        that._initProgressObject(newData);\n        that._addConvenienceMethods(e, newData);\n        result = that._trigger(\n          'add',\n          $.Event('add', { delegatedEvent: e }),\n          newData\n        );\n        return result;\n      });\n      return result;\n    },\n\n    _replaceFileInput: function (data) {\n      var input = data.fileInput,\n        inputClone = input.clone(true),\n        restoreFocus = input.is(document.activeElement);\n      // Add a reference for the new cloned file input to the data argument:\n      data.fileInputClone = inputClone;\n      $('<form></form>').append(inputClone)[0].reset();\n      // Detaching allows to insert the fileInput on another form\n      // without loosing the file input value:\n      input.after(inputClone).detach();\n      // If the fileInput had focus before it was detached,\n      // restore focus to the inputClone.\n      if (restoreFocus) {\n        inputClone.trigger('focus');\n      }\n      // Avoid memory leaks with the detached file input:\n      $.cleanData(input.off('remove'));\n      // Replace the original file input element in the fileInput\n      // elements set with the clone, which has been copied including\n      // event handlers:\n      this.options.fileInput = this.options.fileInput.map(function (i, el) {\n        if (el === input[0]) {\n          return inputClone[0];\n        }\n        return el;\n      });\n      // If the widget has been initialized on the file input itself,\n      // override this.element with the file input clone:\n      if (input[0] === this.element[0]) {\n        this.element = inputClone;\n      }\n    },\n\n    _handleFileTreeEntry: function (entry, path) {\n      var that = this,\n        dfd = $.Deferred(),\n        entries = [],\n        dirReader,\n        errorHandler = function (e) {\n          if (e && !e.entry) {\n            e.entry = entry;\n          }\n          // Since $.when returns immediately if one\n          // Deferred is rejected, we use resolve instead.\n          // This allows valid files and invalid items\n          // to be returned together in one set:\n          dfd.resolve([e]);\n        },\n        successHandler = function (entries) {\n          that\n            ._handleFileTreeEntries(entries, path + entry.name + '/')\n            .done(function (files) {\n              dfd.resolve(files);\n            })\n            .fail(errorHandler);\n        },\n        readEntries = function () {\n          dirReader.readEntries(function (results) {\n            if (!results.length) {\n              successHandler(entries);\n            } else {\n              entries = entries.concat(results);\n              readEntries();\n            }\n          }, errorHandler);\n        };\n      // eslint-disable-next-line no-param-reassign\n      path = path || '';\n      if (entry.isFile) {\n        if (entry._file) {\n          // Workaround for Chrome bug #149735\n          entry._file.relativePath = path;\n          dfd.resolve(entry._file);\n        } else {\n          entry.file(function (file) {\n            file.relativePath = path;\n            dfd.resolve(file);\n          }, errorHandler);\n        }\n      } else if (entry.isDirectory) {\n        dirReader = entry.createReader();\n        readEntries();\n      } else {\n        // Return an empty list for file system items\n        // other than files or directories:\n        dfd.resolve([]);\n      }\n      return dfd.promise();\n    },\n\n    _handleFileTreeEntries: function (entries, path) {\n      var that = this;\n      return $.when\n        .apply(\n          $,\n          $.map(entries, function (entry) {\n            return that._handleFileTreeEntry(entry, path);\n          })\n        )\n        [this._promisePipe](function () {\n          return Array.prototype.concat.apply([], arguments);\n        });\n    },\n\n    _getDroppedFiles: function (dataTransfer) {\n      // eslint-disable-next-line no-param-reassign\n      dataTransfer = dataTransfer || {};\n      var items = dataTransfer.items;\n      if (\n        items &&\n        items.length &&\n        (items[0].webkitGetAsEntry || items[0].getAsEntry)\n      ) {\n        return this._handleFileTreeEntries(\n          $.map(items, function (item) {\n            var entry;\n            if (item.webkitGetAsEntry) {\n              entry = item.webkitGetAsEntry();\n              if (entry) {\n                // Workaround for Chrome bug #149735:\n                entry._file = item.getAsFile();\n              }\n              return entry;\n            }\n            return item.getAsEntry();\n          })\n        );\n      }\n      return $.Deferred().resolve($.makeArray(dataTransfer.files)).promise();\n    },\n\n    _getSingleFileInputFiles: function (fileInput) {\n      // eslint-disable-next-line no-param-reassign\n      fileInput = $(fileInput);\n      var entries =\n          fileInput.prop('webkitEntries') || fileInput.prop('entries'),\n        files,\n        value;\n      if (entries && entries.length) {\n        return this._handleFileTreeEntries(entries);\n      }\n      files = $.makeArray(fileInput.prop('files'));\n      if (!files.length) {\n        value = fileInput.prop('value');\n        if (!value) {\n          return $.Deferred().resolve([]).promise();\n        }\n        // If the files property is not available, the browser does not\n        // support the File API and we add a pseudo File object with\n        // the input value as name with path information removed:\n        files = [{ name: value.replace(/^.*\\\\/, '') }];\n      } else if (files[0].name === undefined && files[0].fileName) {\n        // File normalization for Safari 4 and Firefox 3:\n        $.each(files, function (index, file) {\n          file.name = file.fileName;\n          file.size = file.fileSize;\n        });\n      }\n      return $.Deferred().resolve(files).promise();\n    },\n\n    _getFileInputFiles: function (fileInput) {\n      if (!(fileInput instanceof $) || fileInput.length === 1) {\n        return this._getSingleFileInputFiles(fileInput);\n      }\n      return $.when\n        .apply($, $.map(fileInput, this._getSingleFileInputFiles))\n        [this._promisePipe](function () {\n          return Array.prototype.concat.apply([], arguments);\n        });\n    },\n\n    _onChange: function (e) {\n      var that = this,\n        data = {\n          fileInput: $(e.target),\n          form: $(e.target.form)\n        };\n      this._getFileInputFiles(data.fileInput).always(function (files) {\n        data.files = files;\n        if (that.options.replaceFileInput) {\n          that._replaceFileInput(data);\n        }\n        if (\n          that._trigger(\n            'change',\n            $.Event('change', { delegatedEvent: e }),\n            data\n          ) !== false\n        ) {\n          that._onAdd(e, data);\n        }\n      });\n    },\n\n    _onPaste: function (e) {\n      var items =\n          e.originalEvent &&\n          e.originalEvent.clipboardData &&\n          e.originalEvent.clipboardData.items,\n        data = { files: [] };\n      if (items && items.length) {\n        $.each(items, function (index, item) {\n          var file = item.getAsFile && item.getAsFile();\n          if (file) {\n            data.files.push(file);\n          }\n        });\n        if (\n          this._trigger(\n            'paste',\n            $.Event('paste', { delegatedEvent: e }),\n            data\n          ) !== false\n        ) {\n          this._onAdd(e, data);\n        }\n      }\n    },\n\n    _onDrop: function (e) {\n      e.dataTransfer = e.originalEvent && e.originalEvent.dataTransfer;\n      var that = this,\n        dataTransfer = e.dataTransfer,\n        data = {};\n      if (dataTransfer && dataTransfer.files && dataTransfer.files.length) {\n        e.preventDefault();\n        this._getDroppedFiles(dataTransfer).always(function (files) {\n          data.files = files;\n          if (\n            that._trigger(\n              'drop',\n              $.Event('drop', { delegatedEvent: e }),\n              data\n            ) !== false\n          ) {\n            that._onAdd(e, data);\n          }\n        });\n      }\n    },\n\n    _onDragOver: getDragHandler('dragover'),\n\n    _onDragEnter: getDragHandler('dragenter'),\n\n    _onDragLeave: getDragHandler('dragleave'),\n\n    _initEventHandlers: function () {\n      if (this._isXHRUpload(this.options)) {\n        this._on(this.options.dropZone, {\n          dragover: this._onDragOver,\n          drop: this._onDrop,\n          // event.preventDefault() on dragenter is required for IE10+:\n          dragenter: this._onDragEnter,\n          // dragleave is not required, but added for completeness:\n          dragleave: this._onDragLeave\n        });\n        this._on(this.options.pasteZone, {\n          paste: this._onPaste\n        });\n      }\n      if ($.support.fileInput) {\n        this._on(this.options.fileInput, {\n          change: this._onChange\n        });\n      }\n    },\n\n    _destroyEventHandlers: function () {\n      this._off(this.options.dropZone, 'dragenter dragleave dragover drop');\n      this._off(this.options.pasteZone, 'paste');\n      this._off(this.options.fileInput, 'change');\n    },\n\n    _destroy: function () {\n      this._destroyEventHandlers();\n    },\n\n    _setOption: function (key, value) {\n      var reinit = $.inArray(key, this._specialOptions) !== -1;\n      if (reinit) {\n        this._destroyEventHandlers();\n      }\n      this._super(key, value);\n      if (reinit) {\n        this._initSpecialOptions();\n        this._initEventHandlers();\n      }\n    },\n\n    _initSpecialOptions: function () {\n      var options = this.options;\n      if (options.fileInput === undefined) {\n        options.fileInput = this.element.is('input[type=\"file\"]')\n          ? this.element\n          : this.element.find('input[type=\"file\"]');\n      } else if (!(options.fileInput instanceof $)) {\n        options.fileInput = $(options.fileInput);\n      }\n      if (!(options.dropZone instanceof $)) {\n        options.dropZone = $(options.dropZone);\n      }\n      if (!(options.pasteZone instanceof $)) {\n        options.pasteZone = $(options.pasteZone);\n      }\n    },\n\n    _getRegExp: function (str) {\n      var parts = str.split('/'),\n        modifiers = parts.pop();\n      parts.shift();\n      return new RegExp(parts.join('/'), modifiers);\n    },\n\n    _isRegExpOption: function (key, value) {\n      return (\n        key !== 'url' &&\n        $.type(value) === 'string' &&\n        /^\\/.*\\/[igm]{0,3}$/.test(value)\n      );\n    },\n\n    _initDataAttributes: function () {\n      var that = this,\n        options = this.options,\n        data = this.element.data();\n      // Initialize options set via HTML5 data-attributes:\n      $.each(this.element[0].attributes, function (index, attr) {\n        var key = attr.name.toLowerCase(),\n          value;\n        if (/^data-/.test(key)) {\n          // Convert hyphen-ated key to camelCase:\n          key = key.slice(5).replace(/-[a-z]/g, function (str) {\n            return str.charAt(1).toUpperCase();\n          });\n          value = data[key];\n          if (that._isRegExpOption(key, value)) {\n            value = that._getRegExp(value);\n          }\n          options[key] = value;\n        }\n      });\n    },\n\n    _create: function () {\n      this._initDataAttributes();\n      this._initSpecialOptions();\n      this._slots = [];\n      this._sequence = this._getXHRPromise(true);\n      this._sending = this._active = 0;\n      this._initProgressObject(this);\n      this._initEventHandlers();\n    },\n\n    // This method is exposed to the widget API and allows to query\n    // the number of active uploads:\n    active: function () {\n      return this._active;\n    },\n\n    // This method is exposed to the widget API and allows to query\n    // the widget upload progress.\n    // It returns an object with loaded, total and bitrate properties\n    // for the running uploads:\n    progress: function () {\n      return this._progress;\n    },\n\n    // This method is exposed to the widget API and allows adding files\n    // using the fileupload API. The data parameter accepts an object which\n    // must have a files property and can contain additional options:\n    // .fileupload('add', {files: filesList});\n    add: function (data) {\n      var that = this;\n      if (!data || this.options.disabled) {\n        return;\n      }\n      if (data.fileInput && !data.files) {\n        this._getFileInputFiles(data.fileInput).always(function (files) {\n          data.files = files;\n          that._onAdd(null, data);\n        });\n      } else {\n        data.files = $.makeArray(data.files);\n        this._onAdd(null, data);\n      }\n    },\n\n    // This method is exposed to the widget API and allows sending files\n    // using the fileupload API. The data parameter accepts an object which\n    // must have a files or fileInput property and can contain additional options:\n    // .fileupload('send', {files: filesList});\n    // The method returns a Promise object for the file upload call.\n    send: function (data) {\n      if (data && !this.options.disabled) {\n        if (data.fileInput && !data.files) {\n          var that = this,\n            dfd = $.Deferred(),\n            promise = dfd.promise(),\n            jqXHR,\n            aborted;\n          promise.abort = function () {\n            aborted = true;\n            if (jqXHR) {\n              return jqXHR.abort();\n            }\n            dfd.reject(null, 'abort', 'abort');\n            return promise;\n          };\n          this._getFileInputFiles(data.fileInput).always(function (files) {\n            if (aborted) {\n              return;\n            }\n            if (!files.length) {\n              dfd.reject();\n              return;\n            }\n            data.files = files;\n            jqXHR = that._onSend(null, data);\n            jqXHR.then(\n              function (result, textStatus, jqXHR) {\n                dfd.resolve(result, textStatus, jqXHR);\n              },\n              function (jqXHR, textStatus, errorThrown) {\n                dfd.reject(jqXHR, textStatus, errorThrown);\n              }\n            );\n          });\n          return this._enhancePromise(promise);\n        }\n        data.files = $.makeArray(data.files);\n        if (data.files.length) {\n          return this._onSend(null, data);\n        }\n      }\n      return this._getXHRPromise(false, data && data.context);\n    }\n  });\n});\n", "/*\n * jQuery File Upload Processing Plugin\n * https://github.com/blueimp/jQuery-File-Upload\n *\n * Copyright 2012, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, require */\n\n(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['jquery', './jquery.fileupload'], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS:\n    factory(require('jquery'), require('./jquery.fileupload'));\n  } else {\n    // Browser globals:\n    factory(window.jQuery);\n  }\n})(function ($) {\n  'use strict';\n\n  var originalAdd = $.blueimp.fileupload.prototype.options.add;\n\n  // The File Upload Processing plugin extends the fileupload widget\n  // with file processing functionality:\n  $.widget('blueimp.fileupload', $.blueimp.fileupload, {\n    options: {\n      // The list of processing actions:\n      processQueue: [\n        /*\n                {\n                    action: 'log',\n                    type: 'debug'\n                }\n                */\n      ],\n      add: function (e, data) {\n        var $this = $(this);\n        data.process(function () {\n          return $this.fileupload('process', data);\n        });\n        originalAdd.call(this, e, data);\n      }\n    },\n\n    processActions: {\n      /*\n            log: function (data, options) {\n                console[options.type](\n                    'Processing \"' + data.files[data.index].name + '\"'\n                );\n            }\n            */\n    },\n\n    _processFile: function (data, originalData) {\n      var that = this,\n        // eslint-disable-next-line new-cap\n        dfd = $.Deferred().resolveWith(that, [data]),\n        chain = dfd.promise();\n      this._trigger('process', null, data);\n      $.each(data.processQueue, function (i, settings) {\n        var func = function (data) {\n          if (originalData.errorThrown) {\n            // eslint-disable-next-line new-cap\n            return $.Deferred().rejectWith(that, [originalData]).promise();\n          }\n          return that.processActions[settings.action].call(\n            that,\n            data,\n            settings\n          );\n        };\n        chain = chain[that._promisePipe](func, settings.always && func);\n      });\n      chain\n        .done(function () {\n          that._trigger('processdone', null, data);\n          that._trigger('processalways', null, data);\n        })\n        .fail(function () {\n          that._trigger('processfail', null, data);\n          that._trigger('processalways', null, data);\n        });\n      return chain;\n    },\n\n    // Replaces the settings of each processQueue item that\n    // are strings starting with an \"@\", using the remaining\n    // substring as key for the option map,\n    // e.g. \"@autoUpload\" is replaced with options.autoUpload:\n    _transformProcessQueue: function (options) {\n      var processQueue = [];\n      $.each(options.processQueue, function () {\n        var settings = {},\n          action = this.action,\n          prefix = this.prefix === true ? action : this.prefix;\n        $.each(this, function (key, value) {\n          if ($.type(value) === 'string' && value.charAt(0) === '@') {\n            settings[key] =\n              options[\n                value.slice(1) ||\n                  (prefix\n                    ? prefix + key.charAt(0).toUpperCase() + key.slice(1)\n                    : key)\n              ];\n          } else {\n            settings[key] = value;\n          }\n        });\n        processQueue.push(settings);\n      });\n      options.processQueue = processQueue;\n    },\n\n    // Returns the number of files currently in the processsing queue:\n    processing: function () {\n      return this._processing;\n    },\n\n    // Processes the files given as files property of the data parameter,\n    // returns a Promise object that allows to bind callbacks:\n    process: function (data) {\n      var that = this,\n        options = $.extend({}, this.options, data);\n      if (options.processQueue && options.processQueue.length) {\n        this._transformProcessQueue(options);\n        if (this._processing === 0) {\n          this._trigger('processstart');\n        }\n        $.each(data.files, function (index) {\n          var opts = index ? $.extend({}, options) : options,\n            func = function () {\n              if (data.errorThrown) {\n                // eslint-disable-next-line new-cap\n                return $.Deferred().rejectWith(that, [data]).promise();\n              }\n              return that._processFile(opts, data);\n            };\n          opts.index = index;\n          that._processing += 1;\n          that._processingQueue = that._processingQueue[that._promisePipe](\n            func,\n            func\n          ).always(function () {\n            that._processing -= 1;\n            if (that._processing === 0) {\n              that._trigger('processstop');\n            }\n          });\n        });\n      }\n      return this._processingQueue;\n    },\n\n    _create: function () {\n      this._super();\n      this._processing = 0;\n      // eslint-disable-next-line new-cap\n      this._processingQueue = $.Deferred().resolveWith(this).promise();\n    }\n  });\n});\n", "/*\n * jQuery File Upload Image Preview & Resize Plugin\n * https://github.com/blueimp/jQuery-File-Upload\n *\n * Copyright 2013, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, require */\n\n(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define([\n      'jquery',\n      'load-image',\n      'load-image-meta',\n      'load-image-scale',\n      'load-image-exif',\n      'load-image-orientation',\n      'canvas-to-blob',\n      './jquery.fileupload-process'\n    ], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS:\n    factory(\n      require('jquery'),\n      require('blueimp-load-image/js/load-image'),\n      require('blueimp-load-image/js/load-image-meta'),\n      require('blueimp-load-image/js/load-image-scale'),\n      require('blueimp-load-image/js/load-image-exif'),\n      require('blueimp-load-image/js/load-image-orientation'),\n      require('blueimp-canvas-to-blob'),\n      require('./jquery.fileupload-process')\n    );\n  } else {\n    // Browser globals:\n    factory(window.jQuery, window.loadImage);\n  }\n})(function ($, loadImage) {\n  'use strict';\n\n  // Prepend to the default processQueue:\n  $.blueimp.fileupload.prototype.options.processQueue.unshift(\n    {\n      action: 'loadImageMetaData',\n      maxMetaDataSize: '@',\n      disableImageHead: '@',\n      disableMetaDataParsers: '@',\n      disableExif: '@',\n      disableExifOffsets: '@',\n      includeExifTags: '@',\n      excludeExifTags: '@',\n      disableIptc: '@',\n      disableIptcOffsets: '@',\n      includeIptcTags: '@',\n      excludeIptcTags: '@',\n      disabled: '@disableImageMetaDataLoad'\n    },\n    {\n      action: 'loadImage',\n      // Use the action as prefix for the \"@\" options:\n      prefix: true,\n      fileTypes: '@',\n      maxFileSize: '@',\n      noRevoke: '@',\n      disabled: '@disableImageLoad'\n    },\n    {\n      action: 'resizeImage',\n      // Use \"image\" as prefix for the \"@\" options:\n      prefix: 'image',\n      maxWidth: '@',\n      maxHeight: '@',\n      minWidth: '@',\n      minHeight: '@',\n      crop: '@',\n      orientation: '@',\n      forceResize: '@',\n      disabled: '@disableImageResize'\n    },\n    {\n      action: 'saveImage',\n      quality: '@imageQuality',\n      type: '@imageType',\n      disabled: '@disableImageResize'\n    },\n    {\n      action: 'saveImageMetaData',\n      disabled: '@disableImageMetaDataSave'\n    },\n    {\n      action: 'resizeImage',\n      // Use \"preview\" as prefix for the \"@\" options:\n      prefix: 'preview',\n      maxWidth: '@',\n      maxHeight: '@',\n      minWidth: '@',\n      minHeight: '@',\n      crop: '@',\n      orientation: '@',\n      thumbnail: '@',\n      canvas: '@',\n      disabled: '@disableImagePreview'\n    },\n    {\n      action: 'setImage',\n      name: '@imagePreviewName',\n      disabled: '@disableImagePreview'\n    },\n    {\n      action: 'deleteImageReferences',\n      disabled: '@disableImageReferencesDeletion'\n    }\n  );\n\n  // The File Upload Resize plugin extends the fileupload widget\n  // with image resize functionality:\n  $.widget('blueimp.fileupload', $.blueimp.fileupload, {\n    options: {\n      // The regular expression for the types of images to load:\n      // matched against the file type:\n      loadImageFileTypes: /^image\\/(gif|jpeg|png|svg\\+xml)$/,\n      // The maximum file size of images to load:\n      loadImageMaxFileSize: 10000000, // 10MB\n      // The maximum width of resized images:\n      imageMaxWidth: 1920,\n      // The maximum height of resized images:\n      imageMaxHeight: 1080,\n      // Defines the image orientation (1-8) or takes the orientation\n      // value from Exif data if set to true:\n      imageOrientation: true,\n      // Define if resized images should be cropped or only scaled:\n      imageCrop: false,\n      // Disable the resize image functionality by default:\n      disableImageResize: true,\n      // The maximum width of the preview images:\n      previewMaxWidth: 80,\n      // The maximum height of the preview images:\n      previewMaxHeight: 80,\n      // Defines the preview orientation (1-8) or takes the orientation\n      // value from Exif data if set to true:\n      previewOrientation: true,\n      // Create the preview using the Exif data thumbnail:\n      previewThumbnail: true,\n      // Define if preview images should be cropped or only scaled:\n      previewCrop: false,\n      // Define if preview images should be resized as canvas elements:\n      previewCanvas: true\n    },\n\n    processActions: {\n      // Loads the image given via data.files and data.index\n      // as img element, if the browser supports the File API.\n      // Accepts the options fileTypes (regular expression)\n      // and maxFileSize (integer) to limit the files to load:\n      loadImage: function (data, options) {\n        if (options.disabled) {\n          return data;\n        }\n        var that = this,\n          file = data.files[data.index],\n          // eslint-disable-next-line new-cap\n          dfd = $.Deferred();\n        if (\n          ($.type(options.maxFileSize) === 'number' &&\n            file.size > options.maxFileSize) ||\n          (options.fileTypes && !options.fileTypes.test(file.type)) ||\n          !loadImage(\n            file,\n            function (img) {\n              if (img.src) {\n                data.img = img;\n              }\n              dfd.resolveWith(that, [data]);\n            },\n            options\n          )\n        ) {\n          return data;\n        }\n        return dfd.promise();\n      },\n\n      // Resizes the image given as data.canvas or data.img\n      // and updates data.canvas or data.img with the resized image.\n      // Also stores the resized image as preview property.\n      // Accepts the options maxWidth, maxHeight, minWidth,\n      // minHeight, canvas and crop:\n      resizeImage: function (data, options) {\n        if (options.disabled || !(data.canvas || data.img)) {\n          return data;\n        }\n        // eslint-disable-next-line no-param-reassign\n        options = $.extend({ canvas: true }, options);\n        var that = this,\n          // eslint-disable-next-line new-cap\n          dfd = $.Deferred(),\n          img = (options.canvas && data.canvas) || data.img,\n          resolve = function (newImg) {\n            if (\n              newImg &&\n              (newImg.width !== img.width ||\n                newImg.height !== img.height ||\n                options.forceResize)\n            ) {\n              data[newImg.getContext ? 'canvas' : 'img'] = newImg;\n            }\n            data.preview = newImg;\n            dfd.resolveWith(that, [data]);\n          },\n          thumbnail,\n          thumbnailBlob;\n        if (data.exif && options.thumbnail) {\n          thumbnail = data.exif.get('Thumbnail');\n          thumbnailBlob = thumbnail && thumbnail.get('Blob');\n          if (thumbnailBlob) {\n            options.orientation = data.exif.get('Orientation');\n            loadImage(thumbnailBlob, resolve, options);\n            return dfd.promise();\n          }\n        }\n        if (data.orientation) {\n          // Prevent orienting the same image twice:\n          delete options.orientation;\n        } else {\n          data.orientation = options.orientation || loadImage.orientation;\n        }\n        if (img) {\n          resolve(loadImage.scale(img, options, data));\n          return dfd.promise();\n        }\n        return data;\n      },\n\n      // Saves the processed image given as data.canvas\n      // inplace at data.index of data.files:\n      saveImage: function (data, options) {\n        if (!data.canvas || options.disabled) {\n          return data;\n        }\n        var that = this,\n          file = data.files[data.index],\n          // eslint-disable-next-line new-cap\n          dfd = $.Deferred();\n        if (data.canvas.toBlob) {\n          data.canvas.toBlob(\n            function (blob) {\n              if (!blob.name) {\n                if (file.type === blob.type) {\n                  blob.name = file.name;\n                } else if (file.name) {\n                  blob.name = file.name.replace(\n                    /\\.\\w+$/,\n                    '.' + blob.type.substr(6)\n                  );\n                }\n              }\n              // Don't restore invalid meta data:\n              if (file.type !== blob.type) {\n                delete data.imageHead;\n              }\n              // Store the created blob at the position\n              // of the original file in the files list:\n              data.files[data.index] = blob;\n              dfd.resolveWith(that, [data]);\n            },\n            options.type || file.type,\n            options.quality\n          );\n        } else {\n          return data;\n        }\n        return dfd.promise();\n      },\n\n      loadImageMetaData: function (data, options) {\n        if (options.disabled) {\n          return data;\n        }\n        var that = this,\n          // eslint-disable-next-line new-cap\n          dfd = $.Deferred();\n        loadImage.parseMetaData(\n          data.files[data.index],\n          function (result) {\n            $.extend(data, result);\n            dfd.resolveWith(that, [data]);\n          },\n          options\n        );\n        return dfd.promise();\n      },\n\n      saveImageMetaData: function (data, options) {\n        if (\n          !(\n            data.imageHead &&\n            data.canvas &&\n            data.canvas.toBlob &&\n            !options.disabled\n          )\n        ) {\n          return data;\n        }\n        var that = this,\n          file = data.files[data.index],\n          // eslint-disable-next-line new-cap\n          dfd = $.Deferred();\n        if (data.orientation === true && data.exifOffsets) {\n          // Reset Exif Orientation data:\n          loadImage.writeExifData(data.imageHead, data, 'Orientation', 1);\n        }\n        loadImage.replaceHead(file, data.imageHead, function (blob) {\n          blob.name = file.name;\n          data.files[data.index] = blob;\n          dfd.resolveWith(that, [data]);\n        });\n        return dfd.promise();\n      },\n\n      // Sets the resized version of the image as a property of the\n      // file object, must be called after \"saveImage\":\n      setImage: function (data, options) {\n        if (data.preview && !options.disabled) {\n          data.files[data.index][options.name || 'preview'] = data.preview;\n        }\n        return data;\n      },\n\n      deleteImageReferences: function (data, options) {\n        if (!options.disabled) {\n          delete data.img;\n          delete data.canvas;\n          delete data.preview;\n          delete data.imageHead;\n        }\n        return data;\n      }\n    }\n  });\n});\n", "/*\n * jQuery File Upload Audio Preview Plugin\n * https://github.com/blueimp/jQuery-File-Upload\n *\n * Copyright 2013, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, require */\n\n(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['jquery', 'load-image', './jquery.fileupload-process'], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS:\n    factory(\n      require('jquery'),\n      require('blueimp-load-image/js/load-image'),\n      require('./jquery.fileupload-process')\n    );\n  } else {\n    // Browser globals:\n    factory(window.jQuery, window.loadImage);\n  }\n})(function ($, loadImage) {\n  'use strict';\n\n  // Prepend to the default processQueue:\n  $.blueimp.fileupload.prototype.options.processQueue.unshift(\n    {\n      action: 'loadAudio',\n      // Use the action as prefix for the \"@\" options:\n      prefix: true,\n      fileTypes: '@',\n      maxFileSize: '@',\n      disabled: '@disableAudioPreview'\n    },\n    {\n      action: 'setAudio',\n      name: '@audioPreviewName',\n      disabled: '@disableAudioPreview'\n    }\n  );\n\n  // The File Upload Audio Preview plugin extends the fileupload widget\n  // with audio preview functionality:\n  $.widget('blueimp.fileupload', $.blueimp.fileupload, {\n    options: {\n      // The regular expression for the types of audio files to load,\n      // matched against the file type:\n      loadAudioFileTypes: /^audio\\/.*$/\n    },\n\n    _audioElement: document.createElement('audio'),\n\n    processActions: {\n      // Loads the audio file given via data.files and data.index\n      // as audio element if the browser supports playing it.\n      // Accepts the options fileTypes (regular expression)\n      // and maxFileSize (integer) to limit the files to load:\n      loadAudio: function (data, options) {\n        if (options.disabled) {\n          return data;\n        }\n        var file = data.files[data.index],\n          url,\n          audio;\n        if (\n          this._audioElement.canPlayType &&\n          this._audioElement.canPlayType(file.type) &&\n          ($.type(options.maxFileSize) !== 'number' ||\n            file.size <= options.maxFileSize) &&\n          (!options.fileTypes || options.fileTypes.test(file.type))\n        ) {\n          url = loadImage.createObjectURL(file);\n          if (url) {\n            audio = this._audioElement.cloneNode(false);\n            audio.src = url;\n            audio.controls = true;\n            data.audio = audio;\n            return data;\n          }\n        }\n        return data;\n      },\n\n      // Sets the audio element as a property of the file object:\n      setAudio: function (data, options) {\n        if (data.audio && !options.disabled) {\n          data.files[data.index][options.name || 'preview'] = data.audio;\n        }\n        return data;\n      }\n    }\n  });\n});\n", "/*\n * jQuery File Upload Video Preview Plugin\n * https://github.com/blueimp/jQuery-File-Upload\n *\n * Copyright 2013, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, require */\n\n(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['jquery', 'load-image', './jquery.fileupload-process'], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS:\n    factory(\n      require('jquery'),\n      require('blueimp-load-image/js/load-image'),\n      require('./jquery.fileupload-process')\n    );\n  } else {\n    // Browser globals:\n    factory(window.jQuery, window.loadImage);\n  }\n})(function ($, loadImage) {\n  'use strict';\n\n  // Prepend to the default processQueue:\n  $.blueimp.fileupload.prototype.options.processQueue.unshift(\n    {\n      action: 'loadVideo',\n      // Use the action as prefix for the \"@\" options:\n      prefix: true,\n      fileTypes: '@',\n      maxFileSize: '@',\n      disabled: '@disableVideoPreview'\n    },\n    {\n      action: 'setVideo',\n      name: '@videoPreviewName',\n      disabled: '@disableVideoPreview'\n    }\n  );\n\n  // The File Upload Video Preview plugin extends the fileupload widget\n  // with video preview functionality:\n  $.widget('blueimp.fileupload', $.blueimp.fileupload, {\n    options: {\n      // The regular expression for the types of video files to load,\n      // matched against the file type:\n      loadVideoFileTypes: /^video\\/.*$/\n    },\n\n    _videoElement: document.createElement('video'),\n\n    processActions: {\n      // Loads the video file given via data.files and data.index\n      // as video element if the browser supports playing it.\n      // Accepts the options fileTypes (regular expression)\n      // and maxFileSize (integer) to limit the files to load:\n      loadVideo: function (data, options) {\n        if (options.disabled) {\n          return data;\n        }\n        var file = data.files[data.index],\n          url,\n          video;\n        if (\n          this._videoElement.canPlayType &&\n          this._videoElement.canPlayType(file.type) &&\n          ($.type(options.maxFileSize) !== 'number' ||\n            file.size <= options.maxFileSize) &&\n          (!options.fileTypes || options.fileTypes.test(file.type))\n        ) {\n          url = loadImage.createObjectURL(file);\n          if (url) {\n            video = this._videoElement.cloneNode(false);\n            video.src = url;\n            video.controls = true;\n            data.video = video;\n            return data;\n          }\n        }\n        return data;\n      },\n\n      // Sets the video element as a property of the file object:\n      setVideo: function (data, options) {\n        if (data.video && !options.disabled) {\n          data.files[data.index][options.name || 'preview'] = data.video;\n        }\n        return data;\n      }\n    }\n  });\n});\n", "/*\n * jQuery File Upload Validation Plugin\n * https://github.com/blueimp/jQuery-File-Upload\n *\n * Copyright 2013, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, require */\n\n(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['jquery', './jquery.fileupload-process'], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS:\n    factory(require('jquery'), require('./jquery.fileupload-process'));\n  } else {\n    // Browser globals:\n    factory(window.jQuery);\n  }\n})(function ($) {\n  'use strict';\n\n  // Append to the default processQueue:\n  $.blueimp.fileupload.prototype.options.processQueue.push({\n    action: 'validate',\n    // Always trigger this action,\n    // even if the previous action was rejected:\n    always: true,\n    // Options taken from the global options map:\n    acceptFileTypes: '@',\n    maxFileSize: '@',\n    minFileSize: '@',\n    maxNumberOfFiles: '@',\n    disabled: '@disableValidation'\n  });\n\n  // The File Upload Validation plugin extends the fileupload widget\n  // with file validation functionality:\n  $.widget('blueimp.fileupload', $.blueimp.fileupload, {\n    options: {\n      /*\n            // The regular expression for allowed file types, matches\n            // against either file type or file name:\n            acceptFileTypes: /(\\.|\\/)(gif|jpe?g|png)$/i,\n            // The maximum allowed file size in bytes:\n            maxFileSize: 10000000, // 10 MB\n            // The minimum allowed file size in bytes:\n            minFileSize: undefined, // No minimal file size\n            // The limit of files to be uploaded:\n            maxNumberOfFiles: 10,\n            */\n\n      // Function returning the current number of files,\n      // has to be overriden for maxNumberOfFiles validation:\n      getNumberOfFiles: $.noop,\n\n      // Error and info messages:\n      messages: {\n        maxNumberOfFiles: 'Maximum number of files exceeded',\n        acceptFileTypes: 'File type not allowed',\n        maxFileSize: 'File is too large',\n        minFileSize: 'File is too small'\n      }\n    },\n\n    processActions: {\n      validate: function (data, options) {\n        if (options.disabled) {\n          return data;\n        }\n        // eslint-disable-next-line new-cap\n        var dfd = $.Deferred(),\n          settings = this.options,\n          file = data.files[data.index],\n          fileSize;\n        if (options.minFileSize || options.maxFileSize) {\n          fileSize = file.size;\n        }\n        if (\n          $.type(options.maxNumberOfFiles) === 'number' &&\n          (settings.getNumberOfFiles() || 0) + data.files.length >\n            options.maxNumberOfFiles\n        ) {\n          file.error = settings.i18n('maxNumberOfFiles');\n        } else if (\n          options.acceptFileTypes &&\n          !(\n            options.acceptFileTypes.test(file.type) ||\n            options.acceptFileTypes.test(file.name)\n          )\n        ) {\n          file.error = settings.i18n('acceptFileTypes');\n        } else if (fileSize > options.maxFileSize) {\n          file.error = settings.i18n('maxFileSize');\n        } else if (\n          $.type(fileSize) === 'number' &&\n          fileSize < options.minFileSize\n        ) {\n          file.error = settings.i18n('minFileSize');\n        } else {\n          delete file.error;\n        }\n        if (file.error || data.files.error) {\n          data.files.error = true;\n          dfd.rejectWith(this, [data]);\n        } else {\n          dfd.resolveWith(this, [data]);\n        }\n        return dfd.promise();\n      }\n    }\n  });\n});\n", "/*\n * jQuery File Upload User Interface Plugin\n * https://github.com/blueimp/jQuery-File-Upload\n *\n * Copyright 2010, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, require */\n\n(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define([\n      'jquery',\n      'blueimp-tmpl',\n      './jquery.fileupload-image',\n      './jquery.fileupload-audio',\n      './jquery.fileupload-video',\n      './jquery.fileupload-validate'\n    ], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS:\n    factory(\n      require('jquery'),\n      require('blueimp-tmpl'),\n      require('./jquery.fileupload-image'),\n      require('./jquery.fileupload-audio'),\n      require('./jquery.fileupload-video'),\n      require('./jquery.fileupload-validate')\n    );\n  } else {\n    // Browser globals:\n    factory(window.jQuery, window.tmpl);\n  }\n})(function ($, tmpl) {\n  'use strict';\n\n  $.blueimp.fileupload.prototype._specialOptions.push(\n    'filesContainer',\n    'uploadTemplateId',\n    'downloadTemplateId'\n  );\n\n  // The UI version extends the file upload widget\n  // and adds complete user interface interaction:\n  $.widget('blueimp.fileupload', $.blueimp.fileupload, {\n    options: {\n      // By default, files added to the widget are uploaded as soon\n      // as the user clicks on the start buttons. To enable automatic\n      // uploads, set the following option to true:\n      autoUpload: false,\n      // The class to show/hide UI elements:\n      showElementClass: 'in',\n      // The ID of the upload template:\n      uploadTemplateId: 'template-upload',\n      // The ID of the download template:\n      downloadTemplateId: 'template-download',\n      // The container for the list of files. If undefined, it is set to\n      // an element with class \"files\" inside of the widget element:\n      filesContainer: undefined,\n      // By default, files are appended to the files container.\n      // Set the following option to true, to prepend files instead:\n      prependFiles: false,\n      // The expected data type of the upload response, sets the dataType\n      // option of the $.ajax upload requests:\n      dataType: 'json',\n\n      // Error and info messages:\n      messages: {\n        unknownError: 'Unknown error'\n      },\n\n      // Function returning the current number of files,\n      // used by the maxNumberOfFiles validation:\n      getNumberOfFiles: function () {\n        return this.filesContainer.children().not('.processing').length;\n      },\n\n      // Callback to retrieve the list of files from the server response:\n      getFilesFromResponse: function (data) {\n        if (data.result && $.isArray(data.result.files)) {\n          return data.result.files;\n        }\n        return [];\n      },\n\n      // The add callback is invoked as soon as files are added to the fileupload\n      // widget (via file input selection, drag & drop or add API call).\n      // See the basic file upload widget for more information:\n      add: function (e, data) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var $this = $(this),\n          that = $this.data('blueimp-fileupload') || $this.data('fileupload'),\n          options = that.options;\n        data.context = that\n          ._renderUpload(data.files)\n          .data('data', data)\n          .addClass('processing');\n        options.filesContainer[options.prependFiles ? 'prepend' : 'append'](\n          data.context\n        );\n        that._forceReflow(data.context);\n        that._transition(data.context);\n        data\n          .process(function () {\n            return $this.fileupload('process', data);\n          })\n          .always(function () {\n            data.context\n              .each(function (index) {\n                $(this)\n                  .find('.size')\n                  .text(that._formatFileSize(data.files[index].size));\n              })\n              .removeClass('processing');\n            that._renderPreviews(data);\n          })\n          .done(function () {\n            data.context.find('.edit,.start').prop('disabled', false);\n            if (\n              that._trigger('added', e, data) !== false &&\n              (options.autoUpload || data.autoUpload) &&\n              data.autoUpload !== false\n            ) {\n              data.submit();\n            }\n          })\n          .fail(function () {\n            if (data.files.error) {\n              data.context.each(function (index) {\n                var error = data.files[index].error;\n                if (error) {\n                  $(this).find('.error').text(error);\n                }\n              });\n            }\n          });\n      },\n      // Callback for the start of each file upload request:\n      send: function (e, data) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var that =\n          $(this).data('blueimp-fileupload') || $(this).data('fileupload');\n        if (\n          data.context &&\n          data.dataType &&\n          data.dataType.substr(0, 6) === 'iframe'\n        ) {\n          // Iframe Transport does not support progress events.\n          // In lack of an indeterminate progress bar, we set\n          // the progress to 100%, showing the full animated bar:\n          data.context\n            .find('.progress')\n            .addClass(!$.support.transition && 'progress-animated')\n            .attr('aria-valuenow', 100)\n            .children()\n            .first()\n            .css('width', '100%');\n        }\n        return that._trigger('sent', e, data);\n      },\n      // Callback for successful uploads:\n      done: function (e, data) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var that =\n            $(this).data('blueimp-fileupload') || $(this).data('fileupload'),\n          getFilesFromResponse =\n            data.getFilesFromResponse || that.options.getFilesFromResponse,\n          files = getFilesFromResponse(data),\n          template,\n          deferred;\n        if (data.context) {\n          data.context.each(function (index) {\n            var file = files[index] || { error: 'Empty file upload result' };\n            deferred = that._addFinishedDeferreds();\n            that._transition($(this)).done(function () {\n              var node = $(this);\n              template = that._renderDownload([file]).replaceAll(node);\n              that._forceReflow(template);\n              that._transition(template).done(function () {\n                data.context = $(this);\n                that._trigger('completed', e, data);\n                that._trigger('finished', e, data);\n                deferred.resolve();\n              });\n            });\n          });\n        } else {\n          template = that\n            ._renderDownload(files)\n            [that.options.prependFiles ? 'prependTo' : 'appendTo'](\n              that.options.filesContainer\n            );\n          that._forceReflow(template);\n          deferred = that._addFinishedDeferreds();\n          that._transition(template).done(function () {\n            data.context = $(this);\n            that._trigger('completed', e, data);\n            that._trigger('finished', e, data);\n            deferred.resolve();\n          });\n        }\n      },\n      // Callback for failed (abort or error) uploads:\n      fail: function (e, data) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var that =\n            $(this).data('blueimp-fileupload') || $(this).data('fileupload'),\n          template,\n          deferred;\n        if (data.context) {\n          data.context.each(function (index) {\n            if (data.errorThrown !== 'abort') {\n              var file = data.files[index];\n              file.error =\n                file.error || data.errorThrown || data.i18n('unknownError');\n              deferred = that._addFinishedDeferreds();\n              that._transition($(this)).done(function () {\n                var node = $(this);\n                template = that._renderDownload([file]).replaceAll(node);\n                that._forceReflow(template);\n                that._transition(template).done(function () {\n                  data.context = $(this);\n                  that._trigger('failed', e, data);\n                  that._trigger('finished', e, data);\n                  deferred.resolve();\n                });\n              });\n            } else {\n              deferred = that._addFinishedDeferreds();\n              that._transition($(this)).done(function () {\n                $(this).remove();\n                that._trigger('failed', e, data);\n                that._trigger('finished', e, data);\n                deferred.resolve();\n              });\n            }\n          });\n        } else if (data.errorThrown !== 'abort') {\n          data.context = that\n            ._renderUpload(data.files)\n            [that.options.prependFiles ? 'prependTo' : 'appendTo'](\n              that.options.filesContainer\n            )\n            .data('data', data);\n          that._forceReflow(data.context);\n          deferred = that._addFinishedDeferreds();\n          that._transition(data.context).done(function () {\n            data.context = $(this);\n            that._trigger('failed', e, data);\n            that._trigger('finished', e, data);\n            deferred.resolve();\n          });\n        } else {\n          that._trigger('failed', e, data);\n          that._trigger('finished', e, data);\n          that._addFinishedDeferreds().resolve();\n        }\n      },\n      // Callback for upload progress events:\n      progress: function (e, data) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var progress = Math.floor((data.loaded / data.total) * 100);\n        if (data.context) {\n          data.context.each(function () {\n            $(this)\n              .find('.progress')\n              .attr('aria-valuenow', progress)\n              .children()\n              .first()\n              .css('width', progress + '%');\n          });\n        }\n      },\n      // Callback for global upload progress events:\n      progressall: function (e, data) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var $this = $(this),\n          progress = Math.floor((data.loaded / data.total) * 100),\n          globalProgressNode = $this.find('.fileupload-progress'),\n          extendedProgressNode = globalProgressNode.find('.progress-extended');\n        if (extendedProgressNode.length) {\n          extendedProgressNode.html(\n            (\n              $this.data('blueimp-fileupload') || $this.data('fileupload')\n            )._renderExtendedProgress(data)\n          );\n        }\n        globalProgressNode\n          .find('.progress')\n          .attr('aria-valuenow', progress)\n          .children()\n          .first()\n          .css('width', progress + '%');\n      },\n      // Callback for uploads start, equivalent to the global ajaxStart event:\n      start: function (e) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var that =\n          $(this).data('blueimp-fileupload') || $(this).data('fileupload');\n        that._resetFinishedDeferreds();\n        that\n          ._transition($(this).find('.fileupload-progress'))\n          .done(function () {\n            that._trigger('started', e);\n          });\n      },\n      // Callback for uploads stop, equivalent to the global ajaxStop event:\n      stop: function (e) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var that =\n            $(this).data('blueimp-fileupload') || $(this).data('fileupload'),\n          deferred = that._addFinishedDeferreds();\n        $.when.apply($, that._getFinishedDeferreds()).done(function () {\n          that._trigger('stopped', e);\n        });\n        that\n          ._transition($(this).find('.fileupload-progress'))\n          .done(function () {\n            $(this)\n              .find('.progress')\n              .attr('aria-valuenow', '0')\n              .children()\n              .first()\n              .css('width', '0%');\n            $(this).find('.progress-extended').html('&nbsp;');\n            deferred.resolve();\n          });\n      },\n      processstart: function (e) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        $(this).addClass('fileupload-processing');\n      },\n      processstop: function (e) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        $(this).removeClass('fileupload-processing');\n      },\n      // Callback for file deletion:\n      destroy: function (e, data) {\n        if (e.isDefaultPrevented()) {\n          return false;\n        }\n        var that =\n            $(this).data('blueimp-fileupload') || $(this).data('fileupload'),\n          removeNode = function () {\n            that._transition(data.context).done(function () {\n              $(this).remove();\n              that._trigger('destroyed', e, data);\n            });\n          };\n        if (data.url) {\n          data.dataType = data.dataType || that.options.dataType;\n          $.ajax(data)\n            .done(removeNode)\n            .fail(function () {\n              that._trigger('destroyfailed', e, data);\n            });\n        } else {\n          removeNode();\n        }\n      }\n    },\n\n    _resetFinishedDeferreds: function () {\n      this._finishedUploads = [];\n    },\n\n    _addFinishedDeferreds: function (deferred) {\n      // eslint-disable-next-line new-cap\n      var promise = deferred || $.Deferred();\n      this._finishedUploads.push(promise);\n      return promise;\n    },\n\n    _getFinishedDeferreds: function () {\n      return this._finishedUploads;\n    },\n\n    // Link handler, that allows to download files\n    // by drag & drop of the links to the desktop:\n    _enableDragToDesktop: function () {\n      var link = $(this),\n        url = link.prop('href'),\n        name = link.prop('download'),\n        type = 'application/octet-stream';\n      link.on('dragstart', function (e) {\n        try {\n          e.originalEvent.dataTransfer.setData(\n            'DownloadURL',\n            [type, name, url].join(':')\n          );\n        } catch (ignore) {\n          // Ignore exceptions\n        }\n      });\n    },\n\n    _formatFileSize: function (bytes) {\n      if (typeof bytes !== 'number') {\n        return '';\n      }\n      if (bytes >= 1000000000) {\n        return (bytes / 1000000000).toFixed(2) + ' GB';\n      }\n      if (bytes >= 1000000) {\n        return (bytes / 1000000).toFixed(2) + ' MB';\n      }\n      return (bytes / 1000).toFixed(2) + ' KB';\n    },\n\n    _formatBitrate: function (bits) {\n      if (typeof bits !== 'number') {\n        return '';\n      }\n      if (bits >= 1000000000) {\n        return (bits / 1000000000).toFixed(2) + ' Gbit/s';\n      }\n      if (bits >= 1000000) {\n        return (bits / 1000000).toFixed(2) + ' Mbit/s';\n      }\n      if (bits >= 1000) {\n        return (bits / 1000).toFixed(2) + ' kbit/s';\n      }\n      return bits.toFixed(2) + ' bit/s';\n    },\n\n    _formatTime: function (seconds) {\n      var date = new Date(seconds * 1000),\n        days = Math.floor(seconds / 86400);\n      days = days ? days + 'd ' : '';\n      return (\n        days +\n        ('0' + date.getUTCHours()).slice(-2) +\n        ':' +\n        ('0' + date.getUTCMinutes()).slice(-2) +\n        ':' +\n        ('0' + date.getUTCSeconds()).slice(-2)\n      );\n    },\n\n    _formatPercentage: function (floatValue) {\n      return (floatValue * 100).toFixed(2) + ' %';\n    },\n\n    _renderExtendedProgress: function (data) {\n      return (\n        this._formatBitrate(data.bitrate) +\n        ' | ' +\n        this._formatTime(((data.total - data.loaded) * 8) / data.bitrate) +\n        ' | ' +\n        this._formatPercentage(data.loaded / data.total) +\n        ' | ' +\n        this._formatFileSize(data.loaded) +\n        ' / ' +\n        this._formatFileSize(data.total)\n      );\n    },\n\n    _renderTemplate: function (func, files) {\n      if (!func) {\n        return $();\n      }\n      var result = func({\n        files: files,\n        formatFileSize: this._formatFileSize,\n        options: this.options\n      });\n      if (result instanceof $) {\n        return result;\n      }\n      return $(this.options.templatesContainer).html(result).children();\n    },\n\n    _renderPreviews: function (data) {\n      data.context.find('.preview').each(function (index, elm) {\n        $(elm).empty().append(data.files[index].preview);\n      });\n    },\n\n    _renderUpload: function (files) {\n      return this._renderTemplate(this.options.uploadTemplate, files);\n    },\n\n    _renderDownload: function (files) {\n      return this._renderTemplate(this.options.downloadTemplate, files)\n        .find('a[download]')\n        .each(this._enableDragToDesktop)\n        .end();\n    },\n\n    _editHandler: function (e) {\n      e.preventDefault();\n      if (!this.options.edit) return;\n      var that = this,\n        button = $(e.currentTarget),\n        template = button.closest('.template-upload'),\n        data = template.data('data'),\n        index = button.data().index;\n      this.options.edit(data.files[index]).then(function (file) {\n        if (!file) return;\n        data.files[index] = file;\n        data.context.addClass('processing');\n        template.find('.edit,.start').prop('disabled', true);\n        $(that.element)\n          .fileupload('process', data)\n          .always(function () {\n            template\n              .find('.size')\n              .text(that._formatFileSize(data.files[index].size));\n            data.context.removeClass('processing');\n            that._renderPreviews(data);\n          })\n          .done(function () {\n            template.find('.edit,.start').prop('disabled', false);\n          })\n          .fail(function () {\n            template.find('.edit').prop('disabled', false);\n            var error = data.files[index].error;\n            if (error) {\n              template.find('.error').text(error);\n            }\n          });\n      });\n    },\n\n    _startHandler: function (e) {\n      e.preventDefault();\n      var button = $(e.currentTarget),\n        template = button.closest('.template-upload'),\n        data = template.data('data');\n      button.prop('disabled', true);\n      if (data && data.submit) {\n        data.submit();\n      }\n    },\n\n    _cancelHandler: function (e) {\n      e.preventDefault();\n      var template = $(e.currentTarget).closest(\n          '.template-upload,.template-download'\n        ),\n        data = template.data('data') || {};\n      data.context = data.context || template;\n      if (data.abort) {\n        data.abort();\n      } else {\n        data.errorThrown = 'abort';\n        this._trigger('fail', e, data);\n      }\n    },\n\n    _deleteHandler: function (e) {\n      e.preventDefault();\n      var button = $(e.currentTarget);\n      this._trigger(\n        'destroy',\n        e,\n        $.extend(\n          {\n            context: button.closest('.template-download'),\n            type: 'DELETE'\n          },\n          button.data()\n        )\n      );\n    },\n\n    _forceReflow: function (node) {\n      return $.support.transition && node.length && node[0].offsetWidth;\n    },\n\n    _transition: function (node) {\n      // eslint-disable-next-line new-cap\n      var dfd = $.Deferred();\n      if (\n        $.support.transition &&\n        node.hasClass('fade') &&\n        node.is(':visible')\n      ) {\n        var transitionEndHandler = function (e) {\n          // Make sure we don't respond to other transition events\n          // in the container element, e.g. from button elements:\n          if (e.target === node[0]) {\n            node.off($.support.transition.end, transitionEndHandler);\n            dfd.resolveWith(node);\n          }\n        };\n        node\n          .on($.support.transition.end, transitionEndHandler)\n          .toggleClass(this.options.showElementClass);\n      } else {\n        node.toggleClass(this.options.showElementClass);\n        dfd.resolveWith(node);\n      }\n      return dfd;\n    },\n\n    _initButtonBarEventHandlers: function () {\n      var fileUploadButtonBar = this.element.find('.fileupload-buttonbar'),\n        filesList = this.options.filesContainer;\n      this._on(fileUploadButtonBar.find('.start'), {\n        click: function (e) {\n          e.preventDefault();\n          filesList.find('.start').trigger('click');\n        }\n      });\n      this._on(fileUploadButtonBar.find('.cancel'), {\n        click: function (e) {\n          e.preventDefault();\n          filesList.find('.cancel').trigger('click');\n        }\n      });\n      this._on(fileUploadButtonBar.find('.delete'), {\n        click: function (e) {\n          e.preventDefault();\n          filesList\n            .find('.toggle:checked')\n            .closest('.template-download')\n            .find('.delete')\n            .trigger('click');\n          fileUploadButtonBar.find('.toggle').prop('checked', false);\n        }\n      });\n      this._on(fileUploadButtonBar.find('.toggle'), {\n        change: function (e) {\n          filesList\n            .find('.toggle')\n            .prop('checked', $(e.currentTarget).is(':checked'));\n        }\n      });\n    },\n\n    _destroyButtonBarEventHandlers: function () {\n      this._off(\n        this.element\n          .find('.fileupload-buttonbar')\n          .find('.start, .cancel, .delete'),\n        'click'\n      );\n      this._off(this.element.find('.fileupload-buttonbar .toggle'), 'change.');\n    },\n\n    _initEventHandlers: function () {\n      this._super();\n      this._on(this.options.filesContainer, {\n        'click .edit': this._editHandler,\n        'click .start': this._startHandler,\n        'click .cancel': this._cancelHandler,\n        'click .delete': this._deleteHandler\n      });\n      this._initButtonBarEventHandlers();\n    },\n\n    _destroyEventHandlers: function () {\n      this._destroyButtonBarEventHandlers();\n      this._off(this.options.filesContainer, 'click');\n      this._super();\n    },\n\n    _enableFileInputButton: function () {\n      this.element\n        .find('.fileinput-button input')\n        .prop('disabled', false)\n        .parent()\n        .removeClass('disabled');\n    },\n\n    _disableFileInputButton: function () {\n      this.element\n        .find('.fileinput-button input')\n        .prop('disabled', true)\n        .parent()\n        .addClass('disabled');\n    },\n\n    _initTemplates: function () {\n      var options = this.options;\n      options.templatesContainer = this.document[0].createElement(\n        options.filesContainer.prop('nodeName')\n      );\n      if (tmpl) {\n        if (options.uploadTemplateId) {\n          options.uploadTemplate = tmpl(options.uploadTemplateId);\n        }\n        if (options.downloadTemplateId) {\n          options.downloadTemplate = tmpl(options.downloadTemplateId);\n        }\n      }\n    },\n\n    _initFilesContainer: function () {\n      var options = this.options;\n      if (options.filesContainer === undefined) {\n        options.filesContainer = this.element.find('.files');\n      } else if (!(options.filesContainer instanceof $)) {\n        options.filesContainer = $(options.filesContainer);\n      }\n    },\n\n    _initSpecialOptions: function () {\n      this._super();\n      this._initFilesContainer();\n      this._initTemplates();\n    },\n\n    _create: function () {\n      this._super();\n      this._resetFinishedDeferreds();\n      if (!$.support.fileInput) {\n        this._disableFileInputButton();\n      }\n    },\n\n    enable: function () {\n      var wasDisabled = false;\n      if (this.options.disabled) {\n        wasDisabled = true;\n      }\n      this._super();\n      if (wasDisabled) {\n        this.element.find('input, button').prop('disabled', false);\n        this._enableFileInputButton();\n      }\n    },\n\n    disable: function () {\n      if (!this.options.disabled) {\n        this.element.find('input, button').prop('disabled', true);\n        this._disableFileInputButton();\n      }\n      this._super();\n    }\n  });\n});\n"]}