{"version": 3, "sources": ["pGenerator.jquery.js"], "names": ["$", "numbers_array", "upper_letters_array", "lower_letters_array", "special_chars_array", "methods", "init", "options", "callbacks", "settings", "extend", "bind", "passwordElement", "displayElement", "<PERSON><PERSON><PERSON><PERSON>", "uppercase", "lowercase", "numbers", "specialChars", "additionalSpecialChars", "onPasswordGenerated", "generatedPassword", "i", "push", "concat", "this", "each", "e", "preventDefault", "generatePassword", "call", "password", "Array", "selOptions", "selected", "no_lower_letters", "optionLength", "Math", "floor", "String", "fromCharCode", "randomFromInterval", "length", "remained", "o", "j", "x", "parseInt", "random", "shuffle", "join", "val", "is", "text", "from", "to", "fn", "pGenerator", "method", "apply", "prototype", "slice", "arguments", "error", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;CAQA,SAAUA,GAEN,IAAIC,EAAsB,GACtBC,EAAsB,GACtBC,EAAsB,GACtBC,EAAsB,GAQtBC,EAAU,CAUVC,KAAM,SAASC,EAASC,GAepB,IAbA,IAAIC,EAAWT,EAAEU,OAAO,CACpBC,KAAQ,QACRC,gBAAmB,KACnBC,eAAkB,KAClBC,eAAkB,GAClBC,WAAa,EACbC,WAAa,EACbC,SAAa,EACbC,cAAgB,EAChBC,uBAA0B,GAC1BC,oBAAuB,SAASC,MACjCd,GAEKe,EAAI,GAAIA,EAAI,GAAIA,IACpBrB,EAAcsB,KAAKD,GAGvB,IAAIA,EAAI,GAAIA,EAAI,GAAIA,IAChBpB,EAAoBqB,KAAKD,GAG7B,IAAIA,EAAI,GAAIA,EAAI,IAAKA,IACjBnB,EAAoBoB,KAAKD,GAK7B,OAFAlB,EAAsB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAIoB,OAAOf,EAASU,wBAElGM,KAAKC,MAAK,WAEQ1B,EAAEyB,MAEJd,KAAKF,EAASE,MAAM,SAASgB,GAC5CA,EAAEC,iBACFvB,EAAQwB,iBAAiBC,KAAKL,KAAMhB,UAWhDoB,iBAAkB,SAASpB,GAEvB,IAAIsB,EAAW,IAAIC,MACfC,EAAaxB,EAASM,UAAYN,EAASO,UAAYP,EAASQ,QAAUR,EAASS,aACnFgB,EAAW,EACXC,EAAmB,IAAIH,MAEvBI,EAAeC,KAAKC,MAAM7B,EAASK,eAAiBmB,GAExD,GAAGxB,EAASM,UAAW,CAEnB,IAAI,IAAIO,EAAI,EAAGA,EAAIc,EAAcd,IAC7BS,EAASR,KAAKgB,OAAOC,aAAatC,EAAoBuC,EAAmB,EAAGvC,EAAoBwC,OAAS,MAG7GP,EAAmBA,EAAiBX,OAAOtB,GAE3CgC,IAGJ,GAAGzB,EAASQ,QAAS,CAEjB,IAAQK,EAAI,EAAGA,EAAIc,EAAcd,IAC7BS,EAASR,KAAKgB,OAAOC,aAAavC,EAAcwC,EAAmB,EAAGxC,EAAcyC,OAAS,MAGjGP,EAAmBA,EAAiBX,OAAOvB,GAE3CiC,IAGJ,GAAGzB,EAASS,aAAc,CAEtB,IAAQI,EAAI,EAAGA,EAAIc,EAAcd,IAC7BS,EAASR,KAAKgB,OAAOC,aAAapC,EAAoBqC,EAAmB,EAAGrC,EAAoBsC,OAAS,MAG7GP,EAAmBA,EAAiBX,OAAOpB,GAE3C8B,IAGJ,IAAIS,EAAWlC,EAASK,eAAkBoB,EAAWE,EAErD,GAAG3B,EAASO,UAER,IAAQM,EAAI,EAAGA,EAAIqB,EAAUrB,IACzBS,EAASR,KAAKgB,OAAOC,aAAarC,EAAoBsC,EAAmB,EAAGtC,EAAoBuC,OAAS,WAK7G,IAAQpB,EAAI,EAAGA,EAAIqB,EAAUrB,IACzBS,EAASR,KAAKgB,OAAOC,aAAaL,EAAiBM,EAAmB,EAAGN,EAAiBO,OAAS,MAI3GX,EAyBR,SAAiBa,GAEb,IAAI,IAAIC,EAAGC,EAAGxB,EAAIsB,EAAEF,OAAQpB,EAAGuB,EAAIE,SAASV,KAAKW,SAAW1B,GAAIwB,EAAIF,IAAItB,GAAIsB,EAAEtB,GAAKsB,EAAEC,GAAID,EAAEC,GAAKC,GAEhG,OAAOF,EA7BQK,CAAQlB,GAAUmB,KAAK,IAEF,OAA7BzC,EAASG,iBACRZ,EAAES,EAASG,iBAAiBuC,IAAIpB,GAGL,OAA5BtB,EAASI,iBACLb,EAAES,EAASI,gBAAgBuC,GAAG,SAC7BpD,EAAES,EAASI,gBAAgBsC,IAAIpB,GAE/B/B,EAAES,EAASI,gBAAgBwC,KAAKtB,IAIxCtB,EAASW,oBAAoBU,KAAKL,KAAMM,KA0BhD,SAASU,EAAmBa,EAAMC,GAE9B,OAAOlB,KAAKC,MAAMD,KAAKW,UAAUO,EAAGD,EAAK,GAAGA,GAShDtD,EAAEwD,GAAGC,WAAa,SAASC,GAEvB,OAAIrD,EAAQqD,GACDrD,EAAQqD,GAAQC,MAAMlC,KAAMO,MAAM4B,UAAUC,MAAM/B,KAAKgC,UAAW,IAElD,iBAAXJ,GAAwBA,OAIpC1D,EAAE+D,MAAO,UAAaL,EAAS,wCAHxBrD,EAAQC,KAAKqD,MAAMlC,KAAMqC,YAvL5C,CA8LGE", "sourcesContent": ["/*!\r\n * pGenerator jQuery Plugin v1.0.5\r\n * https://github.com/M1Sh0u/pGenerator\r\n *\r\n * Created by <PERSON><PERSON> <<EMAIL>>\r\n * Released under the MIT License (Feel free to copy, modify or redistribute this plugin.)\r\n */\r\n\r\n(function($){\r\n\r\n    var numbers_array       = [],\r\n        upper_letters_array = [],\r\n        lower_letters_array = [],\r\n        special_chars_array = [],\r\n        $pGeneratorElement  = null;\r\n\r\n    /**\r\n     * Plugin methods.\r\n     *\r\n     * @type {{init: init, generatePassword: generatePassword}}\r\n     */\r\n    var methods = {\r\n\r\n        /**\r\n         * Initialize the object.\r\n         *\r\n         * @param options\r\n         * @param callbacks\r\n         *\r\n         * @returns {*}\r\n         */\r\n        init: function(options, callbacks)\r\n        {\r\n            var settings = $.extend({\r\n                'bind': 'click',\r\n                'passwordElement': null,\r\n                'displayElement': null,\r\n                'passwordLength': 16,\r\n                'uppercase': true,\r\n                'lowercase': true,\r\n                'numbers':   true,\r\n                'specialChars': true,\r\n                'additionalSpecialChars': [],\r\n                'onPasswordGenerated': function(generatedPassword) { }\r\n            }, options);\r\n\r\n            for(var i = 48; i < 58; i++) {\r\n                numbers_array.push(i);\r\n            }\r\n\r\n            for(i = 65; i < 91; i++) {\r\n                upper_letters_array.push(i);\r\n            }\r\n\r\n            for(i = 97; i < 123; i++) {\r\n                lower_letters_array.push(i);\r\n            }\r\n            \r\n            special_chars_array = [33, 35, 64, 36, 42, 91, 93, 123, 125, 92, 47, 63, 58, 59, 95, 45].concat(settings.additionalSpecialChars); //***\r\n\r\n            return this.each(function(){\r\n\r\n                $pGeneratorElement = $(this);\r\n\r\n                $pGeneratorElement.bind(settings.bind, function(e){\r\n                    e.preventDefault();\r\n                    methods.generatePassword.call(this, settings); //***\r\n                });\r\n\r\n            });\r\n        },\r\n\r\n        /**\r\n         * Generate the password.\r\n         *\r\n         * @param {object} settings\r\n         */\r\n        generatePassword: function(settings)\r\n        {\r\n            var password = new Array(),\r\n                selOptions = settings.uppercase + settings.lowercase + settings.numbers + settings.specialChars,\r\n                selected = 0,\r\n                no_lower_letters = new Array();\r\n\r\n            var optionLength = Math.floor(settings.passwordLength / selOptions);\r\n\r\n            if(settings.uppercase) {\r\n                // uppercase letters\r\n                for(var i = 0; i < optionLength; i++) {\r\n                    password.push(String.fromCharCode(upper_letters_array[randomFromInterval(0, upper_letters_array.length - 1)]));\r\n                }\r\n\r\n                no_lower_letters = no_lower_letters.concat(upper_letters_array);\r\n\r\n                selected++;\r\n            }\r\n\r\n            if(settings.numbers) {\r\n                // numbers letters\r\n                for(var i = 0; i < optionLength; i++) {\r\n                    password.push(String.fromCharCode(numbers_array[randomFromInterval(0, numbers_array.length - 1)]));\r\n                }\r\n\r\n                no_lower_letters = no_lower_letters.concat(numbers_array);\r\n\r\n                selected++;\r\n            }\r\n\r\n            if(settings.specialChars) {\r\n                // numbers letters\r\n                for(var i = 0; i < optionLength; i++) {\r\n                    password.push(String.fromCharCode(special_chars_array[randomFromInterval(0, special_chars_array.length - 1)]));\r\n                }\r\n\r\n                no_lower_letters = no_lower_letters.concat(special_chars_array);\r\n\r\n                selected++;\r\n            }\r\n\r\n            var remained = settings.passwordLength - (selected * optionLength);\r\n\r\n            if(settings.lowercase) {\r\n\r\n                for(var i = 0; i < remained; i++) {\r\n                    password.push(String.fromCharCode(lower_letters_array[randomFromInterval(0, lower_letters_array.length - 1)]));\r\n                }\r\n\r\n            } else {\r\n\r\n                for(var i = 0; i < remained; i++) {\r\n                    password.push(String.fromCharCode(no_lower_letters[randomFromInterval(0, no_lower_letters.length - 1)]));\r\n                }\r\n            }\r\n\r\n            password = shuffle(password).join('');\r\n\r\n            if(settings.passwordElement !== null) {\r\n                $(settings.passwordElement).val(password);\r\n            }\r\n\r\n            if(settings.displayElement !== null) {\r\n                if($(settings.displayElement).is(\"input\")) {\r\n                    $(settings.displayElement).val(password);\r\n                } else {\r\n                    $(settings.displayElement).text(password);\r\n                }\r\n            }\r\n\r\n            settings.onPasswordGenerated.call(this, password); //***\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Shuffle the password.\r\n     *\r\n     * @param {Array} o\r\n     *\r\n     * @returns {Array}\r\n     */\r\n    function shuffle(o)\r\n    {\r\n        for(var j, x, i = o.length; i; j = parseInt(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);\r\n\r\n        return o;\r\n    }\r\n\r\n    /**\r\n     * Get a random number in the given interval.\r\n     *\r\n     * @param {number} from\r\n     * @param {number} to\r\n     *\r\n     * @returns {number}\r\n     */\r\n    function randomFromInterval(from, to)\r\n    {\r\n        return Math.floor(Math.random()*(to-from+1)+from);\r\n    }\r\n\r\n    /**\r\n     * Define the pGenerator jQuery plugin.\r\n     *\r\n     * @param method\r\n     * @returns {*}\r\n     */\r\n    $.fn.pGenerator = function(method)\r\n    {\r\n        if (methods[method]) {\r\n            return methods[method].apply(this, Array.prototype.slice.call(arguments, 1));\r\n        }\r\n        else if (typeof method === 'object' || !method) {\r\n            return methods.init.apply(this, arguments);\r\n        }\r\n        else {\r\n            $.error( 'Method ' +  method + ' does not exist on jQuery.pGenerator' );\r\n        }\r\n    };\r\n\r\n})(jQuery);\r\n"]}