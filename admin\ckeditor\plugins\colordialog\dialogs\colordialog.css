/**
 * @license Copyright (c) 2003-2020, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */

.cke_colordialog_colorcell {
	width: 12px; /* All cells have equal width which depends on parent width (in this case table parent). Width works more like max-width. */
	height: 14px;
	padding: 1px; /* Padding is replaced by border for focused cells. Prevents 'jumping' when adding borders. */
}

.cke_colordialog_colorcell.cke_colordialog_focused_light,
.cke_colordialog_colorcell.cke_colordialog_focused_dark {
	padding: 0; /* Shrink cell to allow 1px border indicating focus. */
	border: 1px dotted #000;
}

.cke_colordialog_colorcell.cke_colordialog_focused_dark {
	border-color: #FFF;
}
