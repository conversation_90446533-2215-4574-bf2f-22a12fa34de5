<?php
//include("functions.php");
//$db->debug=true;

// Inizializza il sistema di misurazione dei tempi
$start_time = microtime(true);
$query_times = [];
$query_count = 0;

function logQueryTime($query_name, $start, $end) {
    global $query_times, $query_count;
    $execution_time = ($end - $start) * 1000; // in millisecondi
    $query_times[] = [
        'name' => $query_name,
        'time' => $execution_time,
        'count' => ++$query_count
    ];
    return $execution_time;
}

$i[1]=0;
$i[2]=0;
$i[3]=0;
$i[4]=0;
$i[5]=0;
$i[6]=0;
$i[7]=0;
$i[8]=0;
$i[9]=0;
$i[10]=0;
$i[11]=0;
$i[12]=0;
$i[13]=0;
$i[14]=0;
$i[15]=0;

$inizio=$_REQUEST['inizio'];

if(!$inizio)$inizio=date("Y-m-d");

?>
<style>
body{font-family:Verdana}
table, th, td {
  border: 1px solid black;
  border-collapse: collapse;
}
th, td {
  padding: 1px;
}
td.occupata1{background-color:#ff0000;}
td.occupata2{background-color:#5eb9f2;}
td.ordine_open{background-color:#fff83a;}
td.bloccato{background-color:#000000;}

</style>
<form action="<?=url("calendario")?>" method="post">
<input type='hidden' name='action' value='invia'/>
<input type='text' name='inizio' value='<?=$inizio?>'/>
<input type='text' name='fine' value=''/>
<input type='submit' name='action' value='invia'/>
</form>

<?php
if($inizio){ $inizio=$inizio." 00:00:00";}
else{$inizio=date("Y-m-d")." 00:00:00";}

if($_POST['fine']){$fine=$_POST['fine']." 23:59:00";}
else{
    $A=strtotime($inizio);
    $A= $A+(60*60*24*5);
    $fine=date("Y-m-d",$A)." 23:59:00";
    $next=date("Y-m-d",$A);
}

//print " $inizio $fine";exit;
//$inizio="2022-01-25 00:00:00";
//$fine="2022-02-05 00:00:00";
$A=strtotime($inizio);
$B=strtotime($fine);

?>
<a href="<?=url("calendario")?>?inizio=<?=$next?>">NEXT 5</a>
<table style="font-size:10px">
<?php
print "<tr><td></td><td style=''>";

// Misura il tempo della query piazzole
$query_start = microtime(true);
$db->query("SELECT * FROM piazzole ORDER BY id");
$piazzole=$db->fetch_obj_all();
$query_end = microtime(true);
logQueryTime("SELECT piazzole", $query_start, $query_end);
foreach($piazzole as $p){
    $riga.="$p->piazzola_it</td><td>";
}
$riga=rtrim($riga,"<td>");
$riga.="</tr>";
print $riga;

$riga="";
for($data=$A;$data<=$B;$data+3600){
    $riga="<tr><td cellpadding='0'>";
    $time = date('Y-m-d H:i', $data);
    if(date('H:i',$data)=="00:00"){
        $riga.="<b>".date("d/m",$data)."</b>";
        $txt=true;
    }
    else{
        $riga.=date('H:i',$data);
        $txt=false;
    }
    $riga.="</td>";
    foreach($piazzole as $p){
        $i++;
        $data = $data + 60;
        $time = date('Y-m-d H:i:s', $data);
        //print "$i $data $time";
        

        // Misura il tempo della query ordini
        $query_start = microtime(true);
        $db->query("SELECT id,status FROM ordini WHERE
            ? BETWEEN  data_arrivo AND data_partenza
            AND piazzola_id = '$p->id'
        ",array($time));
        $result=$db->fetch_obj();
        $query_end = microtime(true);
        logQueryTime("SELECT ordini (piazzola $p->id)", $query_start, $query_end);
        
        $new_ordine_id[$p->id]=$result->id;
        
        if($new_ordine_id[$p->id] <> $old_ordine_id[$p->id] && $result->id){
            $i[$p->id]=$i[$p->id]+1;
            //print "<pre>*";print_r($i[15]);print "</pre>";
        }
        
        if($result->status=="close"){
            if($i[$p->id]%2==0){
                $class="occupata1";
            }
            else{
                $class="occupata2";
            }
        }
        if($result->status=="open"){
            $class="ordine_open";
        }
        
        if(!$p->active){
            $class="bloccato";
        }
        
        //$simbolo=count($result)>0?"■":"";
        //$class=count($result)>0?"":"";
        $testo_piazzola=($txt)?$p->piazzola_it:"";
        //$riga.="<td class='".(count($result)>0?$class:"")."' style='width:20px'><a href='https://www.google.com' target='_blank'><div style='width:22px;height:22px;'>$testo_piazzola</div></a></td>";
        if($result->status=="close" OR !$result->status){
            if(!$p->active){
                $riga.="<td class='bloccato' style='width:20px'><div style='width:22px;height:22px;'>$testo_piazzola</div></td>";    
            }
            else{
                $riga.="<td class='".(count($result)>0?$class:"")."' style='width:20px'><a href='prenotazione_detail?id=$result->id' target='_blank'><div style='width:22px;height:22px;'>$testo_piazzola</div></a></td>";
            }
        }
        
        if($result->status=="open"){
            $riga.="<td class='".(count($result)>0?$class:"")."' style='width:20px'><a href='attesa_pagamento' target='_blank'><div style='width:22px;height:22px;'>$testo_piazzola</div></a></td>";
        }
        
        
        //if(date('H:i',$data)=="00:01")$riga.= "|";
        //$riga.= $simbolo;
        $old_ordine_id[$p->id]=$new_ordine_id[$p->id];
        
        
        
        //$riga="";
        $data = $data - 60;
        //$data = $data + 3600;//1 hour
        //print "<br/>";
        
    }
    
    $data = $data + 3600;//1 hour
    $riga.="</tr>\r\n";
    print $riga;
    
}
print "</table>";

// Calcola e mostra le statistiche dei tempi
$total_time = (microtime(true) - $start_time) * 1000; // in millisecondi
$total_query_time = array_sum(array_column($query_times, 'time'));

echo "<div style='margin-top: 20px; padding: 10px; border: 1px solid #ccc; background-color: #f9f9f9;'>";
echo "<h3>📊 Statistiche Tempi di Esecuzione</h3>";
echo "<p><strong>Tempo totale pagina:</strong> " . number_format($total_time, 2) . " ms</p>";
echo "<p><strong>Tempo totale query:</strong> " . number_format($total_query_time, 2) . " ms (" . number_format(($total_query_time/$total_time)*100, 1) . "% del totale)</p>";
echo "<p><strong>Numero totale query:</strong> " . count($query_times) . "</p>";
echo "<p><strong>Tempo medio per query:</strong> " . number_format($total_query_time/count($query_times), 2) . " ms</p>";

// Raggruppa le query per tipo
$query_groups = [];
foreach($query_times as $qt) {
    if(strpos($qt['name'], 'piazzole') !== false) {
        $query_groups['piazzole'][] = $qt['time'];
    } else {
        $query_groups['ordini'][] = $qt['time'];
    }
}

echo "<h4>📈 Dettaglio per tipo di query:</h4>";
foreach($query_groups as $type => $times) {
    $avg_time = array_sum($times) / count($times);
    $total_type_time = array_sum($times);
    echo "<p><strong>Query $type:</strong> " . count($times) . " esecuzioni, ";
    echo "tempo totale: " . number_format($total_type_time, 2) . " ms, ";
    echo "tempo medio: " . number_format($avg_time, 2) . " ms</p>";
}

// Mostra le query più lente
echo "<h4>🐌 Top 10 query più lente:</h4>";
usort($query_times, function($a, $b) { return $b['time'] <=> $a['time']; });
echo "<ul>";
for($i = 0; $i < min(10, count($query_times)); $i++) {
    $qt = $query_times[$i];
    echo "<li>" . htmlspecialchars($qt['name']) . ": " . number_format($qt['time'], 2) . " ms</li>";
}
echo "</ul>";

echo "</div>";

exit;
/*
$riga="";

$db->query("SELECT * FROM piazzole ORDER BY id");
$piazzole=$db->fetch_obj_all();
foreach($piazzole as $p){
    print "<tr><td style='font-size:12px;font-weight:bold;'>Piazzola&nbsp;$p->piazzola_it</td><td>";
    for($data=$A;$data<=$B;$data+3600){
        $i++;
        $data = $data + 60;
        $time = date('Y-m-d H:i:s', $data);
        //print "$i $data $time";
        
        
        $db->query("SELECT * FROM ordini WHERE 
            ? BETWEEN  data_arrivo AND data_partenza
            AND status='close' 
            AND piazzola_id = '$p->id'
        ",array($time));
        $result=$db->fetch_obj();
        $simbolo=count($result)>0?"■":"-";
        if(date('H:i',$data)=="00:01")$riga.= "|";
        $riga.= $simbolo;
        
        
        print $riga;
        $riga="";
        $data = $data - 60;
        $data = $data + 3600;//1 hour
        //print "<br/>";
    }
    print "</td></tr>";
}
?>


<?php
print "<tr><td></td><td style='font-size:12.4px'>";
for($data=$A;$data<=$B;$data+3600){
    
    $time = date('Y-m-d H:i', $data);
    if(date('H:i',$data)=="00:00")$riga.=date("d/m",$data)."&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
    $data = $data + 3600;//1 hour
}
print "$riga</td></tr>";
$riga="";

$db->query("SELECT * FROM piazzole ORDER BY id");
$piazzole=$db->fetch_obj_all();
foreach($piazzole as $p){
    print "<tr><td style='font-size:12px;font-weight:bold;'>Piazzola&nbsp;$p->piazzola_it</td><td>";
    for($data=$A;$data<=$B;$data+3600){
        $i++;
        $data = $data + 60;
        $time = date('Y-m-d H:i:s', $data);
        //print "$i $data $time";
        
        
        $db->query("SELECT * FROM ordini WHERE 
            ? BETWEEN  data_arrivo AND data_partenza
            AND status='close' 
            AND piazzola_id = '$p->id'
        ",array($time));
        $result=$db->fetch_obj();
        $simbolo=count($result)>0?"■":"-";
        if(date('H:i',$data)=="00:01")$riga.= "|";
        $riga.= $simbolo;
        
        
        print $riga;
        $riga="";
        $data = $data - 60;
        $data = $data + 3600;//1 hour
        //print "<br/>";
    }
    print "</td></tr>";
}
?>

</table>
*/
