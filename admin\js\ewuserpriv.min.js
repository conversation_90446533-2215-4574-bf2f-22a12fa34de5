loadjs.ready("makerjs",(function(){var e=jQuery;function a(e,a){return'<div class="custom-control custom-checkbox"><input type="checkbox" class="custom-control-input ew-priv" name="'+e+'" id="'+e+'" onclick="ew.selectAll(this);"><label class="custom-control-label" for="'+e+'">'+ew.language.phrase("Permission"+(a||e))+"</label></div>"}var t,s={table:{title:'<span class="font-weight-normal">'+ew.language.phrase("TableOrView")+"</span>",display:function(e){var a=e.record;return a.table+'<input type="hidden" name="table_'+a.index+'" value="1">'},sorting:!0}};["add","delete","edit","list","lookup","view","search","import","admin"].forEach((function(e){var t,i;s[e]={title:a(e),display:(t=e,i=priv[e],function(e){var a=e.record,s=t+"_"+a.index,r=(a.permission&i)==i;return a.checked=r,'<div class="custom-control custom-checkbox d-inline-block"><input type="checkbox" class="custom-control-input ew-priv ew-multi-select" name="'+s+'" id="'+s+'" value="'+i+'" data-index="'+a.index+'"'+(r?" checked":"")+((a.allowed&i)!=i?" disabled":"")+'><label class="custom-control-label" for="'+s+'"></label></div>'}),sorting:!1}})),e(".ew-card.ew-user-priv .ew-card-body").ewjtable({paging:!1,sorting:!0,defaultSorting:"table ASC",fields:s,actions:{listAction:function(a,t){var s=priv.permissions.slice(0);if(a&&a.table){var i=a.table.toLowerCase();s=e.map(s,(function(e){return e.table.toLowerCase().includes(i)?e:null}))}if(t&&t.sorting){var r=t.sorting.match(/ASC$/);s.sort((function(e,a){return a.table.toLowerCase()>e.table.toLowerCase()?r?-1:1:a.table.toLowerCase()===e.table.toLowerCase()?0:a.table.toLowerCase()<e.table.toLowerCase()?r?1:-1:void 0}))}return{result:"OK",params:Object.assign({},a,t),records:s}}},rowInserted:function(a,t){t.row.find("input[type=checkbox]").on("click",(function(){var a=e(this),t=parseInt(a.data("index"),10),s=parseInt(a.data("value"),10);this.checked?priv.permissions[t].permission=priv.permissions[t].permission|s:priv.permissions[t].permission=priv.permissions[t].permission^s}))},recordsLoaded:function(a,t){var s=t.serverResponse.params.sorting,i=e(this).find(".ewjtable-main-container"),r=i.find(".ewjtable.table"),o=r.find(".ewjtable-column-header-container:first");useFixedHeaderTable&&(tableHeight&&i.height(tableHeight),r.addClass("table-head-fixed ew-fixed-header-table"),ew.USE_OVERLAY_SCROLLBARS&&i.overlayScrollbars(ew.overlayScrollbarsOptions)),o.find(".ew-table-header-sort")[0]||o.append('<span class="ew-table-header-sort"><i class="fas fa-sort-down"></i></span>'),o.find(".ew-table-header-sort i.fas").toggleClass("fa-sort-up",!!s.match(/ASC$/)).toggleClass("fa-sort-down",!!s.match(/DESC$/)),ew.initMultiSelectCheckboxes(),ew.fixLayoutHeight()}}),e("#table-name").on("keydown keypress cut paste",(function(a){t&&t.cancel(),t=e.later(200,null,(function(){e(".ew-card.ew-user-priv .ew-card-body").ewjtable("load",{table:e("#table-name").val()})}))})),e("#table-name").keydown()}));
//# sourceMappingURL=ewuserpriv.min.js.map