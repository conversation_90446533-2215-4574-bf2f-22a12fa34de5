<?php
session_start();
error_reporting(E_ALL & ~E_WARNING & ~E_NOTICE);
set_time_limit(3);
//error_reporting(E_ALL);
//CONFIG
$config=new stdClass();
$config->writeCache=0;

if($_SERVER['SERVER_ADDR']=="************" OR $_SERVER['SERVER_ADDR']=="::1"){  
    $db_ip="localhost";  $db_user="root";  $db_pwd="cocacola";  $db_name="aladin"; 
    define("BASEDIR","http://************/aladin/");
    define("DOCUMENT_ROOT",rtrim($_SERVER['DOCUMENT_ROOT'], '/')."/aladin/");
}
else{                                         
    $db_ip="localhost";  $db_user="al5svisl_1";  $db_pwd="]7TxKq)@5G;B";  $db_name="al5svisl_1";
    $basedir="https://www.aladincamp.it/";
    define("BASEDIR","https://www.aladincamp.it/");
    define("DOCUMENT_ROOT",$_SERVER["DOCUMENT_ROOT"]."/");
}
define("NOME_SITO","ALADIN CAMP");
define("EMAIL","<EMAIL>");
//header('Access-Control-Allow-Origin: *');
//FINE CONFIG

require __DIR__ . '/vendor/autoload.php';
require_once 'vendor/Thumbnails.php';
$db = new Zebra_Database(); $db->connect($db_ip,$db_user,$db_pwd,$db_name);$db->set_charset("utf8");

if($_SERVER['SERVER_ADDR']=="************"  OR $_SERVER['SERVER_ADDR']=="127.0.0.1"){$db->debug = true;}else{$db->debug = false;}
//**** DEBUG *****
$db->debug = true;
$db->debug = false;

$varLang=NOME_SITO."_lang";
if(!$_SESSION[$varLang]){  $_SESSION[$varLang]="it";}
if($_GET['lang']){  $_SESSION[$varLang]=$_GET['lang'];}
$lang=$_SESSION[$varLang];
$_SESSION['lang']=$lang;

//if(!$lang)$lang=$_SESSION['lang'];
//if(!$lang)$lang="it";

if($_GET['traduzione']){$creaArrayTraduzioni=$_GET['traduzione'];}
if($creaArrayTraduzioni){writeTraduzioni();}
$arrayTraduzioni=readTraduzioni();

function isMobile(){
    $useragent=$_SERVER['HTTP_USER_AGENT'];
    if(preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i',$useragent)||preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i',substr($useragent,0,4))){
        return true;
    }
}

function protect_form($errors,$nomeForm=""){
    if($nomeForm)$nomeForm="_".$nomeForm;
    if (@is_readable('/dev/urandom')) {
            $f=fopen('/dev/urandom', 'r');
            $urandom=fread($f, 500);
            fclose($f);
    }
    $random_value='';
    for ($i=0;$i<500;++$i) {
            if (!isset($urandom)) {
                    if ($i%2==0) mt_srand(time()%2147 * 1000000 + (double)microtime() * 1000000);
                    $rand=48+mt_rand()%64;
            } else $rand=48+ord($urandom[$i])%64;
            if ($rand>57)$rand+=7;
            if ($rand>90)$rand+=6;
            if ($rand==123) $rand=52;
            if ($rand==124) $rand=53;
            $random_value.=chr($rand);
    }
    $random_value=hash('sha256',$random_value);
    $_SESSION['csrf_token'.$nomeForm]=$random_value;
    $_SESSION['nomeForm'.$nomeForm]=$_SESSION['formAttivo'];
    $form="<style type='text/css'>.hidden_value{display: none;}</style>
            <div class='hidden_value'>
                <input type='hidden' name='name_form$nomeForm' id='name_form' value='form'>
                <input type='text' name='name_property$nomeForm' value='' autocomplete='off'/>
                <input type='hidden' name='csrf_token$nomeForm' value='$random_value'/>
            </div>";
    if($errors){
        $form.="<div class='alert alert-danger'>$errors</div>";
    }
    return $form;
}

function form_is_valid(){
    if($_SESSION['formAttivo'])$nomeForm=$_SESSION['formAttivo'];
    if($nomeForm)$nomeForm="_".$nomeForm;
    
    if($_SESSION['csrf_token'.$nomeForm]!=$_POST['csrf_token'.$nomeForm])$errors="1 SPAM BOT detection!";
    if($_POST['name_form'.$nomeForm]!="form")$errors="2 SPAM BOT detection!";
    if($_POST['name_property'.$nomeForm]!="")$errors="3 SPAM BOT detection!";
    $_SESSION['formAttivo']="";unset($_SESSION['formAttivo']);
    
    if(!$errors)return true;
}

function getVars($method="get",$exclude=""){//method="input"
    if($method=="get"){
        ksort($_GET);
        foreach ($_GET as $k => $v){
            if($k!=$exclude){
                $vars.="$k=$v&";
            }
        }
    }
    if($method=="input"){
        ksort($_GET);
        foreach ($_GET as $k => $v){
            if($k!=$exclude){
                $vars.="<input type='hidden' name='$k' id='$k' value='$v' />";
            }
        }
    }
    return $vars;
}

function d($data, $metodo) {
    // Delimitatori di testo: barre, punti, trattini
    list($anno, $mese, $giorno) = explode('-', $data);
    list($giorno, $ora) = explode(' ', $giorno);
    list($ore, $minuti, $secondi) = explode(':', $ora);
    $ora=$ore.":".$minuti;
    if ($metodo == "ora") {         $data = "$ora";    }
    if ($metodo == "g")   {         $data = "$giorno";    }
    if ($metodo == "gma") {         $data = "$giorno/$mese/$anno";    }  //es. 10-01-2006
    if ($metodo == "gm")  {         $data = "$giorno/$mese";    }  //es. 10-01
    if ($metodo == "gMa") { //es. 10 gennaio 2006
        if ($mese == "01") $mese = "gennaio";        if ($mese == "02") $mese = "febbraio";        if ($mese == "03") $mese = "marzo";        if ($mese == "04") $mese = "aprile";        if ($mese == "05") $mese = "maggio";        if ($mese == "06") $mese = "giugno";        if ($mese == "07") $mese = "luglio";        if ($mese == "08") $mese = "agosto";        if ($mese == "09") $mese = "settembre";        if ($mese == "10") $mese = "ottobre";        if ($mese == "11") $mese = "novembre";        if ($mese == "12") $mese = "dicembre";                
        /*        if ($mese == "01") $mese = "january";        if ($mese == "02") $mese = "february";        if ($mese == "03") $mese = "march";        if ($mese == "04") $mese = "april";        if ($mese == "05") $mese = "may";        if ($mese == "06") $mese = "june";        if ($mese == "07") $mese = "july";        if ($mese == "08") $mese = "august";        if ($mese == "09") $mese = "september";        if ($mese == "10") $mese = "october";        if ($mese == "11") $mese = "november";        if ($mese == "12") $mese = "december";        */
        $data = "$giorno $mese $anno";
    }
    if ($metodo == "gM") { //es. 10 gennaio
        if ($mese == "01") $mese = "gennaio";        if ($mese == "02") $mese = "febbraio";        if ($mese == "03") $mese = "marzo";        if ($mese == "04") $mese = "aprile";        if ($mese == "05") $mese = "maggio";        if ($mese == "06") $mese = "giugno";        if ($mese == "07") $mese = "luglio";        if ($mese == "08") $mese = "agosto";        if ($mese == "09") $mese = "settembre";        if ($mese == "10") $mese = "ottobre";        if ($mese == "11") $mese = "novembre";        if ($mese == "12") $mese = "dicembre";        
        $data = "$giorno $mese";
    }
    if ($metodo == "m") { //es. 10 gennaio
        if ($mese == "01") $mese = "Gen";        if ($mese == "02") $mese = "Feb";        if ($mese == "03") $mese = "Mar";        if ($mese == "04") $mese = "Apr";        if ($mese == "05") $mese = "Mag";        if ($mese == "06") $mese = "Giu";        if ($mese == "07") $mese = "Lug";        if ($mese == "08") $mese = "Ago";        if ($mese == "09") $mese = "Set";        if ($mese == "10") $mese = "Ott";        if ($mese == "11") $mese = "Nov";        if ($mese == "12") $mese = "Dic";        
        $data = "$mese";
    }
    return $data;
}

function dx($data,$ordine_in="amg",$separatore_in="-",$ordine_out="gma",$separatore_out="/",$ore=false){
  
  list($data, $ora) = explode(' ', $data);
  list($ore, $minuti, $secondi) = explode(':', $ora);
  $ora=$ore.":".$minuti;
  
  if($ordine_in=="amg") list($anno, $mese, $giorno) = explode($separatore_in, $data);
  if($ordine_in=="agm") list($anno, $giorno, $mese) = explode($separatore_in, $data);
  if($ordine_in=="gam") list($giorno, $anno, $mese) = explode($separatore_in, $data);
  if($ordine_in=="gma") list($giorno, $mese, $anno) = explode($separatore_in, $data);
  if($ordine_in=="mag") list($mese, $anno, $giorno) = explode($separatore_in, $data);
  if($ordine_in=="mga") list($mese, $giorno, $anno) = explode($separatore_in, $data);
  
  if($ordine_out=="amg") $data=$anno."$separatore_out".$mese."$separatore_out".$giorno;
  if($ordine_out=="agm") $data=$anno."$separatore_out".$giorno."$separatore_out".$mese;
  if($ordine_out=="gam") $data=$giorno."$separatore_out".$anno."$separatore_out".$mese;
  if($ordine_out=="gma") $data=$giorno."$separatore_out".$mese."$separatore_out".$anno." ".$ora;
  if($ordine_out=="mag") $data=$mese."$separatore_out".$anno."$separatore_out".$giorno;
  if($ordine_out=="mga") $data=$mese."$separatore_out".$giorno."$separatore_out".$anno;
  if($ordine_out=="ora") $data=$ora;
  
  return $data;
  
}

function writeTraduzioni(){
    GLOBAL $db,$lang;
    $db->query("SELECT * FROM traduzioni");
    $traduzioni=$db->fetch_obj_all();
    $aT=array();
    foreach($traduzioni as $t){
      $aT[$t->testo_it]['it']=$t->testo_it;
      $aT[$t->testo_it]['en']=$t->testo_en;
      $aT[$t->testo_it]['es']=$t->testo_es;
      $aT[$t->testo_it]['fr']=$t->testo_fr;
      $aT[$t->testo_it]['zh']=$t->testo_zh;
    }
    $array=json_encode($aT);
    file_put_contents("traduzioni.json",$array);
    return($aT);
}

function readTraduzioni(){
    GLOBAL $db,$lang;
    if(file_exists("traduzioni.json")){
        $json=file_get_contents("traduzioni.json");
        return json_decode($json,true);
    }
    else{
        return writeTraduzioni();
    }
    
}

function __($string){
    GLOBAL $db,$lang,$arrayTraduzioni;
    $string=trim($string);
    if($lang=="ita")$lang="it";
    if($lang=="eng")$lang="en";
    if($lang=="deu")$lang="de";
    if($lang=="fra")$lang="fr";
    if($lang=="esp")$lang="es";
    if($lang=="jap")$lang="ja";
    if($lang=="rus")$lang="ru";

    //Cerco nel file di testo con l'array delle traduzioni
    if($arrayTraduzioni[$string][$lang]){
        return $arrayTraduzioni[$string][$lang];
    }
    else{
            
        if(strstr($_SERVER['SERVER_ADDR'],"192.168.1.")){return $string;}
        //SE NON SONO IN LOCALHOST
        $db->query("SELECT * FROM traduzioni WHERE BINARY testo_it=?",array($string));
        $t=$db->fetch_obj();
        $n=count($t);

        //Se esiste l'italiano significa che sono state create le traduzioni e vado a pescare quelle giuste
        if($n>0){
            if($t->{"testo_".$lang}){
                return $t->{"testo_".$lang};
            }
            else{
                if ((!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') || $_SERVER['SERVER_PORT'] == 443) {
                  $string_tradotta=translate($string,"it",$lang);
                  $db->query("UPDATE traduzioni SET testo_$lang=?,last_update=? WHERE BINARY testo_it=?",array($string_tradotta,time(),$string));
                  writeTraduzioni();
                  return $string_tradotta;
                }
                else{
                    return $t->testo_it;
                }
            }
        }
        // Se non esiste l'italiano, lo inserisco e creo tutte le traduzioni
        else{
            $db->query("INSERT INTO traduzioni SET testo_it=?,last_update=?,insert_date=?",array($string,time(),time()));
            if ((!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') || $_SERVER['SERVER_PORT'] == 443) {
              
              $string_en=translate($string,"it","en");
              $db->query("UPDATE traduzioni SET testo_en=?,last_update=? WHERE BINARY testo_it=?",array($string_en,time(),$string));
              
              //$string_de=translate($string,"it","de");
              //$db->query("UPDATE traduzioni SET testo_de=?,last_update=? WHERE BINARY testo_it=?",array($string_de,time(),$string));
              
              //$string_fr=translate($string,"it","fr");
              //$db->query("UPDATE traduzioni SET testo_fr=?,last_update=? WHERE BINARY testo_it=?",array($string_fr,time(),$string));
              
              //$string_es=translate($string,"it","es");
              //$db->query("UPDATE traduzioni SET testo_es=?,last_update=? WHERE BINARY testo_it=?",array($string_es,time(),$string));
              
              //$string_zh=translate($string,"it","zh");
              //$db->query("UPDATE traduzioni SET testo_zh=?,last_update=? WHERE BINARY testo_it=?",array($string_zh,time(),$string));
              
              //$string_jp=translate($string,"it","ja");
              //$db->query("UPDATE traduzioni SET testo_ja=?,last_update=? WHERE BINARY testo_it=?",array($string_ja,time(),$string));
              
              //$string_ru=translate($string,"it","ru");
              //$db->query("UPDATE traduzioni SET testo_ru=?,last_update=? WHERE BINARY testo_it=?",array($string_ru,time(),$string));
              
              //return ${"testo_".$lang};
              writeTraduzioni();
              return ${"string_".$lang};
            }
            else{
              return $string;
            }
        
        }
    }
}

function translate($stringa,$from="it",$to="en"){
    if(is_array($stringa)){
        foreach($stringa as $s){
            //$translation. = translateWithGoogle($stringa,$from,$to)." ";
            $translation.= translateWithRapidapi($stringa,$from,$to)." ";
        }
        return $translation; 
    }
    else{
        //$translation = translateWithGoogle($stringa,$from,$to);
        $translation = translateWithRapidapi($stringa,$from,$to);
        return $translation; 
    }
}

require_once 'vendor/autoload.php';
use \Statickidz\GoogleTranslate;
function translateWithGoogle($stringa,$from="it",$to="en"){
    $stringa= tagliaStringa($stringa,4500);
    $trans = new GoogleTranslate();
    $translation = $trans->translate($from, $to, $stringa);
    $translation=str_replace("</ strong>","</strong>",$translation);
    return $translation; 
}

//Google Translate
function translateWithRapidapi($stringa,$from="it",$to="en"){
    if($from=="zh")$from="zh-CN";
    if($to=="zh")$to="zh-CN";
    $stringa= tagliaStringa($stringa,4500);
    $apikey="**************************************************";
    if(is_array($stringa)){
        foreach($stringa as $s){
            $ll=mb_strlen($s);
            //print "*** $ll<br/> <pre>$s</pre> <br/><br/>";
            $string=rawurlencode($s);
            
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => "https://google-translate20.p.rapidapi.com/translate?text=$string&tl=$to&sl=$from",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => [
                    "x-rapidapi-host: google-translate20.p.rapidapi.com",
                    "x-rapidapi-key: **************************************************"
                ],
            ]);

            $response = curl_exec($curl);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                echo "cURL Error #:" . $err;
            } else {
                //echo $response;
                $response=json_decode($response);
                //print $response->text[0]."\r\n";
                //$translation.=$response->text[0];
                $response->data->translation=str_replace("</ strong>","</strong>",$response->data->translation);
                $response->data->translation=str_replace("</ p>","</p>",$response->data->translation);
                $response->data->translation=str_replace("<p> / p>","</p>",$response->data->translation);
                $response->data->translation=str_replace("/ p>","</p>",$response->data->translation);
                $translation.=$response->data->translation;
                //print "<br/>";
                //print "<pre>";print_r($response);print "</pre>";exit;
            }
            
        }
        return $translation; 
        
    }
    else{
        $string=rawurlencode($s);
        $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => "https://google-translate20.p.rapidapi.com/translate?text=$string&tl=$to&sl=$from",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => [
                    "x-rapidapi-host: google-translate20.p.rapidapi.com",
                    "x-rapidapi-key: **************************************************"
                ],
            ]);

            $response = curl_exec($curl);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                echo "cURL Error #:" . $err;
            } else {
                //echo $response;
                $response=json_decode($response);
                //print $response->text[0]."\r\n";
                //$translation.=$response->text[0];
                $response->data->translation=str_replace("</ strong>","</strong>",$response->data->translation);
                $response->data->translation=str_replace("</ p>","</p>",$response->data->translation);
                $response->data->translation=str_replace("<p> / p>","</p>",$response->data->translation);
                $response->data->translation=str_replace("/ p>","</p>",$response->data->translation);
                
                $translation.=$response->data->translation;
                //print "<br/>";
                //print "<pre>";print_r($response);print "</pre>";exit;
            }
        return $translation; 
    }

}

//Just Translated
function translateWithRapidapiJust($stringa,$from="it",$to="en"){
    $stringa= tagliaStringa($stringa,4500);
    $apikey="**************************************************";
    if(is_array($stringa)){
        foreach($stringa as $s){
            $ll=mb_strlen($s);
            //print "*** $ll<br/> <pre>$s</pre> <br/><br/>";

            $string=rawurlencode($s);
            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => "https://rapidapi.p.rapidapi.com/?text=$string&lang_from=$from&lang_to=$to",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => [
                    "x-rapidapi-host: just-translated.p.rapidapi.com",
                    "x-rapidapi-key: $apikey"
                ],
            ]);

            $response = curl_exec($curl);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                echo "cURL Error #:" . $err;
            } else {
                $response=json_decode($response);
                //print $response->text[0]."\r\n";
                $translation.=$response->text[0];
                //print "<br/>";
                //print "<pre>";print_r($response);print "</pre>";exit;
            }
        }
        return $translation; 
        
    }
    else{
        $string=rawurlencode($s);
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => "https://rapidapi.p.rapidapi.com/?text=$string&lang_from=$from&lang_to=$to",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => [
                "x-rapidapi-host: just-translated.p.rapidapi.com",
                "x-rapidapi-key: $apikey"
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            echo "cURL Error #:" . $err;
        } else {
            $response=json_decode($response);
            $translation.=$response->text[0];
            //print "<br/>";
            //print "<pre>";print_r($response);print "</pre>";exit;
        }
        return $translation; 
    }

}

function tagliaStringa($str,$lx,$contatore=0,$parti=array()){
    //Se è già un secondo giro con parti
    $stringa=$str;
    if($parti){
        //print "ci sono parti \r\n";
        //print "<pre>";print_r($parti);print "</pre>\r\n";
        foreach($parti as $p){
            $lunghezza=$lunghezza+mb_strlen($p);
        }
        $stringa=mb_substr($str,$lunghezza);
    }
    
    $l=mb_strlen($stringa);
    //print "lunghezza totale: $l\r\n";
    //Se la stringa che resta è già più corta del limite, chiudo definitivamente
    if($l<=$lx){
        //print "si\r\n";
        array_push($parti,$stringa);
        return $parti;
    }
    
    
    //print "Stringa Rimasta: $stringa\r\n";
    //print "lx: $lx\r\n";
    //print "contatore: $contatore\r\n";
    $strDaTradurre=mb_substr($stringa, 0, ($lx-$contatore));
    //print "***$strDaTradurre\r\n";
    //exit;
    if(substr($strDaTradurre,-1)==" "){
       //return $strDaTradurre;
       //print "xxx <br/>";
       array_push($parti,$strDaTradurre);
       return tagliaStringa($str,$lx,0,$parti);
    }
    else{
        //return tagliaStringa($str,($lx-1));
        $contatore++;
        //print "Faccio altro giro: $contatore \r\n";//exit;
        //print "aaa $str\r\n";
        return tagliaStringa($str,$lx,$contatore,$parti);
        //return $parti;
    }
}

function generateRandom($length = 8){
    $password = "";
    $possible = "2346789bcdfghjkmnpqrtvwxyzBCDFGHJKLMNPQRTVWXYZ";
    // we refer to the length of $possible a few times, so let's grab it now
    $maxlength = strlen($possible);
    // check for length overflow and truncate if necessary
    if ($length > $maxlength) {
      $length = $maxlength;
    }
    // set up a counter for how many characters are in the password so far
    $i = 0; 
    // add random characters to $password until $length is reached
    while ($i < $length) { 
      // pick a random character from the possible ones
      $char = substr($possible, mt_rand(0, $maxlength-1), 1);
      // have we already used this character in $password?
      if (!strstr($password, $char)) { 
        // no, so it's OK to add it onto the end of whatever we've already got...
        $password .= $char;
        // ... and increase the counter by one
        $i++;
      }
    }
    // done!
    return $password;
}

use PHPMailer\PHPMailer\PHPMailer;
function spedisciMail($destEmail,$titolo,$testo,$bcc="",$cc="",$allegato=""){
  $mail = new PHPMailer(true);   
  try {  
    $mail->IsMail();
    /*    $mail->IsSMTP(true);            // use SMTP    $mail->SMTPDebug  = 0;        // enables SMTP debug information (for testing) // 1 = errors and messages  // 2 = messages only    $mail->SMTPAuth   = true;                  // enable SMTP authentication    $mail->Host       = "tls://email-smtp.us-east-1.amazonaws.com"; // Amazon SES server, note "tls://" protocol    $mail->Port       = 587;                    // set the SMTP port    //$mail->Port       = 25;                    // set the SMTP port    $mail->Username   = "AKIAJW5H27DZIW7AKPMQ";  // SES SMTP  username    $mail->Password   = "Ah8T3C/TyqIaIlmSjwfRDEocEiYdkIAN/SiLApVQyDss";  // SES SMTP password    */    
    $mail->CharSet = 'UTF-8';
    $mail->From     = EMAIL;
	$mail->FromName = NOME_SITO;
	$mail->Sender   = EMAIL;
    $mail->AddReplyTo(EMAIL);
    
    $mail->AddAddress($destEmail);
    
    if($bcc){
      if(is_array($bcc)){
        foreach($bcc as $bcc_email){
          $mail->AddBCC($bcc_email);    
        }
      }
      else{
        $mail->AddBCC($bcc);
      }
    }

    if($cc){
      if(is_array($cc)){
        foreach($cc as $cc_email){
          $mail->AddCC($cc_email);    
        }
      }
      else{
        $mail->AddCC($cc);
      }
    }

    $mail->WordWrap = 50; 
    if($allegato){
      if (isset($_FILES[$allegato]) && $_FILES[$allegato]['error'] == UPLOAD_ERR_OK) {
        $mail->AddAttachment($_FILES[$allegato]['tmp_name'],$_FILES[$allegato]['name']);
      }
    }

    $mail->IsHTML(true);                               // send as HTML

    $mail->Subject  =  $titolo;
    $mail->Body     =  $testo;
    $mail->AltBody  =  $titolo;
    
    $mail->send();
    return true;
  } 
  catch (Exception $e) {
    return false;
    //echo 'Message could not be sent. Mailer Error: ', $mail->ErrorInfo;
  }
//****FINE  MAIL *************************
}

function spedisciMailHtml($destEmail,$titolo,$fileHtml,$dirHtml,$bcc="",$cc="",$allegato=""){
    GLOBAL $db,$lang,$cart;
    $mail = new PHPMailer(true);   
    try {  
    $mail->IsMail();
    /*    $mail->IsSMTP(true);            // use SMTP    $mail->SMTPDebug  = 0;        // enables SMTP debug information (for testing) // 1 = errors and messages  // 2 = messages only    $mail->SMTPAuth   = true;                  // enable SMTP authentication    $mail->Host       = "tls://email-smtp.us-east-1.amazonaws.com"; // Amazon SES server, note "tls://" protocol    $mail->Port       = 587;                    // set the SMTP port    //$mail->Port       = 25;                    // set the SMTP port    $mail->Username   = "AKIAJW5H27DZIW7AKPMQ";  // SES SMTP  username    $mail->Password   = "Ah8T3C/TyqIaIlmSjwfRDEocEiYdkIAN/SiLApVQyDss";  // SES SMTP password    */    
    $mail->CharSet = 'UTF-8';
    $mail->From     = EMAIL;
	$mail->FromName = NOME_SITO;
	$mail->Sender   = EMAIL;
    $mail->AddReplyTo(EMAIL);

    $mail->AddAddress($destEmail);

    if($bcc){
      if(is_array($bcc)){
        foreach($bcc as $bcc_email){
          $mail->AddBCC($bcc_email);    
        }
      }
      else{
        $mail->AddBCC($bcc);
      }
    }

    if($cc){
      if(is_array($cc)){
        foreach($cc as $cc_email){
          $mail->AddCC($cc_email);    
        }
      }
      else{
        $mail->AddCC($cc);
      }
    }

    $mail->WordWrap = 50; 
    if($allegato){
      if (isset($_FILES[$allegato]) && $_FILES[$allegato]['error'] == UPLOAD_ERR_OK) {
        $mail->AddAttachment($_FILES[$allegato]['tmp_name'],$_FILES[$allegato]['name']);
      }
    }

    $mail->IsHTML(true);                               // send as HTML

    $mail->Subject  =  $titolo;
    //$mail->msgHTML(file_get_contents('views/email/ordine/ordine.php'), $direc);
    //$dirHtml=__DIR__."/views/email/ordine";
    $mail->msgHTML(file_get_contents($fileHtml), $dirHtml);
    $mail->AltBody  =  $titolo;

    $mail->send();
        return true;
    } 
    catch (Exception $e) {
        return false;
    //echo 'Message could not be sent. Mailer Error: ', $mail->ErrorInfo;
    }
//****FINE  MAIL *************************
}

function uniqidReal($lenght = 13) {
    // uniqid gives 13 chars, but you could adjust it to your needs.
    if (function_exists("random_bytes")) {
        $bytes = random_bytes(ceil($lenght / 2));
    } elseif (function_exists("openssl_random_pseudo_bytes")) {
        $bytes = openssl_random_pseudo_bytes(ceil($lenght / 2));
    } else {
        throw new Exception("no cryptographically secure random function available");
    }
    return substr(bin2hex($bytes), 0, $lenght);
}

function debug(){
    foreach($_GET as $k=>$v){$debug.="GET $k=$v <br/>\r\n";}
    foreach($_POST as $k=>$v){$debug.="POST $k=$v <br/>\r\n";}
    foreach($_SESSION as $k=>$v){$debug.="SESSION $k=$v <br/>\r\n";}
    foreach($_COOKIE as $k=>$v){$debug.="COOKIE $k=$v <br/>\r\n";}
    $debug.="REMOTE ADDRESS $_SERVER[REMOTE_ADDR] <br/>\r\n";
    $debug.="TIME ".date("Y-m-d H:i:s")." <br/>\r\n";
    
    return $debug;
}

function br2nl($string){
    return preg_replace('/\<br(\s*)?\/?\>/i', "\n", $string);
}

function slugify($string, $replace = array(), $delimiter = '-') {
  // https://github.com/phalcon/incubator/blob/master/Library/Phalcon/Utils/Slug.php
  if (!extension_loaded('iconv')) {
    throw new Exception('iconv module not loaded');
  }
  // Save the old locale and set the new locale to UTF-8
  $oldLocale = setlocale(LC_ALL, '0');

  setlocale(LC_ALL, 'it_IT.UTF-8');
  $clean = iconv('UTF-8', 'ASCII//TRANSLIT', $string);
  if (!empty($replace)) {
    $clean = str_replace((array) $replace, ' ', $clean);
  }
  $clean = preg_replace("/[^a-zA-Z0-9\/_|+ -]/", '', $clean);
  $clean = strtolower($clean);
  $clean = preg_replace("/[\/_|+ -]+/", $delimiter, $clean);
  $clean = trim($clean, $delimiter);
  // Revert back to the old locale
  setlocale(LC_ALL, $oldLocale);
  return $clean;
}

function slug($slug,$table,$record,$not_in="",$pos=""){
    global $db,$lang;
    if(!$slug)return "";
    if($pos){ $checkSlug=$slug."-".$pos; }
    else{     $checkSlug=$slug;          }
    //$pos=0;
    
    if($not_in) $n = $db->dcount("id", $table, "$record = ? AND id NOT IN ($not_in)", array($checkSlug));
    else        $n = $db->dcount("id", $table, "$record = ?", array($checkSlug));
    if($n==0){ return $checkSlug; }
    else{      return slug($slug,$table,$record,$not_in,$pos+1); }
} 



//************ ROUTING **************
function getPageName($level){
    //echo $_SERVER['REQUEST_URI']."<br/>";exit;
    $request_uri = explode('?', $_SERVER['REQUEST_URI'], 2);
    //print "<pre>";print_r($request_uri);print "</pre>";exit;
    $request_uri=($request_uri)?$request_uri[0]:$_SERVER['REQUEST_URI'];
    //print "<pre>";print_r($request_uri);print "</pre>";exit;
    $requestParams = explode('/', $request_uri);
    $scriptPath = explode('/', $_SERVER['SCRIPT_NAME']);
    //print "<pre>";print_r($scriptPath);print "</pre>";
    //print "<pre>";print_r($requestParams);print "</pre>";exit;
    // remove the base path
    while ($requestParams[0] === $scriptPath[0]){
        array_shift($requestParams);
        array_shift($scriptPath);
    }

    //if($requestParams[0]=="index.php"){return DEFAULT_PAGE_NAME;}
    
    if ($level === 0){
        return $requestParams[0] ?: DEFAULT_PAGE_NAME;
    }

    return isset($requestParams[$level]) ? $requestParams[$level] : ''; 
}

function numeroLivelliRouting(){
    $requestParams = explode('/', $_SERVER['REQUEST_URI']);
    $scriptPath = explode('/', $_SERVER['SCRIPT_NAME']);
    // remove the base path
    while ($requestParams[0] === $scriptPath[0]){
        array_shift($requestParams);
        array_shift($scriptPath);
    }
    $numeroLivelli=count($requestParams);
    return $numeroLivelli; 
}

function passaParametriGet($vars=""){
    //parto da 2 perchè 0 è $lang e 1 è il nome del controller della pagina
    $i=2;
    foreach($vars as $var){
        $_GET[$var]=getPageName($i);
        $i++;
    }
}

//GEOIP
use GeoIp2\Database\Reader;
function country($ip=""){
    if(!$ip)$ip=$_SERVER['SERVER_ADDR'];
    if($_SESSION['country']){
         
        //you can do it for "city" too.. just everywhere change phrase "country" with "city".
        try{
            $reader = new Reader(dirname(__file__)."/vendor/geoip2//GeoLite2-Country.mmdb");
            $record = $reader->country($ip);
            
            //$reader = new Reader(dirname(__file__)."/vendor/geoip2/GeoLite2-City.mmdb");
            //$record = $reader->city($user_ip);    
            
            $reader->close();
            print "<pre>";print_r($record->raw);print "</pre>";exit;
            $country =  $record->raw['country']['names']['iso_code'];
            //$is_in_european_union=$record->raw['country']['is_in_european_union'];
            $continent=$record->raw['continent']['code'];
            /*
            print "<pre>";
            print_r($record->raw['country']);
            print "</pre>";
            exit;
            */
            
        } catch ( GeoIp2\Exception\AddressNotFoundException $e ){    
            $country =  "IT";
            $continent="EU";
            $is_in_european_union="1";
            //$country_name = 'not_found';  
        }
        
        if(!$country)$country="IT";
        
        $_SESSION['country']=$country;
        $_SESSION['continent']=$continent;
        
    }
    return $_SESSION['country'];
}


function n($prezzo,$decimali=2){
    $prezzo=str_replace(",",".",$prezzo);
    $p=number_format($prezzo,$decimali,",",".");
    return $p;
}

//***** CACHE *****
function pageId($id=""){
    ksort($_GET);
    foreach ($_GET as $k => $v){
        if($k!=$exclude){
            $vars.="$k=$v&";
        }
    }
    $url=str_replace("?".$_SERVER['QUERY_STRING'],"",$_SERVER['REQUEST_URI']);
    //print "$url<br/>";
    //URL + VARIABILI + IDENTIFICATIVO UNIVOCO così se ci sono più cache in una pagina non ci sono problemi
    $pageId=$url.$vars.$id;
    //echo $pageId."<br/>";
    $pageIdMd5=md5($pageId);
    return $pageIdMd5;
    //return $pageId;
}

function cache($id=""){
    global $config;
    if(!$config->writeCache){return false;}

    $pageId=pageId($id);
    //echo $id."<br/>";
    //echo $pageId."<br/>";
    $cacheFile="cache/$pageId";
    //echo $cacheFile."<br/>";

    if(file_exists($cacheFile)){
        //print "esiste";
        return true;
    }
    else{
        //print "non esiste";
        ob_start();
        return false;
    }
}

function cache_stop($id=""){
    global $config;
    //Se cache disabilitata
    if(!$config->writeCache){return false;}
    $pageId=pageId($id);
    $cacheFile="cache/$pageId";
    if(file_exists($cacheFile)){
        $cache=file_get_contents($cacheFile);
    }
    else{
        $cache = ob_get_clean();
        // se non c'è la directory cache la creo
        if(!is_dir(DOCUMENT_ROOT."cache"))mkdir(DOCUMENT_ROOT."cache");
        //file_put_contents($cacheFile,$cache.date("Y-m-d H:i:s"));
        file_put_contents($cacheFile,$cache);
    }
    //return $cache;
    echo $cache;
    return true;
}

function cache_delete(){
    $files = glob(DOCUMENT_ROOT."cache/*"); // get all file names
    foreach($files as $file){ // iterate files
        if(is_file($file)){
          unlink($file); // delete file
        }
    }
}
//FINE CACHE ********************

function lockDays($nPiazzole=10){
    global $db;
    /*
    $db->query("SELECT MIN(data_arrivo) as min FROM ordini WHERE data_arrivo>='".date("Y-m-d")."'");
    $data_min=$db->fetch_obj()->min;

    $db->query("SELECT MAX(data_partenza) as max FROM ordini");
    $data_max=$db->fetch_obj()->max;

    //print "$data_min <br/>";
    //print "$data_max <br/>";
    $data=$data_min;
    while($data<=$data_max){
        //print "$data <br/>";
        $db->query("SELECT COUNT(id) as n FROM ordini WHERE status='close' AND data_arrivo<=? AND data_partenza>=?",array($data,$data));
        $n=$ordini=$db->fetch_obj()->n;
        //print "$data $n <br/>";
        if($n>=$nPiazzole){
            $lockDays.="'$data',";
        }
        
        $data=date('Y-m-d', strtotime($data . ' +1 day'));
    }
    */
    
    //Tolgo date in calendario chiusure
    $db->query("SELECT * FROM calendario ORDER BY data");
    $chiusure=$db->fetch_obj_all();
    foreach($chiusure as $c){
        if($c->data_finale){
            $lockDays.="['$c->data','$c->data_finale'],";
        }
        else{
            $lockDays.="'$c->data',";
        }
        
    }
    $lockDays=rtrim($lockDays,",");
    return $lockDays;
}

function sendEmail_order($numeroOrdine){
    GLOBAL $db,$lang;
    $db->query("SELECT * FROM ordini WHERE id=?",array($numeroOrdine));
    $o=$db->fetch_obj();
    $titolo="BOOKING ".NOME_SITO;
    ob_start();
    
    include(DOCUMENT_ROOT."views_email/header_email.php");
    ?>
    
    <table width="100%" align="center">
        <tr>
            <td><?=__("Grazie, abbiamo ricevuto il suo ordine.")?><br/>
            <br/>    
                <div style="text-align:center"><h1><?=NOME_SITO?><br/><?=__("Riepilogo Ordine")?></h1></div>
                <h2><?=__("Ordine")?> n° <?=$o->id?></h2>
                <?=__("IMPORTO")?>: € <?=$o->importo?>
            </td>
        </tr>
    </table>
    <br/>
    <table class='table table-mini-cart' align='center' width='100%' style='border:1px solid #ccc;padding:5px;background-color:#fff;'>
        <tr>
            <td valign='top' width="50%"><b><?=__("CLIENTE")?></b><br/>
            <?=$o->cognome?> <?=$o->nome?><br/>
            <?=$o->email?><br/>
            <?=$o->telefono?><br/>
            <?php if($o->codice_fiscale){?>
            Codice Fiscale: <?=$o->codice_fiscale?><br/>
            <?php } ?>
            <?php if($o->targa){?>
            Targa automezzo: <?=$o->targa?><br/>
            <?php } ?>
            </td>
            <td valign='top' width="50%"><b><?=__("PAGAMENTO")?></b><br/>
            STRIPE
            </td>
        </tr>
        
    </table>
    
    <table class='table table-mini-cart' align='center' width='100%' style='border:1px solid #ccc;padding:5px;background-color:#fff;'>
        <tr>
            <td valign='top' width="50%"><b><?=__("BOOKING")?></b><br/>
            <?=__("Data Arrivo")?>: <?=d($o->data_arrivo,"gma")?><br/>
            <?=__("Data Partenza")?>: <?=d($o->data_partenza,"gma")?><br/>
            <?=__("Persone")?>: <?=$o->persone?><br/>
            <?=__("Nazionalità")?>: <?=$o->country?><br/>
            <?php if($o->presenza_disabili){?>
            Presenza disabili: si<br/>
            <?php } ?>
            <?php if($o->invio_whatsapp){?>
            Invio informazioni tramite whatsapp: autorizzato<br/>
            <?php } ?>
            </td>
            <td valign='top' width="50%"><b><?=__("NOTE")?></b><br/>
            <?=$o->note?>
            </td>
        </tr>
        
    </table>
    
    <table class='table table-mini-cart' align='center' width='100%' style='border:1px solid #ccc;padding:5px;background-color:#fff;'>
        <tr>
            <td valign='top' align='center'>
            <span style="font-size:12px;"><?=__("La sua prenotazione è confermata.")?><br/>
            <?=__("Per agevolare le fasi di check-in inviaci via whatsapp +393534271805 oppure via <NAME_EMAIL> le foto della carta d'identità delle persone in sosta.")?><br/>
            </span>
            </br>
            <h2><?=__("Codice accesso servizi")?></h2>
            2468#
            </td>
        </tr>
        
    </table>
    
    
    
    <?php
    include(DOCUMENT_ROOT."views_email/footer_email.php");
    $messaggio = ob_get_clean();
    //print $messaggio;exit;
    spedisciMail($o->email,$titolo,$messaggio,$bcc="",$cc="",$allegato="");
    spedisciMail("<EMAIL>",$titolo,$messaggio,$bcc="",$cc="",$allegato="");
    spedisciMail("<EMAIL>",$titolo,$messaggio,$bcc="",$cc="",$allegato="");
    
    
    /*
    if(spedisciMail($o->email,$titolo,$messaggio,$bcc="",$cc="",$allegato="")){        return true;    }
    else{        return false;    }
    */
}
?>