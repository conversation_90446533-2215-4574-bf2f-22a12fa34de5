$swal2-white:         #fff !default;
$swal2-black:         #000 !default;
$swal2-outline-color: rgba(100, 150, 200, .5) !default;

// CONTAINER
$swal2-container-padding: .625em !default;

// BOX MODEL
$swal2-width: 32em !default;
$swal2-padding: 0 0 1.25em !default;
$swal2-border: none !default;
$swal2-border-radius: 5px !default;
$swal2-box-shadow: #d9d9d9 !default;

// ANIMATIONS
$swal2-show-animation: swal2-show .3s !default;
$swal2-hide-animation: swal2-hide .15s forwards !default;

// BACKGROUND
$swal2-background: $swal2-white !default;

// TYPOGRAPHY
$swal2-font: inherit !default;
$swal2-font-size: 1rem !default;

// BACKDROP
$swal2-backdrop: rgba($swal2-black, .4) !default;
$swal2-backdrop-transition: background-color .1s !default;

// ICONS
$swal2-icon-size: 5em !default;
$swal2-icon-animations: true !default;
$swal2-icon-margin: 2.5em auto .6em !default;
$swal2-icon-font-family: inherit !default;
$swal2-icon-font-size: 3.75em !default;
$swal2-icon-border-color: #000 !default;
$swal2-icon-zoom: null !default;
$swal2-success: #a5dc86 !default;
$swal2-success-border: rgba($swal2-success, .3) !default;
$swal2-error: #f27474 !default;
$swal2-warning: #f8bb86 !default;
$swal2-info: #3fc3ee !default;
$swal2-question: #87adbd !default;

// IMAGE
$swal2-image-margin: 2em auto 1em !default;

// TITLE
$swal2-title-margin: 0 !default;
$swal2-title-padding: .8em 1em 0 !default;
$swal2-title-color: lighten($swal2-black, 35) !default;
$swal2-title-font-size: 1.875em !default;

// HTML CONTAINER
$swal2-html-container-justify-content: center !default;
$swal2-html-container-margin: 0 !default;
$swal2-html-container-padding: 1em 1.6em .3em !default;
$swal2-html-container-color: lighten($swal2-black, 33) !default;
$swal2-html-container-font-size: 1.125em !default;
$swal2-html-container-font-weight: normal !default;
$swal2-html-container-line-height: normal !default;
$swal2-html-container-text-align: center !default;
$swal2-html-container-word-wrap: break-word !default;

// INPUT
$swal2-input-margin: 1em 2em 0 !default;
$swal2-input-width: auto !default;
$swal2-input-height: 2.625em !default;
$swal2-input-padding: 0 .75em !default;
$swal2-input-border: 1px solid lighten($swal2-black, 85) !default;
$swal2-input-border-radius: .1875em !default;
$swal2-input-box-shadow: inset 0 1px 1px rgba($swal2-black, .06), 0 0 0 3px transparent !default;
$swal2-input-font-size: 1.125em !default;
$swal2-input-background: inherit !default;
$swal2-input-color: inherit !default;
$swal2-input-transition: border-color .1s, box-shadow .1s !default;

// INPUT:FOCUS
$swal2-input-focus-border: 1px solid #b4dbed !default;
$swal2-input-focus-outline: none !default;
$swal2-input-focus-box-shadow: inset 0 1px 1px rgba($swal2-black, .06), 0 0 0 3px $swal2-outline-color !default;

// TEXTAREA SPECIFIC VARIABLES
$swal2-textarea-height: 6.75em !default;
$swal2-textarea-padding: .75em !default;

// INPUT LABEL
$swal2-input-label-margin: 1em auto 0 !default;
$swal2-input-label-justify-content: center !default;

// VALIDATION MESSAGE
$swal2-validation-message-align-items: center !default;
$swal2-validation-message-justify-content: center !default;
$swal2-validation-message-margin: 1em 0 0 !default;
$swal2-validation-message-padding: .625em !default;
$swal2-validation-message-background: lighten($swal2-black, 94) !default;
$swal2-validation-message-color: lighten($swal2-black, 40) !default;
$swal2-validation-message-font-size: 1em !default;
$swal2-validation-message-font-weight: 300 !default;
$swal2-validation-message-icon-background: $swal2-error !default;
$swal2-validation-message-icon-color: $swal2-white !default;
$swal2-validation-message-icon-zoom: null !default;

// PROGRESS STEPS
$swal2-progress-steps-flex-wrap: wrap !default;
$swal2-progress-steps-align-items: center !default;
$swal2-progress-steps-max-width: 100% !default;
$swal2-progress-steps-background: inherit !default;
$swal2-progress-steps-margin: 0 0 1.25em !default;
$swal2-progress-steps-padding: 0 !default;
$swal2-progress-steps-font-weight: 600 !default;
$swal2-progress-steps-distance: 2.5em !default;
$swal2-progress-step-width: 2em;
$swal2-progress-step-height: 2em;
$swal2-progress-step-border-radius: 2em;
$swal2-progress-step-background: #add8e6 !default;
$swal2-progress-step-color: $swal2-white !default;
$swal2-active-step-background: #2778c4 !default;
$swal2-active-step-color: $swal2-white !default;

// FOOTER
$swal2-footer-margin: 1em 0 0 !default;
$swal2-footer-padding: 1em 0 0 !default;
$swal2-footer-border-color: #eee !default;
$swal2-footer-color: lighten($swal2-black, 33) !default;
$swal2-footer-font-size: 1em !default;

// TIMER POGRESS BAR
$swal2-timer-progress-bar-height: .25em;
$swal2-timer-progress-bar-background: rgba($swal2-black, .2) !default;

// CLOSE BUTTON
$swal2-close-button-justify-self: end !default;
$swal2-close-button-align-items: center !default;
$swal2-close-button-justify-content: center !default;
$swal2-close-button-width: 1.2em !default;
$swal2-close-button-height: 1.2em !default;
$swal2-close-button-position: null !default;
$swal2-close-button-gap: 0 !default;
$swal2-close-button-padding: 0 !default;
$swal2-close-button-transition: color .1s, box-shadow .1s !default;
$swal2-close-button-border: none !default;
$swal2-close-button-border-radius: $swal2-border-radius !default;
$swal2-close-button-outline: null !default;
$swal2-close-button-background: transparent !default;
$swal2-close-button-color: #ccc !default;
$swal2-close-button-font-family: serif !default;
$swal2-close-button-font-size: 2.5em !default;
$swal2-close-button-box-shadow: inset 0 0 0 3px transparent !default;

// CLOSE BUTTON:HOVER
$swal2-close-button-hover-transform: none !default;
$swal2-close-button-hover-color: $swal2-error !default;
$swal2-close-button-hover-background: transparent !default;

// CLOSE BUTTON:FOCUS
$swal2-close-button-focus-outline: none !default;
$swal2-close-button-focus-box-shadow: inset 0 0 0 3px $swal2-outline-color !default;

// ACTIONS
$swal2-actions-flex-wrap: wrap !default;
$swal2-actions-align-items: center !default;
$swal2-actions-justify-content: center !default;
$swal2-actions-width: 100% !default;
$swal2-actions-margin: 1.25em auto 0 !default;
$swal2-actions-padding: 0 !default;

// COMMON VARIABLES FOR ALL ACTION BUTTONS
$swal2-button-margin: .3125em !default;
$swal2-button-padding: .625em 1.1em !default;
$swal2-button-transition: box-shadow .1s !default;
$swal2-button-box-shadow: 0 0 0 3px transparent !default;
$swal2-button-font-weight: 500 !default;
$swal2-button-darken-hover: rgba($swal2-black, .1) !default;
$swal2-button-darken-active: rgba($swal2-black, .2) !default;
$swal2-button-focus-outline: none !default;
$swal2-button-focus-box-shadow: 0 0 0 3px $swal2-outline-color !default;

// CONFIRM BUTTON
$swal2-confirm-button-order: null !default;
$swal2-confirm-button-border: 0 !default;
$swal2-confirm-button-border-radius: .25em !default;
$swal2-confirm-button-background-color: #7367f0 !default;
$swal2-confirm-button-color: $swal2-white !default;
$swal2-confirm-button-font-size: 1em !default;
$swal2-confirm-button-focus-box-shadow: 0 0 0 3px rgba($swal2-confirm-button-background-color, .5) !default;

// DENY BUTTON
$swal2-deny-button-order: null !default;
$swal2-deny-button-border: 0 !default;
$swal2-deny-button-border-radius: .25em !default;
$swal2-deny-button-background-color: #ea5455 !default;
$swal2-deny-button-color: $swal2-white !default;
$swal2-deny-button-font-size: 1em !default;
$swal2-deny-button-focus-box-shadow: 0 0 0 3px rgba($swal2-deny-button-background-color, .5) !default;

// CANCEL BUTTON
$swal2-cancel-button-order: null !default;
$swal2-cancel-button-border: 0 !default;
$swal2-cancel-button-border-radius: .25em !default;
$swal2-cancel-button-background-color: #6e7d88 !default;
$swal2-cancel-button-color: $swal2-white !default;
$swal2-cancel-button-font-size: 1em !default;
$swal2-cancel-button-focus-box-shadow: 0 0 0 3px rgba($swal2-cancel-button-background-color, .5) !default;

// LOADER
$swal2-loader-align-items: center !default;
$swal2-loader-justify-content: center !default;
$swal2-loader-width: 2.2em !default;
$swal2-loader-height: 2.2em !default;
$swal2-loader-margin: 0 1.875em !default;
$swal2-loader-animation: swal2-rotate-loading 1.5s linear 0s infinite normal !default;
$swal2-loader-border-width: .25em !default;
$swal2-loader-border-style: solid !default;
$swal2-loader-border-radius: 100% !default;
$swal2-loader-border-color: #2778c4 transparent #2778c4 transparent !default;

// TOASTS
$swal2-toast-show-animation: swal2-toast-show .5s !default;
$swal2-toast-hide-animation: swal2-toast-hide .1s forwards !default;
$swal2-toast-border: none !default;
$swal2-toast-box-shadow: 0 0 .625em #d9d9d9 !default;
$swal2-toast-background: $swal2-white !default;
$swal2-toast-close-button-width: .8em !default;
$swal2-toast-close-button-height: .8em !default;
$swal2-toast-close-button-margin: 0 !default;
$swal2-toast-close-button-font-size: 2em !default;
$swal2-toast-width: 360px !default;
$swal2-toast-padding: 1em !default;
$swal2-toast-title-margin: 1em !default;
$swal2-toast-title-padding: 0 !default;
$swal2-toast-title-font-size: 1em !default;
$swal2-toast-icon-font-size: 1.8em !default;
$swal2-toast-html-container-margin: 1em !default;
$swal2-toast-html-container-padding: 0 !default;
$swal2-toast-html-container-font-size: 1em !default;
$swal2-toast-input-height: 2em !default;
$swal2-toast-input-margin: .5em !default;
$swal2-toast-input-font-size: 1em !default;
$swal2-toast-validation-font-size: 1em !default;
$swal2-toast-buttons-font-size: 1em !default;
$swal2-toast-button-focus-box-shadow: 0 0 0 1px $swal2-background, 0 0 0 3px $swal2-outline-color !default;
$swal2-toast-footer-margin: .5em 0 0 !default;
$swal2-toast-footer-padding: .5em 0 0 !default;
$swal2-toast-footer-font-size: .8em !default;
