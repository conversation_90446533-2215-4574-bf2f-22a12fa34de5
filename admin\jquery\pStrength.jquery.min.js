/*!
 * pStrength jQuery Plugin v1.0.6
 * http://accountspassword.com/pstrength-jquery-plugin
 *
 * Created by AccountsPassword.com
 * Released under the MIT License (Feel free to copy, modify or redistribute this plugin.)
 *
 */
!function(r){var n,a=new Array,o=new Array,c=new Array,t=new Array,e=new Array,s={init:function(s,i){n=r.extend({bind:"keyup change",changeBackground:!0,backgrounds:[["#cc0000","#FFF"],["#cc3333","#FFF"],["#cc6666","#FFF"],["#ff9999","#FFF"],["#e0941c","#FFF"],["#e8a53a","#FFF"],["#eab259","#FFF"],["#efd09e","#FFF"],["#ccffcc","#FFF"],["#66cc66","#FFF"],["#339933","#FFF"],["#006600","#FFF"],["#105610","#FFF"]],passwordValidFrom:60,onValidatePassword:function(r){},onPasswordStrengthChanged:function(r,n){}},s);for(var F=48;F<58;F++)a.push(F);for(F=65;F<91;F++)o.push(F);for(F=97;F<123;F++)c.push(F);for(F=32;F<48;F++)t.push(F);for(F=58;F<65;F++)t.push(F);for(F=91;F<97;F++)t.push(F);for(F=123;F<127;F++)t.push(F);return this.each(r.proxy((function(a,o){e[r(o)]={background:r(o).css("background"),color:r(o).css("color")},h.call(o),r(o).data("pStrength",!0).bind(n.bind,(function(){h.call(this,o)}))}),this))},changeBackground:function(a,o){void 0===o&&(o=a,a=r(this)),o=o>12?12:o,r(a).css({"background-color":n.backgrounds[o][0],color:n.backgrounds[o][1]})},resetStyle:function(n){r(n).css(e[r(n)])}},i=function(r){var n=r+"",a=n.charCodeAt(0);if(55296<=a&&a<=56319){var o=a;return 1===n.length?a:1024*(o-55296)+(n.charCodeAt(1)-56320)+65536}return a},h=function(){var n=0,e=0,s=0,h=0,u=0,d=r(this).val().trim();n+=2*Math.floor(d.length/8);for(var l=0;l<d.length;l++)-1!=r.inArray(i(d.charAt(l)),a)&&e<2?(n++,e++):-1!=r.inArray(i(d.charAt(l)),o)&&s<2?(n++,s++):-1!=r.inArray(i(d.charAt(l)),c)&&h<2?(n++,h++):-1!=r.inArray(i(d.charAt(l)),t)&&u<2&&(n++,u++);return F.call(r(this),n),n},F=function(a){var o=Math.ceil(100*a/12);o=o>100?100:o,n.onPasswordStrengthChanged.call(r(this),a,o),o>=n.passwordValidFrom&&n.onValidatePassword.call(r(this),o),n.changeBackground&&s.changeBackground.call(r(this),a)};r.fn.pStrength=function(n){return s[n]?s[n].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof n&&n?void r.error("Method "+n+" does not exists on jQuery.pStrength"):s.init.apply(this,arguments)}}(jQuery);
//# sourceMappingURL=pStrength.jquery.min.js.map