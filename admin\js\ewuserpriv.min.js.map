{"version": 3, "sources": ["ewuserpriv.js"], "names": ["loadjs", "ready", "$", "j<PERSON><PERSON><PERSON>", "getTitleHtml", "id", "phraseId", "ew", "language", "phrase", "_timer", "_fields", "table", "title", "display", "data", "row", "record", "index", "sorting", "for<PERSON>ach", "name", "trueValue", "priv", "checked", "permission", "allowed", "ewjtable", "paging", "defaultSorting", "fields", "actions", "listAction", "params", "rows", "permissions", "slice", "toLowerCase", "map", "includes", "asc", "match", "sort", "a", "b", "result", "Object", "assign", "records", "rowInserted", "event", "find", "on", "$this", "this", "parseInt", "value", "recordsLoaded", "serverResponse", "$mc", "$t", "$c", "useFixedHeaderTable", "tableHeight", "height", "addClass", "USE_OVERLAY_SCROLLBARS", "overlayScrollbars", "overlayScrollbarsOptions", "append", "toggleClass", "initMultiSelectCheckboxes", "fixLayoutHeight", "e", "cancel", "later", "val", "keydown"], "mappings": "AAAAA,OAAOC,MAAM,WAAW,WACpB,IAAIC,EAAIC,OA+CR,SAASC,EAAaC,EAAIC,GACtB,MAAO,iHAAmHD,EAAK,SAAWA,EAAnI,4EAC2CA,EAAK,KAAOE,GAAGC,SAASC,OAAO,cAAgBH,GAAYD,IAAO,iBAIxH,IAqDIK,EArDAC,EAAU,CACVC,MAAO,CACHC,MAAO,oCAAsCN,GAAGC,SAASC,OAAO,eAAiB,UACjFK,QA1CR,SAA0BC,GACtB,IAAIC,EAAMD,EAAKE,OACf,OAAOD,EAAIJ,MAAQ,oCAAsCI,EAAIE,MAAQ,gBAyCjEC,SAAS,IAGjB,CAAC,MAAO,SAAU,OAAQ,OAAQ,SAAU,OAAQ,SAAU,SAAU,SAASC,SAAQ,SAASf,GA1DlG,IAAsBgB,EAAMC,EA2DxBX,EAAQN,GAAM,CACVQ,MAAOT,EAAaC,GACpBS,SA7DcO,EA6DQhB,EA7DFiB,EA6DMC,KAAKlB,GA5D5B,SAASU,GACZ,IAAIC,EAAMD,EAAKE,OAAQZ,EAAKgB,EAAO,IAAML,EAAIE,MACzCM,GAAWR,EAAIS,WAAaH,IAAcA,EAE9C,OADAN,EAAIQ,QAAUA,EACP,gJAAkJnB,EAAK,SAAWA,EACrK,YAAciB,EAAY,iBAAmBN,EAAIE,MAAQ,KACxDM,EAAU,WAAa,MACrBR,EAAIU,QAAUJ,IAAcA,EAAa,YAAc,IAAM,6CAA+CjB,EAAK,qBAsDxHc,SAAS,MAKjBjB,EAAE,uCAAuCyB,SAAS,CAC9CC,QAAQ,EACRT,SAAS,EACTU,eAAgB,YAChBC,OAAQnB,EACRoB,QAAS,CAAEC,WAvDf,SAAoBjB,EAAMkB,GACtB,IAAIC,EAAOX,KAAKY,YAAYC,MAAM,GAClC,GAAIrB,GAAQA,EAAKH,MAAO,CACpB,IAAIA,EAAQG,EAAKH,MAAMyB,cACvBH,EAAOhC,EAAEoC,IAAIJ,GAAM,SAASlB,GACxB,OAAIA,EAAIJ,MAAMyB,cAAcE,SAAS3B,GAC1BI,EACJ,QAGf,GAAIiB,GAAUA,EAAOd,QAAS,CAC1B,IAAIqB,EAAMP,EAAOd,QAAQsB,MAAM,QAC/BP,EAAKQ,MAAK,SAASC,EAAGC,GAClB,OAAIA,EAAEhC,MAAMyB,cAAgBM,EAAE/B,MAAMyB,cACzB,GAAS,EAAI,EACfO,EAAEhC,MAAMyB,gBAAkBM,EAAE/B,MAAMyB,cAChC,EACFO,EAAEhC,MAAMyB,cAAgBM,EAAE/B,MAAMyB,cAC9B,EAAQ,GAAK,OADnB,KAIb,MAAO,CACHQ,OAAQ,KACRZ,OAAQa,OAAOC,OAAO,GAAIhC,EAAMkB,GAChCe,QAASd,KAgCbe,YAAa,SAASC,EAAOnC,GACdA,EAAKC,IACXmC,KAAK,wBAAwBC,GAAG,SAAS,WAC1C,IAAIC,EAAQnD,EAAEoD,MAAOpC,EAAQqC,SAASF,EAAMtC,KAAK,SAAU,IAAKyC,EAAQD,SAASF,EAAMtC,KAAK,SAAU,IAClGuC,KAAK9B,QACLD,KAAKY,YAAYjB,GAAOO,WAAaF,KAAKY,YAAYjB,GAAOO,WAAa+B,EAE1EjC,KAAKY,YAAYjB,GAAOO,WAAaF,KAAKY,YAAYjB,GAAOO,WAAa+B,MAGtFC,cAAe,SAASP,EAAOnC,GAC3B,IAAII,EAAUJ,EAAK2C,eAAezB,OAAOd,QACrCwC,EAAMzD,EAAEoD,MAAMH,KAAK,4BACnBS,EAAKD,EAAIR,KAAK,mBACdU,EAAKD,EAAGT,KAAK,2CACbW,sBACIC,aACAJ,EAAIK,OAAOD,aACfH,EAAGK,SAAS,0CACR1D,GAAG2D,wBACHP,EAAIQ,kBAAkB5D,GAAG6D,2BAE5BP,EAAGV,KAAK,yBAAyB,IAClCU,EAAGQ,OAAO,8EACdR,EAAGV,KAAK,+BAA+BmB,YAAY,eAAgBnD,EAAQsB,MAAM,SAAS6B,YAAY,iBAAkBnD,EAAQsB,MAAM,UACtIlC,GAAGgE,4BACHhE,GAAGiE,qBAMXtE,EAAE,eAAekD,GAAG,8BAA8B,SAASqB,GACnD/D,GACAA,EAAOgE,SACXhE,EAASR,EAAEyE,MAAM,IAAK,MAAM,WACxBzE,EAAE,uCAAuCyB,SAAS,OAAQ,CACtDf,MAAOV,EAAE,eAAe0E,cAMpC1E,EAAE,eAAe2E", "sourcesContent": ["loadjs.ready(\"makerjs\", function() {\n    var $ = jQuery;\n\n    function getDisplayFn(name, trueValue) {\n        return function(data) {\n            var row = data.record, id = name + '_' + row.index,\n                checked = (row.permission & trueValue) == trueValue;\n            row.checked = checked;\n            return '<div class=\"custom-control custom-checkbox d-inline-block\"><input type=\"checkbox\" class=\"custom-control-input ew-priv ew-multi-select\" name=\"' + id + '\" id=\"' + id +\n                '\" value=\"' + trueValue + '\" data-index=\"' + row.index + '\"' +\n                (checked ? ' checked' : '') +\n                (((row.allowed & trueValue) != trueValue) ? ' disabled' : '') + '><label class=\"custom-control-label\" for=\"' + id + '\"></label></div>';\n        };\n    }\n\n    function displayTableName(data) {\n        var row = data.record;\n        return row.table + '<input type=\"hidden\" name=\"table_' + row.index + '\" value=\"1\">';\n    }\n\n    function getRecords(data, params) {\n        var rows = priv.permissions.slice(0);\n        if (data && data.table) {\n            var table = data.table.toLowerCase();\n            rows = $.map(rows, function(row) {\n                if (row.table.toLowerCase().includes(table))\n                    return row;\n                return null;\n            });\n        }\n        if (params && params.sorting) {\n            var asc = params.sorting.match(/ASC$/);\n            rows.sort(function(a, b) { // Case-insensitive\n                if (b.table.toLowerCase() > a.table.toLowerCase())\n                    return (asc) ? -1 : 1;\n                else if (b.table.toLowerCase() === a.table.toLowerCase())\n                    return 0\n                else if (b.table.toLowerCase() < a.table.toLowerCase())\n                    return (asc) ? 1 : -1;\n            });\n        }\n        return {\n            result: \"OK\",\n            params: Object.assign({}, data, params),\n            records: rows\n        };\n    }\n\n    function getTitleHtml(id, phraseId) {\n        return '<div class=\"custom-control custom-checkbox\"><input type=\"checkbox\" class=\"custom-control-input ew-priv\" name=\"' + id + '\" id=\"' + id + '\" onclick=\"ew.selectAll(this);\">' +\n            '<label class=\"custom-control-label\" for=\"' + id + '\">' + ew.language.phrase(\"Permission\" + (phraseId || id)) + '</label></div>'\n    }\n\n    // Fields\n    var _fields = {\n        table: {\n            title: '<span class=\"font-weight-normal\">' + ew.language.phrase(\"TableOrView\") + '</span>',\n            display: displayTableName,\n            sorting: true\n        }\n    };\n    [\"add\", \"delete\", \"edit\", \"list\", \"lookup\", \"view\", \"search\", \"import\", \"admin\"].forEach(function(id) {\n        _fields[id] = {\n            title: getTitleHtml(id),\n            display: getDisplayFn(id, priv[id]),\n            sorting: false\n        };\n    });\n\n    // Init\n    $(\".ew-card.ew-user-priv .ew-card-body\").ewjtable({\n        paging: false,\n        sorting: true,\n        defaultSorting: \"table ASC\",\n        fields: _fields,\n        actions: { listAction: getRecords },\n        rowInserted: function(event, data) {\n            var $row = data.row;\n            $row.find(\"input[type=checkbox]\").on(\"click\", function() {\n                var $this = $(this), index = parseInt($this.data(\"index\"), 10), value = parseInt($this.data(\"value\"), 10);\n                if (this.checked)\n                    priv.permissions[index].permission = priv.permissions[index].permission | value;\n                else\n                    priv.permissions[index].permission = priv.permissions[index].permission ^ value;\n            });\n        },\n        recordsLoaded: function(event, data) {\n            var sorting = data.serverResponse.params.sorting,\n                $mc = $(this).find(\".ewjtable-main-container\"),\n                $t = $mc.find(\".ewjtable.table\"),\n                $c = $t.find(\".ewjtable-column-header-container:first\");\n            if (useFixedHeaderTable) {\n                if (tableHeight)\n                    $mc.height(tableHeight);\n                $t.addClass(\"table-head-fixed ew-fixed-header-table\");\n                if (ew.USE_OVERLAY_SCROLLBARS)\n                    $mc.overlayScrollbars(ew.overlayScrollbarsOptions);\n            }\n            if (!$c.find(\".ew-table-header-sort\")[0])\n                $c.append('<span class=\"ew-table-header-sort\"><i class=\"fas fa-sort-down\"></i></span>');\n            $c.find(\".ew-table-header-sort i.fas\").toggleClass(\"fa-sort-up\", !!sorting.match(/ASC$/)).toggleClass(\"fa-sort-down\", !!sorting.match(/DESC$/));\n            ew.initMultiSelectCheckboxes();\n            ew.fixLayoutHeight();\n        }\n    });\n\n    // Re-load records on search\n    var _timer;\n    $(\"#table-name\").on(\"keydown keypress cut paste\", function(e) {\n        if (_timer)\n            _timer.cancel();\n        _timer = $.later(200, null, function() {\n            $(\".ew-card.ew-user-priv .ew-card-body\").ewjtable(\"load\", {\n                table: $(\"#table-name\").val()\n            });\n        });\n    });\n\n    // Load all records\n    $(\"#table-name\").keydown();\n});\n"]}