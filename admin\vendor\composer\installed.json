{"packages": [{"name": "athari/yalinqo", "version": "v2.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Athari/YaLinqo.git", "reference": "cb8afe0c03a9ea639f9a14ea78641abb2b7682fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Athari/YaLinqo/zipball/cb8afe0c03a9ea639f9a14ea78641abb2b7682fb", "reference": "cb8afe0c03a9ea639f9a14ea78641abb2b7682fb", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpdocumentor/phpdocumentor": "^2.8", "phpunit/phpunit": "<6", "satooshi/php-coveralls": "^2.0"}, "time": "2019-01-10T20:41:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"YaLinqo\\": "YaLinqo/"}, "files": ["YaLinqo/Linq.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/Athari/"}], "description": "YaLinqo, a LINQ-to-objects library for PHP", "homepage": "http://athari.github.io/YaLinqo", "keywords": ["linq", "linqo", "query", "statistic"], "support": {"docs": "http://athari.github.io/YaLinqo", "issues": "https://github.com/Athari/YaLinqo/issues", "source": "https://github.com/Athari/YaLinqo"}, "install-path": "../athari/yalinqo"}, {"name": "defuse/php-encryption", "version": "v2.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/defuse/php-encryption.git", "reference": "0f407c43b953d571421e0020ba92082ed5fb7620"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/0f407c43b953d571421e0020ba92082ed5fb7620", "reference": "0f407c43b953d571421e0020ba92082ed5fb7620", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.4.0"}, "require-dev": {"nikic/php-parser": "^2.0|^3.0|^4.0", "phpunit/phpunit": "^4|^5"}, "time": "2018-07-24T23:27:56+00:00", "bin": ["bin/generate-defuse-key"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "support": {"issues": "https://github.com/defuse/php-encryption/issues", "source": "https://github.com/defuse/php-encryption/tree/master"}, "install-path": "../defuse/php-encryption"}, {"name": "delight-im/cookie", "version": "v3.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/delight-im/PHP-Cookie.git", "reference": "67065d34272377d63bab0bd58f984f9b228c803f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/delight-im/PHP-<PERSON>ie/zipball/67065d34272377d63bab0bd58f984f9b228c803f", "reference": "67065d34272377d63bab0bd58f984f9b228c803f", "shasum": ""}, "require": {"delight-im/http": "^2.0", "php": ">=5.4.0"}, "time": "2020-04-16T11:01:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Delight\\Cookie\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Modern cookie management for PHP", "homepage": "https://github.com/delight-im/PHP-Cookie", "keywords": ["cookie", "cookies", "csrf", "http", "same-site", "samesite", "xss"], "support": {"issues": "https://github.com/delight-im/PHP-Cookie/issues", "source": "https://github.com/delight-im/PHP-Cookie/tree/v3.4.0"}, "install-path": "../delight-im/cookie"}, {"name": "delight-im/http", "version": "v2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/delight-im/PHP-HTTP.git", "reference": "a5c2c4eae1dd3207f797984e8f64f2d71ed889dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/delight-im/PHP-HTTP/zipball/a5c2c4eae1dd3207f797984e8f64f2d71ed889dd", "reference": "a5c2c4eae1dd3207f797984e8f64f2d71ed889dd", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-10-12T18:52:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Delight\\Http\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hypertext Transfer Protocol (HTTP) utilities for PHP", "homepage": "https://github.com/delight-im/PHP-HTTP", "keywords": ["headers", "http", "https"], "support": {"issues": "https://github.com/delight-im/PHP-HTTP/issues", "source": "https://github.com/delight-im/PHP-HTTP/tree/v2.1.0"}, "install-path": "../delight-im/http"}, {"name": "dflydev/dot-access-data", "version": "v3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "0992cc19268b259a39e86f296da5f0677841f42c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/0992cc19268b259a39e86f296da5f0677841f42c", "reference": "0992cc19268b259a39e86f296da5f0677841f42c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^3.14"}, "time": "2021-08-13T13:06:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.1"}, "install-path": "../dflydev/dot-access-data"}, {"name": "doctrine/cache", "version": "1.12.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "4cf401d14df219fa6f38b671f5493449151c9ad8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/4cf401d14df219fa6f38b671f5493449151c9ad8", "reference": "4cf401d14df219fa6f38b671f5493449151c9ad8", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^8.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.2 || ^6.0@dev", "symfony/var-exporter": "^4.4 || ^5.2 || ^6.0@dev"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "time": "2021-07-17T14:39:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.12.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "install-path": "../doctrine/cache"}, {"name": "doctrine/dbal", "version": "2.10.4", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "47433196b6390d14409a33885ee42b6208160643"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/47433196b6390d14409a33885ee42b6208160643", "reference": "47433196b6390d14409a33885ee42b6208160643", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^8.1", "jetbrains/phpstorm-stubs": "^2019.1", "nikic/php-parser": "^4.4", "phpstan/phpstan": "^0.12.40", "phpunit/phpunit": "^8.5.5", "psalm/plugin-phpunit": "^0.10.0", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "^3.14.2"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "time": "2020-09-12T21:20:41+00:00", "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.10.x-dev", "dev-develop": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.10.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "install-path": "../doctrine/dbal"}, {"name": "doctrine/event-manager", "version": "1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "time": "2020-05-29T18:28:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "install-path": "../doctrine/event-manager"}, {"name": "ezyang/htmlpurifier", "version": "v4.14.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "shasum": ""}, "require": {"php": ">=5.2"}, "time": "2021-12-25T01:21:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.14.0"}, "install-path": "../ezyang/htmlpurifier"}, {"name": "firebase/php-jwt", "version": "v5.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/83b609028194aa042ea33b5af2d41a7427de80e6", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "time": "2021-11-08T20:18:51+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v5.5.1"}, "install-path": "../firebase/php-jwt"}, {"name": "hkvstore/dompdf", "version": "1.0.200", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/hkvstore/dompdf.git", "reference": "d77a64b3df0e5f5ca0d396ba89ad151817b8b7be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hkvstore/dompdf/zipball/d77a64b3df0e5f5ca0d396ba89ad151817b8b7be", "reference": "d77a64b3df0e5f5ca0d396ba89ad151817b8b7be", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "^0.5.2", "phenx/php-svg-lib": "^0.3.3", "php": "^7.2 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "time": "2021-01-11T08:47:07+00:00", "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/hkvstore/dompdf/issues", "source": "https://github.com/hkvstore/dompdf/tree/1.0.200"}, "install-path": "../hkvstore/dompdf"}, {"name": "hkvstore/phpthumb", "version": "v2.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/hkvstore/PHPThumb.git", "reference": "05d0b313b833b4fb5a15d722c2b1b44d5e6af26a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hkvstore/PHPThumb/zipball/05d0b313b833b4fb5a15d722c2b1b44d5e6af26a", "reference": "05d0b313b833b4fb5a15d722c2b1b44d5e6af26a", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2020-10-31T02:39:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"PHPThumb": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "e.World Technology Limited", "email": "<EMAIL>"}], "description": "A library for manipulating images in PHP.", "homepage": "https://github.com/hkvstore/PHPThumb", "keywords": ["image", "resize", "rotate"], "support": {"issues": "https://github.com/hkvstore/PHPThumb/issues", "source": "https://github.com/hkvstore/PHPThumb/tree/v2.1.5"}, "install-path": "../hkvstore/phpthumb"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.37", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "9841e3c46f5bd0739b53aed8ac677fa712943df7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/9841e3c46f5bd0739b53aed8ac677fa712943df7", "reference": "9841e3c46f5bd0739b53aed8ac677fa712943df7", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "time": "2021-02-19T21:22:57+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["Mobile_Detect.php"], "psr-0": {"Detection": "namespaced/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.37"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "install-path": "../mobiledetect/mobiledetectlib"}, {"name": "monolog/monolog", "version": "2.3.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "fd4380d6fc37626e2f799f29d91195040137eba9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/fd4380d6fc37626e2f799f29d91195040137eba9", "reference": "fd4380d6fc37626e2f799f29d91195040137eba9", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2021-10-01T21:08:31+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.5"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "install-path": "../monolog/monolog"}, {"name": "nikic/fast-route", "version": "v1.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nikic/FastRoute.git", "reference": "181d480e08d9476e61381e04a71b34dc0432e812"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/FastRoute/zipball/181d480e08d9476e61381e04a71b34dc0432e812", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "time": "2018-02-13T20:26:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"FastRoute\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "support": {"issues": "https://github.com/nikic/FastRoute/issues", "source": "https://github.com/nikic/FastRoute/tree/master"}, "install-path": "../nikic/fast-route"}, {"name": "nyholm/psr7", "version": "1.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "2212385b47153ea71b1c1b1374f8cb5e4f7892ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/2212385b47153ea71b1c1b1374f8cb5e4f7892ec", "reference": "2212385b47153ea71b1c1b1374f8cb5e4f7892ec", "shasum": ""}, "require": {"php": ">=7.1", "php-http/message-factory": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || 8.5 || 9.4", "symfony/error-handler": "^4.4"}, "time": "2021-07-02T08:32:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.4.1"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "install-path": "../nyholm/psr7"}, {"name": "nyholm/psr7-server", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7-server.git", "reference": "b846a689844cef114e8079d8c80f0afd96745ae3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7-server/zipball/b846a689844cef114e8079d8c80f0afd96745ae3", "reference": "b846a689844cef114e8079d8c80f0afd96745ae3", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"nyholm/nsa": "^1.1", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^7.0 || ^8.5 || ^9.3"}, "time": "2021-05-12T11:11:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Nyholm\\Psr7Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "Helper classes to handle PSR-7 server requests", "homepage": "http://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7-server/issues", "source": "https://github.com/Nyholm/psr7-server/tree/1.0.2"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "install-path": "../nyholm/psr7-server"}, {"name": "opis/closure", "version": "3.6.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "06e2ebd25f2869e54a306dda991f7db58066f7f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/06e2ebd25f2869e54a306dda991f7db58066f7f6", "reference": "06e2ebd25f2869e54a306dda991f7db58066f7f6", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "time": "2021-04-09T13:42:10+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "support": {"issues": "https://github.com/opis/closure/issues", "source": "https://github.com/opis/closure/tree/3.6.2"}, "install-path": "../opis/closure"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "version_normalized": "**********", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2020-10-15T08:29:30+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "install-path": "../paragonie/random_compat"}, {"name": "phenx/php-font-lib", "version": "0.5.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "dd448ad1ce34c63d09baccd05415e361300c35b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/dd448ad1ce34c63d09baccd05415e361300c35b4", "reference": "dd448ad1ce34c63d09baccd05415e361300c35b4", "shasum": ""}, "require": {"ext-mbstring": "*"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5"}, "time": "2021-12-17T19:44:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/0.5.4"}, "install-path": "../phenx/php-font-lib"}, {"name": "phenx/php-svg-lib", "version": "v0.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PhenX/php-svg-lib.git", "reference": "5fa61b65e612ce1ae15f69b3d223cb14ecc60e32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-svg-lib/zipball/5fa61b65e612ce1ae15f69b3d223cb14ecc60e32", "reference": "5fa61b65e612ce1ae15f69b3d223cb14ecc60e32", "shasum": ""}, "require": {"sabberworm/php-css-parser": "^8.3"}, "require-dev": {"phpunit/phpunit": "^5.5|^6.5"}, "time": "2019-09-11T20:02:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "support": {"issues": "https://github.com/PhenX/php-svg-lib/issues", "source": "https://github.com/PhenX/php-svg-lib/tree/master"}, "install-path": "../phenx/php-svg-lib"}, {"name": "php-di/invoker", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHP-DI/Invoker.git", "reference": "540c27c86f663e20fe39a24cd72fa76cdb21d41a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/540c27c86f663e20fe39a24cd72fa76cdb21d41a", "reference": "540c27c86f663e20fe39a24cd72fa76cdb21d41a", "shasum": ""}, "require": {"psr/container": "~1.0"}, "require-dev": {"athletic/athletic": "~0.1.8", "phpunit/phpunit": "~4.5"}, "time": "2017-03-20T19:28:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Invoker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Generic and extensible callable invoker", "homepage": "https://github.com/PHP-DI/Invoker", "keywords": ["callable", "dependency", "dependency-injection", "injection", "invoke", "invoker"], "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/master"}, "install-path": "../php-di/invoker"}, {"name": "php-di/php-di", "version": "6.3.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHP-DI/PHP-DI.git", "reference": "b8126d066ce144765300ee0ab040c1ed6c9ef588"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/b8126d066ce144765300ee0ab040c1ed6c9ef588", "reference": "b8126d066ce144765300ee0ab040c1ed6c9ef588", "shasum": ""}, "require": {"opis/closure": "^3.5.5", "php": ">=7.2.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "psr/container": "^1.0"}, "provide": {"psr/container-implementation": "^1.0"}, "require-dev": {"doctrine/annotations": "~1.2", "friendsofphp/php-cs-fixer": "^2.4", "mnapoli/phpunit-easymock": "^1.2", "ocramius/proxy-manager": "^2.0.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.5|^9.0"}, "suggest": {"doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~2.0)"}, "time": "2021-09-02T09:49:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DI\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "The dependency injection container for humans", "homepage": "https://php-di.org/", "keywords": ["PSR-11", "container", "container-interop", "dependency injection", "di", "ioc", "psr11"], "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.3.5"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/php-di/php-di", "type": "tidelift"}], "install-path": "../php-di/php-di"}, {"name": "php-di/phpdoc-reader", "version": "2.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHP-DI/PhpDocReader.git", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/66daff34cbd2627740ffec9469ffbac9f8c8185c", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"mnapoli/hard-mode": "~0.3.0", "phpunit/phpunit": "^8.5|^9.0"}, "time": "2020-10-12T12:39:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpDocReader\\": "src/PhpDocReader"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PhpDocReader parses @var and @param values in PHP docblocks (supports namespaced class names with the same resolution rules as PHP)", "keywords": ["phpdoc", "reflection"], "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/2.2.1"}, "install-path": "../php-di/phpdoc-reader"}, {"name": "php-http/message-factory", "version": "v1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/a478cb11f66a6ac48d8954216cfed9aa06a501a1", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0"}, "time": "2015-12-19T14:08:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/master"}, "install-path": "../php-http/message-factory"}, {"name": "phpmailer/phpmailer", "version": "v6.5.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "baeb7cde6b60b1286912690ab0693c7789a31e71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/baeb7cde6b60b1286912690ab0693c7789a31e71", "reference": "baeb7cde6b60b1286912690ab0693c7789a31e71", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.2", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.6.0", "yoast/phpunit-polyfills": "^1.0.0"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "stevenmaguire/oauth2-microsoft": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "time": "2021-11-25T16:34:11+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.5.3"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "install-path": "../phpmailer/phpmailer"}, {"name": "psr/container", "version": "1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2021-03-05T17:36:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.1"}, "install-path": "../psr/container"}, {"name": "psr/http-factory", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "time": "2019-04-30T12:38:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "install-path": "../psr/http-message"}, {"name": "psr/http-server-handler", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "aff2f80e33b7f026ec96bb42f63242dc50ffcae7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/aff2f80e33b7f026ec96bb42f63242dc50ffcae7", "reference": "aff2f80e33b7f026ec96bb42f63242dc50ffcae7", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0"}, "time": "2018-10-30T16:46:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"issues": "https://github.com/php-fig/http-server-handler/issues", "source": "https://github.com/php-fig/http-server-handler/tree/master"}, "install-path": "../psr/http-server-handler"}, {"name": "psr/http-server-middleware", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "2296f45510945530b9dceb8bcedb5cb84d40c5f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/2296f45510945530b9dceb8bcedb5cb84d40c5f5", "reference": "2296f45510945530b9dceb8bcedb5cb84d40c5f5", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0", "psr/http-server-handler": "^1.0"}, "time": "2018-10-30T17:12:04+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/master"}, "install-path": "../psr/http-server-middleware"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}, {"name": "sabberworm/php-css-parser", "version": "8.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sabberworm/PHP-CSS-Parser.git", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabberworm/PHP-CSS-Parser/zipball/e41d2140031d533348b2192a83f02d8dd8a71d30", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"codacy/coverage": "^1.4", "phpunit/phpunit": "^4.8.36"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "time": "2021-12-11T13:40:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/sabberworm/PHP-CSS-Parser/issues", "source": "https://github.com/sabberworm/PHP-CSS-Parser/tree/8.4.0"}, "install-path": "../sabberworm/php-css-parser"}, {"name": "selective/samesite-cookie", "version": "0.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/selective-php/samesite-cookie.git", "reference": "805d82de34cb642189932feb17158da98078f9a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/selective-php/samesite-cookie/zipball/805d82de34cb642189932feb17158da98078f9a6", "reference": "805d82de34cb642189932feb17158da98078f9a6", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/http-message": "^1", "psr/http-server-handler": "^1", "psr/http-server-middleware": "^1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2", "middlewares/utils": "^3", "overtrue/phplint": "^2", "phpstan/phpstan": "0.*", "phpunit/phpunit": "^8 || ^9", "slim/psr7": "^1", "squizlabs/php_codesniffer": "^3"}, "time": "2021-01-11T07:49:32+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Selective\\SameSiteCookie\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Secure your site with SameSite cookies", "homepage": "https://github.com/selective-php/samesite-cookie", "keywords": ["cookie", "csrf", "samesite", "samesite-cookie"], "support": {"issues": "https://github.com/selective-php/samesite-cookie/issues", "source": "https://github.com/selective-php/samesite-cookie/tree/0.3.0"}, "install-path": "../selective/samesite-cookie"}, {"name": "slim/csrf", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/slimphp/Slim-Csrf.git", "reference": "fd03c9ed0aaf62c85bbb775aae2126a69e8a5f72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slimphp/Slim-Csrf/zipball/fd03c9ed0aaf62c85bbb775aae2126a69e8a5f72", "reference": "fd03c9ed0aaf62c85bbb775aae2126a69e8a5f72", "shasum": ""}, "require": {"php": "^7.1", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0"}, "require-dev": {"phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5.8"}, "time": "2021-01-08T00:36:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Slim\\Csrf\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://joshlockhart.com"}], "description": "Slim Framework 4 CSRF protection PSR-15 middleware", "homepage": "http://slimframework.com", "keywords": ["csrf", "framework", "middleware", "slim"], "support": {"issues": "https://github.com/slimphp/Slim-Csrf/issues", "source": "https://github.com/slimphp/Slim-Csrf/tree/1.1.0"}, "install-path": "../slim/csrf"}, {"name": "slim/flash", "version": "0.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/slimphp/Slim-Flash.git", "reference": "9aaff5fded3b54f4e519ec3d4ac74d3d1f2cbbbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slimphp/Slim-Flash/zipball/9aaff5fded3b54f4e519ec3d4ac74d3d1f2cbbbc", "reference": "9aaff5fded3b54f4e519ec3d4ac74d3d1f2cbbbc", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "time": "2017-10-22T10:35:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Slim\\Flash\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://joshlockhart.com"}], "description": "Slim Framework Flash message service provider", "homepage": "http://slimframework.com", "keywords": ["flash", "framework", "message", "provider", "slim"], "support": {"issues": "https://github.com/slimphp/<PERSON>-<PERSON>/issues", "source": "https://github.com/slimphp/Slim-Flash/tree/master"}, "install-path": "../slim/flash"}, {"name": "slim/http", "version": "1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/slimphp/Slim-Http.git", "reference": "3bc9d61b5243cab0d75c89d778bd69464de07354"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slimphp/Slim-Http/zipball/3bc9d61b5243cab0d75c89d778bd69464de07354", "reference": "3bc9d61b5243cab0d75c89d778bd69464de07354", "shasum": ""}, "require": {"ext-fileinfo": "*", "ext-json": "*", "ext-libxml": "*", "ext-simplexml": "*", "php": "^7.2 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"adriansuter/php-autoload-override": "^1.2", "laminas/laminas-diactoros": "^2.4", "nyholm/psr7": "^1.3", "php-http/psr7-integration-tests": "dev-master", "phpstan/phpstan": "^0.12.52", "phpunit/phpunit": "^8.5 || ^9.3", "squizlabs/php_codesniffer": "^3.5"}, "time": "2020-11-20T06:43:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Slim\\Http\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://joshlockhart.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://silentworks.co.uk"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://akrabat.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.lgse.com"}], "description": "Slim PSR-7 Object Decorators", "homepage": "http://slimframework.com", "keywords": ["http", "psr-7", "psr7"], "support": {"issues": "https://github.com/slimphp/Slim-Http/issues", "source": "https://github.com/slimphp/Slim-Http/tree/1.2.0"}, "install-path": "../slim/http"}, {"name": "slim/http-cache", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/slimphp/Slim-HttpCache.git", "reference": "d1a091aca45695a2159194132872f4a544416bc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slimphp/Slim-HttpCache/zipball/d1a091aca45695a2159194132872f4a544416bc9", "reference": "d1a091aca45695a2159194132872f4a544416bc9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/http-message": "^1.0", "psr/http-server-middleware": "^1.0"}, "require-dev": {"phpstan/phpstan": "^0.12.28", "phpunit/phpunit": "^8.5.13 || ^9.3.8", "slim/psr7": "^1.1", "squizlabs/php_codesniffer": "^3.5"}, "time": "2020-12-08T17:32:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Slim\\HttpCache\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://joshlockhart.com"}], "description": "Slim Framework HTTP cache middleware and service provider", "homepage": "https://www.slimframework.com", "keywords": ["cache", "framework", "middleware", "slim"], "support": {"issues": "https://github.com/slimphp/Slim-HttpCache/issues", "source": "https://github.com/slimphp/Slim-HttpCache/tree/1.1.0"}, "install-path": "../slim/http-cache"}, {"name": "slim/php-view", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/slimphp/PHP-View.git", "reference": "ea848c71870788a6df819a6f151ea246e005cdab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slimphp/PHP-View/zipball/ea848c71870788a6df819a6f151ea246e005cdab", "reference": "ea848c71870788a6df819a6f151ea246e005cdab", "shasum": ""}, "require": {"php": "^7.1", "psr/http-message": "^1.0"}, "require-dev": {"phpunit/phpunit": "^7.0", "slim/psr7": "^0.6", "squizlabs/php_codesniffer": "^3.5"}, "time": "2020-05-09T18:13:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Slim\\Views\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Render PHP view scripts into a PSR-7 Response object.", "keywords": ["framework", "php", "phtml", "renderer", "slim", "template", "view"], "support": {"issues": "https://github.com/slimphp/PHP-View/issues", "source": "https://github.com/slimphp/PHP-View/tree/3.x"}, "install-path": "../slim/php-view"}, {"name": "slim/slim", "version": "4.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/slimphp/Slim.git", "reference": "c8934c35d9d98b1a1df9f99ee69b77a59e0aa820"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slimphp/Slim/zipball/c8934c35d9d98b1a1df9f99ee69b77a59e0aa820", "reference": "c8934c35d9d98b1a1df9f99ee69b77a59e0aa820", "shasum": ""}, "require": {"ext-json": "*", "nikic/fast-route": "^1.3", "php": "^7.2 || ^8.0", "psr/container": "^1.0 || ^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0", "psr/log": "^1.1"}, "require-dev": {"adriansuter/php-autoload-override": "^1.2", "ext-simplexml": "*", "guzzlehttp/psr7": "^1.8", "http-interop/http-factory-guzzle": "^1.0", "laminas/laminas-diactoros": "^2.4", "nyholm/psr7": "^1.4", "nyholm/psr7-server": "^1.0.1", "phpspec/prophecy": "^1.13", "phpstan/phpstan": "^0.12.85", "phpunit/phpunit": "^8.5.13 || ^9.3.8", "slim/http": "^1.2", "slim/psr7": "^1.3", "squizlabs/php_codesniffer": "^3.6", "weirdan/prophecy-shim": "^1.0 || ^2.0.2"}, "suggest": {"ext-simplexml": "Needed to support XML format in BodyParsingMiddleware", "ext-xml": "Needed to support XML format in BodyParsingMiddleware", "php-di/php-di": "PHP-DI is the recommended container library to be used with Slim", "slim/psr7": "Slim PSR-7 implementation. See https://www.slimframework.com/docs/v4/start/installation.html for more information."}, "time": "2021-06-29T19:41:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Slim\\": "<PERSON>"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://joshlockhart.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://silentworks.co.uk"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://akrabat.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.lgse.com"}, {"name": "<PERSON>", "email": "g<PERSON><PERSON>@me.com", "homepage": "http://gabrielmanricks.com"}], "description": "Slim is a PHP micro framework that helps you quickly write simple yet powerful web applications and APIs", "homepage": "https://www.slimframework.com", "keywords": ["api", "framework", "micro", "router"], "support": {"docs": "https://www.slimframework.com/docs/v4/", "forum": "https://discourse.slimframework.com/", "irc": "irc://irc.freenode.net:6667/slimphp", "issues": "https://github.com/slimphp/Slim/issues", "rss": "https://www.slimframework.com/blog/feed.rss", "slack": "https://slimphp.slack.com/", "source": "https://github.com/slimphp/Slim", "wiki": "https://github.com/slimphp/Slim/wiki"}, "funding": [{"url": "https://opencollective.com/slimphp", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/slim/slim", "type": "tidelift"}], "install-path": "../slim/slim"}, {"name": "soundasleep/html2text", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/soundasleep/html2text.git", "reference": "3243a7107878a61685d2eccf99918d6479e039fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/soundasleep/html2text/zipball/3243a7107878a61685d2eccf99918d6479e039fc", "reference": "3243a7107878a61685d2eccf99918d6479e039fc", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "~7.0", "soundasleep/component-tests": "~0.2"}, "time": "2019-02-15T01:44:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Soundasleep\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jevon.org", "role": "Developer"}], "description": "A PHP script to convert HTML into a plain text format", "homepage": "https://github.com/soundasleep/html2text", "keywords": ["email", "html", "php", "text"], "support": {"email": "<EMAIL>", "issues": "https://github.com/soundasleep/html2text/issues", "source": "https://github.com/soundasleep/html2text/tree/master"}, "install-path": "../soundasleep/html2text"}], "dev": true, "dev-package-names": []}