/*

ewjTable (requires ew*.js)
@license Copyright (C) 2017 by e.World Technology Limited (http://www.hkvstore.com)

Based on:

jTable 2.4.0
http://www.jtable.org

---------------------------------------------------------------------------

Copyright (C) 2011-2014 by <PERSON><PERSON> (http://www.halilibrahimkalkan.com)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

*/
!function(e){var t;e(window).on("beforeunload",(function(){t=!0})),e(window).on("unload",(function(){t=!1})),e.widget("ew.ewjtable",{options:{actions:{},fields:{},animationsEnabled:!0,defaultDateFormat:null,dialogShowEffect:"fade",dialogHideEffect:"fade",showCloseButton:!1,loadingAnimationDelay:500,saveUserPreferences:!0,jqueryuiTheme:!1,unAuthorizedRequestRedirectUrl:null,ajaxSettings:{type:"POST",dataType:"json"},toolbar:{hoverAnimation:!0,hoverAnimationDuration:60,hoverAnimationEasing:void 0,items:[]},closeRequested:function(e,t){},formCreated:function(e,t){},formSubmitting:function(e,t){},formClosed:function(e,t){},loadingRecords:function(e,t){},recordsLoaded:function(e,t){},rowInserted:function(e,t){},rowsRemoved:function(e,t){},messages:{serverCommunicationError:"An error occured while communicating to the server.",loadingMessage:"Loading records...",noDataAvailable:"No data available!",close:"Close"}},_$mainContainer:null,_$titleDiv:null,_$toolbarDiv:null,_$table:null,_$tableBody:null,_$tableRows:null,_$busyDiv:null,_$busyMessageDiv:null,_columnList:null,_fieldList:null,_keyField:null,_firstDataColumnOffset:0,_lastPostData:null,_cache:null,_create:function(){this._normalizeFieldsOptions(),this._initializeFields(),this._createFieldAndColumnList(),this._createMainContainer(),this._createTableTitle(),this._createToolBar(),this._createTable(),this._createBusyPanel(),this._addNoDataRow(),this._cookieKeyPrefix=this._generateCookieKeyPrefix()},_normalizeFieldsOptions:function(){var t=this;e.each(t.options.fields,(function(e,o){t._normalizeFieldOptions(e,o)}))},_normalizeFieldOptions:function(t,o){if(null==o.listClass&&(o.listClass=""),null==o.inputClass&&(o.inputClass=""),o.dependsOn&&"string"===e.type(o.dependsOn)){var a=o.dependsOn.split(",");o.dependsOn=[];for(var i=0;i<a.length;i++)o.dependsOn.push(e.trim(a[i]))}},_initializeFields:function(){this._lastPostData={},this._$tableRows=[],this._columnList=[],this._fieldList=[],this._cache=[]},_createFieldAndColumnList:function(){var t=this;e.each(t.options.fields,(function(e,o){t._fieldList.push(e),1==o.key&&(t._keyField=e),0!=o.list&&"hidden"!=o.type&&t._columnList.push(e)}))},_createMainContainer:function(){this._$mainContainer=e("<div />").addClass("ewjtable-main-container").appendTo(this.element),this._jqueryuiThemeAddClass(this._$mainContainer,"ui-widget")},_createTableTitle:function(){var t=this;if(t.options.title){var o=e("<div />").addClass("ewjtable-title").appendTo(t._$mainContainer);if(t._jqueryuiThemeAddClass(o,"ui-widget-header"),e("<div />").addClass("ewjtable-title-text").appendTo(o).append(t.options.title),t.options.showCloseButton){var a=e("<span />").html(t.options.messages.close);e("<button></button>").addClass("ewjtable-command-button ewjtable-close-button").attr("title",t.options.messages.close).append(a).appendTo(o).click((function(e){e.preventDefault(),e.stopPropagation(),t._onCloseRequested()}))}t._$titleDiv=o}},_createTable:function(){this._$table=e("<table></table>").addClass("ewjtable table table-sm").appendTo(this._$mainContainer),this.options.tableId&&this._$table.attr("id",this.options.tableId),this._jqueryuiThemeAddClass(this._$table,"ui-widget-content"),this._createTableHead(),this._createTableBody()},_createTableHead:function(){var t=e("<thead></thead>").appendTo(this._$table);this._addRowToTableHead(t)},_addRowToTableHead:function(t){var o=e("<tr></tr>").appendTo(t);this._addColumnsToHeaderRow(o)},_addColumnsToHeaderRow:function(e){for(var t=0;t<this._columnList.length;t++){var o=this._columnList[t];this._createHeaderCellForField(o,this.options.fields[o]).appendTo(e)}},_createHeaderCellForField:function(t,o){var a=e("<span />").addClass("ewjtable-column-header-text").html(o.title),i=e("<div />").addClass("ewjtable-column-header-container").append(a),s=e("<th></th>").addClass("ewjtable-column-header").addClass(o.listClass).css("width",o.width).data("fieldName",t).append(i);return this._jqueryuiThemeAddClass(s,"ui-state-default"),s},_createEmptyCommandHeader:function(){var t=e("<th></th>").addClass("ewjtable-command-column-header").css("width","1%");return this._jqueryuiThemeAddClass(t,"ui-state-default"),t},_createTableBody:function(){this._$tableBody=e("<tbody></tbody>").appendTo(this._$table)},_createBusyPanel:function(){this._$busyMessageDiv=e("<div />").addClass("ewjtable-busy-message").prependTo(this._$mainContainer),this._$busyDiv=e("<div />").addClass("ewjtable-busy-panel-background").prependTo(this._$mainContainer),this._jqueryuiThemeAddClass(this._$busyMessageDiv,"ui-widget-header"),this._hideBusy()},load:function(e,t){this._lastPostData=e,this._reloadTable(t)},reload:function(e){this._reloadTable(e)},getRowByKey:function(e){for(var t=0;t<this._$tableRows.length;t++)if(e==this._getKeyValueOfRecord(this._$tableRows[t].data("record")))return this._$tableRows[t];return null},destroy:function(){this.element.empty(),e.Widget.prototype.destroy.call(this)},_setOption:function(e,t){},_reloadTable:function(t){var o=this,a=function(e){o._hideBusy(),"OK"==e.result?(o._removeAllRows("reloading"),o._addRecordsToTable(e.records),o._onRecordsLoaded(e),t&&t(e)):o._showError(e.message)};if(o._showBusy(o.options.messages.loadingMessage,o.options.loadingAnimationDelay),o._onLoadingRecords(),e.isFunction(o.options.actions.listAction)){var i=o.options.actions.listAction(o._lastPostData,o._createJtParamsForLoading());o._isDeferredObject(i)?i.done((function(e){a(e)})).fail((function(){o._showError(o.options.messages.serverCommunicationError)})).always((function(){o._hideBusy()})):a(i)}else{var s=o._createRecordLoadUrl();o._ajax({url:s,data:o._lastPostData,success:function(e){a(e)},error:function(){o._hideBusy(),o._showError(o.options.messages.serverCommunicationError)}})}},_createRecordLoadUrl:function(){return this.options.actions.listAction},_createJtParamsForLoading:function(){return{}},_createRowFromRecord:function(t){var o=e("<tr></tr>").addClass("ewjtable-data-row").attr("data-record-key",this._getKeyValueOfRecord(t)).data("record",t);return this._addCellsToRowUsingRecord(o),o},_addCellsToRowUsingRecord:function(e){for(var t=e.data("record"),o=0;o<this._columnList.length;o++)this._createCellForRecordField(t,this._columnList[o]).appendTo(e)},_createCellForRecordField:function(t,o){return e("<td></td>").addClass(this.options.fields[o].listClass).append(this._getDisplayTextForRecordField(t,o))},_addRecordsToTable:function(t){var o=this;e.each(t,(function(e,t){o._addRow(o._createRowFromRecord(t))})),o._refreshRowStyles()},_addRowToTable:function(e,t,o,a){var i={index:this._normalizeNumber(t,0,this._$tableRows.length,this._$tableRows.length)};1==o&&(i.isNewRow=!0),0==a&&(i.animationsEnabled=!1),this._addRow(e,i)},_addRow:function(t,o){o=e.extend({index:this._$tableRows.length,isNewRow:!1,animationsEnabled:!0},o),this._$tableRows.length<=0&&this._removeNoDataRow(),o.index=this._normalizeNumber(o.index,0,this._$tableRows.length,this._$tableRows.length),o.index==this._$tableRows.length?(this._$tableBody.append(t),this._$tableRows.push(t)):0==o.index?(this._$tableBody.prepend(t),this._$tableRows.unshift(t)):(this._$tableRows[o.index-1].after(t),this._$tableRows.splice(o.index,0,t)),this._onRowInserted(t,o.isNewRow),o.isNewRow&&(this._refreshRowStyles(),this.options.animationsEnabled&&o.animationsEnabled&&this._showNewRowAnimation(t))},_showNewRowAnimation:function(e){var t="ewjtable-row-created";this.options.jqueryuiTheme&&(t+=" ui-state-highlight"),e.addClass(t,"slow","",(function(){e.removeClass(t,5e3)}))},_removeRowsFromTable:function(t,o){var a=this;t.length<=0||(t.addClass("ewjtable-row-removed").remove(),t.each((function(){var t=a._findRowIndex(e(this));t>=0&&a._$tableRows.splice(t,1)})),a._onRowsRemoved(t,o),0==a._$tableRows.length&&a._addNoDataRow(),a._refreshRowStyles())},_findRowIndex:function(e){return this._findIndexInArray(e,this._$tableRows,(function(e,t){return e.data("record")==t.data("record")}))},_removeAllRows:function(e){if(!(this._$tableRows.length<=0)){var t=this._$tableBody.find("tr.ewjtable-data-row");this._$tableBody.empty(),this._$tableRows=[],this._onRowsRemoved(t,e),this._addNoDataRow()}},_addNoDataRow:function(){if(!(this._$tableBody.find(">tr.ewjtable-no-data-row").length>0)){var t=e("<tr></tr>").addClass("ewjtable-no-data-row").appendTo(this._$tableBody),o=this._$table.find("thead th").length;e("<td></td>").attr("colspan",o).html(this.options.messages.noDataAvailable).appendTo(t)}},_removeNoDataRow:function(){this._$tableBody.find(".ewjtable-no-data-row").remove()},_refreshRowStyles:function(){for(var e=0;e<this._$tableRows.length;e++)e%2==0?this._$tableRows[e].addClass("ewjtable-row-even"):this._$tableRows[e].removeClass("ewjtable-row-even")},_getDisplayTextForRecordField:function(e,t){var o=this.options.fields[t],a=e[t];return o.display?o.display({record:e}):"date"==o.type?this._getDisplayTextForDateRecordField(o,a):a},_getDisplayTextForDateRecordField:function(e,t){if(!t)return"";var o=e.displayFormat||this.options.defaultDateFormat;return ew.formatDate(t,o)},_createToolBar:function(){this._$toolbarDiv=e("<div />").addClass("ewjtable-toolbar").appendTo(this._$titleDiv);for(var t=0;t<this.options.toolbar.items.length;t++)this._addToolBarItem(this.options.toolbar.items[t])},_addToolBarItem:function(t){if(null==t||null==t.text&&null==t.icon)return this._logWarn("Can not add tool bar item since it is not valid!"),this._logWarn(t),null;var o=e("<span></span>").addClass("ewjtable-toolbar-item").appendTo(this._$toolbarDiv);if(this._jqueryuiThemeAddClass(o,"ui-widget ui-state-default ui-corner-all","ui-state-hover"),t.cssClass&&o.addClass(t.cssClass),t.tooltip&&o.attr("title",t.tooltip),t.icon){var a=e('<span class="ewjtable-toolbar-item-icon"></span>').appendTo(o);!0===t.icon||e.type("string"===t.icon)&&a.css("background",'url("'+t.icon+'")')}t.text&&e('<span class=""></span>').html(t.text).addClass("ewjtable-toolbar-item-text").appendTo(o),t.click&&o.click((function(){t.click()}));var i=void 0,s=void 0;return this.options.toolbar.hoverAnimation&&(i=this.options.toolbar.hoverAnimationDuration,s=this.options.toolbar.hoverAnimationEasing),o.hover((function(){o.addClass("ewjtable-toolbar-item-hover",i,s)}),(function(){o.removeClass("ewjtable-toolbar-item-hover",i,s)})),o},_showError:function(e){ew.alert(e)},_setBusyTimer:null,_showBusy:function(e,t){var o=this;o._$busyDiv.width(o._$mainContainer.width()).height(o._$mainContainer.height()).addClass("ewjtable-busy-panel-background-invisible").show();var a=function(){o._$busyDiv.removeClass("ewjtable-busy-panel-background-invisible"),o._$busyMessageDiv.html(e).show()};if(t){if(o._setBusyTimer)return;o._setBusyTimer=setTimeout(a,t)}else a()},_hideBusy:function(){clearTimeout(this._setBusyTimer),this._setBusyTimer=null,this._$busyDiv.hide(),this._$busyMessageDiv.html("").hide()},_isBusy:function(){return this._$busyMessageDiv.is(":visible")},_jqueryuiThemeAddClass:function(e,t,o){this.options.jqueryuiTheme&&(e.addClass(t),o&&e.hover((function(){e.addClass(o)}),(function(){e.removeClass(o)})))},_performAjaxCall:function(e,t,o,a,i){this._ajax({url:e,data:t,async:o,success:a,error:i})},_unAuthorizedRequestHandler:function(){this.options.unAuthorizedRequestRedirectUrl?location.href=this.options.unAuthorizedRequestRedirectUrl:location.reload(!0)},_ajax:function(o){var a=this,i={statusCode:{401:function(){a._unAuthorizedRequestHandler()}}};(i=e.extend(i,this.options.ajaxSettings,o)).success=function(e){e&&1==e.unAuthorizedRequest&&a._unAuthorizedRequestHandler(),o.success&&o.success(e)},i.error=function(e,a,i){t?e.abort():o.error&&o.error(arguments)},i.complete=function(){o.complete&&o.complete()},e.ajax(i)},_getKeyValueOfRecord:function(e){return e[this._keyField]},_setCookie:function(e,t){e=this._cookieKeyPrefix+e;var o=new Date;o.setDate(o.getDate()+30),document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+"; expires="+o.toUTCString()},_getCookie:function(e){e=this._cookieKeyPrefix+e;for(var t=document.cookie.split("; "),o=0;o<t.length;o++)if(t[o]){var a=t[o].split("=");if(2==a.length&&decodeURIComponent(a[0])===e)return decodeURIComponent(a[1]||"")}return null},_generateCookieKeyPrefix:function(){var e="";return this.options.tableId&&(e=e+this.options.tableId+"#"),"ewjtable#"+function(e){var t=0;if(0==e.length)return t;for(var o=0;o<e.length;o++){t=(t<<5)-t+e.charCodeAt(o),t&=t}return t}(e=e+this._columnList.join("$")+"#c"+this._$table.find("thead th").length)},_onLoadingRecords:function(){this._trigger("loadingRecords",null,{})},_onRecordsLoaded:function(e){this._trigger("recordsLoaded",null,{records:e.records,serverResponse:e})},_onRowInserted:function(e,t){this._trigger("rowInserted",null,{row:e,record:e.data("record"),isNewRow:t})},_onRowsRemoved:function(e,t){this._trigger("rowsRemoved",null,{rows:e,reason:t})},_onCloseRequested:function(){this._trigger("closeRequested",null,{})}})}(jQuery),function(e){e.extend(!0,e.ew.ewjtable.prototype,{_getPropertyOfObject:function(e,t){if(t.indexOf(".")<0)return e[t];var o=t.substring(0,t.indexOf(".")),a=t.substring(t.indexOf(".")+1);return this._getPropertyOfObject(e[o],a)},_setPropertyOfObject:function(e,t,o){if(t.indexOf(".")<0)e[t]=o;else{var a=t.substring(0,t.indexOf(".")),i=t.substring(t.indexOf(".")+1);this._setPropertyOfObject(e[a],i,o)}},_insertToArrayIfDoesNotExists:function(t,o){e.inArray(o,t)<0&&t.push(o)},_findIndexInArray:function(e,t,o){o||(o=function(e,t){return e==t});for(var a=0;a<t.length;a++)if(o(e,t[a]))return a;return-1},_normalizeNumber:function(e,t,o,a){return null==e||null==e||isNaN(e)?a:e<t?t:e>o?o:e},_formatString:function(){if(0==arguments.length)return null;for(var e=arguments[0],t=1;t<arguments.length;t++){var o="{"+(t-1)+"}";e=e.replace(o,arguments[t])}return e},_isDeferredObject:function(e){return e.then&&e.done&&e.fail},_logDebug:function(e){window.console&&console.log("ewjTable DEBUG: "+e)},_logInfo:function(e){window.console&&console.log("ewjTable INFO: "+e)},_logWarn:function(e){window.console&&console.log("ewjTable WARNING: "+e)},_logError:function(e){window.console&&console.log("ewjTable ERROR: "+e)}}),Array.prototype.indexOf||(Array.prototype.indexOf=function(e){var t=this.length,o=Number(arguments[1])||0;for((o=o<0?Math.ceil(o):Math.floor(o))<0&&(o+=t);o<t;o++)if(o in this&&this[o]===e)return o;return-1})}(jQuery),function(e){var t={_create:e.ew.ewjtable.prototype._create,_addColumnsToHeaderRow:e.ew.ewjtable.prototype._addColumnsToHeaderRow,_addCellsToRowUsingRecord:e.ew.ewjtable.prototype._addCellsToRowUsingRecord,_onLoadingRecords:e.ew.ewjtable.prototype._onLoadingRecords,_onRecordsLoaded:e.ew.ewjtable.prototype._onRecordsLoaded,_onRowsRemoved:e.ew.ewjtable.prototype._onRowsRemoved};e.extend(!0,e.ew.ewjtable.prototype,{options:{selecting:!1,multiselect:!1,selectingCheckboxes:!1,selectOnRowClick:!0,selectionChanged:function(e,t){}},_selectedRecordIdsBeforeLoad:null,_$selectAllCheckbox:null,_shiftKeyDown:!1,_create:function(){this.options.selecting&&this.options.selectingCheckboxes&&(++this._firstDataColumnOffset,this._bindKeyboardEvents()),t._create.apply(this,arguments)},_bindKeyboardEvents:function(){var t=this;e(document).keydown((function(e){switch(e.which){case 16:t._shiftKeyDown=!0}})).keyup((function(e){switch(e.which){case 16:t._shiftKeyDown=!1}}))},selectedRows:function(){return this._getSelectedRows()},selectRows:function(t){this._selectRows(t),this._onSelectionChanged(e.Event("selectRows"))},_addColumnsToHeaderRow:function(e){this.options.selecting&&this.options.selectingCheckboxes&&(this.options.multiselect?e.append(this._createSelectAllHeader()):e.append(this._createEmptyCommandHeader())),t._addColumnsToHeaderRow.apply(this,arguments)},_addCellsToRowUsingRecord:function(e){this.options.selecting&&this._makeRowSelectable(e),t._addCellsToRowUsingRecord.apply(this,arguments)},_onLoadingRecords:function(){this.options.selecting&&this._storeSelectionList(),t._onLoadingRecords.apply(this,arguments)},_onRecordsLoaded:function(){this.options.selecting&&this._restoreSelectionList(),t._onRecordsLoaded.apply(this,arguments)},_onRowsRemoved:function(o,a){this.options.selecting&&"reloading"!=a&&o.filter(".ewjtable-row-selected").length>0&&this._onSelectionChanged(e.Event("rowsRemoved")),t._onRowsRemoved.apply(this,arguments)},_createSelectAllHeader:function(){var t=this,o=e('<th class=""></th>').addClass("ewjtable-command-column-header ewjtable-column-header-selecting");this._jqueryuiThemeAddClass(o,"ui-state-default");var a=e("<div />").addClass("ewjtable-column-header-container").appendTo(o);return t._$selectAllCheckbox=e('<input type="checkbox" class="custom-control-input" id="modal_select_all">').click((function(){if(t._$tableRows.length<=0)t._$selectAllCheckbox.prop("checked",!1);else{var o=t._$tableBody.find(">tr.ewjtable-data-row");t._$selectAllCheckbox.is(":checked")?t._selectRows(o):t._deselectRows(o),t._onSelectionChanged(e.Event("selectAll",{rows:o}))}})),e('<div class="custom-control custom-checkbox d-inline-block"></div>').append(t._$selectAllCheckbox).append('<label class="custom-control-label" for="modal_select_all"></label>').appendTo(a),o},_storeSelectionList:function(){var t=this;t.options.selecting&&(t._selectedRecordIdsBeforeLoad=[],t._getSelectedRows().each((function(){t._selectedRecordIdsBeforeLoad.push(t._getKeyValueOfRecord(e(this).data("record")))})))},_restoreSelectionList:function(){if(this.options.selecting){for(var t=0,o=0;o<this._$tableRows.length;++o){var a=this._getKeyValueOfRecord(this._$tableRows[o].data("record"));e.inArray(a,this._selectedRecordIdsBeforeLoad)>-1&&(this._selectRows(this._$tableRows[o]),++t)}this._selectedRecordIdsBeforeLoad.length>0&&this._selectedRecordIdsBeforeLoad.length!=t&&this._onSelectionChanged(e.Event("restoreSelectionList")),this._selectedRecordIdsBeforeLoad=[],this._refreshSelectAllCheckboxState()}},_getSelectedRows:function(){return this._$tableBody.find(">tr.ewjtable-row-selected")},_makeRowSelectable:function(t){var o=this;if(o.options.selectOnRowClick&&t.click((function(a){e(a.target).is(".custom-control-label")||o._invertRowSelection(t)})),o.options.selectingCheckboxes){var a=e("<td></td>").addClass("ewjtable-selecting-column"),i=o.options.multiselect?"checkbox":"radio",s="modal_"+i+"_"+ew.random(),n=e('<div class="custom-control custom-'+i+' d-inline-block"><input type="'+i+'" class="custom-control-input" id="'+s+'"><label class="custom-control-label" for="'+s+'"></label></div>').appendTo(a);o.options.selectOnRowClick||n.find("input").click((function(e){o._invertRowSelection(t)})),t.append(a)}},_invertRowSelection:function(t){if(t.hasClass("ewjtable-row-selected"))this._deselectRows(t);else if(this._shiftKeyDown){var o=this._findRowIndex(t),a=this._findFirstSelectedRowIndexBeforeIndex(o)+1;if(a>0&&a<o)this._selectRows(this._$tableBody.find("tr").slice(a,o+1));else{var i=this._findFirstSelectedRowIndexAfterIndex(o)-1;i>o?this._selectRows(this._$tableBody.find("tr").slice(o,i+1)):this._selectRows(t)}}else this._selectRows(t);this._onSelectionChanged(e.Event("invertRowSelection",{rows:t}))},_findFirstSelectedRowIndexBeforeIndex:function(e){for(var t=e-1;t>=0;--t)if(this._$tableRows[t].hasClass("ewjtable-row-selected"))return t;return-1},_findFirstSelectedRowIndexAfterIndex:function(e){for(var t=e+1;t<this._$tableRows.length;++t)if(this._$tableRows[t].hasClass("ewjtable-row-selected"))return t;return-1},_selectRows:function(e){this.options.multiselect||this._deselectRows(this._getSelectedRows()),e.addClass("ewjtable-row-selected"),this._jqueryuiThemeAddClass(e,"ui-state-highlight"),this.options.selectingCheckboxes&&e.find(">td.ewjtable-selecting-column input").prop("checked",!0),this._refreshSelectAllCheckboxState()},_deselectRows:function(e){e.removeClass("ewjtable-row-selected ui-state-highlight"),this.options.selectingCheckboxes&&e.find(">td.ewjtable-selecting-column input").prop("checked",!1),this._refreshSelectAllCheckboxState()},_refreshSelectAllCheckboxState:function(){if(this.options.selectingCheckboxes&&this.options.multiselect){var e=this._$tableRows.length,t=this._getSelectedRows().length;0==t?(this._$selectAllCheckbox.prop("indeterminate",!1),this._$selectAllCheckbox.prop("checked",!1)):t==e?(this._$selectAllCheckbox.prop("indeterminate",!1),this._$selectAllCheckbox.prop("checked",!0)):(this._$selectAllCheckbox.prop("checked",!1),this._$selectAllCheckbox.prop("indeterminate",!0))}},_onSelectionChanged:function(e){this._trigger("selectionChanged",null,e||{})}})}(jQuery),function(e){var t={load:e.ew.ewjtable.prototype.load,_create:e.ew.ewjtable.prototype._create,_setOption:e.ew.ewjtable.prototype._setOption,_createRecordLoadUrl:e.ew.ewjtable.prototype._createRecordLoadUrl,_createJtParamsForLoading:e.ew.ewjtable.prototype._createJtParamsForLoading,_addRowToTable:e.ew.ewjtable.prototype._addRowToTable,_addRow:e.ew.ewjtable.prototype._addRow,_removeRowsFromTable:e.ew.ewjtable.prototype._removeRowsFromTable,_onRecordsLoaded:e.ew.ewjtable.prototype._onRecordsLoaded};e.extend(!0,e.ew.ewjtable.prototype,{options:{paging:!0,pageList:"minimal",pageSize:10,pageSizes:[10,20,30],pageSizeChangeArea:!1,gotoPageArea:"combobox",messages:{pagingInfo:"Showing {0}-{1} of {2}",pageSizeChangeLabel:"Row count",gotoPageLabel:"Go to page"}},_$bottomPanel:null,_$pagingListArea:null,_$pageSizeChangeArea:null,_$pageInfoSpan:null,_$gotoPageArea:null,_$gotoPageInput:null,_totalRecordCount:0,_currentPageNo:1,_create:function(){t._create.apply(this,arguments),this.options.paging&&(this._loadPagingSettings(),this._createBottomPanel(),this._createPageListArea(),this._createGotoPageInput(),this._createPageSizeSelection())},_loadPagingSettings:function(){if(this.options.saveUserPreferences){var e=this._getCookie("page-size");e&&(this.options.pageSize=this._normalizeNumber(e,1,1e6,this.options.pageSize))}},_createBottomPanel:function(){this._$bottomPanel=e("<div />").addClass("ewjtable-bottom-panel clearfix").insertAfter(this._$table),this._jqueryuiThemeAddClass(this._$bottomPanel,"ui-state-default"),e("<div />").addClass("ewjtable-left-area float-left").appendTo(this._$bottomPanel),e("<div />").addClass("ewjtable-right-area float-right").appendTo(this._$bottomPanel)},_createPageListArea:function(){this._$pagingListArea=e("<span></span>").addClass("ewjtable-page-list").appendTo(this._$bottomPanel.find(".ewjtable-left-area")),this._$pageInfoSpan=e("<span></span>").addClass("ewjtable-page-info").appendTo(this._$bottomPanel.find(".ewjtable-right-area"))},_createPageSizeSelection:function(){var t=this;if(t.options.pageSizeChangeArea){t._findIndexInArray(t.options.pageSize,t.options.pageSizes)<0&&(t.options.pageSizes.push(parseInt(t.options.pageSize)),t.options.pageSizes.sort((function(e,t){return e-t}))),t._$pageSizeChangeArea=e("<span></span>").addClass("ewjtable-page-size-change").appendTo(t._$bottomPanel.find(".ewjtable-left-area")),t._$pageSizeChangeArea.append("<span>"+t.options.messages.pageSizeChangeLabel+": </span>");for(var o=e("<select></select>").appendTo(t._$pageSizeChangeArea),a=0;a<t.options.pageSizes.length;a++)o.append('<option value="'+t.options.pageSizes[a]+'">'+t.options.pageSizes[a]+"</option>");o.val(t.options.pageSize),o.change((function(){t._changePageSize(parseInt(e(this).val()))}))}},_createGotoPageInput:function(){var t=this;t.options.gotoPageArea&&"none"!=t.options.gotoPageArea&&(this._$gotoPageArea=e("<span></span>").addClass("ewjtable-goto-page").appendTo(t._$bottomPanel.find(".ewjtable-left-area")),this._$gotoPageArea.append("<span>"+t.options.messages.gotoPageLabel+"</span>"),"combobox"==t.options.gotoPageArea?(t._$gotoPageInput=e("<select></select>").addClass("custom-select custom-select-sm").appendTo(this._$gotoPageArea).data("pageCount",1).change((function(){t._changePage(parseInt(e(this).val()))})),t._$gotoPageInput.append('<option value="1">1</option>')):t._$gotoPageInput=e('<input type="text" maxlength="10" value="'+t._currentPageNo+'" />').addClass("form-control form-control-sm").appendTo(this._$gotoPageArea).keypress((function(e){if(13==e.which)e.preventDefault(),t._changePage(parseInt(t._$gotoPageInput.val()));else if(43==e.which)e.preventDefault(),t._changePage(parseInt(t._$gotoPageInput.val())+1);else if(45==e.which)e.preventDefault(),t._changePage(parseInt(t._$gotoPageInput.val())-1);else{47<e.keyCode&&e.keyCode<58&&0==e.shiftKey&&0==e.altKey||8==e.keyCode||9==e.keyCode||e.preventDefault()}})))},_refreshGotoPageInput:function(){if(this.options.gotoPageArea&&"none"!=this.options.gotoPageArea){if(this._totalRecordCount<=0||this.options.pageSize>this._totalRecordCount?this._$gotoPageArea.hide():this._$gotoPageArea.show(),"combobox"==this.options.gotoPageArea){var e=this._$gotoPageInput.data("pageCount"),t=this._calculatePageCount();if(e!=t){this._$gotoPageInput.empty();var o=1;t>1e4?o=100:t>5e3?o=10:t>2e3?o=5:t>1e3&&(o=2);for(var a=o;a<=t;a+=o)this._$gotoPageInput.append('<option value="'+a+'">'+a+"</option>");this._$gotoPageInput.data("pageCount",t)}}this._$gotoPageInput.val(this._currentPageNo)}},load:function(){this._currentPageNo=1,t.load.apply(this,arguments)},_setOption:function(e,o){t._setOption.apply(this,arguments),"pageSize"==e&&this._changePageSize(parseInt(o))},_changePageSize:function(e){if(e!=this.options.pageSize){this.options.pageSize=e;var t=this._calculatePageCount();this._currentPageNo>t&&(this._currentPageNo=t),this._currentPageNo<=0&&(this._currentPageNo=1);var o=this._$bottomPanel.find(".ewjtable-page-size-change select");if(o.length>0)if(parseInt(o.val())!=e)o.find("option[value="+e+"]").length>0&&o.val(e);this._savePagingSettings(),this._reloadTable()}},_savePagingSettings:function(){this.options.saveUserPreferences&&this._setCookie("page-size",this.options.pageSize)},_createRecordLoadUrl:function(){var e=t._createRecordLoadUrl.apply(this,arguments);return e=this._addPagingInfoToUrl(e,this._currentPageNo)},_createJtParamsForLoading:function(){var e=t._createJtParamsForLoading.apply(this,arguments);return this.options.paging&&(e.start=(this._currentPageNo-1)*this.options.pageSize,e.recperpage=this.options.pageSize),e},_addRowToTable:function(e,o,a){a&&this.options.paging?this._reloadTable():t._addRowToTable.apply(this,arguments)},_addRow:function(e,o){o&&o.isNewRow&&this.options.paging?this._reloadTable():t._addRow.apply(this,arguments)},_removeRowsFromTable:function(e,o){t._removeRowsFromTable.apply(this,arguments),this.options.paging&&(this._$tableRows.length<=0&&this._currentPageNo>1&&--this._currentPageNo,this._reloadTable())},_onRecordsLoaded:function(e){this.options.paging&&(this._totalRecordCount=e.totalRecordCount,this._createPagingList(),this._createPagingInfo(),this._refreshGotoPageInput()),t._onRecordsLoaded.apply(this,arguments)},_addPagingInfoToUrl:function(e,t){if(!this.options.paging)return e;var o=(t-1)*this.options.pageSize,a=this.options.pageSize;return e+(e.indexOf("?")<0?"?":"&")+"start="+o+"&recperpage="+a},_createPagingList:function(){if(!(this.options.pageSize<=0||(this._$pagingListArea.empty(),this._totalRecordCount<=0||this.options.pageSize>this._totalRecordCount))){var e=this._calculatePageCount();this._createFirstAndPreviousPageButtons(),"normal"==this.options.pageList&&this._createPageNumberButtons(this._calculatePageNumbers(e)),this._createLastAndNextPageButtons(e),this._bindClickEventsToPageNumberButtons(),this._$pagingListArea.wrapInner('<div class="btn-group btn-group-sm" role="group"></div>')}},_createFirstAndPreviousPageButtons:function(){var t=e("<button></button>").addClass("ewjtable-page-number-first btn btn-default").html('<i class="icon-first ew-icon"></i>').data("pageNumber",1).appendTo(this._$pagingListArea),o=e("<button></button>").addClass("ewjtable-page-number-previous btn btn-default").html('<i class="icon-prev ew-icon"></i>').data("pageNumber",this._currentPageNo-1).appendTo(this._$pagingListArea);this._jqueryuiThemeAddClass(t,"ui-button ui-state-default","ui-state-hover"),this._jqueryuiThemeAddClass(o,"ui-button ui-state-default","ui-state-hover"),this._currentPageNo<=1&&(t.addClass("ewjtable-page-number-disabled disabled"),o.addClass("ewjtable-page-number-disabled disabled"),this._jqueryuiThemeAddClass(t,"ui-state-disabled"),this._jqueryuiThemeAddClass(o,"ui-state-disabled"))},_createLastAndNextPageButtons:function(t){var o=e("<button></button>").addClass("ewjtable-page-number-next btn btn-default").html('<i class="icon-next ew-icon"></i>').data("pageNumber",this._currentPageNo+1).appendTo(this._$pagingListArea),a=e("<button></button>").addClass("ewjtable-page-number-last btn btn-default").html('<i class="icon-last ew-icon"></i>').data("pageNumber",t).appendTo(this._$pagingListArea);this._jqueryuiThemeAddClass(o,"ui-button ui-state-default","ui-state-hover"),this._jqueryuiThemeAddClass(a,"ui-button ui-state-default","ui-state-hover"),this._currentPageNo>=t&&(o.addClass("ewjtable-page-number-disabled disabled"),a.addClass("ewjtable-page-number-disabled disabled"),this._jqueryuiThemeAddClass(o,"ui-state-disabled"),this._jqueryuiThemeAddClass(a,"ui-state-disabled"))},_createPageNumberButtons:function(t){for(var o=0,a=0;a<t.length;a++)t[a]-o>1&&e("<button></button>").addClass("ewjtable-page-number-space btn btn-default").html('<i class="fas fa-ellipsis-h ew-icon"></i>').appendTo(this._$pagingListArea),this._createPageNumberButton(t[a]),o=t[a]},_createPageNumberButton:function(t){var o=e("<button></button>").addClass("ewjtable-page-number btn btn-default").html(t).data("pageNumber",t).appendTo(this._$pagingListArea);this._jqueryuiThemeAddClass(o,"ui-button ui-state-default","ui-state-hover"),this._currentPageNo==t&&(o.addClass("ewjtable-page-number-active ewjtable-page-number-disabled"),this._jqueryuiThemeAddClass(o,"ui-state-active"))},_calculatePageCount:function(){var e=Math.floor(this._totalRecordCount/this.options.pageSize);return this._totalRecordCount%this.options.pageSize!=0&&++e,e},_calculatePageNumbers:function(e){if(e<=4){for(var t=[],o=1;o<=e;++o)t.push(o);return t}var a=[1,2,e-1,e],i=this._normalizeNumber(this._currentPageNo-1,1,e,1),s=this._normalizeNumber(this._currentPageNo+1,1,e,1);return this._insertToArrayIfDoesNotExists(a,i),this._insertToArrayIfDoesNotExists(a,this._currentPageNo),this._insertToArrayIfDoesNotExists(a,s),a.sort((function(e,t){return e-t})),a},_createPagingInfo:function(){if(this._totalRecordCount<=0||this.options.pageSize>this._totalRecordCount)this._$pageInfoSpan.empty();else{var e=(this._currentPageNo-1)*this.options.pageSize+1,t=this._currentPageNo*this.options.pageSize;if((t=this._normalizeNumber(t,e,this._totalRecordCount,0))>=e){var o=this._formatString(this.options.messages.pagingInfo,e,t,this._totalRecordCount);this._$pageInfoSpan.html(o)}}},_bindClickEventsToPageNumberButtons:function(){var t=this;t._$pagingListArea.find(".ewjtable-page-number,.ewjtable-page-number-previous,.ewjtable-page-number-next,.ewjtable-page-number-first,.ewjtable-page-number-last").not(".ewjtable-page-number-disabled").click((function(o){o.preventDefault(),t._changePage(e(this).data("pageNumber"))}))},_changePage:function(e){(e=this._normalizeNumber(e,1,this._calculatePageCount(),1))!=this._currentPageNo?(this._currentPageNo=e,this._reloadTable()):this._refreshGotoPageInput()}})}(jQuery),function(e){var t={_initializeFields:e.ew.ewjtable.prototype._initializeFields,_normalizeFieldOptions:e.ew.ewjtable.prototype._normalizeFieldOptions,_createHeaderCellForField:e.ew.ewjtable.prototype._createHeaderCellForField,_createRecordLoadUrl:e.ew.ewjtable.prototype._createRecordLoadUrl,_createJtParamsForLoading:e.ew.ewjtable.prototype._createJtParamsForLoading};e.extend(!0,e.ew.ewjtable.prototype,{options:{sorting:!1,multiSorting:!1,defaultSorting:""},_lastSorting:null,_initializeFields:function(){t._initializeFields.apply(this,arguments),this._lastSorting=[],this.options.sorting&&this._buildDefaultSortingArray()},_normalizeFieldOptions:function(e,o){t._normalizeFieldOptions.apply(this,arguments),o.sorting=0!=o.sorting},_createHeaderCellForField:function(e,o){var a=t._createHeaderCellForField.apply(this,arguments);return this.options.sorting&&o.sorting&&this._makeColumnSortable(a,e),a},_createRecordLoadUrl:function(){var e=t._createRecordLoadUrl.apply(this,arguments);return e=this._addSortingInfoToUrl(e)},_buildDefaultSortingArray:function(){var t=this;e.each(t.options.defaultSorting.split(","),(function(o,a){e.each(t.options.fields,(function(e,o){if(o.sorting){var i=a.indexOf(e);i>-1&&(a.toUpperCase().indexOf(" DESC",i)>-1?t._lastSorting.push({fieldName:e,sortOrder:"DESC"}):t._lastSorting.push({fieldName:e,sortOrder:"ASC"}))}}))}))},_makeColumnSortable:function(t,o){var a=this;t.addClass("ewjtable-column-header-sortable").click((function(e){e.preventDefault(),a.options.multiSorting&&e.ctrlKey||(a._lastSorting=[]),a._sortTableByColumn(t)})),e.each(this._lastSorting,(function(e,a){a.fieldName==o&&("DESC"==a.sortOrder?t.addClass("ewjtable-column-header-sorted-desc"):t.addClass("ewjtable-column-header-sorted-asc"))}))},_sortTableByColumn:function(e){0==this._lastSorting.length&&e.siblings().removeClass("ewjtable-column-header-sorted-asc jtable-column-header-sorted-desc");for(var t=0;t<this._lastSorting.length;t++)this._lastSorting[t].fieldName==e.data("fieldName")&&this._lastSorting.splice(t--,1);e.hasClass("ewjtable-column-header-sorted-asc")?(e.removeClass("ewjtable-column-header-sorted-asc").addClass("ewjtable-column-header-sorted-desc"),this._lastSorting.push({fieldName:e.data("fieldName"),sortOrder:"DESC"})):(e.removeClass("ewjtable-column-header-sorted-desc").addClass("ewjtable-column-header-sorted-asc"),this._lastSorting.push({fieldName:e.data("fieldName"),sortOrder:"ASC"})),this._reloadTable()},_addSortingInfoToUrl:function(t){if(!this.options.sorting||0==this._lastSorting.length)return t;var o=[];return e.each(this._lastSorting,(function(e,t){o.push(t.fieldName+" "+t.sortOrder)})),t+(t.indexOf("?")<0?"?":"&")+"sorting="+o.join(",")},_createJtParamsForLoading:function(){var o=t._createJtParamsForLoading.apply(this,arguments);if(this.options.sorting&&this._lastSorting.length){var a=[];e.each(this._lastSorting,(function(e,t){a.push(t.fieldName+" "+t.sortOrder)})),o.sorting=a.join(",")}return o}})}(jQuery);
//# sourceMappingURL=jquery.ewjtable.min.js.map