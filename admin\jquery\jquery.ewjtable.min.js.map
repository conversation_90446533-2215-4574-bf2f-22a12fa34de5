{"version": 3, "sources": ["jquery.ewjtable.js"], "names": ["$", "unloadingPage", "window", "on", "widget", "options", "actions", "fields", "animationsEnabled", "defaultDateFormat", "dialogShowEffect", "dialogHideEffect", "showCloseButton", "loadingAnimationDelay", "saveUserPreferences", "jqueryuiTheme", "unAuthorizedRequestRedirectUrl", "ajaxSettings", "type", "dataType", "toolbar", "hoverAnimation", "hoverAnimationDuration", "hoverAnimationEasing", "undefined", "items", "closeRequested", "event", "data", "formCreated", "formSubmitting", "formClosed", "loadingRecords", "recordsLoaded", "rowInserted", "rowsRemoved", "messages", "serverCommunicationError", "loadingMessage", "noDataAvailable", "close", "_$mainContainer", "_$titleDiv", "_$toolbarDiv", "_$table", "_$tableBody", "_$tableRows", "_$busyDiv", "_$busyMessageDiv", "_columnList", "_fieldList", "_keyField", "_firstDataColumnOffset", "_lastPostData", "_cache", "_create", "this", "_normalizeFieldsOptions", "_initializeFields", "_createFieldAndColumnList", "_createMainContainer", "_createTableTitle", "_createToolBar", "_createTable", "_createBusyPanel", "_addNoDataRow", "_cookieKeyPrefix", "_generateCookieKeyPrefix", "self", "each", "fieldName", "props", "_normalizeFieldOptions", "listClass", "inputClass", "dependsOn", "dependsOnArray", "split", "i", "length", "push", "trim", "name", "key", "list", "addClass", "appendTo", "element", "_jqueryuiThemeAddClass", "title", "$titleDiv", "append", "$textSpan", "html", "attr", "click", "e", "preventDefault", "stopPropagation", "_onCloseRequested", "tableId", "_createTableHead", "_createTableBody", "$thead", "_addRowToTableHead", "$tr", "_addColumnsToHeaderRow", "_createHeaderCellForField", "field", "$headerTextSpan", "$headerContainerDiv", "$th", "css", "width", "_createEmptyCommandHeader", "prependTo", "_hideBusy", "load", "postData", "completeCallback", "_reloadTable", "reload", "getRowByKey", "_getKeyValueOfRecord", "destroy", "empty", "Widget", "prototype", "call", "_setOption", "value", "completeReload", "result", "_removeAllRows", "_addRecordsToTable", "records", "_onRecordsLoaded", "_showError", "message", "_showBusy", "_onLoadingRecords", "isFunction", "listAction", "funcResult", "_createJtParamsForLoading", "_isDeferredObject", "done", "fail", "always", "loadUrl", "_createRecordLoadUrl", "_ajax", "url", "success", "error", "_createRowFromRecord", "record", "_addCellsToRowUsingRecord", "$row", "_createCellForRecordField", "_getDisplayTextForRecordField", "index", "_addRow", "_refreshRowStyles", "_addRowToTable", "$tableRow", "isNewRow", "_normalizeNumber", "extend", "_removeNoDataRow", "prepend", "unshift", "after", "splice", "_onRowInserted", "_showNewRowAnimation", "className", "removeClass", "_removeRowsFromTable", "$rows", "reason", "remove", "_findRowIndex", "_onRowsRemoved", "_findIndexInArray", "$row1", "$row2", "find", "totalColumnCount", "fieldValue", "display", "_getDisplayTextForDateRecordField", "displayFormat", "ew", "formatDate", "_addToolBarItem", "item", "text", "icon", "_logWarn", "$toolBarItem", "cssClass", "tooltip", "$icon", "hover", "alert", "_setBusyTimer", "delay", "height", "show", "makeVisible", "setTimeout", "clearTimeout", "hide", "_isBusy", "is", "$elm", "hoverClassName", "_performAjaxCall", "async", "_unAuthorizedRequestHandler", "location", "href", "opts", "statusCode", "401", "unAuthorizedRequest", "jqXHR", "textStatus", "errorThrown", "abort", "arguments", "complete", "ajax", "_set<PERSON><PERSON>ie", "expireDate", "Date", "setDate", "getDate", "document", "cookie", "encodeURIComponent", "toUTCString", "_get<PERSON><PERSON>ie", "equalities", "splitted", "decodeURIComponent", "strToHash", "hash", "charCodeAt", "simpleHash", "join", "_trigger", "serverResponse", "row", "rows", "j<PERSON><PERSON><PERSON>", "ewjtable", "_getPropertyOfObject", "obj", "propName", "indexOf", "preDot", "substring", "postDot", "_setPropertyOfObject", "_insertToArrayIfDoesNotExists", "array", "inArray", "compareFunc", "a", "b", "number", "min", "max", "defaultValue", "isNaN", "_formatString", "str", "placeHolder", "replace", "then", "_logDebug", "console", "log", "_logInfo", "_logError", "Array", "elt", "len", "from", "Number", "Math", "ceil", "floor", "base", "selecting", "multiselect", "selectingCheckboxes", "selectOnRowClick", "selectionChanged", "_selectedRecordIdsBeforeLoad", "_$selectAllCheckbox", "_shiftKeyDown", "_bindKeyboardEvents", "apply", "keydown", "which", "keyup", "selectedRows", "_getSelectedRows", "selectRows", "_selectRows", "_onSelectionChanged", "Event", "_createSelectAllHeader", "_makeRowSelectable", "_storeSelectionList", "_restoreSelectionList", "filter", "$columnHeader", "$headerContainer", "prop", "allRows", "_deselectRows", "selectedRowCount", "recordId", "_refreshSelectAllCheckboxState", "target", "_invertRowSelection", "$cell", "id", "random", "$selectCheckbox", "hasClass", "rowIndex", "beforeIndex", "_findFirstSelectedRowIndexBeforeIndex", "slice", "afterIndex", "_findFirstSelectedRowIndexAfterIndex", "totalRowCount", "paging", "pageList", "pageSize", "pageSizes", "pageSizeChangeArea", "gotoPageArea", "pagingInfo", "pageSizeChangeLabel", "gotoPageLabel", "_$bottomPanel", "_$pagingListArea", "_$pageSizeChangeArea", "_$pageInfoSpan", "_$gotoPageArea", "_$gotoPageInput", "_totalRecordCount", "_currentPageNo", "_loadPagingSettings", "_createBottomPanel", "_createPageListArea", "_createGotoPageInput", "_createPageSizeSelection", "insertAfter", "parseInt", "sort", "$pageSizeChangeCombobox", "val", "change", "_changePageSize", "_changePage", "keypress", "keyCode", "shift<PERSON>ey", "altKey", "_refreshGotoPageInput", "oldPageCount", "currentPageCount", "_calculatePageCount", "pageStep", "pageCount", "_savePagingSettings", "_addPagingInfoToUrl", "jtParams", "start", "recperpage", "totalRecordCount", "_createPagingList", "_createPagingInfo", "pageNumber", "startIndex", "_createFirstAndPreviousPageButtons", "_createPageNumberButtons", "_calculatePageNumbers", "_createLastAndNextPageButtons", "_bindClickEventsToPageNumberButtons", "wrapInner", "$first", "$previous", "$next", "$last", "pageNumbers", "previousNumber", "_createPageNumberButton", "$pageNumber", "shownPageNumbers", "previousPageNo", "nextPageNo", "startNo", "endNo", "pagingInfoMessage", "not", "pageNo", "sorting", "multiSorting", "defaultSorting", "_lastSorting", "_buildDefaultSortingArray", "$headerCell", "_makeColumnSortable", "_addSortingInfoToUrl", "orderIndex", "orderValue", "fieldProps", "colOffset", "toUpperCase", "sortOrder", "ctrl<PERSON>ey", "_sortTableByColumn", "sortIndex", "sortField", "siblings", "idx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,SAAUA,GAEV,IAAIC,EAEJD,EAAEE,QAAQC,GAAG,gBAAgB,WAC5BF,GAAgB,KAEjBD,EAAEE,QAAQC,GAAG,UAAU,WACtBF,GAAgB,KAGjBD,EAAEI,OAAO,cAAe,CAKvBC,QAAS,CAGRC,QAAS,GACTC,OAAQ,GACRC,mBAAmB,EACnBC,kBAAmB,KACnBC,iBAAkB,OAClBC,iBAAkB,OAClBC,iBAAiB,EACjBC,sBAAuB,IACvBC,qBAAqB,EACrBC,eAAe,EACfC,+BAAgC,KAEhCC,aAAc,CACbC,KAAM,OACNC,SAAU,QAGXC,QAAS,CACRC,gBAAgB,EAChBC,uBAAwB,GACxBC,0BAAsBC,EACtBC,MAAO,IAIRC,eAAgB,SAAUC,EAAOC,KACjCC,YAAa,SAAUF,EAAOC,KAC9BE,eAAgB,SAAUH,EAAOC,KACjCG,WAAY,SAAUJ,EAAOC,KAC7BI,eAAgB,SAAUL,EAAOC,KACjCK,cAAe,SAAUN,EAAOC,KAChCM,YAAa,SAAUP,EAAOC,KAC9BO,YAAa,SAAUR,EAAOC,KAG9BQ,SAAU,CACTC,yBAA0B,sDAC1BC,eAAgB,qBAChBC,gBAAiB,qBACjBC,MAAO,UASTC,gBAAiB,KAEjBC,WAAY,KACZC,aAAc,KAEdC,QAAS,KACTC,YAAa,KACbC,YAAa,KAEbC,UAAW,KACXC,iBAAkB,KAElBC,YAAa,KACbC,WAAY,KACZC,UAAW,KAEXC,uBAAwB,EACxBC,cAAe,KAEfC,OAAQ,KAQRC,QAAS,WAGRC,KAAKC,0BACLD,KAAKE,oBACLF,KAAKG,4BAGLH,KAAKI,uBACLJ,KAAKK,oBACLL,KAAKM,iBACLN,KAAKO,eACLP,KAAKQ,mBACLR,KAAKS,gBAELT,KAAKU,iBAAmBV,KAAKW,4BAK9BV,wBAAyB,WACxB,IAAIW,EAAOZ,KACXxD,EAAEqE,KAAKD,EAAK/D,QAAQE,QAAQ,SAAU+D,EAAWC,GAChDH,EAAKI,uBAAuBF,EAAWC,OAMzCC,uBAAwB,SAAUF,EAAWC,GAS5C,GARuB/C,MAAnB+C,EAAME,YACTF,EAAME,UAAY,IAEKjD,MAApB+C,EAAMG,aACTH,EAAMG,WAAa,IAIhBH,EAAMI,WAAyC,WAA5B3E,EAAEkB,KAAKqD,EAAMI,WAAyB,CAC5D,IAAIC,EAAiBL,EAAMI,UAAUE,MAAM,KAC3CN,EAAMI,UAAY,GAClB,IAAK,IAAIG,EAAI,EAAGA,EAAIF,EAAeG,OAAQD,IAC1CP,EAAMI,UAAUK,KAAKhF,EAAEiF,KAAKL,EAAeE,OAO9CpB,kBAAmB,WAClBF,KAAKH,cAAgB,GACrBG,KAAKV,YAAc,GACnBU,KAAKP,YAAc,GACnBO,KAAKN,WAAa,GAClBM,KAAKF,OAAS,IAKfK,0BAA2B,WAC1B,IAAIS,EAAOZ,KAEXxD,EAAEqE,KAAKD,EAAK/D,QAAQE,QAAQ,SAAU2E,EAAMX,GAG3CH,EAAKlB,WAAW8B,KAAKE,GAGJ,GAAbX,EAAMY,MACTf,EAAKjB,UAAY+B,GAIA,GAAdX,EAAMa,MAA+B,UAAdb,EAAMrD,MAChCkD,EAAKnB,YAAY+B,KAAKE,OAOzBtB,qBAAsB,WACrBJ,KAAKf,gBAAkBzC,EAAE,WACvBqF,SAAS,2BACTC,SAAS9B,KAAK+B,SAEhB/B,KAAKgC,uBAAuBhC,KAAKf,gBAAiB,cAKnDoB,kBAAmB,WAClB,IAAIO,EAAOZ,KAEX,GAAKY,EAAK/D,QAAQoF,MAAlB,CAIA,IAAIC,EAAY1F,EAAE,WAChBqF,SAAS,kBACTC,SAASlB,EAAK3B,iBAShB,GAPA2B,EAAKoB,uBAAuBE,EAAW,oBAEvC1F,EAAE,WACAqF,SAAS,uBACTC,SAASI,GACTC,OAAOvB,EAAK/D,QAAQoF,OAElBrB,EAAK/D,QAAQO,gBAAiB,CAEjC,IAAIgF,EAAY5F,EAAE,YAChB6F,KAAKzB,EAAK/D,QAAQ+B,SAASI,OAE7BxC,EAAE,qBACAqF,SAAS,iDACTS,KAAK,QAAS1B,EAAK/D,QAAQ+B,SAASI,OACpCmD,OAAOC,GACPN,SAASI,GACTK,OAAM,SAAUC,GAChBA,EAAEC,iBACFD,EAAEE,kBACF9B,EAAK+B,uBAIR/B,EAAK1B,WAAagD,IAKnB3B,aAAc,WACbP,KAAKZ,QAAU5C,EAAE,mBACfqF,SAAS,2BACTC,SAAS9B,KAAKf,iBAEZe,KAAKnD,QAAQ+F,SAChB5C,KAAKZ,QAAQkD,KAAK,KAAMtC,KAAKnD,QAAQ+F,SAGtC5C,KAAKgC,uBAAuBhC,KAAKZ,QAAS,qBAE1CY,KAAK6C,mBACL7C,KAAK8C,oBAKND,iBAAkB,WACjB,IAAIE,EAASvG,EAAE,mBACbsF,SAAS9B,KAAKZ,SAEhBY,KAAKgD,mBAAmBD,IAKzBC,mBAAoB,SAAUD,GAC7B,IAAIE,EAAMzG,EAAE,aACVsF,SAASiB,GAEX/C,KAAKkD,uBAAuBD,IAK7BC,uBAAwB,SAAUD,GACjC,IAAK,IAAI3B,EAAI,EAAGA,EAAItB,KAAKP,YAAY8B,OAAQD,IAAK,CACjD,IAAIR,EAAYd,KAAKP,YAAY6B,GACftB,KAAKmD,0BAA0BrC,EAAWd,KAAKnD,QAAQE,OAAO+D,IACpEgB,SAASmB,KAOvBE,0BAA2B,SAAUrC,EAAWsC,GAG/C,IAAIC,EAAkB7G,EAAE,YACtBqF,SAAS,+BACTQ,KAAKe,EAAMnB,OAETqB,EAAsB9G,EAAE,WAC1BqF,SAAS,oCACTM,OAAOkB,GAELE,EAAM/G,EAAE,aACVqF,SAAS,0BACTA,SAASuB,EAAMnC,WACfuC,IAAI,QAASJ,EAAMK,OACnBrF,KAAK,YAAa0C,GAClBqB,OAAOmB,GAIT,OAFAtD,KAAKgC,uBAAuBuB,EAAK,oBAE1BA,GAKRG,0BAA2B,WAC1B,IAAIH,EAAM/G,EAAE,aACVqF,SAAS,kCACT2B,IAAI,QAAS,MAIf,OAFAxD,KAAKgC,uBAAuBuB,EAAK,oBAE1BA,GAKRT,iBAAkB,WACjB9C,KAAKX,YAAc7C,EAAE,mBAAmBsF,SAAS9B,KAAKZ,UAKvDoB,iBAAkB,WACjBR,KAAKR,iBAAmBhD,EAAE,WAAWqF,SAAS,yBAAyB8B,UAAU3D,KAAKf,iBACtFe,KAAKT,UAAY/C,EAAE,WAAWqF,SAAS,kCAAkC8B,UAAU3D,KAAKf,iBACxFe,KAAKgC,uBAAuBhC,KAAKR,iBAAkB,oBACnDQ,KAAK4D,aASNC,KAAM,SAAUC,EAAUC,GACzB/D,KAAKH,cAAgBiE,EACrB9D,KAAKgE,aAAaD,IAKnBE,OAAQ,SAAUF,GACjB/D,KAAKgE,aAAaD,IAKnBG,YAAa,SAAUvC,GACtB,IAAK,IAAIL,EAAI,EAAGA,EAAItB,KAAKV,YAAYiC,OAAQD,IAC5C,GAAIK,GAAO3B,KAAKmE,qBAAqBnE,KAAKV,YAAYgC,GAAGlD,KAAK,WAC7D,OAAO4B,KAAKV,YAAYgC,GAI1B,OAAO,MAKR8C,QAAS,WACRpE,KAAK+B,QAAQsC,QACb7H,EAAE8H,OAAOC,UAAUH,QAAQI,KAAKxE,OASjCyE,WAAY,SAAU9C,EAAK+C,KAQ3BV,aAAc,SAAUD,GACvB,IAAInD,EAAOZ,KAEP2E,EAAiB,SAASvG,GAC7BwC,EAAKgD,YAGc,MAAfxF,EAAKwG,QAMThE,EAAKiE,eAAe,aACpBjE,EAAKkE,mBAAmB1G,EAAK2G,SAE7BnE,EAAKoE,iBAAiB5G,GAGlB2F,GACHA,EAAiB3F,IAZjBwC,EAAKqE,WAAW7G,EAAK8G,UAoBvB,GAJAtE,EAAKuE,UAAUvE,EAAK/D,QAAQ+B,SAASE,eAAgB8B,EAAK/D,QAAQQ,uBAClEuD,EAAKwE,oBAGD5I,EAAE6I,WAAWzE,EAAK/D,QAAQC,QAAQwI,YAAa,CAGlD,IAAIC,EAAa3E,EAAK/D,QAAQC,QAAQwI,WAAW1E,EAAKf,cAAee,EAAK4E,6BAGtE5E,EAAK6E,kBAAkBF,GAC1BA,EAAWG,MAAK,SAAStH,GACxBuG,EAAevG,MACbuH,MAAK,WACP/E,EAAKqE,WAAWrE,EAAK/D,QAAQ+B,SAASC,6BACpC+G,QAAO,WACThF,EAAKgD,eAGNe,EAAeY,OAGV,CAGN,IAAIM,EAAUjF,EAAKkF,uBAGnBlF,EAAKmF,MAAM,CACVC,IAAKH,EACLzH,KAAMwC,EAAKf,cACXoG,QAAS,SAAU7H,GAClBuG,EAAevG,IAEhB8H,MAAO,WACNtF,EAAKgD,YACLhD,EAAKqE,WAAWrE,EAAK/D,QAAQ+B,SAASC,+BAS1CiH,qBAAsB,WACrB,OAAO9F,KAAKnD,QAAQC,QAAQwI,YAG7BE,0BAA2B,WAC1B,MAAO,IASRW,qBAAsB,SAAUC,GAC/B,IAAInD,EAAMzG,EAAE,aACVqF,SAAS,qBACTS,KAAK,kBAAmBtC,KAAKmE,qBAAqBiC,IAClDhI,KAAK,SAAUgI,GAGjB,OADApG,KAAKqG,0BAA0BpD,GACxBA,GAKRoD,0BAA2B,SAAUC,GAEpC,IADA,IAAIF,EAASE,EAAKlI,KAAK,UACdkD,EAAI,EAAGA,EAAItB,KAAKP,YAAY8B,OAAQD,IAC5CtB,KAAKuG,0BAA0BH,EAAQpG,KAAKP,YAAY6B,IACtDQ,SAASwE,IAMbC,0BAA2B,SAAUH,EAAQtF,GAC5C,OAAOtE,EAAE,aACPqF,SAAS7B,KAAKnD,QAAQE,OAAO+D,GAAWG,WACxCkB,OAAQnC,KAAKwG,8BAA8BJ,EAAQtF,KAKtDgE,mBAAoB,SAAUC,GAC7B,IAAInE,EAAOZ,KAEXxD,EAAEqE,KAAKkE,GAAS,SAAU0B,EAAOL,GAChCxF,EAAK8F,QAAQ9F,EAAKuF,qBAAqBC,OAGxCxF,EAAK+F,qBAONC,eAAgB,SAAUC,EAAWJ,EAAOK,EAAU9J,GACrD,IAAIH,EAAU,CACb4J,MAAOzG,KAAK+G,iBAAiBN,EAAO,EAAGzG,KAAKV,YAAYiC,OAAQvB,KAAKV,YAAYiC,SAGlE,GAAZuF,IACHjK,EAAQiK,UAAW,GAGK,GAArB9J,IACHH,EAAQG,mBAAoB,GAG7BgD,KAAK0G,QAAQG,EAAWhK,IAKzB6J,QAAS,SAAUJ,EAAMzJ,GAExBA,EAAUL,EAAEwK,OAAO,CAClBP,MAAOzG,KAAKV,YAAYiC,OACxBuF,UAAU,EACV9J,mBAAmB,GACjBH,GAGCmD,KAAKV,YAAYiC,QAAU,GAC9BvB,KAAKiH,mBAINpK,EAAQ4J,MAAQzG,KAAK+G,iBAAiBlK,EAAQ4J,MAAO,EAAGzG,KAAKV,YAAYiC,OAAQvB,KAAKV,YAAYiC,QAC9F1E,EAAQ4J,OAASzG,KAAKV,YAAYiC,QAErCvB,KAAKX,YAAY8C,OAAOmE,GACxBtG,KAAKV,YAAYkC,KAAK8E,IACK,GAAjBzJ,EAAQ4J,OAElBzG,KAAKX,YAAY6H,QAAQZ,GACzBtG,KAAKV,YAAY6H,QAAQb,KAGzBtG,KAAKV,YAAYzC,EAAQ4J,MAAQ,GAAGW,MAAMd,GAC1CtG,KAAKV,YAAY+H,OAAOxK,EAAQ4J,MAAO,EAAGH,IAG3CtG,KAAKsH,eAAehB,EAAMzJ,EAAQiK,UAG9BjK,EAAQiK,WACX9G,KAAK2G,oBACD3G,KAAKnD,QAAQG,mBAAqBH,EAAQG,mBAC7CgD,KAAKuH,qBAAqBjB,KAQ7BiB,qBAAsB,SAAUV,GAC/B,IAAIW,EAAY,uBACZxH,KAAKnD,QAAQU,gBAChBiK,GAAwB,uBAGzBX,EAAUhF,SAAS2F,EAAW,OAAQ,IAAI,WACzCX,EAAUY,YAAYD,EAAW,SAMnCE,qBAAsB,SAAUC,EAAOC,GACtC,IAAIhH,EAAOZ,KAGP2H,EAAMpG,QAAU,IAKpBoG,EAAM9F,SAAS,wBAAwBgG,SAGvCF,EAAM9G,MAAK,WACV,IAAI4F,EAAQ7F,EAAKkH,cAActL,EAAEwD,OAC7ByG,GAAS,GACZ7F,EAAKtB,YAAY+H,OAAOZ,EAAO,MAIjC7F,EAAKmH,eAAeJ,EAAOC,GAGI,GAA3BhH,EAAKtB,YAAYiC,QACpBX,EAAKH,gBAGNG,EAAK+F,sBAKNmB,cAAe,SAAUxB,GACxB,OAAOtG,KAAKgI,kBAAkB1B,EAAMtG,KAAKV,aAAa,SAAU2I,EAAOC,GACtE,OAAOD,EAAM7J,KAAK,WAAa8J,EAAM9J,KAAK,cAM5CyG,eAAgB,SAAU+C,GAEzB,KAAI5H,KAAKV,YAAYiC,QAAU,GAA/B,CAKA,IAAIoG,EAAQ3H,KAAKX,YAAY8I,KAAK,wBAGlCnI,KAAKX,YAAYgF,QACjBrE,KAAKV,YAAc,GAEnBU,KAAK+H,eAAeJ,EAAOC,GAG3B5H,KAAKS,kBAKNA,cAAe,WACd,KAAIT,KAAKX,YAAY8I,KAAK,4BAA4B5G,OAAS,GAA/D,CAIA,IAAI0B,EAAMzG,EAAE,aACVqF,SAAS,wBACTC,SAAS9B,KAAKX,aAEZ+I,EAAmBpI,KAAKZ,QAAQ+I,KAAK,YAAY5G,OACrD/E,EAAE,aACA8F,KAAK,UAAW8F,GAChB/F,KAAKrC,KAAKnD,QAAQ+B,SAASG,iBAC3B+C,SAASmB,KAKZgE,iBAAkB,WACjBjH,KAAKX,YAAY8I,KAAK,yBAAyBN,UAKhDlB,kBAAmB,WAClB,IAAK,IAAIrF,EAAI,EAAGA,EAAItB,KAAKV,YAAYiC,OAAQD,IACxCA,EAAI,GAAK,EACZtB,KAAKV,YAAYgC,GAAGO,SAAS,qBAE7B7B,KAAKV,YAAYgC,GAAGmG,YAAY,sBASnCjB,8BAA+B,SAAUJ,EAAQtF,GAChD,IAAIsC,EAAQpD,KAAKnD,QAAQE,OAAO+D,GAC5BuH,EAAajC,EAAOtF,GAGxB,OAAIsC,EAAMkF,QACFlF,EAAMkF,QAAQ,CAAElC,OAAQA,IAGd,QAAdhD,EAAM1F,KACFsC,KAAKuI,kCAAkCnF,EAAOiF,GAE9CA,GAMTE,kCAAmC,SAAUnF,EAAOiF,GACnD,IAAKA,EACJ,MAAO,GAGR,IAAIG,EAAgBpF,EAAMoF,eAAiBxI,KAAKnD,QAAQI,kBACxD,OAAOwL,GAAGC,WAAWL,EAAYG,IAOlClI,eAAgB,WACfN,KAAKb,aAAe3C,EAAE,WACrBqF,SAAS,oBACTC,SAAS9B,KAAKd,YAEf,IAAK,IAAIoC,EAAI,EAAGA,EAAItB,KAAKnD,QAAQe,QAAQK,MAAMsD,OAAQD,IACtDtB,KAAK2I,gBAAgB3I,KAAKnD,QAAQe,QAAQK,MAAMqD,KAMlDqH,gBAAiB,SAAUC,GAG1B,GAAa5K,MAAR4K,GAAoC5K,MAAb4K,EAAKC,MAAkC7K,MAAb4K,EAAKE,KAG1D,OAFA9I,KAAK+I,SAAS,oDACd/I,KAAK+I,SAASH,GACP,KAGR,IAAII,EAAexM,EAAE,iBACnBqF,SAAS,yBACTC,SAAS9B,KAAKb,cAiBhB,GAfAa,KAAKgC,uBAAuBgH,EAAc,2CAA4C,kBAGlFJ,EAAKK,UACRD,EACEnH,SAAS+G,EAAKK,UAIbL,EAAKM,SACRF,EACE1G,KAAK,QAASsG,EAAKM,SAIlBN,EAAKE,KAAM,CACd,IAAIK,EAAQ3M,EAAE,oDAAoDsF,SAASkH,IACzD,IAAdJ,EAAKE,MAEEtM,EAAEkB,KAAmB,WAAdkL,EAAKE,OACtBK,EAAM3F,IAAI,aAAc,QAAUoF,EAAKE,KAAO,MAK5CF,EAAKC,MACRrM,EAAE,0BACA6F,KAAKuG,EAAKC,MACVhH,SAAS,8BAA8BC,SAASkH,GAI/CJ,EAAKrG,OACRyG,EAAazG,OAAM,WAClBqG,EAAKrG,WAKP,IAAIzE,OAAyBE,EACzBD,OAAuBC,EAa3B,OAZIgC,KAAKnD,QAAQe,QAAQC,iBACxBC,EAAyBkC,KAAKnD,QAAQe,QAAQE,uBAC9CC,EAAuBiC,KAAKnD,QAAQe,QAAQG,sBAI7CiL,EAAaI,OAAM,WAClBJ,EAAanH,SAAS,8BAA+B/D,EAAwBC,MAC3E,WACFiL,EAAavB,YAAY,8BAA+B3J,EAAwBC,MAG1EiL,GAOR/D,WAAY,SAAUC,GACrBuD,GAAGY,MAAMnE,IAQVoE,cAAe,KACfnE,UAAW,SAAUD,EAASqE,GAC7B,IAAI3I,EAAOZ,KAGXY,EAAKrB,UACHkE,MAAM7C,EAAK3B,gBAAgBwE,SAC3B+F,OAAO5I,EAAK3B,gBAAgBuK,UAC5B3H,SAAS,4CACT4H,OAEF,IAAIC,EAAc,WACjB9I,EAAKrB,UAAUkI,YAAY,4CAC3B7G,EAAKpB,iBAAiB6C,KAAK6C,GAASuE,QAGrC,GAAIF,EAAO,CACV,GAAI3I,EAAK0I,cACR,OAGD1I,EAAK0I,cAAgBK,WAAWD,EAAaH,QAE7CG,KAMF9F,UAAW,WACVgG,aAAa5J,KAAKsJ,eAClBtJ,KAAKsJ,cAAgB,KACrBtJ,KAAKT,UAAUsK,OACf7J,KAAKR,iBAAiB6C,KAAK,IAAIwH,QAKhCC,QAAS,WACR,OAAO9J,KAAKR,iBAAiBuK,GAAG,aAKjC/H,uBAAwB,SAAUgI,EAAMxC,EAAWyC,GAC7CjK,KAAKnD,QAAQU,gBAIlByM,EAAKnI,SAAS2F,GAEVyC,GACHD,EAAKZ,OAAM,WACVY,EAAKnI,SAASoI,MACZ,WACFD,EAAKvC,YAAYwC,QAWpBC,iBAAkB,SAAUlE,EAAKlC,EAAUqG,EAAOlE,EAASC,GAC1DlG,KAAK+F,MAAM,CACVC,IAAKA,EACL5H,KAAM0F,EACNqG,MAAOA,EACPlE,QAASA,EACTC,MAAOA,KAITkE,4BAA6B,WACxBpK,KAAKnD,QAAQW,+BAChB6M,SAASC,KAAOtK,KAAKnD,QAAQW,+BAE7B6M,SAASpG,QAAO,IAOlB8B,MAAO,SAAUlJ,GAChB,IAAI+D,EAAOZ,KAGPuK,EAAO,CACVC,WAAY,CACXC,IAAK,WACJ7J,EAAKwJ,kCAKRG,EAAO/N,EAAEwK,OAAOuD,EAAMvK,KAAKnD,QAAQY,aAAcZ,IAG5CoJ,QAAU,SAAU7H,GAEpBA,GAAoC,GAA5BA,EAAKsM,qBAChB9J,EAAKwJ,8BAGFvN,EAAQoJ,SACXpJ,EAAQoJ,QAAQ7H,IAKlBmM,EAAKrE,MAAQ,SAAUyE,EAAOC,EAAYC,GACrCpO,EACHkO,EAAMG,QAIHjO,EAAQqJ,OACXrJ,EAAQqJ,MAAM6E,YAKhBR,EAAKS,SAAW,WACXnO,EAAQmO,UACXnO,EAAQmO,YAIVxO,EAAEyO,KAAKV,IAKRpG,qBAAsB,SAAUiC,GAC/B,OAAOA,EAAOpG,KAAKL,YASpBuL,WAAY,SAAUvJ,EAAK+C,GAC1B/C,EAAM3B,KAAKU,iBAAmBiB,EAE9B,IAAIwJ,EAAa,IAAIC,KACrBD,EAAWE,QAAQF,EAAWG,UAAY,IAC1CC,SAASC,OAASC,mBAAmB9J,GAAO,IAAM8J,mBAAmB/G,GAAS,aAAeyG,EAAWO,eAKzGC,WAAY,SAAUhK,GACrBA,EAAM3B,KAAKU,iBAAmBiB,EAG9B,IADA,IAAIiK,EAAaL,SAASC,OAAOnK,MAAM,MAC9BC,EAAI,EAAGA,EAAIsK,EAAWrK,OAAQD,IACtC,GAAKsK,EAAWtK,GAAhB,CAIA,IAAIuK,EAAWD,EAAWtK,GAAGD,MAAM,KACnC,GAAuB,GAAnBwK,EAAStK,QAITuK,mBAAmBD,EAAS,MAAQlK,EACvC,OAAOmK,mBAAmBD,EAAS,IAAM,IAI3C,OAAO,MAKRlL,yBAA0B,WAEzB,IAeIoL,EAAY,GAMhB,OALI/L,KAAKnD,QAAQ+F,UAChBmJ,EAAYA,EAAY/L,KAAKnD,QAAQ+F,QAAU,KAIzC,YArBU,SAAU8B,GAC1B,IAAIsH,EAAO,EACX,GAAoB,GAAhBtH,EAAMnD,OACT,OAAOyK,EAGR,IAAK,IAAI1K,EAAI,EAAGA,EAAIoD,EAAMnD,OAAQD,IAAK,CAEtC0K,GAASA,GAAQ,GAAKA,EADbtH,EAAMuH,WAAW3K,GAE1B0K,GAAcA,EAGf,OAAOA,EASaE,CADrBH,EAAYA,EAAY/L,KAAKP,YAAY0M,KAAK,KAAO,KAAOnM,KAAKZ,QAAQ+I,KAAK,YAAY5G,SAQ3F6D,kBAAmB,WAClBpF,KAAKoM,SAAS,iBAAkB,KAAM,KAGvCpH,iBAAkB,SAAU5G,GAC3B4B,KAAKoM,SAAS,gBAAiB,KAAM,CAAErH,QAAS3G,EAAK2G,QAASsH,eAAgBjO,KAG/EkJ,eAAgB,SAAUhB,EAAMQ,GAC/B9G,KAAKoM,SAAS,cAAe,KAAM,CAAEE,IAAKhG,EAAMF,OAAQE,EAAKlI,KAAK,UAAW0I,SAAUA,KAGxFiB,eAAgB,SAAUJ,EAAOC,GAChC5H,KAAKoM,SAAS,cAAe,KAAM,CAAEG,KAAM5E,EAAOC,OAAQA,KAG3DjF,kBAAmB,WAClB3C,KAAKoM,SAAS,iBAAkB,KAAM,OA//BzC,CAogCEI,QAMF,SAAWhQ,GAEVA,EAAEwK,QAAO,EAAMxK,EAAEiM,GAAGgE,SAASlI,UAAW,CAIvCmI,qBAAsB,SAAUC,EAAKC,GACpC,GAAIA,EAASC,QAAQ,KAAO,EAC3B,OAAOF,EAAIC,GAEX,IAAIE,EAASF,EAASG,UAAU,EAAGH,EAASC,QAAQ,MAChDG,EAAUJ,EAASG,UAAUH,EAASC,QAAQ,KAAO,GACzD,OAAO7M,KAAK0M,qBAAqBC,EAAIG,GAASE,IAMhDC,qBAAsB,SAAUN,EAAKC,EAAUlI,GAC9C,GAAIkI,EAASC,QAAQ,KAAO,EAC3BF,EAAIC,GAAYlI,MACV,CACN,IAAIoI,EAASF,EAASG,UAAU,EAAGH,EAASC,QAAQ,MAChDG,EAAUJ,EAASG,UAAUH,EAASC,QAAQ,KAAO,GACzD7M,KAAKiN,qBAAqBN,EAAIG,GAASE,EAAStI,KAMlDwI,8BAA+B,SAAUC,EAAOzI,GAC3ClI,EAAE4Q,QAAQ1I,EAAOyI,GAAS,GAC7BA,EAAM3L,KAAKkD,IAMbsD,kBAAmB,SAAUtD,EAAOyI,EAAOE,GAGrCA,IACJA,EAAc,SAAUC,EAAGC,GAC1B,OAAOD,GAAKC,IAId,IAAK,IAAIjM,EAAI,EAAGA,EAAI6L,EAAM5L,OAAQD,IACjC,GAAI+L,EAAY3I,EAAOyI,EAAM7L,IAC5B,OAAOA,EAIT,OAAQ,GAMTyF,iBAAkB,SAAUyG,EAAQC,EAAKC,EAAKC,GAC7C,OAAc3P,MAAVwP,GAAiC,MAAVA,GAAkBI,MAAMJ,GAC3CG,EAGJH,EAASC,EACLA,EAGJD,EAASE,EACLA,EAGDF,GAORK,cAAe,WACd,GAAwB,GAApB9C,UAAUxJ,OACb,OAAO,KAIR,IADA,IAAIuM,EAAM/C,UAAU,GACXzJ,EAAI,EAAGA,EAAIyJ,UAAUxJ,OAAQD,IAAK,CAC1C,IAAIyM,EAAc,KAAOzM,EAAI,GAAK,IAClCwM,EAAMA,EAAIE,QAAQD,EAAahD,UAAUzJ,IAG1C,OAAOwM,GAKRrI,kBAAmB,SAAUkH,GAC5B,OAAOA,EAAIsB,MAAQtB,EAAIjH,MAAQiH,EAAIhH,MAKpCuI,UAAW,SAAUrF,GACfnM,OAAOyR,SAIZA,QAAQC,IAAI,mBAAqBvF,IAGlCwF,SAAU,SAAUxF,GACdnM,OAAOyR,SAIZA,QAAQC,IAAI,kBAAoBvF,IAGjCE,SAAU,SAAUF,GACdnM,OAAOyR,SAIZA,QAAQC,IAAI,qBAAuBvF,IAGpCyF,UAAW,SAAUzF,GACfnM,OAAOyR,SAIZA,QAAQC,IAAI,mBAAqBvF,MAO9B0F,MAAMhK,UAAUsI,UACpB0B,MAAMhK,UAAUsI,QAAU,SAAU2B,GACnC,IAAIC,EAAMzO,KAAKuB,OACXmN,EAAOC,OAAO5D,UAAU,KAAO,EAMnC,KALA2D,EAAQA,EAAO,EACXE,KAAKC,KAAKH,GACVE,KAAKE,MAAMJ,IACJ,IACVA,GAAQD,GACFC,EAAOD,EAAKC,IAClB,GAAIA,KAAQ1O,MACXA,KAAK0O,KAAUF,EACf,OAAOE,EAET,OAAQ,IAvJX,CA2JGlC,QAKH,SAAWhQ,GAGV,IAAIuS,EAAO,CACVhP,QAASvD,EAAEiM,GAAGgE,SAASlI,UAAUxE,QACjCmD,uBAAwB1G,EAAEiM,GAAGgE,SAASlI,UAAUrB,uBAChDmD,0BAA2B7J,EAAEiM,GAAGgE,SAASlI,UAAU8B,0BACnDjB,kBAAmB5I,EAAEiM,GAAGgE,SAASlI,UAAUa,kBAC3CJ,iBAAkBxI,EAAEiM,GAAGgE,SAASlI,UAAUS,iBAC1C+C,eAAgBvL,EAAEiM,GAAGgE,SAASlI,UAAUwD,gBAIzCvL,EAAEwK,QAAO,EAAMxK,EAAEiM,GAAGgE,SAASlI,UAAW,CAKvC1H,QAAS,CAGRmS,WAAW,EACXC,aAAa,EACbC,qBAAqB,EACrBC,kBAAkB,EAGlBC,iBAAkB,SAAUjR,EAAOC,MAOpCiR,6BAA8B,KAC9BC,oBAAqB,KACrBC,eAAe,EAQfxP,QAAS,WACJC,KAAKnD,QAAQmS,WAAahP,KAAKnD,QAAQqS,wBACxClP,KAAKJ,uBACPI,KAAKwP,uBAINT,EAAKhP,QAAQ0P,MAAMzP,KAAM+K,YAK1ByE,oBAAqB,WACpB,IAAI5O,EAAOZ,KAEXxD,EAAE+O,UACAmE,SAAQ,SAAUvR,GAClB,OAAQA,EAAMwR,OACb,KAAK,GACJ/O,EAAK2O,eAAgB,MAIvBK,OAAM,SAAUzR,GAChB,OAAQA,EAAMwR,OACb,KAAK,GACJ/O,EAAK2O,eAAgB,OAY1BM,aAAc,WACb,OAAO7P,KAAK8P,oBAKbC,WAAY,SAAUpI,GACrB3H,KAAKgQ,YAAYrI,GAEjB3H,KAAKiQ,oBAAoBzT,EAAE0T,MAAM,gBASlChN,uBAAwB,SAAUD,GAC7BjD,KAAKnD,QAAQmS,WAAahP,KAAKnD,QAAQqS,sBACtClP,KAAKnD,QAAQoS,YAChBhM,EAAId,OAAOnC,KAAKmQ,0BAEhBlN,EAAId,OAAOnC,KAAK0D,8BAIlBqL,EAAK7L,uBAAuBuM,MAAMzP,KAAM+K,YAKzC1E,0BAA2B,SAAUC,GAChCtG,KAAKnD,QAAQmS,WAChBhP,KAAKoQ,mBAAmB9J,GAGzByI,EAAK1I,0BAA0BoJ,MAAMzP,KAAM+K,YAK5C3F,kBAAmB,WACdpF,KAAKnD,QAAQmS,WAChBhP,KAAKqQ,sBAGNtB,EAAK3J,kBAAkBqK,MAAMzP,KAAM+K,YAKpC/F,iBAAkB,WACbhF,KAAKnD,QAAQmS,WAChBhP,KAAKsQ,wBAGNvB,EAAK/J,iBAAiByK,MAAMzP,KAAM+K,YAKnChD,eAAgB,SAAUJ,EAAOC,GAC5B5H,KAAKnD,QAAQmS,WAAwB,aAAVpH,GAA2BD,EAAM4I,OAAO,0BAA0BhP,OAAS,GACzGvB,KAAKiQ,oBAAoBzT,EAAE0T,MAAM,gBAGlCnB,EAAKhH,eAAe0H,MAAMzP,KAAM+K,YASjCoF,uBAAwB,WACvB,IAAIvP,EAAOZ,KAEPwQ,EAAgBhU,EAAE,sBACpBqF,SAAS,mEACX7B,KAAKgC,uBAAuBwO,EAAe,oBAE3C,IAAIC,EAAmBjU,EAAE,WACvBqF,SAAS,oCACTC,SAAS0O,GAwBX,OAtBA5P,EAAK0O,oBAAsB9S,EAAE,8EAC3B+F,OAAM,WACN,GAAI3B,EAAKtB,YAAYiC,QAAU,EAC9BX,EAAK0O,oBAAoBoB,KAAK,WAAW,OAD1C,CAKA,IAAIC,EAAU/P,EAAKvB,YAAY8I,KAAK,yBAChCvH,EAAK0O,oBAAoBvF,GAAG,YAC/BnJ,EAAKoP,YAAYW,GAEjB/P,EAAKgQ,cAAcD,GAGpB/P,EAAKqP,oBAAoBzT,EAAE0T,MAAM,YAAa,CAAE3D,KAAQoE,SAG1DnU,EAAE,qEACA2F,OAAOvB,EAAK0O,qBACZnN,OAAO,uEACPL,SAAS2O,GAEJD,GAKRH,oBAAqB,WACpB,IAAIzP,EAAOZ,KAENY,EAAK/D,QAAQmS,YAIlBpO,EAAKyO,6BAA+B,GACpCzO,EAAKkP,mBAAmBjP,MAAK,WAC5BD,EAAKyO,6BAA6B7N,KAAKZ,EAAKuD,qBAAqB3H,EAAEwD,MAAM5B,KAAK,iBAMhFkS,sBAAuB,WAGtB,GAFWtQ,KAEDnD,QAAQmS,UAAlB,CAKA,IADA,IAAI6B,EAAmB,EACdvP,EAAI,EAAGA,EAPLtB,KAOcV,YAAYiC,SAAUD,EAAG,CACjD,IAAIwP,EARM9Q,KAQUmE,qBARVnE,KAQoCV,YAAYgC,GAAGlD,KAAK,WAC9D5B,EAAE4Q,QAAQ0D,EATJ9Q,KASmBqP,+BAAiC,IATpDrP,KAUJgQ,YAVIhQ,KAUaV,YAAYgC,MAChCuP,GAXO7Q,KAeFqP,6BAA6B9N,OAAS,GAfpCvB,KAe8CqP,6BAA6B9N,QAAUsP,GAfrF7Q,KAgBLiQ,oBAAoBzT,EAAE0T,MAAM,yBAhBvBlQ,KAmBNqP,6BAA+B,GAnBzBrP,KAoBN+Q,mCAKNjB,iBAAkB,WACjB,OAAO9P,KAAKX,YACV8I,KAAK,8BAKRiI,mBAAoB,SAAU9J,GAC7B,IAAI1F,EAAOZ,KAYX,GATIY,EAAK/D,QAAQsS,kBAChB7I,EAAK/D,OAAM,SAAUC,GAChBhG,EAAEgG,EAAEwO,QAAQjH,GAAG,0BAEnBnJ,EAAKqQ,oBAAoB3K,MAKvB1F,EAAK/D,QAAQqS,oBAAqB,CACrC,IAAIgC,EAAQ1U,EAAE,aAAaqF,SAAS,6BAChCnE,EAAQkD,EAAK/D,QAAmB,YAAI,WAAa,QACjDsU,EAAK,SAAWzT,EAAO,IAAM+K,GAAG2I,SAChCC,EAAkB7U,EAAE,qCAAuCkB,EAAO,iCAAmCA,EAAO,sCAAwCyT,EAAK,8CAAgDA,EAAK,oBAAoBrP,SAASoP,GAC1OtQ,EAAK/D,QAAQsS,kBACjBkC,EAAgBlJ,KAAK,SAAS5F,OAAM,SAAUC,GAC7C5B,EAAKqQ,oBAAoB3K,MAI3BA,EAAKnE,OAAO+O,KAMdD,oBAAqB,SAAU3K,GAC9B,GAAIA,EAAKgL,SAAS,yBACjBtR,KAAK4Q,cAActK,QAGnB,GAAItG,KAAKuP,cAAe,CACvB,IAAIgC,EAAWvR,KAAK8H,cAAcxB,GAE9BkL,EAAcxR,KAAKyR,sCAAsCF,GAAY,EACzE,GAAIC,EAAc,GAAKA,EAAcD,EACpCvR,KAAKgQ,YAAYhQ,KAAKX,YAAY8I,KAAK,MAAMuJ,MAAMF,EAAaD,EAAW,QACrE,CAEN,IAAII,EAAa3R,KAAK4R,qCAAqCL,GAAY,EACnEI,EAAaJ,EAChBvR,KAAKgQ,YAAYhQ,KAAKX,YAAY8I,KAAK,MAAMuJ,MAAMH,EAAUI,EAAa,IAG1E3R,KAAKgQ,YAAY1J,SAInBtG,KAAKgQ,YAAY1J,GAInBtG,KAAKiQ,oBAAoBzT,EAAE0T,MAAM,qBAAsB,CAAE3D,KAAQjG,MAKlEmL,sCAAuC,SAAUF,GAChD,IAAK,IAAIjQ,EAAIiQ,EAAW,EAAGjQ,GAAK,IAAKA,EACpC,GAAItB,KAAKV,YAAYgC,GAAGgQ,SAAS,yBAChC,OAAOhQ,EAIT,OAAQ,GAKTsQ,qCAAsC,SAAUL,GAC/C,IAAK,IAAIjQ,EAAIiQ,EAAW,EAAGjQ,EAAItB,KAAKV,YAAYiC,SAAUD,EACzD,GAAItB,KAAKV,YAAYgC,GAAGgQ,SAAS,yBAChC,OAAOhQ,EAIT,OAAQ,GAKT0O,YAAa,SAAUrI,GACjB3H,KAAKnD,QAAQoS,aACjBjP,KAAK4Q,cAAc5Q,KAAK8P,oBAGzBnI,EAAM9F,SAAS,yBACf7B,KAAKgC,uBAAuB2F,EAAO,sBAE/B3H,KAAKnD,QAAQqS,qBAChBvH,EAAMQ,KAAK,uCAAuCuI,KAAK,WAAW,GAGnE1Q,KAAK+Q,kCAKNH,cAAe,SAAUjJ,GACxBA,EAAMF,YAAY,4CACdzH,KAAKnD,QAAQqS,qBAChBvH,EAAMQ,KAAK,uCAAuCuI,KAAK,WAAW,GAGnE1Q,KAAK+Q,kCAKNA,+BAAgC,WAC/B,GAAK/Q,KAAKnD,QAAQqS,qBAAwBlP,KAAKnD,QAAQoS,YAAvD,CAIA,IAAI4C,EAAgB7R,KAAKV,YAAYiC,OACjCsP,EAAmB7Q,KAAK8P,mBAAmBvO,OAEvB,GAApBsP,GACH7Q,KAAKsP,oBAAoBoB,KAAK,iBAAiB,GAC/C1Q,KAAKsP,oBAAoBoB,KAAK,WAAW,IAC/BG,GAAoBgB,GAC9B7R,KAAKsP,oBAAoBoB,KAAK,iBAAiB,GAC/C1Q,KAAKsP,oBAAoBoB,KAAK,WAAW,KAEzC1Q,KAAKsP,oBAAoBoB,KAAK,WAAW,GACzC1Q,KAAKsP,oBAAoBoB,KAAK,iBAAiB,MAQjDT,oBAAqB,SAAUzN,GAC9BxC,KAAKoM,SAAS,mBAAoB,KAAM5J,GAAK,OA9XhD,CAmYGgK,QAMH,SAAWhQ,GAGV,IAAIuS,EAAO,CACVlL,KAAMrH,EAAEiM,GAAGgE,SAASlI,UAAUV,KAC9B9D,QAASvD,EAAEiM,GAAGgE,SAASlI,UAAUxE,QACjC0E,WAAYjI,EAAEiM,GAAGgE,SAASlI,UAAUE,WACpCqB,qBAAsBtJ,EAAEiM,GAAGgE,SAASlI,UAAUuB,qBAC9CN,0BAA2BhJ,EAAEiM,GAAGgE,SAASlI,UAAUiB,0BACnDoB,eAAgBpK,EAAEiM,GAAGgE,SAASlI,UAAUqC,eACxCF,QAASlK,EAAEiM,GAAGgE,SAASlI,UAAUmC,QACjCgB,qBAAsBlL,EAAEiM,GAAGgE,SAASlI,UAAUmD,qBAC9C1C,iBAAkBxI,EAAEiM,GAAGgE,SAASlI,UAAUS,kBAI3CxI,EAAEwK,QAAO,EAAMxK,EAAEiM,GAAGgE,SAASlI,UAAW,CAKvC1H,QAAS,CACRiV,QAAQ,EACRC,SAAU,UACVC,SAAU,GACVC,UAAW,CAAC,GAAI,GAAI,IACpBC,oBAAoB,EACpBC,aAAc,WAEdvT,SAAU,CACTwT,WAAY,yBACZC,oBAAqB,YACrBC,cAAe,eASjBC,cAAe,KACfC,iBAAkB,KAClBC,qBAAsB,KACtBC,eAAgB,KAChBC,eAAgB,KAChBC,gBAAiB,KACjBC,kBAAmB,EACnBC,eAAgB,EAQhB/S,QAAS,WACRgP,EAAKhP,QAAQ0P,MAAMzP,KAAM+K,WACrB/K,KAAKnD,QAAQiV,SAChB9R,KAAK+S,sBACL/S,KAAKgT,qBACLhT,KAAKiT,sBACLjT,KAAKkT,uBACLlT,KAAKmT,6BAMPJ,oBAAqB,WACpB,GAAK/S,KAAKnD,QAAQS,oBAAlB,CAIA,IAAI0U,EAAWhS,KAAK2L,WAAW,aAC3BqG,IACHhS,KAAKnD,QAAQmV,SAAWhS,KAAK+G,iBAAiBiL,EAAU,EAAG,IAAShS,KAAKnD,QAAQmV,aAMnFgB,mBAAoB,WACnBhT,KAAKuS,cAAgB/V,EAAE,WACrBqF,SAAS,kCACTuR,YAAYpT,KAAKZ,SAEnBY,KAAKgC,uBAAuBhC,KAAKuS,cAAe,oBAEhD/V,EAAE,WAAWqF,SAAS,iCAAiCC,SAAS9B,KAAKuS,eACrE/V,EAAE,WAAWqF,SAAS,mCAAmCC,SAAS9B,KAAKuS,gBAKxEU,oBAAqB,WACpBjT,KAAKwS,iBAAmBhW,EAAE,iBACxBqF,SAAS,sBACTC,SAAS9B,KAAKuS,cAAcpK,KAAK,wBAEnCnI,KAAK0S,eAAiBlW,EAAE,iBACtBqF,SAAS,sBACTC,SAAS9B,KAAKuS,cAAcpK,KAAK,0BAKpCgL,yBAA0B,WACzB,IAAIvS,EAAOZ,KAEX,GAAKY,EAAK/D,QAAQqV,mBAAlB,CAKItR,EAAKoH,kBAAkBpH,EAAK/D,QAAQmV,SAAUpR,EAAK/D,QAAQoV,WAAa,IAC3ErR,EAAK/D,QAAQoV,UAAUzQ,KAAK6R,SAASzS,EAAK/D,QAAQmV,WAClDpR,EAAK/D,QAAQoV,UAAUqB,MAAK,SAAShG,EAAGC,GAAK,OAAOD,EAAIC,MAIzD3M,EAAK6R,qBAAuBjW,EAAE,iBAC5BqF,SAAS,6BACTC,SAASlB,EAAK2R,cAAcpK,KAAK,wBAGnCvH,EAAK6R,qBAAqBtQ,OAAO,SAAWvB,EAAK/D,QAAQ+B,SAASyT,oBAAsB,aAMxF,IAHA,IAAIkB,EAA0B/W,EAAE,qBAAqBsF,SAASlB,EAAK6R,sBAG1DnR,EAAI,EAAGA,EAAIV,EAAK/D,QAAQoV,UAAU1Q,OAAQD,IAClDiS,EAAwBpR,OAAO,kBAAoBvB,EAAK/D,QAAQoV,UAAU3Q,GAAK,KAAOV,EAAK/D,QAAQoV,UAAU3Q,GAAK,aAInHiS,EAAwBC,IAAI5S,EAAK/D,QAAQmV,UAGzCuB,EAAwBE,QAAO,WAC9B7S,EAAK8S,gBAAgBL,SAAS7W,EAAEwD,MAAMwT,aAMxCN,qBAAsB,WACrB,IAAItS,EAAOZ,KAENY,EAAK/D,QAAQsV,cAA6C,QAA7BvR,EAAK/D,QAAQsV,eAK/CnS,KAAK2S,eAAiBnW,EAAE,iBACtBqF,SAAS,sBACTC,SAASlB,EAAK2R,cAAcpK,KAAK,wBAGnCnI,KAAK2S,eAAexQ,OAAO,SAAWvB,EAAK/D,QAAQ+B,SAAS0T,cAAgB,WAG3C,YAA7B1R,EAAK/D,QAAQsV,cAEhBvR,EAAKgS,gBAAkBpW,EAAE,qBACvBqF,SAAS,kCACTC,SAAS9B,KAAK2S,gBACdvU,KAAK,YAAa,GAClBqV,QAAO,WACP7S,EAAK+S,YAAYN,SAAS7W,EAAEwD,MAAMwT,WAEpC5S,EAAKgS,gBAAgBzQ,OAAO,iCAI5BvB,EAAKgS,gBAAkBpW,EAAE,4CAA8CoE,EAAKkS,eAAiB,QAC3FjR,SAAS,gCACTC,SAAS9B,KAAK2S,gBACdiB,UAAS,SAASzV,GAClB,GAAmB,IAAfA,EAAMwR,MACTxR,EAAMsE,iBACN7B,EAAK+S,YAAYN,SAASzS,EAAKgS,gBAAgBY,aACzC,GAAmB,IAAfrV,EAAMwR,MAChBxR,EAAMsE,iBACN7B,EAAK+S,YAAYN,SAASzS,EAAKgS,gBAAgBY,OAAS,QAClD,GAAmB,IAAfrV,EAAMwR,MAChBxR,EAAMsE,iBACN7B,EAAK+S,YAAYN,SAASzS,EAAKgS,gBAAgBY,OAAS,OAClD,CAGJ,GAAKrV,EAAM0V,SAAW1V,EAAM0V,QAAU,IAAwB,GAAlB1V,EAAM2V,UAAqC,GAAhB3V,EAAM4V,QACxD,GAAjB5V,EAAM0V,SACW,GAAjB1V,EAAM0V,SAIX1V,EAAMsE,uBAUZuR,sBAAuB,WACtB,GAAKhU,KAAKnD,QAAQsV,cAA6C,QAA7BnS,KAAKnD,QAAQsV,aAA/C,CAUA,GANInS,KAAK6S,mBAAqB,GAAK7S,KAAKnD,QAAQmV,SAAWhS,KAAK6S,kBAC/D7S,KAAK2S,eAAe9I,OAEpB7J,KAAK2S,eAAelJ,OAGY,YAA7BzJ,KAAKnD,QAAQsV,aAA4B,CAC5C,IAAI8B,EAAejU,KAAK4S,gBAAgBxU,KAAK,aACzC8V,EAAmBlU,KAAKmU,sBAC5B,GAAIF,GAAgBC,EAAkB,CACrClU,KAAK4S,gBAAgBvO,QAGrB,IAAI+P,EAAW,EACXF,EAAmB,IACtBE,EAAW,IACDF,EAAmB,IAC7BE,EAAW,GACDF,EAAmB,IAC7BE,EAAW,EACDF,EAAmB,MAC7BE,EAAW,GAGZ,IAAK,IAAI9S,EAAI8S,EAAU9S,GAAK4S,EAAkB5S,GAAK8S,EAClDpU,KAAK4S,gBAAgBzQ,OAAO,kBAAoBb,EAAI,KAAOA,EAAI,aAGhEtB,KAAK4S,gBAAgBxU,KAAK,YAAa8V,IAKzClU,KAAK4S,gBAAgBY,IAAIxT,KAAK8S,kBAS/BjP,KAAM,WACL7D,KAAK8S,eAAiB,EAEtB/D,EAAKlL,KAAK4L,MAAMzP,KAAM+K,YAKvBtG,WAAY,SAAS9C,EAAK+C,GACzBqK,EAAKtK,WAAWgL,MAAMzP,KAAM+K,WAEjB,YAAPpJ,GACH3B,KAAK0T,gBAAgBL,SAAS3O,KAMhCgP,gBAAiB,SAAS1B,GACzB,GAAIA,GAAYhS,KAAKnD,QAAQmV,SAA7B,CAIAhS,KAAKnD,QAAQmV,SAAWA,EAGxB,IAAIqC,EAAYrU,KAAKmU,sBACjBnU,KAAK8S,eAAiBuB,IACzBrU,KAAK8S,eAAiBuB,GAEnBrU,KAAK8S,gBAAkB,IAC1B9S,KAAK8S,eAAiB,GAIvB,IAAIS,EAA0BvT,KAAKuS,cAAcpK,KAAK,qCACtD,GAAIoL,EAAwBhS,OAAS,EACpC,GAAI8R,SAASE,EAAwBC,QAAUxB,EACzBuB,EAAwBpL,KAAK,gBAAkB6J,EAAW,KAC5DzQ,OAAS,GAC3BgS,EAAwBC,IAAIxB,GAK/BhS,KAAKsU,sBACLtU,KAAKgE,iBAKNsQ,oBAAqB,WACftU,KAAKnD,QAAQS,qBAIlB0C,KAAKkL,WAAW,YAAalL,KAAKnD,QAAQmV,WAK3ClM,qBAAsB,WACrB,IAAID,EAAUkJ,EAAKjJ,qBAAqB2J,MAAMzP,KAAM+K,WAEpD,OADAlF,EAAU7F,KAAKuU,oBAAoB1O,EAAS7F,KAAK8S,iBAMlDtN,0BAA2B,WAC1B,IAAIgP,EAAWzF,EAAKvJ,0BAA0BiK,MAAMzP,KAAM+K,WAO1D,OALI/K,KAAKnD,QAAQiV,SAChB0C,EAASC,OAASzU,KAAK8S,eAAiB,GAAK9S,KAAKnD,QAAQmV,SAC1DwC,EAASE,WAAa1U,KAAKnD,QAAQmV,UAG7BwC,GAOR5N,eAAgB,SAAUC,EAAWJ,EAAOK,GACvCA,GAAY9G,KAAKnD,QAAQiV,OAC5B9R,KAAKgE,eAIN+K,EAAKnI,eAAe6I,MAAMzP,KAAM+K,YAKjCrE,QAAS,SAAUJ,EAAMzJ,GACpBA,GAAWA,EAAQiK,UAAY9G,KAAKnD,QAAQiV,OAC/C9R,KAAKgE,eAIN+K,EAAKrI,QAAQ+I,MAAMzP,KAAM+K,YAK1BrD,qBAAsB,SAAUC,EAAOC,GACtCmH,EAAKrH,qBAAqB+H,MAAMzP,KAAM+K,WAElC/K,KAAKnD,QAAQiV,SACZ9R,KAAKV,YAAYiC,QAAU,GAAKvB,KAAK8S,eAAiB,KACvD9S,KAAK8S,eAGR9S,KAAKgE,iBAMPgB,iBAAkB,SAAU5G,GACvB4B,KAAKnD,QAAQiV,SAChB9R,KAAK6S,kBAAoBzU,EAAKuW,iBAC9B3U,KAAK4U,oBACL5U,KAAK6U,oBACL7U,KAAKgU,yBAGNjF,EAAK/J,iBAAiByK,MAAMzP,KAAM+K,YASnCwJ,oBAAqB,SAAUvO,EAAK8O,GACnC,IAAK9U,KAAKnD,QAAQiV,OACjB,OAAO9L,EAGR,IAAI+O,GAAcD,EAAa,GAAK9U,KAAKnD,QAAQmV,SAC7CA,EAAWhS,KAAKnD,QAAQmV,SAE5B,OAAQhM,GAAOA,EAAI6G,QAAQ,KAAO,EAAI,IAAM,KAAO,SAAWkI,EAAa,eAAiB/C,GAK7F4C,kBAAmB,WAClB,KAAI5U,KAAKnD,QAAQmV,UAAY,IAI7BhS,KAAKwS,iBAAiBnO,QAClBrE,KAAK6S,mBAAqB,GAAK7S,KAAKnD,QAAQmV,SAAWhS,KAAK6S,oBAAhE,CAIA,IAAIwB,EAAYrU,KAAKmU,sBAErBnU,KAAKgV,qCACwB,UAAzBhV,KAAKnD,QAAQkV,UAChB/R,KAAKiV,yBAAyBjV,KAAKkV,sBAAsBb,IAI1DrU,KAAKmV,8BAA8Bd,GACnCrU,KAAKoV,sCAELpV,KAAKwS,iBAAiB6C,UAAU,6DAKjCL,mCAAoC,WACnC,IAAIM,EAAS9Y,EAAE,qBACbqF,SAAS,8CACTQ,KAAK,sCACLjE,KAAK,aAAc,GACnB0D,SAAS9B,KAAKwS,kBAEZ+C,EAAY/Y,EAAE,qBAChBqF,SAAS,iDACTQ,KAAK,qCACLjE,KAAK,aAAc4B,KAAK8S,eAAiB,GACzChR,SAAS9B,KAAKwS,kBAEhBxS,KAAKgC,uBAAuBsT,EAAQ,6BAA8B,kBAClEtV,KAAKgC,uBAAuBuT,EAAW,6BAA8B,kBAEjEvV,KAAK8S,gBAAkB,IAC1BwC,EAAOzT,SAAS,0CAChB0T,EAAU1T,SAAS,0CACnB7B,KAAKgC,uBAAuBsT,EAAQ,qBACpCtV,KAAKgC,uBAAuBuT,EAAW,uBAMzCJ,8BAA+B,SAAUd,GACxC,IAAImB,EAAQhZ,EAAE,qBACZqF,SAAS,6CACTQ,KAAK,qCACLjE,KAAK,aAAc4B,KAAK8S,eAAiB,GACzChR,SAAS9B,KAAKwS,kBACZiD,EAAQjZ,EAAE,qBACZqF,SAAS,6CACTQ,KAAK,qCACLjE,KAAK,aAAciW,GACnBvS,SAAS9B,KAAKwS,kBAEhBxS,KAAKgC,uBAAuBwT,EAAO,6BAA8B,kBACjExV,KAAKgC,uBAAuByT,EAAO,6BAA8B,kBAE7DzV,KAAK8S,gBAAkBuB,IAC1BmB,EAAM3T,SAAS,0CACf4T,EAAM5T,SAAS,0CACf7B,KAAKgC,uBAAuBwT,EAAO,qBACnCxV,KAAKgC,uBAAuByT,EAAO,uBAMrCR,yBAA0B,SAAUS,GAEnC,IADA,IAAIC,EAAiB,EACZrU,EAAI,EAAGA,EAAIoU,EAAYnU,OAAQD,IAElCoU,EAAYpU,GAAKqU,EAAkB,GACvCnZ,EAAE,qBACAqF,SAAS,8CACTQ,KAAK,6CACLP,SAAS9B,KAAKwS,kBAGjBxS,KAAK4V,wBAAwBF,EAAYpU,IACzCqU,EAAiBD,EAAYpU,IAM/BsU,wBAAyB,SAAUd,GAClC,IAAIe,EAAcrZ,EAAE,qBAClBqF,SAAS,wCACTQ,KAAKyS,GACL1W,KAAK,aAAc0W,GACnBhT,SAAS9B,KAAKwS,kBAEhBxS,KAAKgC,uBAAuB6T,EAAa,6BAA8B,kBAEnE7V,KAAK8S,gBAAkBgC,IAC1Be,EAAYhU,SAAS,6DACrB7B,KAAKgC,uBAAuB6T,EAAa,qBAM3C1B,oBAAqB,WACpB,IAAIE,EAAYzF,KAAKE,MAAM9O,KAAK6S,kBAAoB7S,KAAKnD,QAAQmV,UAKjE,OAJIhS,KAAK6S,kBAAoB7S,KAAKnD,QAAQmV,UAAY,KACnDqC,EAGIA,GAKRa,sBAAuB,SAAUb,GAChC,GAAIA,GAAa,EAAG,CAGnB,IADA,IAAIqB,EAAc,GACTpU,EAAI,EAAGA,GAAK+S,IAAa/S,EACjCoU,EAAYlU,KAAKF,GAGlB,OAAOoU,EAGP,IAAII,EAAmB,CAAC,EAAG,EAAGzB,EAAY,EAAGA,GACzC0B,EAAiB/V,KAAK+G,iBAAiB/G,KAAK8S,eAAiB,EAAG,EAAGuB,EAAW,GAC9E2B,EAAahW,KAAK+G,iBAAiB/G,KAAK8S,eAAiB,EAAG,EAAGuB,EAAW,GAO9E,OALArU,KAAKkN,8BAA8B4I,EAAkBC,GACrD/V,KAAKkN,8BAA8B4I,EAAkB9V,KAAK8S,gBAC1D9S,KAAKkN,8BAA8B4I,EAAkBE,GAErDF,EAAiBxC,MAAK,SAAUhG,EAAGC,GAAK,OAAOD,EAAIC,KAC5CuI,GAMTjB,kBAAmB,WAClB,GAAI7U,KAAK6S,mBAAqB,GAAK7S,KAAKnD,QAAQmV,SAAWhS,KAAK6S,kBAC/D7S,KAAK0S,eAAerO,YADrB,CAKA,IAAI4R,GAAWjW,KAAK8S,eAAiB,GAAK9S,KAAKnD,QAAQmV,SAAW,EAC9DkE,EAAQlW,KAAK8S,eAAiB9S,KAAKnD,QAAQmV,SAG/C,IAFAkE,EAAQlW,KAAK+G,iBAAiBmP,EAAOD,EAASjW,KAAK6S,kBAAmB,KAEzDoD,EAAS,CACrB,IAAIE,EAAoBnW,KAAK6N,cAAc7N,KAAKnD,QAAQ+B,SAASwT,WAAY6D,EAASC,EAAOlW,KAAK6S,mBAClG7S,KAAK0S,eAAerQ,KAAK8T,MAM3Bf,oCAAqC,WACpC,IAAIxU,EAAOZ,KACXY,EAAK4R,iBACHrK,KAAK,0IACLiO,IAAI,kCACJ7T,OAAM,SAAUC,GAChBA,EAAEC,iBACF7B,EAAK+S,YAAYnX,EAAEwD,MAAM5B,KAAK,mBAMjCuV,YAAa,SAAU0C,IACtBA,EAASrW,KAAK+G,iBAAiBsP,EAAQ,EAAGrW,KAAKmU,sBAAuB,KACxDnU,KAAK8S,gBAKnB9S,KAAK8S,eAAiBuD,EACtBrW,KAAKgE,gBALJhE,KAAKgU,2BA5kBT,CAslBGxH,QAMH,SAAWhQ,GAGV,IAAIuS,EAAO,CACV7O,kBAAmB1D,EAAEiM,GAAGgE,SAASlI,UAAUrE,kBAC3Cc,uBAAwBxE,EAAEiM,GAAGgE,SAASlI,UAAUvD,uBAChDmC,0BAA2B3G,EAAEiM,GAAGgE,SAASlI,UAAUpB,0BACnD2C,qBAAsBtJ,EAAEiM,GAAGgE,SAASlI,UAAUuB,qBAC9CN,0BAA2BhJ,EAAEiM,GAAGgE,SAASlI,UAAUiB,2BAIpDhJ,EAAEwK,QAAO,EAAMxK,EAAEiM,GAAGgE,SAASlI,UAAW,CAKvC1H,QAAS,CACRyZ,SAAS,EACTC,cAAc,EACdC,eAAgB,IAOjBC,aAAc,KAQdvW,kBAAmB,WAClB6O,EAAK7O,kBAAkBuP,MAAMzP,KAAM+K,WAEnC/K,KAAKyW,aAAe,GAChBzW,KAAKnD,QAAQyZ,SAChBtW,KAAK0W,6BAMP1V,uBAAwB,SAAUF,EAAWC,GAC5CgO,EAAK/N,uBAAuByO,MAAMzP,KAAM+K,WACxChK,EAAMuV,QAA4B,GAAjBvV,EAAMuV,SAKxBnT,0BAA2B,SAAUrC,EAAWsC,GAC/C,IAAIuT,EAAc5H,EAAK5L,0BAA0BsM,MAAMzP,KAAM+K,WAK7D,OAJI/K,KAAKnD,QAAQyZ,SAAWlT,EAAMkT,SACjCtW,KAAK4W,oBAAoBD,EAAa7V,GAGhC6V,GAKR7Q,qBAAsB,WACrB,IAAID,EAAUkJ,EAAKjJ,qBAAqB2J,MAAMzP,KAAM+K,WAEpD,OADAlF,EAAU7F,KAAK6W,qBAAqBhR,IAUrC6Q,0BAA2B,WAC1B,IAAI9V,EAAOZ,KAEXxD,EAAEqE,KAAKD,EAAK/D,QAAQ2Z,eAAenV,MAAM,MAAM,SAAUyV,EAAYC,GACpEva,EAAEqE,KAAKD,EAAK/D,QAAQE,QAAQ,SAAU+D,EAAWkW,GAChD,GAAIA,EAAWV,QAAS,CACvB,IAAIW,EAAYF,EAAWlK,QAAQ/L,GAC/BmW,GAAa,IACZF,EAAWG,cAAcrK,QAAQ,QAASoK,IAAc,EAC3DrW,EAAK6V,aAAajV,KAAK,CACtBV,UAAWA,EACXqW,UAAW,SAGZvW,EAAK6V,aAAajV,KAAK,CACtBV,UAAWA,EACXqW,UAAW,iBAWlBP,oBAAqB,SAAUpG,EAAe1P,GAC7C,IAAIF,EAAOZ,KAEXwQ,EACE3O,SAAS,mCACTU,OAAM,SAAUC,GAChBA,EAAEC,iBAEG7B,EAAK/D,QAAQ0Z,cAAiB/T,EAAE4U,UACpCxW,EAAK6V,aAAe,IAGrB7V,EAAKyW,mBAAmB7G,MAI1BhU,EAAEqE,KAAKb,KAAKyW,cAAc,SAAUa,EAAWC,GAC1CA,EAAUzW,WAAaA,IACC,QAAvByW,EAAUJ,UACb3G,EAAc3O,SAAS,sCAEvB2O,EAAc3O,SAAS,0CAQ3BwV,mBAAoB,SAAU7G,GAEG,GAA5BxQ,KAAKyW,aAAalV,QACrBiP,EAAcgH,WAAW/P,YAAY,sEAItC,IAAK,IAAInG,EAAI,EAAGA,EAAItB,KAAKyW,aAAalV,OAAQD,IACzCtB,KAAKyW,aAAanV,GAAGR,WAAa0P,EAAcpS,KAAK,cACxD4B,KAAKyW,aAAapP,OAAO/F,IAAK,GAK5BkP,EAAcc,SAAS,sCAC1Bd,EAAc/I,YAAY,qCAAqC5F,SAAS,sCACxE7B,KAAKyW,aAAajV,KAAK,CACtBV,UAAa0P,EAAcpS,KAAK,aAChC+Y,UAAW,WAGZ3G,EAAc/I,YAAY,sCAAsC5F,SAAS,qCACzE7B,KAAKyW,aAAajV,KAAK,CACtBV,UAAa0P,EAAcpS,KAAK,aAChC+Y,UAAW,SAKbnX,KAAKgE,gBAKN6S,qBAAsB,SAAU7Q,GAC/B,IAAKhG,KAAKnD,QAAQyZ,SAAuC,GAA5BtW,KAAKyW,aAAalV,OAC9C,OAAOyE,EAGR,IAAIsQ,EAAU,GAKd,OAJA9Z,EAAEqE,KAAKb,KAAKyW,cAAc,SAAUgB,EAAK/S,GACxC4R,EAAQ9U,KAAKkD,EAAM5D,UAAY,IAAM4D,EAAMyS,cAGpCnR,GAAOA,EAAI6G,QAAQ,KAAO,EAAI,IAAM,KAAO,WAAayJ,EAAQnK,KAAK,MAK9E3G,0BAA2B,WAC1B,IAAIgP,EAAWzF,EAAKvJ,0BAA0BiK,MAAMzP,KAAM+K,WAE1D,GAAI/K,KAAKnD,QAAQyZ,SAAWtW,KAAKyW,aAAalV,OAAQ,CACrD,IAAI+U,EAAU,GACd9Z,EAAEqE,KAAKb,KAAKyW,cAAc,SAAUgB,EAAK/S,GACxC4R,EAAQ9U,KAAKkD,EAAM5D,UAAY,IAAM4D,EAAMyS,cAG5C3C,EAAS8B,QAAUA,EAAQnK,KAAK,KAGjC,OAAOqI,KAjMV,CAsMGhI", "sourcesContent": ["﻿/*\r\n\r\newjTable (requires ew*.js)\r\n@license Copyright (C) 2017 by e.World Technology Limited (http://www.hkvstore.com)\r\n\r\nBased on:\r\n\r\njTable 2.4.0\r\nhttp://www.jtable.org\r\n\r\n---------------------------------------------------------------------------\r\n\r\nCopyright (C) 2011-2014 by <PERSON><PERSON> (http://www.halilibrahimkalkan.com)\r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy\r\nof this software and associated documentation files (the \"Software\"), to deal\r\nin the Software without restriction, including without limitation the rights\r\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\ncopies of the Software, and to permit persons to whom the Software is\r\nfurnished to do so, subject to the following conditions:\r\n\r\nThe above copyright notice and this permission notice shall be included in\r\nall copies or substantial portions of the Software.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\nTHE SOFTWARE.\r\n\r\n*/\r\n\r\n/************************************************************************\r\n* CORE ewjTable module                                                    *\r\n*************************************************************************/\r\n(function ($) {\r\n\r\n\tvar unloadingPage;\r\n\r\n\t$(window).on('beforeunload', function () {\r\n\t\tunloadingPage = true;\r\n\t});\r\n\t$(window).on('unload', function () {\r\n\t\tunloadingPage = false;\r\n\t});\r\n\r\n\t$.widget(\"ew.ewjtable\", {\r\n\r\n\t\t/************************************************************************\r\n\t\t* DEFAULT OPTIONS / EVENTS                                              *\r\n\t\t*************************************************************************/\r\n\t\toptions: {\r\n\r\n\t\t\t//Options\r\n\t\t\tactions: {},\r\n\t\t\tfields: {},\r\n\t\t\tanimationsEnabled: true,\r\n\t\t\tdefaultDateFormat: null,\r\n\t\t\tdialogShowEffect: 'fade',\r\n\t\t\tdialogHideEffect: 'fade',\r\n\t\t\tshowCloseButton: false,\r\n\t\t\tloadingAnimationDelay: 500,\r\n\t\t\tsaveUserPreferences: true,\r\n\t\t\tjqueryuiTheme: false,\r\n\t\t\tunAuthorizedRequestRedirectUrl: null,\r\n\r\n\t\t\tajaxSettings: {\r\n\t\t\t\ttype: 'POST',\r\n\t\t\t\tdataType: 'json'\r\n\t\t\t},\r\n\r\n\t\t\ttoolbar: {\r\n\t\t\t\thoverAnimation: true,\r\n\t\t\t\thoverAnimationDuration: 60,\r\n\t\t\t\thoverAnimationEasing: undefined,\r\n\t\t\t\titems: []\r\n\t\t\t},\r\n\r\n\t\t\t//Events\r\n\t\t\tcloseRequested: function (event, data) { },\r\n\t\t\tformCreated: function (event, data) { },\r\n\t\t\tformSubmitting: function (event, data) { },\r\n\t\t\tformClosed: function (event, data) { },\r\n\t\t\tloadingRecords: function (event, data) { },\r\n\t\t\trecordsLoaded: function (event, data) { },\r\n\t\t\trowInserted: function (event, data) { },\r\n\t\t\trowsRemoved: function (event, data) { },\r\n\r\n\t\t\t//Localization\r\n\t\t\tmessages: {\r\n\t\t\t\tserverCommunicationError: 'An error occured while communicating to the server.',\r\n\t\t\t\tloadingMessage: 'Loading records...',\r\n\t\t\t\tnoDataAvailable: 'No data available!',\r\n\t\t\t\tclose: 'Close'\r\n\t\t\t}\r\n\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PRIVATE FIELDS                                                        *\r\n\t\t*************************************************************************/\r\n\r\n\t\t_$mainContainer: null, //Reference to the main container of all elements that are created by this plug-in (jQuery object)\r\n\r\n\t\t_$titleDiv: null, //Reference to the title div (jQuery object)\r\n\t\t_$toolbarDiv: null, //Reference to the toolbar div (jQuery object)\r\n\r\n\t\t_$table: null, //Reference to the main <table> (jQuery object)\r\n\t\t_$tableBody: null, //Reference to <body> in the table (jQuery object)\r\n\t\t_$tableRows: null, //Array of all <tr> in the table (except \"no data\" row) (jQuery object array)\r\n\r\n\t\t_$busyDiv: null, //Reference to the div that is used to block UI while busy (jQuery object)\r\n\t\t_$busyMessageDiv: null, //Reference to the div that is used to show some message when UI is blocked (jQuery object)\r\n\r\n\t\t_columnList: null, //Name of all data columns in the table (select column and command columns are not included) (string array)\r\n\t\t_fieldList: null, //Name of all fields of a record (defined in fields option) (string array)\r\n\t\t_keyField: null, //Name of the key field of a record (that is defined as 'key: true' in the fields option) (string)\r\n\r\n\t\t_firstDataColumnOffset: 0, //Start index of first record field in table columns (some columns can be placed before first data column, such as select checkbox column) (integer)\r\n\t\t_lastPostData: null, //Last posted data on load method (object)\r\n\r\n\t\t_cache: null, //General purpose cache dictionary (object)\r\n\r\n\t\t/************************************************************************\r\n\t\t* CONSTRUCTOR AND INITIALIZATION METHODS                                *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Contructor.\r\n\t\t*************************************************************************/\r\n\t\t_create: function () {\r\n\r\n\t\t\t//Initialization\r\n\t\t\tthis._normalizeFieldsOptions();\r\n\t\t\tthis._initializeFields();\r\n\t\t\tthis._createFieldAndColumnList();\r\n\r\n\t\t\t//Creating DOM elements\r\n\t\t\tthis._createMainContainer();\r\n\t\t\tthis._createTableTitle();\r\n\t\t\tthis._createToolBar();\r\n\t\t\tthis._createTable();\r\n\t\t\tthis._createBusyPanel();\r\n\t\t\tthis._addNoDataRow();\r\n\r\n\t\t\tthis._cookieKeyPrefix = this._generateCookieKeyPrefix();\r\n\t\t},\r\n\r\n\t\t/* Normalizes some options for all fields (sets default values).\r\n\t\t*************************************************************************/\r\n\t\t_normalizeFieldsOptions: function () {\r\n\t\t\tvar self = this;\r\n\t\t\t$.each(self.options.fields, function (fieldName, props) {\r\n\t\t\t\tself._normalizeFieldOptions(fieldName, props);\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Normalizes some options for a field (sets default values).\r\n\t\t*************************************************************************/\r\n\t\t_normalizeFieldOptions: function (fieldName, props) {\r\n\t\t\tif (props.listClass == undefined) {\r\n\t\t\t\tprops.listClass = '';\r\n\t\t\t}\r\n\t\t\tif (props.inputClass == undefined) {\r\n\t\t\t\tprops.inputClass = '';\r\n\t\t\t}\r\n\r\n\t\t\t//Convert dependsOn to array if it's a comma seperated lists\r\n\t\t\tif (props.dependsOn && $.type(props.dependsOn) === 'string') {\r\n\t\t\t\tvar dependsOnArray = props.dependsOn.split(',');\r\n\t\t\t\tprops.dependsOn = [];\r\n\t\t\t\tfor (var i = 0; i < dependsOnArray.length; i++) {\r\n\t\t\t\t\tprops.dependsOn.push($.trim(dependsOnArray[i]));\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Intializes some private variables.\r\n\t\t*************************************************************************/\r\n\t\t_initializeFields: function () {\r\n\t\t\tthis._lastPostData = {};\r\n\t\t\tthis._$tableRows = [];\r\n\t\t\tthis._columnList = [];\r\n\t\t\tthis._fieldList = [];\r\n\t\t\tthis._cache = [];\r\n\t\t},\r\n\r\n\t\t/* Fills _fieldList, _columnList arrays and sets _keyField variable.\r\n\t\t*************************************************************************/\r\n\t\t_createFieldAndColumnList: function () {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\t$.each(self.options.fields, function (name, props) {\r\n\r\n\t\t\t\t//Add field to the field list\r\n\t\t\t\tself._fieldList.push(name);\r\n\r\n\t\t\t\t//Check if this field is the key field\r\n\t\t\t\tif (props.key == true) {\r\n\t\t\t\t\tself._keyField = name;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t//Add field to column list if it is shown in the table\r\n\t\t\t\tif (props.list != false && props.type != 'hidden') {\r\n\t\t\t\t\tself._columnList.push(name);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Creates the main container div.\r\n\t\t*************************************************************************/\r\n\t\t_createMainContainer: function () {\r\n\t\t\tthis._$mainContainer = $('<div />')\r\n\t\t\t\t.addClass('ewjtable-main-container')\r\n\t\t\t\t.appendTo(this.element);\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass(this._$mainContainer, 'ui-widget');\r\n\t\t},\r\n\r\n\t\t/* Creates title of the table if a title supplied in options.\r\n\t\t*************************************************************************/\r\n\t\t_createTableTitle: function () {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\tif (!self.options.title) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar $titleDiv = $('<div />')\r\n\t\t\t\t.addClass('ewjtable-title')\r\n\t\t\t\t.appendTo(self._$mainContainer);\r\n\r\n\t\t\tself._jqueryuiThemeAddClass($titleDiv, 'ui-widget-header');\r\n\r\n\t\t\t$('<div />')\r\n\t\t\t\t.addClass('ewjtable-title-text')\r\n\t\t\t\t.appendTo($titleDiv)\r\n\t\t\t\t.append(self.options.title);\r\n\r\n\t\t\tif (self.options.showCloseButton) {\r\n\r\n\t\t\t\tvar $textSpan = $('<span />')\r\n\t\t\t\t\t.html(self.options.messages.close);\r\n\r\n\t\t\t\t$('<button></button>')\r\n\t\t\t\t\t.addClass('ewjtable-command-button ewjtable-close-button')\r\n\t\t\t\t\t.attr('title', self.options.messages.close)\r\n\t\t\t\t\t.append($textSpan)\r\n\t\t\t\t\t.appendTo($titleDiv)\r\n\t\t\t\t\t.click(function (e) {\r\n\t\t\t\t\t\te.preventDefault();\r\n\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\tself._onCloseRequested();\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tself._$titleDiv = $titleDiv;\r\n\t\t},\r\n\r\n\t\t/* Creates the table.\r\n\t\t*************************************************************************/\r\n\t\t_createTable: function () {\r\n\t\t\tthis._$table = $('<table></table>')\r\n\t\t\t\t.addClass('ewjtable table table-sm')\r\n\t\t\t\t.appendTo(this._$mainContainer);\r\n\r\n\t\t\tif (this.options.tableId) {\r\n\t\t\t\tthis._$table.attr('id', this.options.tableId);\r\n\t\t\t}\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass(this._$table, 'ui-widget-content');\r\n\r\n\t\t\tthis._createTableHead();\r\n\t\t\tthis._createTableBody();\r\n\t\t},\r\n\r\n\t\t/* Creates header (all column headers) of the table.\r\n\t\t*************************************************************************/\r\n\t\t_createTableHead: function () {\r\n\t\t\tvar $thead = $('<thead></thead>')\r\n\t\t\t\t.appendTo(this._$table);\r\n\r\n\t\t\tthis._addRowToTableHead($thead);\r\n\t\t},\r\n\r\n\t\t/* Adds tr element to given thead element\r\n\t\t*************************************************************************/\r\n\t\t_addRowToTableHead: function ($thead) {\r\n\t\t\tvar $tr = $('<tr></tr>')\r\n\t\t\t\t.appendTo($thead);\r\n\r\n\t\t\tthis._addColumnsToHeaderRow($tr);\r\n\t\t},\r\n\r\n\t\t/* Adds column header cells to given tr element.\r\n\t\t*************************************************************************/\r\n\t\t_addColumnsToHeaderRow: function ($tr) {\r\n\t\t\tfor (var i = 0; i < this._columnList.length; i++) {\r\n\t\t\t\tvar fieldName = this._columnList[i];\r\n\t\t\t\tvar $headerCell = this._createHeaderCellForField(fieldName, this.options.fields[fieldName]);\r\n\t\t\t\t$headerCell.appendTo($tr);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Creates a header cell for given field.\r\n\t\t*  Returns th jQuery object.\r\n\t\t*************************************************************************/\r\n\t\t_createHeaderCellForField: function (fieldName, field) {\r\n\t\t\t//***field.width = field.width || '10%'; //default column width: 10%.\r\n\r\n\t\t\tvar $headerTextSpan = $('<span />')\r\n\t\t\t\t.addClass('ewjtable-column-header-text')\r\n\t\t\t\t.html(field.title);\r\n\r\n\t\t\tvar $headerContainerDiv = $('<div />')\r\n\t\t\t\t.addClass('ewjtable-column-header-container')\r\n\t\t\t\t.append($headerTextSpan);\r\n\r\n\t\t\tvar $th = $('<th></th>')\r\n\t\t\t\t.addClass('ewjtable-column-header')\r\n\t\t\t\t.addClass(field.listClass)\r\n\t\t\t\t.css('width', field.width)\r\n\t\t\t\t.data('fieldName', fieldName)\r\n\t\t\t\t.append($headerContainerDiv);\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass($th, 'ui-state-default');\r\n\r\n\t\t\treturn $th;\r\n\t\t},\r\n\r\n\t\t/* Creates an empty header cell that can be used as command column headers.\r\n\t\t*************************************************************************/\r\n\t\t_createEmptyCommandHeader: function () {\r\n\t\t\tvar $th = $('<th></th>')\r\n\t\t\t\t.addClass('ewjtable-command-column-header')\r\n\t\t\t\t.css('width', '1%');\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass($th, 'ui-state-default');\r\n\r\n\t\t\treturn $th;\r\n\t\t},\r\n\r\n\t\t/* Creates tbody tag and adds to the table.\r\n\t\t*************************************************************************/\r\n\t\t_createTableBody: function () {\r\n\t\t\tthis._$tableBody = $('<tbody></tbody>').appendTo(this._$table);\r\n\t\t},\r\n\r\n\t\t/* Creates a div to block UI while ewjTable is busy.\r\n\t\t*************************************************************************/\r\n\t\t_createBusyPanel: function () {\r\n\t\t\tthis._$busyMessageDiv = $('<div />').addClass('ewjtable-busy-message').prependTo(this._$mainContainer);\r\n\t\t\tthis._$busyDiv = $('<div />').addClass('ewjtable-busy-panel-background').prependTo(this._$mainContainer);\r\n\t\t\tthis._jqueryuiThemeAddClass(this._$busyMessageDiv, 'ui-widget-header');\r\n\t\t\tthis._hideBusy();\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PUBLIC METHODS                                                        *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Loads data using AJAX call, clears table and fills with new data.\r\n\t\t*************************************************************************/\r\n\t\tload: function (postData, completeCallback) {\r\n\t\t\tthis._lastPostData = postData;\r\n\t\t\tthis._reloadTable(completeCallback);\r\n\t\t},\r\n\r\n\t\t/* Refreshes (re-loads) table data with last postData.\r\n\t\t*************************************************************************/\r\n\t\treload: function (completeCallback) {\r\n\t\t\tthis._reloadTable(completeCallback);\r\n\t\t},\r\n\r\n\t\t/* Gets a jQuery row object according to given record key\r\n\t\t*************************************************************************/\r\n\t\tgetRowByKey: function (key) {\r\n\t\t\tfor (var i = 0; i < this._$tableRows.length; i++) {\r\n\t\t\t\tif (key == this._getKeyValueOfRecord(this._$tableRows[i].data('record'))) {\r\n\t\t\t\t\treturn this._$tableRows[i];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn null;\r\n\t\t},\r\n\r\n\t\t/* Completely removes the table from it's container.\r\n\t\t*************************************************************************/\r\n\t\tdestroy: function () {\r\n\t\t\tthis.element.empty();\r\n\t\t\t$.Widget.prototype.destroy.call(this);\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PRIVATE METHODS                                                       *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Used to change options dynamically after initialization.\r\n\t\t*************************************************************************/\r\n\t\t_setOption: function (key, value) {\r\n\r\n\t\t},\r\n\r\n\t\t/* LOADING RECORDS  *****************************************************/\r\n\r\n\t\t/* Performs an AJAX call to reload data of the table.\r\n\t\t*************************************************************************/\r\n\t\t_reloadTable: function (completeCallback) {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\tvar completeReload = function(data) {\r\n\t\t\t\tself._hideBusy();\r\n\r\n\t\t\t\t//Show the error message if server returns error\r\n\t\t\t\tif (data.result != 'OK') { //***\r\n\t\t\t\t\tself._showError(data.message); //***\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t//Re-generate table rows\r\n\t\t\t\tself._removeAllRows('reloading');\r\n\t\t\t\tself._addRecordsToTable(data.records); //***\r\n\r\n\t\t\t\tself._onRecordsLoaded(data);\r\n\r\n\t\t\t\t//Call complete callback\r\n\t\t\t\tif (completeCallback) {\r\n\t\t\t\t\tcompleteCallback(data);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\tself._showBusy(self.options.messages.loadingMessage, self.options.loadingAnimationDelay); //Disable table since it's busy\r\n\t\t\tself._onLoadingRecords();\r\n\r\n\t\t\t//listAction may be a function, check if it is\r\n\t\t\tif ($.isFunction(self.options.actions.listAction)) {\r\n\r\n\t\t\t\t//Execute the function\r\n\t\t\t\tvar funcResult = self.options.actions.listAction(self._lastPostData, self._createJtParamsForLoading());\r\n\r\n\t\t\t\t//Check if result is a jQuery Deferred object\r\n\t\t\t\tif (self._isDeferredObject(funcResult)) {\r\n\t\t\t\t\tfuncResult.done(function(data) {\r\n\t\t\t\t\t\tcompleteReload(data);\r\n\t\t\t\t\t}).fail(function() {\r\n\t\t\t\t\t\tself._showError(self.options.messages.serverCommunicationError);\r\n\t\t\t\t\t}).always(function() {\r\n\t\t\t\t\t\tself._hideBusy();\r\n\t\t\t\t\t});\r\n\t\t\t\t} else { //assume it's the data we're loading\r\n\t\t\t\t\tcompleteReload(funcResult);\r\n\t\t\t\t}\r\n\r\n\t\t\t} else { //assume listAction as URL string.\r\n\r\n\t\t\t\t//Generate URL (with query string parameters) to load records\r\n\t\t\t\tvar loadUrl = self._createRecordLoadUrl();\r\n\r\n\t\t\t\t//Load data from server using AJAX\r\n\t\t\t\tself._ajax({\r\n\t\t\t\t\turl: loadUrl,\r\n\t\t\t\t\tdata: self._lastPostData,\r\n\t\t\t\t\tsuccess: function (data) {\r\n\t\t\t\t\t\tcompleteReload(data);\r\n\t\t\t\t\t},\r\n\t\t\t\t\terror: function () {\r\n\t\t\t\t\t\tself._hideBusy();\r\n\t\t\t\t\t\tself._showError(self.options.messages.serverCommunicationError);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Creates URL to load records.\r\n\t\t*************************************************************************/\r\n\t\t_createRecordLoadUrl: function () {\r\n\t\t\treturn this.options.actions.listAction;\r\n\t\t},\r\n\r\n\t\t_createJtParamsForLoading: function() {\r\n\t\t\treturn {\r\n\t\t\t\t//Empty as default, paging, sorting or other extensions can override this method to add additional params to load request\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\t/* TABLE MANIPULATION METHODS *******************************************/\r\n\r\n\t\t/* Creates a row from given record\r\n\t\t*************************************************************************/\r\n\t\t_createRowFromRecord: function (record) {\r\n\t\t\tvar $tr = $('<tr></tr>')\r\n\t\t\t\t.addClass('ewjtable-data-row')\r\n\t\t\t\t.attr('data-record-key', this._getKeyValueOfRecord(record))\r\n\t\t\t\t.data('record', record);\r\n\r\n\t\t\tthis._addCellsToRowUsingRecord($tr);\r\n\t\t\treturn $tr;\r\n\t\t},\r\n\r\n\t\t/* Adds all cells to given row.\r\n\t\t*************************************************************************/\r\n\t\t_addCellsToRowUsingRecord: function ($row) {\r\n\t\t\tvar record = $row.data('record');\r\n\t\t\tfor (var i = 0; i < this._columnList.length; i++) {\r\n\t\t\t\tthis._createCellForRecordField(record, this._columnList[i])\r\n\t\t\t\t\t.appendTo($row);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Create a cell for given field.\r\n\t\t*************************************************************************/\r\n\t\t_createCellForRecordField: function (record, fieldName) {\r\n\t\t\treturn $('<td></td>')\r\n\t\t\t\t.addClass(this.options.fields[fieldName].listClass)\r\n\t\t\t\t.append((this._getDisplayTextForRecordField(record, fieldName)));\r\n\t\t},\r\n\r\n\t\t/* Adds a list of records to the table.\r\n\t\t*************************************************************************/\r\n\t\t_addRecordsToTable: function (records) {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\t$.each(records, function (index, record) {\r\n\t\t\t\tself._addRow(self._createRowFromRecord(record));\r\n\t\t\t});\r\n\r\n\t\t\tself._refreshRowStyles();\r\n\t\t},\r\n\r\n\t\t/* Adds a single row to the table.\r\n\t\t* NOTE: THIS METHOD IS DEPRECATED AND WILL BE REMOVED FROM FEATURE RELEASES.\r\n\t\t* USE _addRow METHOD.\r\n\t\t*************************************************************************/\r\n\t\t_addRowToTable: function ($tableRow, index, isNewRow, animationsEnabled) {\r\n\t\t\tvar options = {\r\n\t\t\t\tindex: this._normalizeNumber(index, 0, this._$tableRows.length, this._$tableRows.length)\r\n\t\t\t};\r\n\r\n\t\t\tif (isNewRow == true) {\r\n\t\t\t\toptions.isNewRow = true;\r\n\t\t\t}\r\n\r\n\t\t\tif (animationsEnabled == false) {\r\n\t\t\t\toptions.animationsEnabled = false;\r\n\t\t\t}\r\n\r\n\t\t\tthis._addRow($tableRow, options);\r\n\t\t},\r\n\r\n\t\t/* Adds a single row to the table.\r\n\t\t*************************************************************************/\r\n\t\t_addRow: function ($row, options) {\r\n\t\t\t//Set defaults\r\n\t\t\toptions = $.extend({\r\n\t\t\t\tindex: this._$tableRows.length,\r\n\t\t\t\tisNewRow: false,\r\n\t\t\t\tanimationsEnabled: true\r\n\t\t\t}, options);\r\n\r\n\t\t\t//Remove 'no data' row if this is first row\r\n\t\t\tif (this._$tableRows.length <= 0) {\r\n\t\t\t\tthis._removeNoDataRow();\r\n\t\t\t}\r\n\r\n\t\t\t//Add new row to the table according to it's index\r\n\t\t\toptions.index = this._normalizeNumber(options.index, 0, this._$tableRows.length, this._$tableRows.length);\r\n\t\t\tif (options.index == this._$tableRows.length) {\r\n\t\t\t\t//add as last row\r\n\t\t\t\tthis._$tableBody.append($row);\r\n\t\t\t\tthis._$tableRows.push($row);\r\n\t\t\t} else if (options.index == 0) {\r\n\t\t\t\t//add as first row\r\n\t\t\t\tthis._$tableBody.prepend($row);\r\n\t\t\t\tthis._$tableRows.unshift($row);\r\n\t\t\t} else {\r\n\t\t\t\t//insert to specified index\r\n\t\t\t\tthis._$tableRows[options.index - 1].after($row);\r\n\t\t\t\tthis._$tableRows.splice(options.index, 0, $row);\r\n\t\t\t}\r\n\r\n\t\t\tthis._onRowInserted($row, options.isNewRow);\r\n\r\n\t\t\t//Show animation if needed\r\n\t\t\tif (options.isNewRow) {\r\n\t\t\t\tthis._refreshRowStyles();\r\n\t\t\t\tif (this.options.animationsEnabled && options.animationsEnabled) {\r\n\t\t\t\t\tthis._showNewRowAnimation($row);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Shows created animation for a table row\r\n\t\t* TODO: Make this animation cofigurable and changable\r\n\t\t*************************************************************************/\r\n\t\t_showNewRowAnimation: function ($tableRow) {\r\n\t\t\tvar className = 'ewjtable-row-created';\r\n\t\t\tif (this.options.jqueryuiTheme) {\r\n\t\t\t\tclassName = className + ' ui-state-highlight';\r\n\t\t\t}\r\n\r\n\t\t\t$tableRow.addClass(className, 'slow', '', function () {\r\n\t\t\t\t$tableRow.removeClass(className, 5000);\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Removes a row or rows (jQuery selection) from table.\r\n\t\t*************************************************************************/\r\n\t\t_removeRowsFromTable: function ($rows, reason) {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\t//Check if any row specified\r\n\t\t\tif ($rows.length <= 0) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t//remove from DOM\r\n\t\t\t$rows.addClass('ewjtable-row-removed').remove();\r\n\r\n\t\t\t//remove from _$tableRows array\r\n\t\t\t$rows.each(function () {\r\n\t\t\t\tvar index = self._findRowIndex($(this));\r\n\t\t\t\tif (index >= 0) {\r\n\t\t\t\t\tself._$tableRows.splice(index, 1);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tself._onRowsRemoved($rows, reason);\r\n\r\n\t\t\t//Add 'no data' row if all rows removed from table\r\n\t\t\tif (self._$tableRows.length == 0) {\r\n\t\t\t\tself._addNoDataRow();\r\n\t\t\t}\r\n\r\n\t\t\tself._refreshRowStyles();\r\n\t\t},\r\n\r\n\t\t/* Finds index of a row in table.\r\n\t\t*************************************************************************/\r\n\t\t_findRowIndex: function ($row) {\r\n\t\t\treturn this._findIndexInArray($row, this._$tableRows, function ($row1, $row2) {\r\n\t\t\t\treturn $row1.data('record') == $row2.data('record');\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Removes all rows in the table and adds 'no data' row.\r\n\t\t*************************************************************************/\r\n\t\t_removeAllRows: function (reason) {\r\n\t\t\t//If no rows does exists, do nothing\r\n\t\t\tif (this._$tableRows.length <= 0) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t//Select all rows (to pass it on raising _onRowsRemoved event)\r\n\t\t\tvar $rows = this._$tableBody.find('tr.ewjtable-data-row');\r\n\r\n\t\t\t//Remove all rows from DOM and the _$tableRows array\r\n\t\t\tthis._$tableBody.empty();\r\n\t\t\tthis._$tableRows = [];\r\n\r\n\t\t\tthis._onRowsRemoved($rows, reason);\r\n\r\n\t\t\t//Add 'no data' row since we removed all rows\r\n\t\t\tthis._addNoDataRow();\r\n\t\t},\r\n\r\n\t\t/* Adds \"no data available\" row to the table.\r\n\t\t*************************************************************************/\r\n\t\t_addNoDataRow: function () {\r\n\t\t\tif (this._$tableBody.find('>tr.ewjtable-no-data-row').length > 0) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar $tr = $('<tr></tr>')\r\n\t\t\t\t.addClass('ewjtable-no-data-row')\r\n\t\t\t\t.appendTo(this._$tableBody);\r\n\r\n\t\t\tvar totalColumnCount = this._$table.find('thead th').length;\r\n\t\t\t$('<td></td>')\r\n\t\t\t\t.attr('colspan', totalColumnCount)\r\n\t\t\t\t.html(this.options.messages.noDataAvailable)\r\n\t\t\t\t.appendTo($tr);\r\n\t\t},\r\n\r\n\t\t/* Removes \"no data available\" row from the table.\r\n\t\t*************************************************************************/\r\n\t\t_removeNoDataRow: function () {\r\n\t\t\tthis._$tableBody.find('.ewjtable-no-data-row').remove();\r\n\t\t},\r\n\r\n\t\t/* Refreshes styles of all rows in the table\r\n\t\t*************************************************************************/\r\n\t\t_refreshRowStyles: function () {\r\n\t\t\tfor (var i = 0; i < this._$tableRows.length; i++) {\r\n\t\t\t\tif (i % 2 == 0) {\r\n\t\t\t\t\tthis._$tableRows[i].addClass('ewjtable-row-even');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis._$tableRows[i].removeClass('ewjtable-row-even');\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* RENDERING FIELD VALUES ***********************************************/\r\n\r\n\t\t/* Gets text for a field of a record according to it's type.\r\n\t\t*************************************************************************/\r\n\t\t_getDisplayTextForRecordField: function (record, fieldName) {\r\n\t\t\tvar field = this.options.fields[fieldName];\r\n\t\t\tvar fieldValue = record[fieldName];\r\n\r\n\t\t\t//if this is a custom field, call display function\r\n\t\t\tif (field.display) {\r\n\t\t\t\treturn field.display({ record: record });\r\n\t\t\t}\r\n\r\n\t\t\tif (field.type == 'date') {\r\n\t\t\t\treturn this._getDisplayTextForDateRecordField(field, fieldValue);\r\n\t\t\t} else { //other types\r\n\t\t\t\treturn fieldValue;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Gets text for a date field.\r\n\t\t*************************************************************************/\r\n\t\t_getDisplayTextForDateRecordField: function (field, fieldValue) {\r\n\t\t\tif (!fieldValue) {\r\n\t\t\t\treturn '';\r\n\t\t\t}\r\n\r\n\t\t\tvar displayFormat = field.displayFormat || this.options.defaultDateFormat;\r\n\t\t\treturn ew.formatDate(fieldValue, displayFormat);\r\n\t\t},\r\n\r\n\t\t/* TOOLBAR *************************************************************/\r\n\r\n\t\t/* Creates the toolbar.\r\n\t\t*************************************************************************/\r\n\t\t_createToolBar: function () {\r\n\t\t\tthis._$toolbarDiv = $('<div />')\r\n\t\t\t.addClass('ewjtable-toolbar')\r\n\t\t\t.appendTo(this._$titleDiv);\r\n\r\n\t\t\tfor (var i = 0; i < this.options.toolbar.items.length; i++) {\r\n\t\t\t\tthis._addToolBarItem(this.options.toolbar.items[i]);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Adds a new item to the toolbar.\r\n\t\t*************************************************************************/\r\n\t\t_addToolBarItem: function (item) {\r\n\r\n\t\t\t//Check if item is valid\r\n\t\t\tif ((item == undefined) || (item.text == undefined && item.icon == undefined)) {\r\n\t\t\t\tthis._logWarn('Can not add tool bar item since it is not valid!');\r\n\t\t\t\tthis._logWarn(item);\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\r\n\t\t\tvar $toolBarItem = $('<span></span>')\r\n\t\t\t\t.addClass('ewjtable-toolbar-item')\r\n\t\t\t\t.appendTo(this._$toolbarDiv);\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass($toolBarItem, 'ui-widget ui-state-default ui-corner-all', 'ui-state-hover');\r\n\r\n\t\t\t//cssClass property\r\n\t\t\tif (item.cssClass) {\r\n\t\t\t\t$toolBarItem\r\n\t\t\t\t\t.addClass(item.cssClass);\r\n\t\t\t}\r\n\r\n\t\t\t//tooltip property\r\n\t\t\tif (item.tooltip) {\r\n\t\t\t\t$toolBarItem\r\n\t\t\t\t\t.attr('title', item.tooltip);\r\n\t\t\t}\r\n\r\n\t\t\t//icon property\r\n\t\t\tif (item.icon) {\r\n\t\t\t\tvar $icon = $('<span class=\"ewjtable-toolbar-item-icon\"></span>').appendTo($toolBarItem);\r\n\t\t\t\tif (item.icon === true) {\r\n\t\t\t\t\t//do nothing\r\n\t\t\t\t} else if ($.type(item.icon === 'string')) {\r\n\t\t\t\t\t$icon.css('background', 'url(\"' + item.icon + '\")');\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t//text property\r\n\t\t\tif (item.text) {\r\n\t\t\t\t$('<span class=\"\"></span>')\r\n\t\t\t\t\t.html(item.text)\r\n\t\t\t\t\t.addClass('ewjtable-toolbar-item-text').appendTo($toolBarItem);\r\n\t\t\t}\r\n\r\n\t\t\t//click event\r\n\t\t\tif (item.click) {\r\n\t\t\t\t$toolBarItem.click(function () {\r\n\t\t\t\t\titem.click();\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t//set hover animation parameters\r\n\t\t\tvar hoverAnimationDuration = undefined;\r\n\t\t\tvar hoverAnimationEasing = undefined;\r\n\t\t\tif (this.options.toolbar.hoverAnimation) {\r\n\t\t\t\thoverAnimationDuration = this.options.toolbar.hoverAnimationDuration;\r\n\t\t\t\thoverAnimationEasing = this.options.toolbar.hoverAnimationEasing;\r\n\t\t\t}\r\n\r\n\t\t\t//change class on hover\r\n\t\t\t$toolBarItem.hover(function () {\r\n\t\t\t\t$toolBarItem.addClass('ewjtable-toolbar-item-hover', hoverAnimationDuration, hoverAnimationEasing);\r\n\t\t\t}, function () {\r\n\t\t\t\t$toolBarItem.removeClass('ewjtable-toolbar-item-hover', hoverAnimationDuration, hoverAnimationEasing);\r\n\t\t\t});\r\n\r\n\t\t\treturn $toolBarItem;\r\n\t\t},\r\n\r\n\t\t/* ERROR DIALOG *********************************************************/\r\n\r\n\t\t/* Shows error message dialog with given message.\r\n\t\t*************************************************************************/\r\n\t\t_showError: function (message) {\r\n\t\t\tew.alert(message);\r\n\t\t},\r\n\r\n\t\t/* BUSY PANEL ***********************************************************/\r\n\r\n\t\t/* Shows busy indicator and blocks table UI.\r\n\t\t* TODO: Make this cofigurable and changable\r\n\t\t*************************************************************************/\r\n\t\t_setBusyTimer: null,\r\n\t\t_showBusy: function (message, delay) {\r\n\t\t\tvar self = this;  //\r\n\r\n\t\t\t//Show a transparent overlay to prevent clicking to the table\r\n\t\t\tself._$busyDiv\r\n\t\t\t\t.width(self._$mainContainer.width())\r\n\t\t\t\t.height(self._$mainContainer.height())\r\n\t\t\t\t.addClass('ewjtable-busy-panel-background-invisible')\r\n\t\t\t\t.show();\r\n\r\n\t\t\tvar makeVisible = function () {\r\n\t\t\t\tself._$busyDiv.removeClass('ewjtable-busy-panel-background-invisible');\r\n\t\t\t\tself._$busyMessageDiv.html(message).show();\r\n\t\t\t};\r\n\r\n\t\t\tif (delay) {\r\n\t\t\t\tif (self._setBusyTimer) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tself._setBusyTimer = setTimeout(makeVisible, delay);\r\n\t\t\t} else {\r\n\t\t\t\tmakeVisible();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Hides busy indicator and unblocks table UI.\r\n\t\t*************************************************************************/\r\n\t\t_hideBusy: function () {\r\n\t\t\tclearTimeout(this._setBusyTimer);\r\n\t\t\tthis._setBusyTimer = null;\r\n\t\t\tthis._$busyDiv.hide();\r\n\t\t\tthis._$busyMessageDiv.html('').hide();\r\n\t\t},\r\n\r\n\t\t/* Returns true if ewjTable is busy.\r\n\t\t*************************************************************************/\r\n\t\t_isBusy: function () {\r\n\t\t\treturn this._$busyMessageDiv.is(':visible');\r\n\t\t},\r\n\r\n\t\t/* Adds jQueryUI class to an item.\r\n\t\t*************************************************************************/\r\n\t\t_jqueryuiThemeAddClass: function ($elm, className, hoverClassName) {\r\n\t\t\tif (!this.options.jqueryuiTheme) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t$elm.addClass(className);\r\n\r\n\t\t\tif (hoverClassName) {\r\n\t\t\t\t$elm.hover(function () {\r\n\t\t\t\t\t$elm.addClass(hoverClassName);\r\n\t\t\t\t}, function () {\r\n\t\t\t\t\t$elm.removeClass(hoverClassName);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* COMMON METHODS *******************************************************/\r\n\r\n\t\t/* Performs an AJAX call to specified URL.\r\n\t\t* THIS METHOD IS DEPRECATED AND WILL BE REMOVED FROM FEATURE RELEASES.\r\n\t\t* USE _ajax METHOD.\r\n\t\t*************************************************************************/\r\n\t\t_performAjaxCall: function (url, postData, async, success, error) {\r\n\t\t\tthis._ajax({\r\n\t\t\t\turl: url,\r\n\t\t\t\tdata: postData,\r\n\t\t\t\tasync: async,\r\n\t\t\t\tsuccess: success,\r\n\t\t\t\terror: error\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t_unAuthorizedRequestHandler: function() {\r\n\t\t\tif (this.options.unAuthorizedRequestRedirectUrl) {\r\n\t\t\t\tlocation.href = this.options.unAuthorizedRequestRedirectUrl;\r\n\t\t\t} else {\r\n\t\t\t\tlocation.reload(true);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* This method is used to perform AJAX calls in ewjTable instead of direct\r\n\t\t* usage of jQuery.ajax method.\r\n\t\t*************************************************************************/\r\n\t\t_ajax: function (options) {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\t//Handlers for HTTP status codes\r\n\t\t\tvar opts = {\r\n\t\t\t\tstatusCode: {\r\n\t\t\t\t\t401: function () { //Unauthorized\r\n\t\t\t\t\t\tself._unAuthorizedRequestHandler();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\topts = $.extend(opts, this.options.ajaxSettings, options);\r\n\r\n\t\t\t//Override success\r\n\t\t\topts.success = function (data) {\r\n\t\t\t\t//Checking for Authorization error\r\n\t\t\t\tif (data && data.unAuthorizedRequest == true) { //***\r\n\t\t\t\t\tself._unAuthorizedRequestHandler();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (options.success) {\r\n\t\t\t\t\toptions.success(data);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\t//Override error\r\n\t\t\topts.error = function (jqXHR, textStatus, errorThrown) {\r\n\t\t\t\tif (unloadingPage) {\r\n\t\t\t\t\tjqXHR.abort();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (options.error) {\r\n\t\t\t\t\toptions.error(arguments);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\t//Override complete\r\n\t\t\topts.complete = function () {\r\n\t\t\t\tif (options.complete) {\r\n\t\t\t\t\toptions.complete();\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\t$.ajax(opts);\r\n\t\t},\r\n\r\n\t\t/* Gets value of key field of a record.\r\n\t\t*************************************************************************/\r\n\t\t_getKeyValueOfRecord: function (record) {\r\n\t\t\treturn record[this._keyField];\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* COOKIE                                                                *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Sets a cookie with given key.\r\n\t\t*************************************************************************/\r\n\t\t_setCookie: function (key, value) {\r\n\t\t\tkey = this._cookieKeyPrefix + key;\r\n\r\n\t\t\tvar expireDate = new Date();\r\n\t\t\texpireDate.setDate(expireDate.getDate() + 30);\r\n\t\t\tdocument.cookie = encodeURIComponent(key) + '=' + encodeURIComponent(value) + \"; expires=\" + expireDate.toUTCString();\r\n\t\t},\r\n\r\n\t\t/* Gets a cookie with given key.\r\n\t\t*************************************************************************/\r\n\t\t_getCookie: function (key) {\r\n\t\t\tkey = this._cookieKeyPrefix + key;\r\n\r\n\t\t\tvar equalities = document.cookie.split('; ');\r\n\t\t\tfor (var i = 0; i < equalities.length; i++) {\r\n\t\t\t\tif (!equalities[i]) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar splitted = equalities[i].split('=');\r\n\t\t\t\tif (splitted.length != 2) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (decodeURIComponent(splitted[0]) === key) {\r\n\t\t\t\t\treturn decodeURIComponent(splitted[1] || '');\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn null;\r\n\t\t},\r\n\r\n\t\t/* Generates a hash key to be prefix for all cookies for this ewjtable instance.\r\n\t\t*************************************************************************/\r\n\t\t_generateCookieKeyPrefix: function () {\r\n\r\n\t\t\tvar simpleHash = function (value) {\r\n\t\t\t\tvar hash = 0;\r\n\t\t\t\tif (value.length == 0) {\r\n\t\t\t\t\treturn hash;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfor (var i = 0; i < value.length; i++) {\r\n\t\t\t\t\tvar ch = value.charCodeAt(i);\r\n\t\t\t\t\thash = ((hash << 5) - hash) + ch;\r\n\t\t\t\t\thash = hash & hash;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn hash;\r\n\t\t\t};\r\n\r\n\t\t\tvar strToHash = '';\r\n\t\t\tif (this.options.tableId) {\r\n\t\t\t\tstrToHash = strToHash + this.options.tableId + '#';\r\n\t\t\t}\r\n\r\n\t\t\tstrToHash = strToHash + this._columnList.join('$') + '#c' + this._$table.find('thead th').length;\r\n\t\t\treturn 'ewjtable#' + simpleHash(strToHash);\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* EVENT RAISING METHODS                                                 *\r\n\t\t*************************************************************************/\r\n\r\n\t\t_onLoadingRecords: function () {\r\n\t\t\tthis._trigger(\"loadingRecords\", null, {});\r\n\t\t},\r\n\r\n\t\t_onRecordsLoaded: function (data) {\r\n\t\t\tthis._trigger(\"recordsLoaded\", null, { records: data.records, serverResponse: data }); //***\r\n\t\t},\r\n\r\n\t\t_onRowInserted: function ($row, isNewRow) {\r\n\t\t\tthis._trigger(\"rowInserted\", null, { row: $row, record: $row.data('record'), isNewRow: isNewRow });\r\n\t\t},\r\n\r\n\t\t_onRowsRemoved: function ($rows, reason) {\r\n\t\t\tthis._trigger(\"rowsRemoved\", null, { rows: $rows, reason: reason });\r\n\t\t},\r\n\r\n\t\t_onCloseRequested: function () {\r\n\t\t\tthis._trigger(\"closeRequested\", null, {});\r\n\t\t}\r\n\r\n\t});\r\n\r\n}(jQuery));\r\n\r\n\r\n/************************************************************************\r\n* Some UTULITY methods used by ewjTable                                   *\r\n*************************************************************************/\r\n(function ($) {\r\n\r\n\t$.extend(true, $.ew.ewjtable.prototype, {\r\n\r\n\t\t/* Gets property value of an object recursively.\r\n\t\t*************************************************************************/\r\n\t\t_getPropertyOfObject: function (obj, propName) {\r\n\t\t\tif (propName.indexOf('.') < 0) {\r\n\t\t\t\treturn obj[propName];\r\n\t\t\t} else {\r\n\t\t\t\tvar preDot = propName.substring(0, propName.indexOf('.'));\r\n\t\t\t\tvar postDot = propName.substring(propName.indexOf('.') + 1);\r\n\t\t\t\treturn this._getPropertyOfObject(obj[preDot], postDot);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Sets property value of an object recursively.\r\n\t\t*************************************************************************/\r\n\t\t_setPropertyOfObject: function (obj, propName, value) {\r\n\t\t\tif (propName.indexOf('.') < 0) {\r\n\t\t\t\tobj[propName] = value;\r\n\t\t\t} else {\r\n\t\t\t\tvar preDot = propName.substring(0, propName.indexOf('.'));\r\n\t\t\t\tvar postDot = propName.substring(propName.indexOf('.') + 1);\r\n\t\t\t\tthis._setPropertyOfObject(obj[preDot], postDot, value);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Inserts a value to an array if it does not exists in the array.\r\n\t\t*************************************************************************/\r\n\t\t_insertToArrayIfDoesNotExists: function (array, value) {\r\n\t\t\tif ($.inArray(value, array) < 0) {\r\n\t\t\t\tarray.push(value);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Finds index of an element in an array according to given comparision function\r\n\t\t*************************************************************************/\r\n\t\t_findIndexInArray: function (value, array, compareFunc) {\r\n\r\n\t\t\t//If not defined, use default comparision\r\n\t\t\tif (!compareFunc) {\r\n\t\t\t\tcompareFunc = function (a, b) {\r\n\t\t\t\t\treturn a == b;\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\tfor (var i = 0; i < array.length; i++) {\r\n\t\t\t\tif (compareFunc(value, array[i])) {\r\n\t\t\t\t\treturn i;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn -1;\r\n\t\t},\r\n\r\n\t\t/* Normalizes a number between given bounds or sets to a defaultValue\r\n\t\t*  if it is undefined\r\n\t\t*************************************************************************/\r\n\t\t_normalizeNumber: function (number, min, max, defaultValue) {\r\n\t\t\tif (number == undefined || number == null || isNaN(number)) {\r\n\t\t\t\treturn defaultValue;\r\n\t\t\t}\r\n\r\n\t\t\tif (number < min) {\r\n\t\t\t\treturn min;\r\n\t\t\t}\r\n\r\n\t\t\tif (number > max) {\r\n\t\t\t\treturn max;\r\n\t\t\t}\r\n\r\n\t\t\treturn number;\r\n\t\t},\r\n\r\n\t\t/* Formats a string just like string.format in c#.\r\n\t\t*  Example:\r\n\t\t*  _formatString('Hello {0}','Halil') = 'Hello Halil'\r\n\t\t*************************************************************************/\r\n\t\t_formatString: function () {\r\n\t\t\tif (arguments.length == 0) {\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\r\n\t\t\tvar str = arguments[0];\r\n\t\t\tfor (var i = 1; i < arguments.length; i++) {\r\n\t\t\t\tvar placeHolder = '{' + (i - 1) + '}';\r\n\t\t\t\tstr = str.replace(placeHolder, arguments[i]);\r\n\t\t\t}\r\n\r\n\t\t\treturn str;\r\n\t\t},\r\n\r\n\t\t/* Checks if given object is a jQuery Deferred object.\r\n\t\t */\r\n\t\t_isDeferredObject: function (obj) {\r\n\t\t\treturn obj.then && obj.done && obj.fail;\r\n\t\t},\r\n\r\n\t\t//Logging methods ////////////////////////////////////////////////////////\r\n\r\n\t\t_logDebug: function (text) {\r\n\t\t\tif (!window.console) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('ewjTable DEBUG: ' + text);\r\n\t\t},\r\n\r\n\t\t_logInfo: function (text) {\r\n\t\t\tif (!window.console) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('ewjTable INFO: ' + text);\r\n\t\t},\r\n\r\n\t\t_logWarn: function (text) {\r\n\t\t\tif (!window.console) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('ewjTable WARNING: ' + text);\r\n\t\t},\r\n\r\n\t\t_logError: function (text) {\r\n\t\t\tif (!window.console) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('ewjTable ERROR: ' + text);\r\n\t\t}\r\n\r\n\t});\r\n\r\n\t/* Fix for array.indexOf method in IE7.\r\n\t * This code is taken from http://www.tutorialspoint.com/javascript/array_indexof.htm */\r\n\tif (!Array.prototype.indexOf) {\r\n\t\tArray.prototype.indexOf = function (elt) {\r\n\t\t\tvar len = this.length;\r\n\t\t\tvar from = Number(arguments[1]) || 0;\r\n\t\t\tfrom = (from < 0)\r\n\t\t\t\t ? Math.ceil(from)\r\n\t\t\t\t : Math.floor(from);\r\n\t\t\tif (from < 0)\r\n\t\t\t\tfrom += len;\r\n\t\t\tfor (; from < len; from++) {\r\n\t\t\t\tif (from in this &&\r\n\t\t\t\t\tthis[from] === elt)\r\n\t\t\t\t\treturn from;\r\n\t\t\t}\r\n\t\t\treturn -1;\r\n\t\t};\r\n\t}\r\n\r\n})(jQuery);\r\n\r\n/************************************************************************\r\n* SELECTING extension for ewjTable                                        *\r\n*************************************************************************/\r\n(function ($) {\r\n\r\n\t//Reference to base object members\r\n\tvar base = {\r\n\t\t_create: $.ew.ewjtable.prototype._create,\r\n\t\t_addColumnsToHeaderRow: $.ew.ewjtable.prototype._addColumnsToHeaderRow,\r\n\t\t_addCellsToRowUsingRecord: $.ew.ewjtable.prototype._addCellsToRowUsingRecord,\r\n\t\t_onLoadingRecords: $.ew.ewjtable.prototype._onLoadingRecords,\r\n\t\t_onRecordsLoaded: $.ew.ewjtable.prototype._onRecordsLoaded,\r\n\t\t_onRowsRemoved: $.ew.ewjtable.prototype._onRowsRemoved\r\n\t};\r\n\r\n\t//extension members\r\n\t$.extend(true, $.ew.ewjtable.prototype, {\r\n\r\n\t\t/************************************************************************\r\n\t\t* DEFAULT OPTIONS / EVENTS                                              *\r\n\t\t*************************************************************************/\r\n\t\toptions: {\r\n\r\n\t\t\t//Options\r\n\t\t\tselecting: false,\r\n\t\t\tmultiselect: false,\r\n\t\t\tselectingCheckboxes: false,\r\n\t\t\tselectOnRowClick: true,\r\n\r\n\t\t\t//Events\r\n\t\t\tselectionChanged: function (event, data) { }\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PRIVATE FIELDS                                                        *\r\n\t\t*************************************************************************/\r\n\r\n\t\t_selectedRecordIdsBeforeLoad: null, //This array is used to store selected row Id's to restore them after a page refresh (string array).\r\n\t\t_$selectAllCheckbox: null, //Reference to the 'select/deselect all' checkbox (jQuery object)\r\n\t\t_shiftKeyDown: false, //True, if shift key is currently down.\r\n\r\n\t\t/************************************************************************\r\n\t\t* CONSTRUCTOR                                                           *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Overrides base method to do selecting-specific constructions.\r\n\t\t*************************************************************************/\r\n\t\t_create: function () {\r\n\t\t\tif (this.options.selecting && this.options.selectingCheckboxes) {\r\n\t\t\t\t++this._firstDataColumnOffset;\r\n\t\t\t\tthis._bindKeyboardEvents();\r\n\t\t\t}\r\n\r\n\t\t\t//Call base method\r\n\t\t\tbase._create.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/* Registers to keyboard events those are needed for selection\r\n\t\t*************************************************************************/\r\n\t\t_bindKeyboardEvents: function () {\r\n\t\t\tvar self = this;\r\n\t\t\t//Register to events to set _shiftKeyDown value\r\n\t\t\t$(document)\r\n\t\t\t\t.keydown(function (event) {\r\n\t\t\t\t\tswitch (event.which) {\r\n\t\t\t\t\t\tcase 16:\r\n\t\t\t\t\t\t\tself._shiftKeyDown = true;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.keyup(function (event) {\r\n\t\t\t\t\tswitch (event.which) {\r\n\t\t\t\t\t\tcase 16:\r\n\t\t\t\t\t\t\tself._shiftKeyDown = false;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PUBLIC METHODS                                                        *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Gets jQuery selection for currently selected rows.\r\n\t\t*************************************************************************/\r\n\t\tselectedRows: function () {\r\n\t\t\treturn this._getSelectedRows();\r\n\t\t},\r\n\r\n\t\t/* Makes row/rows 'selected'.\r\n\t\t*************************************************************************/\r\n\t\tselectRows: function ($rows) {\r\n\t\t\tthis._selectRows($rows);\r\n\r\n\t\t\tthis._onSelectionChanged($.Event(\"selectRows\")); //TODO: trigger only if selected rows changes?\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* OVERRIDED METHODS                                                     *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Overrides base method to add a 'select column' to header row.\r\n\t\t*************************************************************************/\r\n\t\t_addColumnsToHeaderRow: function ($tr) {\r\n\t\t\tif (this.options.selecting && this.options.selectingCheckboxes) {\r\n\t\t\t\tif (this.options.multiselect) {\r\n\t\t\t\t\t$tr.append(this._createSelectAllHeader());\r\n\t\t\t\t} else {\r\n\t\t\t\t\t$tr.append(this._createEmptyCommandHeader());\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tbase._addColumnsToHeaderRow.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/* Overrides base method to add a 'delete command cell' to a row.\r\n\t\t*************************************************************************/\r\n\t\t_addCellsToRowUsingRecord: function ($row) {\r\n\t\t\tif (this.options.selecting) {\r\n\t\t\t\tthis._makeRowSelectable($row);\r\n\t\t\t}\r\n\r\n\t\t\tbase._addCellsToRowUsingRecord.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/* Overrides base event to store selection list\r\n\t\t*************************************************************************/\r\n\t\t_onLoadingRecords: function () {\r\n\t\t\tif (this.options.selecting) {\r\n\t\t\t\tthis._storeSelectionList();\r\n\t\t\t}\r\n\r\n\t\t\tbase._onLoadingRecords.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/* Overrides base event to restore selection list\r\n\t\t*************************************************************************/\r\n\t\t_onRecordsLoaded: function () {\r\n\t\t\tif (this.options.selecting) {\r\n\t\t\t\tthis._restoreSelectionList();\r\n\t\t\t}\r\n\r\n\t\t\tbase._onRecordsLoaded.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/* Overrides base event to check is any selected row is being removed.\r\n\t\t*************************************************************************/\r\n\t\t_onRowsRemoved: function ($rows, reason) {\r\n\t\t\tif (this.options.selecting && (reason != 'reloading') && ($rows.filter('.ewjtable-row-selected').length > 0)) {\r\n\t\t\t\tthis._onSelectionChanged($.Event(\"rowsRemoved\"));\r\n\t\t\t}\r\n\r\n\t\t\tbase._onRowsRemoved.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PRIVATE METHODS                                                       *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Creates a header column to select/deselect all rows.\r\n\t\t*************************************************************************/\r\n\t\t_createSelectAllHeader: function () {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\tvar $columnHeader = $('<th class=\"\"></th>')\r\n\t\t\t\t.addClass('ewjtable-command-column-header ewjtable-column-header-selecting');\r\n\t\t\tthis._jqueryuiThemeAddClass($columnHeader, 'ui-state-default');\r\n\r\n\t\t\tvar $headerContainer = $('<div />')\r\n\t\t\t\t.addClass('ewjtable-column-header-container')\r\n\t\t\t\t.appendTo($columnHeader);\r\n\r\n\t\t\tself._$selectAllCheckbox = $('<input type=\"checkbox\" class=\"custom-control-input\" id=\"modal_select_all\">') //***\r\n\t\t\t\t.click(function () {\r\n\t\t\t\t\tif (self._$tableRows.length <= 0) {\r\n\t\t\t\t\t\tself._$selectAllCheckbox.prop('checked', false);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvar allRows = self._$tableBody.find('>tr.ewjtable-data-row');\r\n\t\t\t\t\tif (self._$selectAllCheckbox.is(':checked')) {\r\n\t\t\t\t\t\tself._selectRows(allRows);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tself._deselectRows(allRows);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tself._onSelectionChanged($.Event(\"selectAll\", { \"rows\": allRows }));\r\n\t\t\t\t});\r\n\r\n\t\t\t$('<div class=\"custom-control custom-checkbox d-inline-block\"></div>')\r\n\t\t\t\t.append(self._$selectAllCheckbox)\r\n\t\t\t\t.append('<label class=\"custom-control-label\" for=\"modal_select_all\"></label>')\r\n\t\t\t\t.appendTo($headerContainer); //***\r\n\r\n\t\t\treturn $columnHeader;\r\n\t\t},\r\n\r\n\t\t/* Stores Id's of currently selected records to _selectedRecordIdsBeforeLoad.\r\n\t\t*************************************************************************/\r\n\t\t_storeSelectionList: function () {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\tif (!self.options.selecting) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tself._selectedRecordIdsBeforeLoad = [];\r\n\t\t\tself._getSelectedRows().each(function () {\r\n\t\t\t\tself._selectedRecordIdsBeforeLoad.push(self._getKeyValueOfRecord($(this).data('record')));\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Selects rows whose Id is in _selectedRecordIdsBeforeLoad;\r\n\t\t*************************************************************************/\r\n\t\t_restoreSelectionList: function () {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\tif (!self.options.selecting) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar selectedRowCount = 0;\r\n\t\t\tfor (var i = 0; i < self._$tableRows.length; ++i) {\r\n\t\t\t\tvar recordId = self._getKeyValueOfRecord(self._$tableRows[i].data('record'));\r\n\t\t\t\tif ($.inArray(recordId, self._selectedRecordIdsBeforeLoad) > -1) {\r\n\t\t\t\t\tself._selectRows(self._$tableRows[i]);\r\n\t\t\t\t\t++selectedRowCount;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (self._selectedRecordIdsBeforeLoad.length > 0 && self._selectedRecordIdsBeforeLoad.length != selectedRowCount) {\r\n\t\t\t\tself._onSelectionChanged($.Event(\"restoreSelectionList\"));\r\n\t\t\t}\r\n\r\n\t\t\tself._selectedRecordIdsBeforeLoad = [];\r\n\t\t\tself._refreshSelectAllCheckboxState();\r\n\t\t},\r\n\r\n\t\t/* Gets all selected rows.\r\n\t\t*************************************************************************/\r\n\t\t_getSelectedRows: function () {\r\n\t\t\treturn this._$tableBody\r\n\t\t\t\t.find('>tr.ewjtable-row-selected');\r\n\t\t},\r\n\r\n\t\t/* Adds selectable feature to a row.\r\n\t\t*************************************************************************/\r\n\t\t_makeRowSelectable: function ($row) {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\t//Select/deselect on row click\r\n\t\t\tif (self.options.selectOnRowClick) {\r\n\t\t\t\t$row.click(function (e) {\r\n\t\t\t\t\tif ($(e.target).is(\".custom-control-label\")) //***\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\tself._invertRowSelection($row);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t//'select/deselect' checkbox column\r\n\t\t\tif (self.options.selectingCheckboxes) {\r\n\t\t\t\tvar $cell = $('<td></td>').addClass('ewjtable-selecting-column');\r\n\t\t\t\tvar type = (self.options.multiselect) ? \"checkbox\" : \"radio\"; //***\r\n\t\t\t\tvar id = \"modal_\" + type + \"_\" + ew.random(); //***\r\n\t\t\t\tvar $selectCheckbox = $('<div class=\"custom-control custom-' + type + ' d-inline-block\"><input type=\"' + type + '\" class=\"custom-control-input\" id=\"' + id + '\"><label class=\"custom-control-label\" for=\"' + id + '\"></label></div>').appendTo($cell); //***\r\n\t\t\t\tif (!self.options.selectOnRowClick) {\r\n\t\t\t\t\t$selectCheckbox.find(\"input\").click(function (e) {\r\n\t\t\t\t\t\tself._invertRowSelection($row);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t$row.append($cell);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Inverts selection state of a single row.\r\n\t\t*************************************************************************/\r\n\t\t_invertRowSelection: function ($row) {\r\n\t\t\tif ($row.hasClass('ewjtable-row-selected')) {\r\n\t\t\t\tthis._deselectRows($row);\r\n\t\t\t} else {\r\n\t\t\t\t//Shift key?\r\n\t\t\t\tif (this._shiftKeyDown) {\r\n\t\t\t\t\tvar rowIndex = this._findRowIndex($row);\r\n\t\t\t\t\t//try to select row and above rows until first selected row\r\n\t\t\t\t\tvar beforeIndex = this._findFirstSelectedRowIndexBeforeIndex(rowIndex) + 1;\r\n\t\t\t\t\tif (beforeIndex > 0 && beforeIndex < rowIndex) {\r\n\t\t\t\t\t\tthis._selectRows(this._$tableBody.find('tr').slice(beforeIndex, rowIndex + 1));\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//try to select row and below rows until first selected row\r\n\t\t\t\t\t\tvar afterIndex = this._findFirstSelectedRowIndexAfterIndex(rowIndex) - 1;\r\n\t\t\t\t\t\tif (afterIndex > rowIndex) {\r\n\t\t\t\t\t\t\tthis._selectRows(this._$tableBody.find('tr').slice(rowIndex, afterIndex + 1));\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t//just select this row\r\n\t\t\t\t\t\t\tthis._selectRows($row);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis._selectRows($row);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tthis._onSelectionChanged($.Event(\"invertRowSelection\", { \"rows\": $row }));\r\n\t\t},\r\n\r\n\t\t/* Search for a selected row (that is before given row index) to up and returns it's index\r\n\t\t*************************************************************************/\r\n\t\t_findFirstSelectedRowIndexBeforeIndex: function (rowIndex) {\r\n\t\t\tfor (var i = rowIndex - 1; i >= 0; --i) {\r\n\t\t\t\tif (this._$tableRows[i].hasClass('ewjtable-row-selected')) {\r\n\t\t\t\t\treturn i;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn -1;\r\n\t\t},\r\n\r\n\t\t/* Search for a selected row (that is after given row index) to down and returns it's index\r\n\t\t*************************************************************************/\r\n\t\t_findFirstSelectedRowIndexAfterIndex: function (rowIndex) {\r\n\t\t\tfor (var i = rowIndex + 1; i < this._$tableRows.length; ++i) {\r\n\t\t\t\tif (this._$tableRows[i].hasClass('ewjtable-row-selected')) {\r\n\t\t\t\t\treturn i;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn -1;\r\n\t\t},\r\n\r\n\t\t/* Makes row/rows 'selected'.\r\n\t\t*************************************************************************/\r\n\t\t_selectRows: function ($rows) {\r\n\t\t\tif (!this.options.multiselect) {\r\n\t\t\t\tthis._deselectRows(this._getSelectedRows());\r\n\t\t\t}\r\n\r\n\t\t\t$rows.addClass('ewjtable-row-selected');\r\n\t\t\tthis._jqueryuiThemeAddClass($rows, 'ui-state-highlight');\r\n\r\n\t\t\tif (this.options.selectingCheckboxes) {\r\n\t\t\t\t$rows.find('>td.ewjtable-selecting-column input').prop('checked', true); //***\r\n\t\t\t}\r\n\r\n\t\t\tthis._refreshSelectAllCheckboxState();\r\n\t\t},\r\n\r\n\t\t/* Makes row/rows 'non selected'.\r\n\t\t*************************************************************************/\r\n\t\t_deselectRows: function ($rows) {\r\n\t\t\t$rows.removeClass('ewjtable-row-selected ui-state-highlight');\r\n\t\t\tif (this.options.selectingCheckboxes) {\r\n\t\t\t\t$rows.find('>td.ewjtable-selecting-column input').prop('checked', false); //***\r\n\t\t\t}\r\n\r\n\t\t\tthis._refreshSelectAllCheckboxState();\r\n\t\t},\r\n\r\n\t\t/* Updates state of the 'select/deselect' all checkbox according to count of selected rows.\r\n\t\t*************************************************************************/\r\n\t\t_refreshSelectAllCheckboxState: function () {\r\n\t\t\tif (!this.options.selectingCheckboxes || !this.options.multiselect) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar totalRowCount = this._$tableRows.length;\r\n\t\t\tvar selectedRowCount = this._getSelectedRows().length;\r\n\r\n\t\t\tif (selectedRowCount == 0) {\r\n\t\t\t\tthis._$selectAllCheckbox.prop('indeterminate', false);\r\n\t\t\t\tthis._$selectAllCheckbox.prop('checked', false);\r\n\t\t\t} else if (selectedRowCount == totalRowCount) {\r\n\t\t\t\tthis._$selectAllCheckbox.prop('indeterminate', false);\r\n\t\t\t\tthis._$selectAllCheckbox.prop('checked', true);\r\n\t\t\t} else {\r\n\t\t\t\tthis._$selectAllCheckbox.prop('checked', false);\r\n\t\t\t\tthis._$selectAllCheckbox.prop('indeterminate', true);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* EVENT RAISING METHODS                                                 *\r\n\t\t*************************************************************************/\r\n\r\n\t\t_onSelectionChanged: function (e) {\r\n\t\t\tthis._trigger(\"selectionChanged\", null, e || {});\r\n\t\t}\r\n\r\n\t});\r\n\r\n})(jQuery);\r\n\r\n\r\n/************************************************************************\r\n* PAGING extension for ewjTable                                           *\r\n*************************************************************************/\r\n(function ($) {\r\n\r\n\t//Reference to base object members\r\n\tvar base = {\r\n\t\tload: $.ew.ewjtable.prototype.load,\r\n\t\t_create: $.ew.ewjtable.prototype._create,\r\n\t\t_setOption: $.ew.ewjtable.prototype._setOption,\r\n\t\t_createRecordLoadUrl: $.ew.ewjtable.prototype._createRecordLoadUrl,\r\n\t\t_createJtParamsForLoading: $.ew.ewjtable.prototype._createJtParamsForLoading,\r\n\t\t_addRowToTable: $.ew.ewjtable.prototype._addRowToTable,\r\n\t\t_addRow: $.ew.ewjtable.prototype._addRow,\r\n\t\t_removeRowsFromTable: $.ew.ewjtable.prototype._removeRowsFromTable,\r\n\t\t_onRecordsLoaded: $.ew.ewjtable.prototype._onRecordsLoaded\r\n\t};\r\n\r\n\t//extension members\r\n\t$.extend(true, $.ew.ewjtable.prototype, {\r\n\r\n\t\t/************************************************************************\r\n\t\t* DEFAULT OPTIONS / EVENTS                                              *\r\n\t\t*************************************************************************/\r\n\t\toptions: {\r\n\t\t\tpaging: true,\r\n\t\t\tpageList: 'minimal', //possible values: 'minimal', 'normal'\r\n\t\t\tpageSize: 10,\r\n\t\t\tpageSizes: [10, 20, 30],\r\n\t\t\tpageSizeChangeArea: false,\r\n\t\t\tgotoPageArea: 'combobox', //possible values: 'textbox', 'combobox', 'none'\r\n\r\n\t\t\tmessages: {\r\n\t\t\t\tpagingInfo: 'Showing {0}-{1} of {2}',\r\n\t\t\t\tpageSizeChangeLabel: 'Row count',\r\n\t\t\t\tgotoPageLabel: 'Go to page'\r\n\t\t\t}\r\n\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PRIVATE FIELDS                                                        *\r\n\t\t*************************************************************************/\r\n\r\n\t\t_$bottomPanel: null, //Reference to the panel at the bottom of the table (jQuery object)\r\n\t\t_$pagingListArea: null, //Reference to the page list area in to bottom panel (jQuery object)\r\n\t\t_$pageSizeChangeArea: null, //Reference to the page size change area in to bottom panel (jQuery object)\r\n\t\t_$pageInfoSpan: null, //Reference to the paging info area in to bottom panel (jQuery object)\r\n\t\t_$gotoPageArea: null, //Reference to 'Go to page' input area in to bottom panel (jQuery object)\r\n\t\t_$gotoPageInput: null, //Reference to 'Go to page' input in to bottom panel (jQuery object)\r\n\t\t_totalRecordCount: 0, //Total count of records on all pages\r\n\t\t_currentPageNo: 1, //Current page number\r\n\r\n\t\t/************************************************************************\r\n\t\t* CONSTRUCTOR AND INITIALIZING METHODS                                  *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Overrides base method to do paging-specific constructions.\r\n\t\t*************************************************************************/\r\n\t\t_create: function() {\r\n\t\t\tbase._create.apply(this, arguments);\r\n\t\t\tif (this.options.paging) {\r\n\t\t\t\tthis._loadPagingSettings();\r\n\t\t\t\tthis._createBottomPanel();\r\n\t\t\t\tthis._createPageListArea();\r\n\t\t\t\tthis._createGotoPageInput();\r\n\t\t\t\tthis._createPageSizeSelection();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Loads user preferences for paging.\r\n\t\t*************************************************************************/\r\n\t\t_loadPagingSettings: function() {\r\n\t\t\tif (!this.options.saveUserPreferences) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar pageSize = this._getCookie('page-size');\r\n\t\t\tif (pageSize) {\r\n\t\t\t\tthis.options.pageSize = this._normalizeNumber(pageSize, 1, 1000000, this.options.pageSize);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Creates bottom panel and adds to the page.\r\n\t\t*************************************************************************/\r\n\t\t_createBottomPanel: function() {\r\n\t\t\tthis._$bottomPanel = $('<div />')\r\n\t\t\t\t.addClass('ewjtable-bottom-panel clearfix') //***\r\n\t\t\t\t.insertAfter(this._$table);\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass(this._$bottomPanel, 'ui-state-default');\r\n\r\n\t\t\t$('<div />').addClass('ewjtable-left-area float-left').appendTo(this._$bottomPanel); //***\r\n\t\t\t$('<div />').addClass('ewjtable-right-area float-right').appendTo(this._$bottomPanel); //***\r\n\t\t},\r\n\r\n\t\t/* Creates page list area.\r\n\t\t*************************************************************************/\r\n\t\t_createPageListArea: function() {\r\n\t\t\tthis._$pagingListArea = $('<span></span>')\r\n\t\t\t\t.addClass('ewjtable-page-list')\r\n\t\t\t\t.appendTo(this._$bottomPanel.find('.ewjtable-left-area'));\r\n\r\n\t\t\tthis._$pageInfoSpan = $('<span></span>')\r\n\t\t\t\t.addClass('ewjtable-page-info')\r\n\t\t\t\t.appendTo(this._$bottomPanel.find('.ewjtable-right-area'));\r\n\t\t},\r\n\r\n\t\t/* Creates page list change area.\r\n\t\t*************************************************************************/\r\n\t\t_createPageSizeSelection: function() {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\tif (!self.options.pageSizeChangeArea) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t//Add current page size to page sizes list if not contains it\r\n\t\t\tif (self._findIndexInArray(self.options.pageSize, self.options.pageSizes) < 0) {\r\n\t\t\t\tself.options.pageSizes.push(parseInt(self.options.pageSize));\r\n\t\t\t\tself.options.pageSizes.sort(function(a, b) { return a - b; });\r\n\t\t\t}\r\n\r\n\t\t\t//Add a span to contain page size change items\r\n\t\t\tself._$pageSizeChangeArea = $('<span></span>')\r\n\t\t\t\t.addClass('ewjtable-page-size-change')\r\n\t\t\t\t.appendTo(self._$bottomPanel.find('.ewjtable-left-area'));\r\n\r\n\t\t\t//Page size label\r\n\t\t\tself._$pageSizeChangeArea.append('<span>' + self.options.messages.pageSizeChangeLabel + ': </span>');\r\n\r\n\t\t\t//Page size change combobox\r\n\t\t\tvar $pageSizeChangeCombobox = $('<select></select>').appendTo(self._$pageSizeChangeArea);\r\n\r\n\t\t\t//Add page sizes to the combobox\r\n\t\t\tfor (var i = 0; i < self.options.pageSizes.length; i++) {\r\n\t\t\t\t$pageSizeChangeCombobox.append('<option value=\"' + self.options.pageSizes[i] + '\">' + self.options.pageSizes[i] + '</option>');\r\n\t\t\t}\r\n\r\n\t\t\t//Select current page size\r\n\t\t\t$pageSizeChangeCombobox.val(self.options.pageSize);\r\n\r\n\t\t\t//Change page size on combobox change\r\n\t\t\t$pageSizeChangeCombobox.change(function() {\r\n\t\t\t\tself._changePageSize(parseInt($(this).val()));\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Creates go to page area.\r\n\t\t*************************************************************************/\r\n\t\t_createGotoPageInput: function() {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\tif (!self.options.gotoPageArea || self.options.gotoPageArea == 'none') {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t//Add a span to contain goto page items\r\n\t\t\tthis._$gotoPageArea = $('<span></span>')\r\n\t\t\t\t.addClass('ewjtable-goto-page')\r\n\t\t\t\t.appendTo(self._$bottomPanel.find('.ewjtable-left-area'));\r\n\r\n\t\t\t//Goto page label\r\n\t\t\tthis._$gotoPageArea.append('<span>' + self.options.messages.gotoPageLabel + '</span>'); //***\r\n\r\n\t\t\t//Goto page input\r\n\t\t\tif (self.options.gotoPageArea == 'combobox') {\r\n\r\n\t\t\t\tself._$gotoPageInput = $('<select></select>')\r\n\t\t\t\t\t.addClass(\"custom-select custom-select-sm\")\r\n\t\t\t\t\t.appendTo(this._$gotoPageArea)\r\n\t\t\t\t\t.data('pageCount', 1)\r\n\t\t\t\t\t.change(function() {\r\n\t\t\t\t\t\tself._changePage(parseInt($(this).val()));\r\n\t\t\t\t\t});\r\n\t\t\t\tself._$gotoPageInput.append('<option value=\"1\">1</option>');\r\n\r\n\t\t\t} else { //textbox\r\n\r\n\t\t\t\tself._$gotoPageInput = $('<input type=\"text\" maxlength=\"10\" value=\"' + self._currentPageNo + '\" />')\r\n\t\t\t\t\t.addClass(\"form-control form-control-sm\")\r\n\t\t\t\t\t.appendTo(this._$gotoPageArea)\r\n\t\t\t\t\t.keypress(function(event) {\r\n\t\t\t\t\t\tif (event.which == 13) { //enter\r\n\t\t\t\t\t\t\tevent.preventDefault();\r\n\t\t\t\t\t\t\tself._changePage(parseInt(self._$gotoPageInput.val()));\r\n\t\t\t\t\t\t} else if (event.which == 43) { // +\r\n\t\t\t\t\t\t\tevent.preventDefault();\r\n\t\t\t\t\t\t\tself._changePage(parseInt(self._$gotoPageInput.val()) + 1);\r\n\t\t\t\t\t\t} else if (event.which == 45) { // -\r\n\t\t\t\t\t\t\tevent.preventDefault();\r\n\t\t\t\t\t\t\tself._changePage(parseInt(self._$gotoPageInput.val()) - 1);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t//Allow only digits\r\n\t\t\t\t\t\t\tvar isValid = (\r\n\t\t\t\t\t\t\t\t(47 < event.keyCode && event.keyCode < 58 && event.shiftKey == false && event.altKey == false)\r\n\t\t\t\t\t\t\t\t\t|| (event.keyCode == 8)\r\n\t\t\t\t\t\t\t\t\t|| (event.keyCode == 9)\r\n\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\tif (!isValid) {\r\n\t\t\t\t\t\t\t\tevent.preventDefault();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Refreshes the 'go to page' input.\r\n\t\t*************************************************************************/\r\n\t\t_refreshGotoPageInput: function() {\r\n\t\t\tif (!this.options.gotoPageArea || this.options.gotoPageArea == 'none') {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (this._totalRecordCount <= 0 || this.options.pageSize > this._totalRecordCount) { //***\r\n\t\t\t\tthis._$gotoPageArea.hide();\r\n\t\t\t} else {\r\n\t\t\t\tthis._$gotoPageArea.show();\r\n\t\t\t}\r\n\r\n\t\t\tif (this.options.gotoPageArea == 'combobox') {\r\n\t\t\t\tvar oldPageCount = this._$gotoPageInput.data('pageCount');\r\n\t\t\t\tvar currentPageCount = this._calculatePageCount();\r\n\t\t\t\tif (oldPageCount != currentPageCount) {\r\n\t\t\t\t\tthis._$gotoPageInput.empty();\r\n\r\n\t\t\t\t\t//Skip some pages is there are too many pages\r\n\t\t\t\t\tvar pageStep = 1;\r\n\t\t\t\t\tif (currentPageCount > 10000) {\r\n\t\t\t\t\t\tpageStep = 100;\r\n\t\t\t\t\t} else if (currentPageCount > 5000) {\r\n\t\t\t\t\t\tpageStep = 10;\r\n\t\t\t\t\t} else if (currentPageCount > 2000) {\r\n\t\t\t\t\t\tpageStep = 5;\r\n\t\t\t\t\t} else if (currentPageCount > 1000) {\r\n\t\t\t\t\t\tpageStep = 2;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tfor (var i = pageStep; i <= currentPageCount; i += pageStep) {\r\n\t\t\t\t\t\tthis._$gotoPageInput.append('<option value=\"' + i + '\">' + i + '</option>');\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthis._$gotoPageInput.data('pageCount', currentPageCount);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t//same for 'textbox' and 'combobox'\r\n\t\t\tthis._$gotoPageInput.val(this._currentPageNo);\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* OVERRIDED METHODS                                                     *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Overrides load method to set current page to 1.\r\n\t\t*************************************************************************/\r\n\t\tload: function() {\r\n\t\t\tthis._currentPageNo = 1;\r\n\r\n\t\t\tbase.load.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/* Used to change options dynamically after initialization.\r\n\t\t*************************************************************************/\r\n\t\t_setOption: function(key, value) {\r\n\t\t\tbase._setOption.apply(this, arguments);\r\n\r\n\t\t\tif (key == 'pageSize') {\r\n\t\t\t\tthis._changePageSize(parseInt(value));\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Changes current page size with given value.\r\n\t\t*************************************************************************/\r\n\t\t_changePageSize: function(pageSize) {\r\n\t\t\tif (pageSize == this.options.pageSize) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.options.pageSize = pageSize;\r\n\r\n\t\t\t//Normalize current page\r\n\t\t\tvar pageCount = this._calculatePageCount();\r\n\t\t\tif (this._currentPageNo > pageCount) {\r\n\t\t\t\tthis._currentPageNo = pageCount;\r\n\t\t\t}\r\n\t\t\tif (this._currentPageNo <= 0) {\r\n\t\t\t\tthis._currentPageNo = 1;\r\n\t\t\t}\r\n\r\n\t\t\t//if user sets one of the options on the combobox, then select it.\r\n\t\t\tvar $pageSizeChangeCombobox = this._$bottomPanel.find('.ewjtable-page-size-change select');\r\n\t\t\tif ($pageSizeChangeCombobox.length > 0) {\r\n\t\t\t\tif (parseInt($pageSizeChangeCombobox.val()) != pageSize) {\r\n\t\t\t\t\tvar selectedOption = $pageSizeChangeCombobox.find('option[value=' + pageSize + ']');\r\n\t\t\t\t\tif (selectedOption.length > 0) {\r\n\t\t\t\t\t\t$pageSizeChangeCombobox.val(pageSize);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tthis._savePagingSettings();\r\n\t\t\tthis._reloadTable();\r\n\t\t},\r\n\r\n\t\t/* Saves user preferences for paging\r\n\t\t*************************************************************************/\r\n\t\t_savePagingSettings: function() {\r\n\t\t\tif (!this.options.saveUserPreferences) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis._setCookie('page-size', this.options.pageSize);\r\n\t\t},\r\n\r\n\t\t/* Overrides _createRecordLoadUrl method to add paging info to URL.\r\n\t\t*************************************************************************/\r\n\t\t_createRecordLoadUrl: function() {\r\n\t\t\tvar loadUrl = base._createRecordLoadUrl.apply(this, arguments);\r\n\t\t\tloadUrl = this._addPagingInfoToUrl(loadUrl, this._currentPageNo);\r\n\t\t\treturn loadUrl;\r\n\t\t},\r\n\r\n\t\t/* Overrides _createJtParamsForLoading method to add paging parameters to jtParams object.\r\n\t\t*************************************************************************/\r\n\t\t_createJtParamsForLoading: function () {\r\n\t\t\tvar jtParams = base._createJtParamsForLoading.apply(this, arguments);\r\n\r\n\t\t\tif (this.options.paging) {\r\n\t\t\t\tjtParams.start = (this._currentPageNo - 1) * this.options.pageSize;\r\n\t\t\t\tjtParams.recperpage = this.options.pageSize;\r\n\t\t\t}\r\n\r\n\t\t\treturn jtParams;\r\n\t\t},\r\n\r\n\t\t/* Overrides _addRowToTable method to re-load table when a new row is created.\r\n\t\t* NOTE: THIS METHOD IS DEPRECATED AND WILL BE REMOVED FROM FEATURE RELEASES.\r\n\t\t* USE _addRow METHOD.\r\n\t\t*************************************************************************/\r\n\t\t_addRowToTable: function ($tableRow, index, isNewRow) {\r\n\t\t\tif (isNewRow && this.options.paging) {\r\n\t\t\t\tthis._reloadTable();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tbase._addRowToTable.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/* Overrides _addRow method to re-load table when a new row is created.\r\n\t\t*************************************************************************/\r\n\t\t_addRow: function ($row, options) {\r\n\t\t\tif (options && options.isNewRow && this.options.paging) {\r\n\t\t\t\tthis._reloadTable();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tbase._addRow.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/* Overrides _removeRowsFromTable method to re-load table when a row is removed from table.\r\n\t\t*************************************************************************/\r\n\t\t_removeRowsFromTable: function ($rows, reason) {\r\n\t\t\tbase._removeRowsFromTable.apply(this, arguments);\r\n\r\n\t\t\tif (this.options.paging) {\r\n\t\t\t\tif (this._$tableRows.length <= 0 && this._currentPageNo > 1) {\r\n\t\t\t\t\t--this._currentPageNo;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis._reloadTable();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Overrides _onRecordsLoaded method to to do paging specific tasks.\r\n\t\t*************************************************************************/\r\n\t\t_onRecordsLoaded: function (data) {\r\n\t\t\tif (this.options.paging) {\r\n\t\t\t\tthis._totalRecordCount = data.totalRecordCount; //***\r\n\t\t\t\tthis._createPagingList();\r\n\t\t\t\tthis._createPagingInfo();\r\n\t\t\t\tthis._refreshGotoPageInput();\r\n\t\t\t}\r\n\r\n\t\t\tbase._onRecordsLoaded.apply(this, arguments);\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PRIVATE METHODS                                                       *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Adds StartIndex and PageSize parameters to a URL as query string.\r\n\t\t*************************************************************************/\r\n\t\t_addPagingInfoToUrl: function (url, pageNumber) {\r\n\t\t\tif (!this.options.paging) {\r\n\t\t\t\treturn url;\r\n\t\t\t}\r\n\r\n\t\t\tvar startIndex = (pageNumber - 1) * this.options.pageSize;\r\n\t\t\tvar pageSize = this.options.pageSize;\r\n\r\n\t\t\treturn (url + (url.indexOf('?') < 0 ? '?' : '&') + 'start=' + startIndex + '&recperpage=' + pageSize);\r\n\t\t},\r\n\r\n\t\t/* Creates and shows the page list.\r\n\t\t*************************************************************************/\r\n\t\t_createPagingList: function () {\r\n\t\t\tif (this.options.pageSize <= 0) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis._$pagingListArea.empty();\r\n\t\t\tif (this._totalRecordCount <= 0 || this.options.pageSize > this._totalRecordCount) { //***\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar pageCount = this._calculatePageCount();\r\n\r\n\t\t\tthis._createFirstAndPreviousPageButtons();\r\n\t\t\tif (this.options.pageList == 'normal') {\r\n\t\t\t\tthis._createPageNumberButtons(this._calculatePageNumbers(pageCount));\r\n\t\t\t} else {\r\n\r\n\t\t\t}\r\n\t\t\tthis._createLastAndNextPageButtons(pageCount);\r\n\t\t\tthis._bindClickEventsToPageNumberButtons();\r\n\r\n\t\t\tthis._$pagingListArea.wrapInner('<div class=\"btn-group btn-group-sm\" role=\"group\"></div>');\r\n\t\t},\r\n\r\n\t\t/* Creates and shows previous and first page links.\r\n\t\t*************************************************************************/\r\n\t\t_createFirstAndPreviousPageButtons: function () {\r\n\t\t\tvar $first = $('<button></button>')\r\n\t\t\t\t.addClass('ewjtable-page-number-first btn btn-default')\r\n\t\t\t\t.html('<i class=\"icon-first ew-icon\"></i>')\r\n\t\t\t\t.data('pageNumber', 1)\r\n\t\t\t\t.appendTo(this._$pagingListArea);\r\n\r\n\t\t\tvar $previous = $('<button></button>')\r\n\t\t\t\t.addClass('ewjtable-page-number-previous btn btn-default')\r\n\t\t\t\t.html('<i class=\"icon-prev ew-icon\"></i>')\r\n\t\t\t\t.data('pageNumber', this._currentPageNo - 1)\r\n\t\t\t\t.appendTo(this._$pagingListArea);\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass($first, 'ui-button ui-state-default', 'ui-state-hover');\r\n\t\t\tthis._jqueryuiThemeAddClass($previous, 'ui-button ui-state-default', 'ui-state-hover');\r\n\r\n\t\t\tif (this._currentPageNo <= 1) {\r\n\t\t\t\t$first.addClass('ewjtable-page-number-disabled disabled');\r\n\t\t\t\t$previous.addClass('ewjtable-page-number-disabled disabled');\r\n\t\t\t\tthis._jqueryuiThemeAddClass($first, 'ui-state-disabled');\r\n\t\t\t\tthis._jqueryuiThemeAddClass($previous, 'ui-state-disabled');\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Creates and shows next and last page links.\r\n\t\t*************************************************************************/\r\n\t\t_createLastAndNextPageButtons: function (pageCount) {\r\n\t\t\tvar $next = $('<button></button>')\r\n\t\t\t\t.addClass('ewjtable-page-number-next btn btn-default')\r\n\t\t\t\t.html('<i class=\"icon-next ew-icon\"></i>')\r\n\t\t\t\t.data('pageNumber', this._currentPageNo + 1)\r\n\t\t\t\t.appendTo(this._$pagingListArea);\r\n\t\t\tvar $last = $('<button></button>')\r\n\t\t\t\t.addClass('ewjtable-page-number-last btn btn-default')\r\n\t\t\t\t.html('<i class=\"icon-last ew-icon\"></i>')\r\n\t\t\t\t.data('pageNumber', pageCount)\r\n\t\t\t\t.appendTo(this._$pagingListArea);\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass($next, 'ui-button ui-state-default', 'ui-state-hover');\r\n\t\t\tthis._jqueryuiThemeAddClass($last, 'ui-button ui-state-default', 'ui-state-hover');\r\n\r\n\t\t\tif (this._currentPageNo >= pageCount) {\r\n\t\t\t\t$next.addClass('ewjtable-page-number-disabled disabled');\r\n\t\t\t\t$last.addClass('ewjtable-page-number-disabled disabled');\r\n\t\t\t\tthis._jqueryuiThemeAddClass($next, 'ui-state-disabled');\r\n\t\t\t\tthis._jqueryuiThemeAddClass($last, 'ui-state-disabled');\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Creates and shows page number links for given number array.\r\n\t\t*************************************************************************/\r\n\t\t_createPageNumberButtons: function (pageNumbers) {\r\n\t\t\tvar previousNumber = 0;\r\n\t\t\tfor (var i = 0; i < pageNumbers.length; i++) {\r\n\t\t\t\t//Create \"...\" between page numbers if needed\r\n\t\t\t\tif ((pageNumbers[i] - previousNumber) > 1) {\r\n\t\t\t\t\t$('<button></button>')\r\n\t\t\t\t\t\t.addClass('ewjtable-page-number-space btn btn-default')\r\n\t\t\t\t\t\t.html('<i class=\"fas fa-ellipsis-h ew-icon\"></i>')\r\n\t\t\t\t\t\t.appendTo(this._$pagingListArea);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis._createPageNumberButton(pageNumbers[i]);\r\n\t\t\t\tpreviousNumber = pageNumbers[i];\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Creates a page number link and adds to paging area.\r\n\t\t*************************************************************************/\r\n\t\t_createPageNumberButton: function (pageNumber) {\r\n\t\t\tvar $pageNumber = $('<button></button>')\r\n\t\t\t\t.addClass('ewjtable-page-number btn btn-default')\r\n\t\t\t\t.html(pageNumber)\r\n\t\t\t\t.data('pageNumber', pageNumber)\r\n\t\t\t\t.appendTo(this._$pagingListArea);\r\n\r\n\t\t\tthis._jqueryuiThemeAddClass($pageNumber, 'ui-button ui-state-default', 'ui-state-hover');\r\n\r\n\t\t\tif (this._currentPageNo == pageNumber) {\r\n\t\t\t\t$pageNumber.addClass('ewjtable-page-number-active ewjtable-page-number-disabled');\r\n\t\t\t\tthis._jqueryuiThemeAddClass($pageNumber, 'ui-state-active');\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Calculates total page count according to page size and total record count.\r\n\t\t*************************************************************************/\r\n\t\t_calculatePageCount: function () {\r\n\t\t\tvar pageCount = Math.floor(this._totalRecordCount / this.options.pageSize);\r\n\t\t\tif (this._totalRecordCount % this.options.pageSize != 0) {\r\n\t\t\t\t++pageCount;\r\n\t\t\t}\r\n\r\n\t\t\treturn pageCount;\r\n\t\t},\r\n\r\n\t\t/* Calculates page numbers and returns an array of these numbers.\r\n\t\t*************************************************************************/\r\n\t\t_calculatePageNumbers: function (pageCount) {\r\n\t\t\tif (pageCount <= 4) {\r\n\t\t\t\t//Show all pages\r\n\t\t\t\tvar pageNumbers = [];\r\n\t\t\t\tfor (var i = 1; i <= pageCount; ++i) {\r\n\t\t\t\t\tpageNumbers.push(i);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn pageNumbers;\r\n\t\t\t} else {\r\n\t\t\t\t//show first three, last three, current, previous and next page numbers\r\n\t\t\t\tvar shownPageNumbers = [1, 2, pageCount - 1, pageCount];\r\n\t\t\t\tvar previousPageNo = this._normalizeNumber(this._currentPageNo - 1, 1, pageCount, 1);\r\n\t\t\t\tvar nextPageNo = this._normalizeNumber(this._currentPageNo + 1, 1, pageCount, 1);\r\n\r\n\t\t\t\tthis._insertToArrayIfDoesNotExists(shownPageNumbers, previousPageNo);\r\n\t\t\t\tthis._insertToArrayIfDoesNotExists(shownPageNumbers, this._currentPageNo);\r\n\t\t\t\tthis._insertToArrayIfDoesNotExists(shownPageNumbers, nextPageNo);\r\n\r\n\t\t\t\tshownPageNumbers.sort(function (a, b) { return a - b; });\r\n\t\t\t\treturn shownPageNumbers;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Creates and shows paging informations.\r\n\t\t*************************************************************************/\r\n\t\t_createPagingInfo: function () {\r\n\t\t\tif (this._totalRecordCount <= 0 || this.options.pageSize > this._totalRecordCount) { //***\r\n\t\t\t\tthis._$pageInfoSpan.empty();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tvar startNo = (this._currentPageNo - 1) * this.options.pageSize + 1;\r\n\t\t\tvar endNo = this._currentPageNo * this.options.pageSize;\r\n\t\t\tendNo = this._normalizeNumber(endNo, startNo, this._totalRecordCount, 0);\r\n\r\n\t\t\tif (endNo >= startNo) {\r\n\t\t\t\tvar pagingInfoMessage = this._formatString(this.options.messages.pagingInfo, startNo, endNo, this._totalRecordCount);\r\n\t\t\t\tthis._$pageInfoSpan.html(pagingInfoMessage);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Binds click events of all page links to change the page.\r\n\t\t*************************************************************************/\r\n\t\t_bindClickEventsToPageNumberButtons: function () {\r\n\t\t\tvar self = this;\r\n\t\t\tself._$pagingListArea\r\n\t\t\t\t.find('.ewjtable-page-number,.ewjtable-page-number-previous,.ewjtable-page-number-next,.ewjtable-page-number-first,.ewjtable-page-number-last')\r\n\t\t\t\t.not('.ewjtable-page-number-disabled')\r\n\t\t\t\t.click(function (e) {\r\n\t\t\t\t\te.preventDefault();\r\n\t\t\t\t\tself._changePage($(this).data('pageNumber'));\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Changes current page to given value.\r\n\t\t*************************************************************************/\r\n\t\t_changePage: function (pageNo) {\r\n\t\t\tpageNo = this._normalizeNumber(pageNo, 1, this._calculatePageCount(), 1);\r\n\t\t\tif (pageNo == this._currentPageNo) {\r\n\t\t\t\tthis._refreshGotoPageInput();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis._currentPageNo = pageNo;\r\n\t\t\tthis._reloadTable();\r\n\t\t}\r\n\r\n\t});\r\n\r\n})(jQuery);\r\n\r\n\r\n/************************************************************************\r\n* SORTING extension for jTable                                          *\r\n*************************************************************************/\r\n(function ($) {\r\n\r\n\t//Reference to base object members\r\n\tvar base = {\r\n\t\t_initializeFields: $.ew.ewjtable.prototype._initializeFields,\r\n\t\t_normalizeFieldOptions: $.ew.ewjtable.prototype._normalizeFieldOptions,\r\n\t\t_createHeaderCellForField: $.ew.ewjtable.prototype._createHeaderCellForField,\r\n\t\t_createRecordLoadUrl: $.ew.ewjtable.prototype._createRecordLoadUrl,\r\n\t\t_createJtParamsForLoading: $.ew.ewjtable.prototype._createJtParamsForLoading\r\n\t};\r\n\r\n\t//extension members\r\n\t$.extend(true, $.ew.ewjtable.prototype, {\r\n\r\n\t\t/************************************************************************\r\n\t\t* DEFAULT OPTIONS / EVENTS                                              *\r\n\t\t*************************************************************************/\r\n\t\toptions: {\r\n\t\t\tsorting: false,\r\n\t\t\tmultiSorting: false,\r\n\t\t\tdefaultSorting: ''\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PRIVATE FIELDS                                                        *\r\n\t\t*************************************************************************/\r\n\r\n\t\t_lastSorting: null, //Last sorting of the table\r\n\r\n\t\t/************************************************************************\r\n\t\t* OVERRIDED METHODS                                                     *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Overrides base method to create sorting array.\r\n\t\t*************************************************************************/\r\n\t\t_initializeFields: function () {\r\n\t\t\tbase._initializeFields.apply(this, arguments);\r\n\r\n\t\t\tthis._lastSorting = [];\r\n\t\t\tif (this.options.sorting) {\r\n\t\t\t\tthis._buildDefaultSortingArray();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/* Overrides _normalizeFieldOptions method to normalize sorting option for fields.\r\n\t\t*************************************************************************/\r\n\t\t_normalizeFieldOptions: function (fieldName, props) {\r\n\t\t\tbase._normalizeFieldOptions.apply(this, arguments);\r\n\t\t\tprops.sorting = (props.sorting != false);\r\n\t\t},\r\n\r\n\t\t/* Overrides _createHeaderCellForField to make columns sortable.\r\n\t\t*************************************************************************/\r\n\t\t_createHeaderCellForField: function (fieldName, field) {\r\n\t\t\tvar $headerCell = base._createHeaderCellForField.apply(this, arguments);\r\n\t\t\tif (this.options.sorting && field.sorting) {\r\n\t\t\t\tthis._makeColumnSortable($headerCell, fieldName);\r\n\t\t\t}\r\n\r\n\t\t\treturn $headerCell;\r\n\t\t},\r\n\r\n\t\t/* Overrides _createRecordLoadUrl to add sorting specific info to URL.\r\n\t\t*************************************************************************/\r\n\t\t_createRecordLoadUrl: function () {\r\n\t\t\tvar loadUrl = base._createRecordLoadUrl.apply(this, arguments);\r\n\t\t\tloadUrl = this._addSortingInfoToUrl(loadUrl);\r\n\t\t\treturn loadUrl;\r\n\t\t},\r\n\r\n\t\t/************************************************************************\r\n\t\t* PRIVATE METHODS                                                       *\r\n\t\t*************************************************************************/\r\n\r\n\t\t/* Builds the sorting array according to defaultSorting string\r\n\t\t*************************************************************************/\r\n\t\t_buildDefaultSortingArray: function () {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\t$.each(self.options.defaultSorting.split(\",\"), function (orderIndex, orderValue) {\r\n\t\t\t\t$.each(self.options.fields, function (fieldName, fieldProps) {\r\n\t\t\t\t\tif (fieldProps.sorting) {\r\n\t\t\t\t\t\tvar colOffset = orderValue.indexOf(fieldName);\r\n\t\t\t\t\t\tif (colOffset > -1) {\r\n\t\t\t\t\t\t\tif (orderValue.toUpperCase().indexOf(' DESC', colOffset) > -1) {\r\n\t\t\t\t\t\t\t\tself._lastSorting.push({\r\n\t\t\t\t\t\t\t\t\tfieldName: fieldName,\r\n\t\t\t\t\t\t\t\t\tsortOrder: 'DESC'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tself._lastSorting.push({\r\n\t\t\t\t\t\t\t\t\tfieldName: fieldName,\r\n\t\t\t\t\t\t\t\t\tsortOrder: 'ASC'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Makes a column sortable.\r\n\t\t*************************************************************************/\r\n\t\t_makeColumnSortable: function ($columnHeader, fieldName) {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\t$columnHeader\r\n\t\t\t\t.addClass('ewjtable-column-header-sortable')\r\n\t\t\t\t.click(function (e) {\r\n\t\t\t\t\te.preventDefault();\r\n\r\n\t\t\t\t\tif (!self.options.multiSorting || !e.ctrlKey) {\r\n\t\t\t\t\t\tself._lastSorting = []; //clear previous sorting\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tself._sortTableByColumn($columnHeader);\r\n\t\t\t\t});\r\n\r\n\t\t\t//Set default sorting\r\n\t\t\t$.each(this._lastSorting, function (sortIndex, sortField) {\r\n\t\t\t\tif (sortField.fieldName == fieldName) {\r\n\t\t\t\t\tif (sortField.sortOrder == 'DESC') {\r\n\t\t\t\t\t\t$columnHeader.addClass('ewjtable-column-header-sorted-desc');\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t$columnHeader.addClass('ewjtable-column-header-sorted-asc');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/* Sorts table according to a column header.\r\n\t\t*************************************************************************/\r\n\t\t_sortTableByColumn: function ($columnHeader) {\r\n\t\t\t//Remove sorting styles from all columns except this one\r\n\t\t\tif (this._lastSorting.length == 0) {\r\n\t\t\t\t$columnHeader.siblings().removeClass('ewjtable-column-header-sorted-asc jtable-column-header-sorted-desc');\r\n\t\t\t}\r\n\r\n\t\t\t//If current sorting list includes this column, remove it from the list\r\n\t\t\tfor (var i = 0; i < this._lastSorting.length; i++) {\r\n\t\t\t\tif (this._lastSorting[i].fieldName == $columnHeader.data('fieldName')) {\r\n\t\t\t\t\tthis._lastSorting.splice(i--, 1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t//Sort ASC or DESC according to current sorting state\r\n\t\t\tif ($columnHeader.hasClass('ewjtable-column-header-sorted-asc')) {\r\n\t\t\t\t$columnHeader.removeClass('ewjtable-column-header-sorted-asc').addClass('ewjtable-column-header-sorted-desc');\r\n\t\t\t\tthis._lastSorting.push({\r\n\t\t\t\t\t'fieldName': $columnHeader.data('fieldName'),\r\n\t\t\t\t\tsortOrder: 'DESC'\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t$columnHeader.removeClass('ewjtable-column-header-sorted-desc').addClass('ewjtable-column-header-sorted-asc');\r\n\t\t\t\tthis._lastSorting.push({\r\n\t\t\t\t\t'fieldName': $columnHeader.data('fieldName'),\r\n\t\t\t\t\tsortOrder: 'ASC'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t//Load current page again\r\n\t\t\tthis._reloadTable();\r\n\t\t},\r\n\r\n\t\t/* Adds jtSorting parameter to a URL as query string.\r\n\t\t*************************************************************************/\r\n\t\t_addSortingInfoToUrl: function (url) {\r\n\t\t\tif (!this.options.sorting || this._lastSorting.length == 0) {\r\n\t\t\t\treturn url;\r\n\t\t\t}\r\n\r\n\t\t\tvar sorting = [];\r\n\t\t\t$.each(this._lastSorting, function (idx, value) {\r\n\t\t\t\tsorting.push(value.fieldName + ' ' + value.sortOrder);\r\n\t\t\t});\r\n\r\n\t\t\treturn (url + (url.indexOf('?') < 0 ? '?' : '&') + 'sorting=' + sorting.join(\",\"));\r\n\t\t},\r\n\r\n\t\t/* Overrides _createJtParamsForLoading method to add sorging parameters to jtParams object.\r\n\t\t*************************************************************************/\r\n\t\t_createJtParamsForLoading: function () {\r\n\t\t\tvar jtParams = base._createJtParamsForLoading.apply(this, arguments);\r\n\r\n\t\t\tif (this.options.sorting && this._lastSorting.length) {\r\n\t\t\t\tvar sorting = [];\r\n\t\t\t\t$.each(this._lastSorting, function (idx, value) {\r\n\t\t\t\t\tsorting.push(value.fieldName + ' ' + value.sortOrder);\r\n\t\t\t\t});\r\n\r\n\t\t\t\tjtParams.sorting = sorting.join(\",\");\r\n\t\t\t}\r\n\r\n\t\t\treturn jtParams;\r\n\t\t}\r\n\r\n\t});\r\n\r\n})(jQuery);"]}