@import 'sweetalert2/src/variables'; //***

// Function
@function str-replace($string, $search, $replace: '') {
  $index: str-index($string, $search);

  @if $index {
    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);
  }

  @return $string;
}

// Theme Variables
// Color
$bootstrap-primary: #007bff !default;
$bootstrap-success: #28a745 !default;
$bootstrap-danger: #dc3545 !default;
$bootstrap-warning: #ffc107 !default;
$bootstrap-info: #17a2b8 !default;
$bootstrap-secondary: #6c757d !default;

$bootstrap-gray-100: #f8f9fa !default;
$bootstrap-gray-200: #e9ecef !default;
$bootstrap-gray-300: #dee2e6 !default;
$bootstrap-gray-400: #ced4da !default;
$bootstrap-gray-500: #adb5bd !default;
$bootstrap-gray-600: #6c757d !default;
$bootstrap-gray-700: #495057 !default;
$bootstrap-gray-800: #343a40 !default;
$bootstrap-gray-900: #212529 !default;

$bootstrap-theme-color-interval: 8% !default;

// Alert
$bootstrap-alert-border-level: -9 !default;
$bootstrap-alert-bg-level: -10 !default;
$bootstrap-alert-color-level: 6 !default;
$bootstrap-alert-border-color: mix($swal2-white, $bootstrap-secondary, abs($bootstrap-alert-border-level) * $bootstrap-theme-color-interval) !default;
$bootstrap-alert-background: mix($swal2-white, $bootstrap-secondary, abs($bootstrap-alert-bg-level) * $bootstrap-theme-color-interval) !default;
$bootstrap-alert-color: mix($swal2-black, $bootstrap-secondary, abs($bootstrap-alert-color-level) * $bootstrap-theme-color-interval) !default;
$bootstrap-alert-padding-y: .75rem !default;
$bootstrap-alert-padding-x: 1.25rem !default;
$bootstrap-alert-margin-bottom: 1rem !default;
$bootstrap-alert-border-radius: .25rem !default;
$bootstrap-alert-border-width: 1px !default;
$bootstrap-alert-font-size: 1rem !default;

// Input
$bootstrap-input-color: $bootstrap-gray-700 !default;
$bootstrap-input-bg: $swal2-white !default;
$bootstrap-input-border-color: $bootstrap-gray-300 !default;
$bootstrap-input-border-radius: .25rem !default;
$bootstrap-input-border-width: 1px !default;
$bootstrap-input-padding-y: .375rem !default;
$bootstrap-input-padding-x: .75rem !default;
$bootstrap-input-line-height: 1.5 !default;
$bootstrap-input-height-border: $bootstrap-input-border-width * 2 !default;
$bootstrap-input-height: calc(#{$bootstrap-input-line-height * 1em} + #{$bootstrap-input-padding-y * 2} + #{$bootstrap-input-height-border}) !default;

$bootstrap-input-disabled-color: $swal2-white !default;
$bootstrap-input-disabled-bg: $bootstrap-gray-200 !default;

$bootstrap-input-focus-width: .2rem !default;
$bootstrap-input-focus-border: 1px solid lighten($bootstrap-primary, 25%) !default;
$bootstrap-input-focus-box-shadow: 0 0 0 $bootstrap-input-focus-width rgba($bootstrap-primary, .25) !default;

// Button
$bootstrap-btn-transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;
$bootstrap-btn-secondary-focus-box-shadow: 0 0 0 $bootstrap-input-focus-width rgba($bootstrap-secondary, .25) !default;

// Custom Shared Variables
$bootstrap-custom-forms-transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;

$bootstrap-custom-control-gutter: .5rem !default;
$bootstrap-custom-control-indicator-size: 1rem !default;
$bootstrap-custom-control-indicator-bg: $swal2-white !default;
$bootstrap-custom-control-indicator-bg-size: 50% 50% !default;
$bootstrap-custom-control-indicator-border-color: $bootstrap-gray-500 !default;
$bootstrap-custom-control-indicator-border-width: $bootstrap-input-border-width !default;
$bootstrap-custom-control-indicator-checked-color: $swal2-white !default;
$bootstrap-custom-control-indicator-checked-bg: $bootstrap-primary !default;
$bootstrap-custom-control-indicator-checked-disabled-bg: rgba($bootstrap-primary, .5) !default;
$bootstrap-custom-control-indicator-checked-border-color: $bootstrap-custom-control-indicator-checked-bg !default;

$bootstrap-custom-control-indicator-active-color: $swal2-white !default;
$bootstrap-custom-control-indicator-active-bg: lighten($bootstrap-primary, 35%) !default;
$bootstrap-custom-control-indicator-active-box-shadow: none !default;
$bootstrap-custom-control-indicator-active-border-color: $bootstrap-custom-control-indicator-active-bg !default;

$bootstrap-custom-control-indicator-focus-box-shadow: $bootstrap-input-focus-box-shadow !default;
$bootstrap-custom-control-indicator-focus-border-color: lighten($bootstrap-primary, 25%) !default;

// Custom Select
$bootstrap-custom-select-bg-size: 8px 10px !default;
$bootstrap-custom-select-indicator-color: $bootstrap-gray-800 !default;
$bootstrap-custom-select-indicator: str-replace(url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"%3e%3cpath fill="#{$bootstrap-custom-select-indicator-color}" d="M2 0L0 2h4zm0 5L0 3h4z"/%3e%3c/svg%3e'), '#', '%23') !default;
$bootstrap-custom-select-background: $bootstrap-custom-select-indicator no-repeat right $bootstrap-input-padding-x center / $bootstrap-custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)

// Custom Radio
$bootstrap-custom-radio-indicator-border-radius: 50% !default;
$bootstrap-custom-radio-indicator-icon-checked: str-replace(url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="-4 -4 8 8"%3e%3ccircle r="3" fill="#{$bootstrap-custom-control-indicator-checked-color}"/%3e%3c/svg%3e'), '#', '%23') !default;

// Custom Checkbox
$bootstrap-custom-checkbox-indicator-icon-checked: str-replace(url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 8"%3e%3cpath fill="#{$bootstrap-custom-control-indicator-checked-color}" d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/%3e%3c/svg%3e'), '#', '%23') !default;

// Custom Range
$bootstrap-custom-range-track-width: 100% !default;
$bootstrap-custom-range-track-height: .5rem !default;
$bootstrap-custom-range-track-cursor: pointer !default;
$bootstrap-custom-range-track-bg: $bootstrap-gray-300 !default;
$bootstrap-custom-range-track-border-radius: 1rem !default;
$bootstrap-custom-range-track-box-shadow: inset 0 .25rem .25rem rgba($swal2-black, .1) !default;

$bootstrap-custom-range-thumb-width: 1rem !default;
$bootstrap-custom-range-thumb-height: $bootstrap-custom-range-thumb-width !default;
$bootstrap-custom-range-thumb-bg: $bootstrap-primary !default;
$bootstrap-custom-range-thumb-border: 0 !default;
$bootstrap-custom-range-thumb-border-radius: 1rem !default;
$bootstrap-custom-range-thumb-box-shadow: 0 .1rem .25rem rgba($swal2-black, .1) !default;
$bootstrap-custom-range-thumb-focus-box-shadow: 0 0 0 1px $swal2-white, $bootstrap-input-focus-box-shadow !default;
$bootstrap-custom-range-thumb-focus-box-shadow-width: $bootstrap-input-focus-width !default; // For focus box shadow issue in IE/Edge
$bootstrap-custom-range-thumb-active-bg: lighten($bootstrap-primary, 35%) !default;
$bootstrap-custom-range-thumb-disabled-bg: $bootstrap-gray-500 !default;

// Toast
$bootstrap-toast-max-width:                   350px !default;
$bootstrap-toast-padding-x:                   .75rem !default;
$bootstrap-toast-padding-y:                   .25rem !default;
$bootstrap-toast-font-size:                   .875rem !default;
$bootstrap-toast-background-color:            rgba($swal2-white, .85) !default;
$bootstrap-toast-border-width:                1px !default;
$bootstrap-toast-border-color:                rgba(0, 0, 0, .1) !default;
$bootstrap-toast-border-radius:               .25rem !default;
$bootstrap-toast-box-shadow:                  0 .25rem .75rem rgba($swal2-black, .1) !default;

// override SASS variables here

// BOX MODEL
$swal2-padding: 1rem;
$swal2-border-radius: .3rem;

// ANIMATIONS
// animate.css/fading_entrances/fadeInDown.css
@keyframes fadeInDown {
  from {
    transform: translate3d(0, -100%, 0);
    opacity: 0;
  }

  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

// animate.css/fading_exits/fadeOutUp.css
@keyframes fadeOutUp {
  from {
    opacity: 1;
  }

  to {
    transform: translate3d(0, -100%, 0);
    opacity: 0;
  }
}

$swal2-show-animation: fadeInDown .5s;
$swal2-hide-animation: fadeOutUp .5s;

// BACKDROP
$swal2-backdrop: rgba($swal2-black, .5);
$swal2-backdrop-transition: background-color .5s;

// ICONS
$swal2-success: $bootstrap-success;
$swal2-error: $bootstrap-danger;
$swal2-warning: $bootstrap-warning;
$swal2-info: $bootstrap-info;
$swal2-question: $bootstrap-secondary;

// INPUT
$swal2-input-border: $bootstrap-input-border-width solid $bootstrap-input-border-color;
$swal2-input-border-radius: $bootstrap-input-border-radius;
$swal2-input-border-focus: none;
$swal2-input-box-shadow-focus: none;
$swal2-input-font-size: 1rem;
$swal2-input-padding: $bootstrap-input-padding-y $bootstrap-input-padding-x;

// CLOSE BUTTON
$swal2-close-button-width: 2em;
$swal2-close-button-height: 2em;
$swal2-close-button-line-height: 1;
$swal2-close-button-color: rgba($swal2-black, .5);
$swal2-close-button-font-size: 1.5rem;

// CLOSE BUTTON:HOVER
$swal2-close-button-hover-color: $swal2-black;

// CONFIRM BUTTON
$swal2-confirm-button-background-color: $bootstrap-primary;

// CANCEL BUTTON
$swal2-cancel-button-background-color: $bootstrap-secondary;

// COMMON VARIABLES FOR CONFIRM AND CANCEL BUTTONS
$swal2-button-focus-outline: none;

// TOASTS
$swal2-toast-border: $bootstrap-toast-border-color solid $bootstrap-toast-border-width;
$swal2-toast-box-shadow: $bootstrap-toast-box-shadow;
$swal2-toast-padding: $bootstrap-toast-padding-x $bootstrap-toast-padding-y;
$swal2-toast-title-font-size: $bootstrap-toast-font-size;
$swal2-toast-content-font-size: $bootstrap-toast-font-size;
$swal2-toast-input-font-size: $bootstrap-toast-font-size;
$swal2-toast-validation-font-size: $bootstrap-toast-font-size;
$swal2-toast-buttons-font-size: $bootstrap-toast-font-size;

@import 'sweetalert2/src/sweetalert2';

.swal2-confirm,
.swal2-cancel {
  transition: $bootstrap-btn-transition;
}

.swal2-popup {
  border: $bootstrap-input-border-width solid rgba(0, 0, 0, .2);

  &.swal2-toast {
    padding: .25rem .75rem;
    font-size: .875rem;

    .swal2-header {
      padding: 0;
      border-bottom: 0;
    }

    .swal2-title {
      margin: 0;
      color: $bootstrap-gray-600;
    }

    .swal2-content {
      padding: 0 .5rem;
    }

    .swal2-actions {
      flex-basis: 0 !important;
      margin: 0;
      padding: 0;
    }

    .swal2-styled {
      margin: 0;
    }
  }
}

.swal2-title {
  margin: 0;
  font-size: 1.25rem;
}

.swal2-content {
  padding: 1rem 1rem 0;
}

.swal2-actions {
  border-radius: $bootstrap-input-border-radius;
}

.swal2-footer {
  padding: 1rem;
  border-top: $bootstrap-input-border-width solid $bootstrap-input-border-color;
}

.swal2-close {
  height: auto;
  padding: 1rem 1.2rem 1rem 1rem;
  font-weight: 700;
}

.swal2-input,
.swal2-textarea {
  height: inherit;
  padding: $swal2-input-padding;
  transition: $bootstrap-custom-forms-transition;
  line-height: $bootstrap-input-line-height;

  &:focus {
    border: $bootstrap-input-focus-border;
    outline: 0;
    box-shadow: $bootstrap-input-focus-box-shadow;
    color: $bootstrap-input-color;
  }

  &.swal2-inputerror {
    box-shadow: none !important;

    &:focus {
      border-color: $bootstrap-danger;
      box-shadow: 0 0 0 $bootstrap-input-focus-width rgba($bootstrap-danger, .25) !important;
    }
  }
}

.swal2-styled {
  &.swal2-confirm {
    padding: $swal2-input-padding;
    font-size: 1rem;

    &:hover {
      border-color: darken($bootstrap-primary, 10%);
      background-color: darken($bootstrap-primary, 7.5%);
    }

    &:focus {
      outline: 0;
      box-shadow: $bootstrap-input-focus-box-shadow;
    }

    &:active {
      border-color: darken($bootstrap-primary, 12.5%);
      background-color: darken($bootstrap-primary, 10%);

      &:focus {
        box-shadow: $bootstrap-input-focus-box-shadow;
      }
    }
  }

  &.swal2-cancel {
    padding: $swal2-input-padding;
    font-size: 1rem;

    &:hover {
      border-color: darken($bootstrap-secondary, 10%);
      background-color: darken($bootstrap-secondary, 7.5%);
    }

    &:focus {
      outline: 0;
      box-shadow: $bootstrap-btn-secondary-focus-box-shadow;
    }

    &:active {
      border-color: darken($bootstrap-secondary, 12.5%);
      background-color: darken($bootstrap-secondary, 10%);

      &:focus {
        box-shadow: $bootstrap-btn-secondary-focus-box-shadow;
      }
    }
  }
}

.swal2-select {
  display: inline-block;
  width: 100%;
  height: $bootstrap-input-height;
  padding: .375rem 1.75rem .375rem .75rem;
  transition: $bootstrap-custom-forms-transition;
  border: $bootstrap-input-border-width solid $bootstrap-input-border-color;
  border-radius: $bootstrap-input-border-radius;
  background: $bootstrap-custom-select-background;
  background-color: $bootstrap-input-bg;
  color: $bootstrap-input-color;
  vertical-align: middle;
  // @include box-shadow($custom-select-box-shadow);
  appearance: none;

  &:focus {
    border: $bootstrap-input-focus-border;
    outline: 0;
    box-shadow: $bootstrap-input-focus-box-shadow;

    &::-ms-value {
      background-color: $bootstrap-input-bg;
      color: $bootstrap-input-color;
    }
  }

  &[multiple],
  &[size]:not([size='1']) {
    height: auto;
    padding-right: $bootstrap-input-padding-x;
    background-image: none;
  }

  &:disabled {
    background-color: $bootstrap-input-disabled-bg;
    color: $bootstrap-input-disabled-color;
  }

  &::-ms-expand {
    display: none;
  }
}

.swal2-radio {
  label {
    position: relative;
    margin-right: 1rem;
    margin-left: 1.5rem;

    input {
      position: absolute;
      z-index: -1;
      opacity: 0;

      &:checked ~ .swal2-label::before {
        border-color: $bootstrap-custom-control-indicator-checked-border-color;
        background: $bootstrap-custom-control-indicator-checked-bg;
        color: $bootstrap-custom-control-indicator-checked-color;
      }

      &:focus ~ .swal2-label::before {
        box-shadow: $bootstrap-custom-control-indicator-focus-box-shadow;
      }

      &:focus:not(:checked) ~ .swal2-label::before {
        border-color: $bootstrap-custom-control-indicator-focus-border-color;
      }

      &:not(:disabled):active ~ .swal2-label::before {
        border-color: $bootstrap-custom-control-indicator-active-border-color;
        background-color: $bootstrap-custom-control-indicator-active-bg;
        color: $bootstrap-custom-control-indicator-active-color;
      }
    }

    input:checked ~ .swal2-label::after {
      background-image: $bootstrap-custom-radio-indicator-icon-checked;
    }

    input:disabled:checked ~ .swal2-label::before {
      background-color: $bootstrap-custom-control-indicator-checked-disabled-bg;
    }
  }

  .swal2-label {
    &::before {
      content: '';
      display: block;
      position: absolute;
      top: ($swal2-input-font-size * $bootstrap-input-line-height - $bootstrap-custom-control-indicator-size) / 6;
      left: -($bootstrap-custom-control-gutter + $bootstrap-custom-control-indicator-size);
      width: $bootstrap-custom-control-indicator-size;
      height: $bootstrap-custom-control-indicator-size;
      transition: $bootstrap-custom-forms-transition;
      border: $bootstrap-custom-control-indicator-border-color solid $bootstrap-custom-control-indicator-border-width;
      border-radius: $bootstrap-custom-radio-indicator-border-radius;
      background-color: $bootstrap-custom-control-indicator-bg;
      pointer-events: none;
    }

    &::after {
      content: '';
      display: block;
      position: absolute;
      top: ($swal2-input-font-size * $bootstrap-input-line-height - $bootstrap-custom-control-indicator-size) / 6;
      left: -($bootstrap-custom-control-gutter + $bootstrap-custom-control-indicator-size);
      width: $bootstrap-custom-control-indicator-size;
      height: $bootstrap-custom-control-indicator-size;
      transition: $bootstrap-custom-forms-transition;
      border: transparent solid $bootstrap-custom-control-indicator-border-width;
      background: no-repeat 50% / #{$bootstrap-custom-control-indicator-bg-size};
    }
  }
}

.swal2-checkbox {
  margin-right: 1rem;
  padding-left: 1.5rem;

  input {
    z-index: -1;
    opacity: 0;

    &:checked ~ .swal2-label::before {
      border-color: $bootstrap-custom-control-indicator-checked-border-color;
      background: $bootstrap-custom-control-indicator-checked-bg;
      color: $bootstrap-custom-control-indicator-checked-color;
    }

    &:focus ~ .swal2-label::before {
      box-shadow: $bootstrap-custom-control-indicator-focus-box-shadow;
    }

    &:focus:not(:checked) ~ .swal2-label::before {
      border-color: $bootstrap-custom-control-indicator-focus-border-color;
    }

    &:not(:disabled):active ~ .swal2-label::before {
      border-color: $bootstrap-custom-control-indicator-active-border-color;
      background-color: $bootstrap-custom-control-indicator-active-bg;
      color: $bootstrap-custom-control-indicator-active-color;
    }
  }

  input:checked ~ .swal2-label::after {
    background-image: $bootstrap-custom-checkbox-indicator-icon-checked;
  }

  input:disabled:checked ~ .swal2-label::before {
    background-color: $bootstrap-custom-control-indicator-checked-disabled-bg;
  }

  .swal2-label {
    position: relative;

    &::before {
      content: '';
      display: block;
      position: absolute;
      top: ($swal2-input-font-size * $bootstrap-input-line-height - $bootstrap-custom-control-indicator-size) / 2;
      left: -($bootstrap-custom-control-gutter + $bootstrap-custom-control-indicator-size);
      width: $bootstrap-custom-control-indicator-size;
      height: $bootstrap-custom-control-indicator-size;
      transition: $bootstrap-custom-forms-transition;
      border: $bootstrap-custom-control-indicator-border-color solid $bootstrap-custom-control-indicator-border-width;
      background-color: $bootstrap-custom-control-indicator-bg;
      pointer-events: none;
    }

    &::after {
      content: '';
      display: block;
      position: absolute;
      top: ($swal2-input-font-size * $bootstrap-input-line-height - $bootstrap-custom-control-indicator-size) / 2;
      left: -($bootstrap-custom-control-gutter + $bootstrap-custom-control-indicator-size);
      width: $bootstrap-custom-control-indicator-size;
      height: $bootstrap-custom-control-indicator-size;
      transition: $bootstrap-custom-forms-transition;
      border: transparent solid $bootstrap-custom-control-indicator-border-width;
      background: no-repeat 50% / #{$bootstrap-custom-control-indicator-bg-size};
    }
  }
}

.swal2-range {
  align-items: center;

  input {
    width: 100%;
    height: calc(#{$bootstrap-custom-range-thumb-height} + #{$bootstrap-custom-range-thumb-focus-box-shadow-width * 3});
    padding: 0;
    background-color: transparent;
    appearance: none;

    &:focus {
      outline: none;

      &::-webkit-slider-thumb {
        box-shadow: $bootstrap-custom-range-thumb-focus-box-shadow;
      }

      &::-moz-range-thumb {
        box-shadow: $bootstrap-custom-range-thumb-focus-box-shadow;
      }

      &::-ms-thumb {
        box-shadow: $bootstrap-custom-range-thumb-focus-box-shadow;
      }
    }

    &::-moz-focus-outer {
      border: 0;
    }

    &::-webkit-slider-thumb {
      width: $bootstrap-custom-range-thumb-width;
      height: $bootstrap-custom-range-thumb-height;
      margin-top: ($bootstrap-custom-range-track-height - $bootstrap-custom-range-thumb-height) / 2;
      transition: $bootstrap-custom-forms-transition;
      border: $bootstrap-custom-range-thumb-border;
      border-radius: $bootstrap-custom-range-thumb-border-radius;
      background: $bootstrap-custom-range-thumb-bg;
      box-shadow: $bootstrap-custom-range-thumb-box-shadow;
      appearance: none;

      &:active {
        background: $bootstrap-custom-range-thumb-active-bg;
      }
    }

    &::-webkit-slider-runnable-track {
      width: $bootstrap-custom-range-track-width;
      height: $bootstrap-custom-range-track-height;
      border-radius: $bootstrap-custom-range-track-border-radius;
      border-color: transparent;
      background-color: $bootstrap-custom-range-track-bg;
      box-shadow: $bootstrap-custom-range-track-box-shadow;
      color: transparent;
      cursor: $bootstrap-custom-range-track-cursor;
    }

    &::-moz-range-thumb {
      width: $bootstrap-custom-range-thumb-width;
      height: $bootstrap-custom-range-thumb-height;
      margin-top: 1rem;
      transition: $bootstrap-custom-forms-transition;
      border: $bootstrap-custom-range-thumb-border;
      border-radius: $bootstrap-custom-range-thumb-border-radius;
      background: $bootstrap-custom-range-thumb-bg;
      box-shadow: $bootstrap-custom-range-thumb-box-shadow;
      appearance: none;

      &:active {
        background: $bootstrap-custom-range-thumb-active-bg;
      }
    }

    &::-moz-range-track {
      width: $bootstrap-custom-range-track-width;
      height: $bootstrap-custom-range-track-height;
      border-radius: $bootstrap-custom-range-track-border-radius;
      border-color: transparent;
      background-color: $bootstrap-custom-range-track-bg;
      box-shadow: $bootstrap-custom-range-track-box-shadow;
      color: transparent;
      cursor: $bootstrap-custom-range-track-cursor;
    }

    &::-ms-thumb {
      width: $bootstrap-custom-range-thumb-width;
      height: $bootstrap-custom-range-thumb-height;
      margin-top: 0;
      margin-right: $bootstrap-custom-range-thumb-focus-box-shadow-width;
      margin-left: $bootstrap-custom-range-thumb-focus-box-shadow-width;
      transition: $bootstrap-custom-forms-transition;
      border: $bootstrap-custom-range-thumb-border;
      border-radius: $bootstrap-custom-range-thumb-border-radius;
      background: $bootstrap-custom-range-thumb-bg;
      box-shadow: $bootstrap-custom-range-thumb-box-shadow;
      appearance: none;

      &:active {
        background: $bootstrap-custom-range-thumb-active-bg;
      }
    }

    &::-ms-track {
      width: $bootstrap-custom-range-track-width;
      height: $bootstrap-custom-range-track-height;
      border-width: $bootstrap-custom-range-thumb-height / 2;
      border-color: transparent;
      background-color: transparent;
      box-shadow: $bootstrap-custom-range-track-box-shadow;
      color: transparent;
      cursor: $bootstrap-custom-range-track-cursor;
    }

    &::-ms-fill-lower {
      border-radius: $bootstrap-custom-range-track-border-radius;
      background-color: $bootstrap-custom-range-track-bg;
    }

    &::-ms-fill-upper {
      margin-right: 15px;
      border-radius: $bootstrap-custom-range-track-border-radius;
      background-color: $bootstrap-custom-range-track-bg;
    }

    &:disabled {
      &::-webkit-slider-thumb {
        background-color: $bootstrap-custom-range-thumb-disabled-bg;
      }

      &::-webkit-slider-runnable-track {
        cursor: default;
      }

      &::-moz-range-thumb {
        background-color: $bootstrap-custom-range-thumb-disabled-bg;
      }

      &::-moz-range-track {
        cursor: default;
      }

      &::-ms-thumb {
        background-color: $bootstrap-custom-range-thumb-disabled-bg;
      }
    }
  }
}

.swal2-validation-message {
  position: relative;
  margin-bottom: $bootstrap-alert-margin-bottom;
  padding: $bootstrap-alert-padding-y $bootstrap-alert-padding-x;
  border: $bootstrap-alert-border-width solid transparent;
  border-radius: $bootstrap-alert-border-radius;
  border-color: $bootstrap-alert-border-color;
  background: $bootstrap-alert-background;
  color: $bootstrap-alert-color;
  font-size: $bootstrap-alert-font-size;

  &::before {
    background-color: lighten($bootstrap-danger, 10%);
  }
}

.swal2-toast {
  max-width: $bootstrap-toast-max-width;
  border-radius: $bootstrap-toast-border-radius;
  background-color: $bootstrap-toast-background-color;
}

//
