<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Dompdf\\Cpdf' => $vendorDir . '/hkvstore/dompdf/lib/Cpdf.php',
    'FontLib\\AdobeFontMetrics' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/AdobeFontMetrics.php',
    'FontLib\\Autoloader' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Autoloader.php',
    'FontLib\\BinaryStream' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/BinaryStream.php',
    'FontLib\\EOT\\File' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/EOT/File.php',
    'FontLib\\EOT\\Header' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/EOT/Header.php',
    'FontLib\\EncodingMap' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/EncodingMap.php',
    'FontLib\\Exception\\FontNotFoundException' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Exception/FontNotFoundException.php',
    'FontLib\\Font' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Font.php',
    'FontLib\\Glyph\\Outline' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Glyph/Outline.php',
    'FontLib\\Glyph\\OutlineComponent' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Glyph/OutlineComponent.php',
    'FontLib\\Glyph\\OutlineComposite' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Glyph/OutlineComposite.php',
    'FontLib\\Glyph\\OutlineSimple' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Glyph/OutlineSimple.php',
    'FontLib\\Header' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Header.php',
    'FontLib\\OpenType\\File' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/OpenType/File.php',
    'FontLib\\OpenType\\TableDirectoryEntry' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/OpenType/TableDirectoryEntry.php',
    'FontLib\\Table\\DirectoryEntry' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/DirectoryEntry.php',
    'FontLib\\Table\\Table' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Table.php',
    'FontLib\\Table\\Type\\cmap' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/cmap.php',
    'FontLib\\Table\\Type\\glyf' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/glyf.php',
    'FontLib\\Table\\Type\\head' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/head.php',
    'FontLib\\Table\\Type\\hhea' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/hhea.php',
    'FontLib\\Table\\Type\\hmtx' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/hmtx.php',
    'FontLib\\Table\\Type\\kern' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/kern.php',
    'FontLib\\Table\\Type\\loca' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/loca.php',
    'FontLib\\Table\\Type\\maxp' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/maxp.php',
    'FontLib\\Table\\Type\\name' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/name.php',
    'FontLib\\Table\\Type\\nameRecord' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/nameRecord.php',
    'FontLib\\Table\\Type\\os2' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/os2.php',
    'FontLib\\Table\\Type\\post' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/Table/Type/post.php',
    'FontLib\\Tests\\FontTest' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/tests/FontLib/FontTest.php',
    'FontLib\\TrueType\\Collection' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/TrueType/Collection.php',
    'FontLib\\TrueType\\File' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/TrueType/File.php',
    'FontLib\\TrueType\\Header' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/TrueType/Header.php',
    'FontLib\\TrueType\\TableDirectoryEntry' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/TrueType/TableDirectoryEntry.php',
    'FontLib\\WOFF\\File' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/WOFF/File.php',
    'FontLib\\WOFF\\Header' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/WOFF/Header.php',
    'FontLib\\WOFF\\TableDirectoryEntry' => $vendorDir . '/hkvstore/dompdf/lib/php-font-lib/src/FontLib/WOFF/TableDirectoryEntry.php',
    'HTML5_Data' => $vendorDir . '/hkvstore/dompdf/lib/html5lib/Data.php',
    'HTML5_InputStream' => $vendorDir . '/hkvstore/dompdf/lib/html5lib/InputStream.php',
    'HTML5_Parser' => $vendorDir . '/hkvstore/dompdf/lib/html5lib/Parser.php',
    'HTML5_Tokenizer' => $vendorDir . '/hkvstore/dompdf/lib/html5lib/Tokenizer.php',
    'HTML5_TreeBuilder' => $vendorDir . '/hkvstore/dompdf/lib/html5lib/TreeBuilder.php',
    'Mobile_Detect' => $vendorDir . '/mobiledetect/mobiledetectlib/Mobile_Detect.php',
    'PasswordHash' => $baseDir . '/src/PasswordHash.php',
    'Sabberworm\\CSS\\CSSList\\AtRuleBlockList' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/CSSList/AtRuleBlockList.php',
    'Sabberworm\\CSS\\CSSList\\AtRuleBlockListTest' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/tests/Sabberworm/CSS/CSSList/AtRuleBlockListTest.php',
    'Sabberworm\\CSS\\CSSList\\CSSBlockList' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/CSSList/CSSBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSList' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/CSSList/CSSList.php',
    'Sabberworm\\CSS\\CSSList\\Document' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/CSSList/Document.php',
    'Sabberworm\\CSS\\CSSList\\DocumentTest' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/tests/Sabberworm/CSS/CSSList/DocumentTest.php',
    'Sabberworm\\CSS\\CSSList\\KeyFrame' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/CSSList/KeyFrame.php',
    'Sabberworm\\CSS\\Comment\\Comment' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Comment/Comment.php',
    'Sabberworm\\CSS\\Comment\\Commentable' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Comment/Commentable.php',
    'Sabberworm\\CSS\\OutputFormat' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/OutputFormat.php',
    'Sabberworm\\CSS\\OutputFormatTest' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/tests/Sabberworm/CSS/OutputFormatTest.php',
    'Sabberworm\\CSS\\OutputFormatter' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/OutputFormat.php',
    'Sabberworm\\CSS\\Parser' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Parser.php',
    'Sabberworm\\CSS\\ParserTest' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/tests/Sabberworm/CSS/ParserTest.php',
    'Sabberworm\\CSS\\Parsing\\OutputException' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Parsing/OutputException.php',
    'Sabberworm\\CSS\\Parsing\\ParserState' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Parsing/ParserState.php',
    'Sabberworm\\CSS\\Parsing\\SourceException' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Parsing/SourceException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedTokenException' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Parsing/UnexpectedTokenException.php',
    'Sabberworm\\CSS\\Property\\AtRule' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Property/AtRule.php',
    'Sabberworm\\CSS\\Property\\CSSNamespace' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Property/CSSNamespace.php',
    'Sabberworm\\CSS\\Property\\Charset' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Property/Charset.php',
    'Sabberworm\\CSS\\Property\\Import' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Property/Import.php',
    'Sabberworm\\CSS\\Property\\Selector' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Property/Selector.php',
    'Sabberworm\\CSS\\Renderable' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Renderable.php',
    'Sabberworm\\CSS\\RuleSet\\AtRuleSet' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/RuleSet/AtRuleSet.php',
    'Sabberworm\\CSS\\RuleSet\\DeclarationBlock' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/RuleSet/DeclarationBlock.php',
    'Sabberworm\\CSS\\RuleSet\\DeclarationBlockTest' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/tests/Sabberworm/CSS/RuleSet/DeclarationBlockTest.php',
    'Sabberworm\\CSS\\RuleSet\\LenientParsingTest' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/tests/Sabberworm/CSS/RuleSet/LenientParsingTest.php',
    'Sabberworm\\CSS\\RuleSet\\RuleSet' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/RuleSet/RuleSet.php',
    'Sabberworm\\CSS\\Rule\\Rule' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Rule/Rule.php',
    'Sabberworm\\CSS\\Settings' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Settings.php',
    'Sabberworm\\CSS\\Value\\CSSFunction' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/CSSFunction.php',
    'Sabberworm\\CSS\\Value\\CSSString' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/CSSString.php',
    'Sabberworm\\CSS\\Value\\CalcFunction' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/CalcFunction.php',
    'Sabberworm\\CSS\\Value\\CalcRuleValueList' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/CalcRuleValueList.php',
    'Sabberworm\\CSS\\Value\\Color' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/Color.php',
    'Sabberworm\\CSS\\Value\\LineName' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/LineName.php',
    'Sabberworm\\CSS\\Value\\PrimitiveValue' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/PrimitiveValue.php',
    'Sabberworm\\CSS\\Value\\RuleValueList' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/RuleValueList.php',
    'Sabberworm\\CSS\\Value\\Size' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/Size.php',
    'Sabberworm\\CSS\\Value\\URL' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/URL.php',
    'Sabberworm\\CSS\\Value\\Value' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/Value.php',
    'Sabberworm\\CSS\\Value\\ValueList' => $vendorDir . '/hkvstore/dompdf/lib/php-css-parser/lib/Sabberworm/CSS/Value/ValueList.php',
    'Svg\\DefaultStyle' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/DefaultStyle.php',
    'Svg\\Document' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Document.php',
    'Svg\\Gradient\\Stop' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Gradient/Stop.php',
    'Svg\\Style' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Style.php',
    'Svg\\Surface\\CPdf' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Surface/CPdf.php',
    'Svg\\Surface\\SurfaceCpdf' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Surface/SurfaceCpdf.php',
    'Svg\\Surface\\SurfaceGmagick' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Surface/SurfaceGmagick.php',
    'Svg\\Surface\\SurfaceInterface' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Surface/SurfaceInterface.php',
    'Svg\\Surface\\SurfacePDFLib' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Surface/SurfacePDFLib.php',
    'Svg\\Tag\\AbstractTag' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/AbstractTag.php',
    'Svg\\Tag\\Anchor' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Anchor.php',
    'Svg\\Tag\\Circle' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Circle.php',
    'Svg\\Tag\\ClipPath' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/ClipPath.php',
    'Svg\\Tag\\Ellipse' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Ellipse.php',
    'Svg\\Tag\\Group' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Group.php',
    'Svg\\Tag\\Image' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Image.php',
    'Svg\\Tag\\Line' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Line.php',
    'Svg\\Tag\\LinearGradient' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/LinearGradient.php',
    'Svg\\Tag\\Path' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Path.php',
    'Svg\\Tag\\Polygon' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Polygon.php',
    'Svg\\Tag\\Polyline' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Polyline.php',
    'Svg\\Tag\\RadialGradient' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/RadialGradient.php',
    'Svg\\Tag\\Rect' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Rect.php',
    'Svg\\Tag\\Shape' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Shape.php',
    'Svg\\Tag\\Stop' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Stop.php',
    'Svg\\Tag\\StyleTag' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/StyleTag.php',
    'Svg\\Tag\\Text' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/Text.php',
    'Svg\\Tag\\UseTag' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/src/Svg/Tag/UseTag.php',
    'Svg\\Tests\\StyleTest' => $vendorDir . '/hkvstore/dompdf/lib/php-svg-lib/tests/Svg/StyleTest.php',
    'UploadHandler' => $baseDir . '/src/UploadHandler.php',
);
