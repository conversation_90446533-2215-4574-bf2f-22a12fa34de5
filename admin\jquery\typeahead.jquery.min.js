/*!
 * typeahead.js 0.11.1
 * https://github.com/twitter/typeahead.js
 * Copyright 2013-2015 Twitter, Inc. and other contributors; Licensed MIT
 */
!function(t,e){"function"==typeof define&&define.amd?define("typeahead.js",["jquery"],(function(t){return e(t)})):"object"==typeof exports?module.exports=e(require("jquery")):e(jQuery)}(0,(function(t){var e=function(){"use strict";return{isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:t.isArray,isFunction:t.isFunction,isObject:t.isPlainObject,isUndefined:function(t){return void 0===t},isElement:function(t){return!(!t||1!==t.nodeType)},isJQuery:function(e){return e instanceof t},toStr:function(t){return e.isUndefined(t)||null===t?"":t+""},bind:t.proxy,each:function(e,n){t.each(e,(function(t,e){return n(e,t)}))},map:t.map,filter:t.grep,every:function(e,n){var i=!0;return e?(t.each(e,(function(t,s){if(!(i=n.call(null,s,t,e)))return!1})),!!i):i},some:function(e,n){var i=!1;return e?(t.each(e,(function(t,s){if(i=n.call(null,s,t,e))return!1})),!!i):i},mixin:t.extend,identity:function(t){return t},clone:function(e){return t.extend(!0,{},e)},getIdGenerator:function(){var t=0;return function(){return t++}},templatify:function(e){return t.isFunction(e)?e:function(){return String(e)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,n){var i,s;return function(){var r,a,o=this,u=arguments;return r=function(){i=null,n||(s=t.apply(o,u))},a=n&&!i,clearTimeout(i),i=setTimeout(r,e),a&&(s=t.apply(o,u)),s}},throttle:function(t,e){var n,i,s,r,a,o;return a=0,o=function(){a=new Date,s=null,r=t.apply(n,i)},function(){var u=new Date,c=e-(u-a);return n=this,i=arguments,c<=0?(clearTimeout(s),s=null,a=u,r=t.apply(n,i)):s||(s=setTimeout(o,c)),r}},stringify:function(t){return e.isString(t)?t:JSON.stringify(t)},noop:function(){}}}(),n=function(){"use strict";var t={wrapper:"twitter-typeahead",input:"tt-input",hint:"tt-hint",menu:"tt-menu",dataset:"tt-dataset",suggestion:"tt-suggestion",selectable:"tt-selectable",empty:"tt-empty",open:"tt-open",cursor:"tt-cursor",highlight:"tt-highlight"};return function(r){var a,o;return o=e.mixin({},t,r),{css:(a={css:s(),classes:o,html:n(o),selectors:i(o)}).css,html:a.html,classes:a.classes,selectors:a.selectors,mixin:function(t){e.mixin(t,a)}}};function n(t){return{wrapper:'<span class="'+t.wrapper+'"></span>',menu:'<div class="'+t.menu+'"></div>'}}function i(t){var n={};return e.each(t,(function(t,e){n[e]="."+t})),n}function s(){var t={wrapper:{position:"relative",display:"inline-block"},hint:{position:"absolute",top:"0",left:"0",borderColor:"transparent",boxShadow:"none",opacity:"1"},input:{position:"relative",verticalAlign:"top",backgroundColor:"transparent"},inputWithNoHint:{position:"relative",verticalAlign:"top"},menu:{position:"absolute",top:"100%",left:"0",zIndex:"100",display:"none"},ltr:{left:"0",right:"auto"},rtl:{left:"auto",right:" 0"}};return e.isMsie()&&e.mixin(t.input,{backgroundImage:"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"}),t}}(),i=function(){"use strict";var n;function i(e){e&&e.el||t.error("EventBus initialized without el"),this.$el=t(e.el)}return"typeahead:",n={render:"rendered",cursorchange:"cursorchanged",select:"selected",autocomplete:"autocompleted"},e.mixin(i.prototype,{_trigger:function(e,n){var i;return i=t.Event("typeahead:"+e),(n=n||[]).unshift(i),this.$el.trigger.apply(this.$el,n),i},before:function(t){var e;return e=[].slice.call(arguments,1),this._trigger("before"+t,e).isDefaultPrevented()},trigger:function(t){var e;this._trigger(t,[].slice.call(arguments,1)),(e=n[t])&&this._trigger(e,[].slice.call(arguments,1))}}),i}(),s=function(){"use strict";var t=/\s+/,e=function(){var t;t=window.setImmediate?function(t){setImmediate((function(){t()}))}:function(t){setTimeout((function(){t()}),0)};return t}();return{onSync:function(t,e,i){return n.call(this,"sync",t,e,i)},onAsync:function(t,e,i){return n.call(this,"async",t,e,i)},off:function(e){var n;if(!this._callbacks)return this;e=e.split(t);for(;n=e.shift();)delete this._callbacks[n];return this},trigger:function(n){var s,r,a,o,u;if(!this._callbacks)return this;n=n.split(t),a=[].slice.call(arguments,1);for(;(s=n.shift())&&(r=this._callbacks[s]);)o=i(r.sync,this,[s].concat(a)),u=i(r.async,this,[s].concat(a)),o()&&e(u);return this}};function n(e,n,i,s){var r;if(!i)return this;for(n=n.split(t),i=s?function(t,e){return t.bind?t.bind(e):function(){t.apply(e,[].slice.call(arguments,0))}}(i,s):i,this._callbacks=this._callbacks||{};r=n.shift();)this._callbacks[r]=this._callbacks[r]||{sync:[],async:[]},this._callbacks[r][e].push(i);return this}function i(t,e,n){return function(){for(var i,s=0,r=t.length;!i&&s<r;s+=1)i=!1===t[s].apply(e,n);return!i}}}(),r=function(t){"use strict";var n={node:null,pattern:null,tagName:"strong",className:null,wordsOnly:!1,caseSensitive:!1};return function(i){var s;(i=e.mixin({},n,i)).node&&i.pattern&&(i.pattern=e.isArray(i.pattern)?i.pattern:[i.pattern],s=function(t,n,i){for(var s,r=[],a=0,o=t.length;a<o;a++)r.push(e.escapeRegExChars(t[a]));return s=i?"\\b("+r.join("|")+")\\b":"("+r.join("|")+")",n?new RegExp(s):new RegExp(s,"i")}(i.pattern,i.caseSensitive,i.wordsOnly),function t(e,n){for(var i,s=0;s<e.childNodes.length;s++)3===(i=e.childNodes[s]).nodeType?s+=n(i)?1:0:t(i,n)}(i.node,(function(e){var n,r,a;(n=s.exec(e.data))&&(a=t.createElement(i.tagName),i.className&&(a.className=i.className),(r=e.splitText(n.index)).splitText(n[0].length),a.appendChild(r.cloneNode(!0)),e.parentNode.replaceChild(a,r));return!!n})))}}(window.document),a=function(){"use strict";var n;function i(n,i){var s;(n=n||{}).input||t.error("input is missing"),i.mixin(this),this.$hint=t(n.hint),this.$input=t(n.input),this.query=this.$input.val(),this.queryWhenFocused=this.hasFocus()?this.query:null,this.$overflowHelper=(s=this.$input,t('<pre aria-hidden="true"></pre>').css({position:"absolute",visibility:"hidden",whiteSpace:"pre",fontFamily:s.css("font-family"),fontSize:s.css("font-size"),fontStyle:s.css("font-style"),fontVariant:s.css("font-variant"),fontWeight:s.css("font-weight"),wordSpacing:s.css("word-spacing"),letterSpacing:s.css("letter-spacing"),textIndent:s.css("text-indent"),textRendering:s.css("text-rendering"),textTransform:s.css("text-transform")}).insertAfter(s)),this._checkLanguageDirection(),0===this.$hint.length&&(this.setHint=this.getHint=this.clearHint=this.clearHintIfInvalid=e.noop)}return n={9:"tab",27:"esc",37:"left",39:"right",13:"enter",38:"up",40:"down"},i.normalizeQuery=function(t){return e.toStr(t).replace(/^\s*/g,"").replace(/\s{2,}/g," ")},e.mixin(i.prototype,s,{_onBlur:function(){this.resetInputValue(),this.trigger("blurred")},_onFocus:function(){this.queryWhenFocused=this.query,this.trigger("focused")},_onKeydown:function(t){var e=n[t.which||t.keyCode];this._managePreventDefault(e,t),e&&this._shouldTrigger(e,t)&&this.trigger(e+"Keyed",t)},_onInput:function(){this._setQuery(this.getInputValue()),this.clearHintIfInvalid(),this._checkLanguageDirection()},_managePreventDefault:function(t,e){var n;switch(t){case"up":case"down":n=!r(e);break;default:n=!1}n&&e.preventDefault()},_shouldTrigger:function(t,e){var n;switch(t){case"tab":n=!r(e);break;default:n=!0}return n},_checkLanguageDirection:function(){var t=(this.$input.css("direction")||"ltr").toLowerCase();this.dir!==t&&(this.dir=t,this.$hint.attr("dir",t),this.trigger("langDirChanged",t))},_setQuery:function(t,e){var n,s,r,a;r=t,a=this.query,s=!!(n=i.normalizeQuery(r)===i.normalizeQuery(a))&&this.query.length!==t.length,this.query=t,e||n?!e&&s&&this.trigger("whitespaceChanged",this.query):this.trigger("queryChanged",this.query)},bind:function(){var t,i,s,r,a=this;return t=e.bind(this._onBlur,this),i=e.bind(this._onFocus,this),s=e.bind(this._onKeydown,this),r=e.bind(this._onInput,this),this.$input.on("blur.tt",t).on("focus.tt",i).on("keydown.tt",s),!e.isMsie()||e.isMsie()>9?this.$input.on("input.tt",r):this.$input.on("keydown.tt keypress.tt cut.tt paste.tt",(function(t){n[t.which||t.keyCode]||e.defer(e.bind(a._onInput,a,t))})),this},focus:function(){this.$input.focus()},blur:function(){this.$input.blur()},getLangDir:function(){return this.dir},getQuery:function(){return this.query||""},setQuery:function(t,e){this.setInputValue(t),this._setQuery(t,e)},hasQueryChangedSinceLastFocus:function(){return this.query!==this.queryWhenFocused},getInputValue:function(){return this.$input.val()},setInputValue:function(t){this.$input.val(t),this.clearHintIfInvalid(),this._checkLanguageDirection()},resetInputValue:function(){this.setInputValue(this.query)},getHint:function(){return this.$hint.val()},setHint:function(t){this.$hint.val(t)},clearHint:function(){this.setHint("")},clearHintIfInvalid:function(){var t,e,n;n=(t=this.getInputValue())!==(e=this.getHint())&&0===e.indexOf(t),!(""!==t&&n&&!this.hasOverflow())&&this.clearHint()},hasFocus:function(){return this.$input.is(":focus")},hasOverflow:function(){var t=this.$input.width()-2;return this.$overflowHelper.text(this.getInputValue()),this.$overflowHelper.width()>=t},isCursorAtEnd:function(){var t,n,i;return t=this.$input.val().length,n=this.$input[0].selectionStart,e.isNumber(n)?n===t:!document.selection||((i=document.selection.createRange()).moveStart("character",-t),t===i.text.length)},destroy:function(){this.$hint.off(".tt"),this.$input.off(".tt"),this.$overflowHelper.remove(),this.$hint=this.$input=this.$overflowHelper=t("<div>")}}),i;function r(t){return t.altKey||t.ctrlKey||t.metaKey||t.shiftKey}}(),o=function(){"use strict";var n,i;function a(n,s){var r,a,o,u;(n=n||{}).templates=n.templates||{},n.templates.notFound=n.templates.notFound||n.templates.empty,n.source||t.error("missing source"),n.node||t.error("missing node"),n.name&&(r=n.name,!/^[_a-zA-Z0-9-]+$/.test(r))&&t.error("invalid dataset name: "+n.name),s.mixin(this),this.highlight=!!n.highlight,this.name=n.name||i(),this.limit=n.limit||5,this.displayFn=(a=(a=n.display||n.displayKey)||e.stringify,e.isFunction(a)?a:function(t){return t[a]}),this.templates=(o=n.templates,u=this.displayFn,{notFound:o.notFound&&e.templatify(o.notFound),pending:o.pending&&e.templatify(o.pending),header:o.header&&e.templatify(o.header),footer:o.footer&&e.templatify(o.footer),suggestion:o.suggestion||function(e){return t("<div>").text(u(e))}}),this.source=n.source.__ttAdapter?n.source.__ttAdapter():n.source,this.async=e.isUndefined(n.async)?this.source.length>2:!!n.async,this._resetLastSuggestion(),this.$el=t(n.node).addClass(this.classes.dataset).addClass(this.classes.dataset+"-"+this.name)}return n={val:"tt-selectable-display",obj:"tt-selectable-object"},i=e.getIdGenerator(),a.extractData=function(e){var i=t(e);return i.data(n.obj)?{val:i.data(n.val)||"",obj:i.data(n.obj)||null}:null},e.mixin(a.prototype,s,{_overwrite:function(t,e){(e=e||[]).length?this._renderSuggestions(t,e):this.async&&this.templates.pending?this._renderPending(t):!this.async&&this.templates.notFound?this._renderNotFound(t):this._empty(),this.trigger("rendered",this.name,e,!1)},_append:function(t,e){(e=e||[]).length&&this.$lastSuggestion.length?this._appendSuggestions(t,e):e.length?this._renderSuggestions(t,e):!this.$lastSuggestion.length&&this.templates.notFound&&this._renderNotFound(t),this.trigger("rendered",this.name,e,!0)},_renderSuggestions:function(t,e){var n;n=this._getSuggestionsFragment(t,e),this.$lastSuggestion=n.children().last(),this.$el.html(n).prepend(this._getHeader(t,e)).append(this._getFooter(t,e))},_appendSuggestions:function(t,e){var n,i;i=(n=this._getSuggestionsFragment(t,e)).children().last(),this.$lastSuggestion.after(n),this.$lastSuggestion=i},_renderPending:function(t){var e=this.templates.pending;this._resetLastSuggestion(),e&&this.$el.html(e({query:t,dataset:this.name}))},_renderNotFound:function(t){var e=this.templates.notFound;this._resetLastSuggestion(),e&&this.$el.html(e({query:t,dataset:this.name}))},_empty:function(){this.$el.empty(),this._resetLastSuggestion()},_getSuggestionsFragment:function(i,s){var a,o=this;return a=document.createDocumentFragment(),e.each(s,(function(e){var s,r;r=o._injectQuery(i,e),s=t(o.templates.suggestion(r)).data(n.obj,e).data(n.val,o.displayFn(e)).addClass(o.classes.suggestion+" "+o.classes.selectable),a.appendChild(s[0])})),this.highlight&&r({className:this.classes.highlight,node:a,pattern:i}),t(a)},_getFooter:function(t,e){return this.templates.footer?this.templates.footer({query:t,suggestions:e,dataset:this.name}):null},_getHeader:function(t,e){return this.templates.header?this.templates.header({query:t,suggestions:e,dataset:this.name}):null},_resetLastSuggestion:function(){this.$lastSuggestion=t()},_injectQuery:function(t,n){return e.isObject(n)?e.mixin({_query:t},n):n},update:function(e){var n=this,i=!1,s=!1,r=0;function a(t){s||(s=!0,t=(t||[]).slice(0,n.limit),r=t.length,n._overwrite(e,t),r<n.limit&&n.async&&n.trigger("asyncRequested",e))}this.cancel(),this.cancel=function(){i=!0,n.cancel=t.noop,n.async&&n.trigger("asyncCanceled",e)},this.source(e,a,(function(s){s=s||[],!i&&r<n.limit&&(n.cancel=t.noop,n._append(e,s.slice(0,n.limit-r)),r+=s.length,n.async&&n.trigger("asyncReceived",e))})),!s&&a([])},cancel:t.noop,clear:function(){this._empty(),this.cancel(),this.trigger("cleared")},isEmpty:function(){return this.$el.is(":empty")},destroy:function(){this.$el=t("<div>")}}),a}(),u=function(){"use strict";function n(n,i){var s=this;(n=n||{}).node||t.error("node is required"),i.mixin(this),this.$node=t(n.node),this.query=null,this.datasets=e.map(n.datasets,(function(e){var n=s.$node.find(e.node).first();return e.node=n.length?n:t("<div>").appendTo(s.$node),new o(e,i)}))}return e.mixin(n.prototype,s,{_onSelectableClick:function(e){this.trigger("selectableClicked",t(e.currentTarget))},_onRendered:function(t,e,n,i){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetRendered",e,n,i)},_onCleared:function(){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetCleared")},_propagate:function(){this.trigger.apply(this,arguments)},_allDatasetsEmpty:function(){return e.every(this.datasets,(function(t){return t.isEmpty()}))},_getSelectables:function(){return this.$node.find(this.selectors.selectable)},_removeCursor:function(){var t=this.getActiveSelectable();t&&t.removeClass(this.classes.cursor)},_ensureVisible:function(t){var e,n,i,s;n=(e=t.position().top)+t.outerHeight(!0),i=this.$node.scrollTop(),s=this.$node.height()+parseInt(this.$node.css("paddingTop"),10)+parseInt(this.$node.css("paddingBottom"),10),e<0?this.$node.scrollTop(i+e):s<n&&this.$node.scrollTop(i+(n-s))},bind:function(){var t,n=this;return t=e.bind(this._onSelectableClick,this),this.$node.on("click.tt",this.selectors.selectable,t),e.each(this.datasets,(function(t){t.onSync("asyncRequested",n._propagate,n).onSync("asyncCanceled",n._propagate,n).onSync("asyncReceived",n._propagate,n).onSync("rendered",n._onRendered,n).onSync("cleared",n._onCleared,n)})),this},isOpen:function(){return this.$node.hasClass(this.classes.open)},open:function(){this.$node.addClass(this.classes.open)},close:function(){this.$node.removeClass(this.classes.open),this._removeCursor()},setLanguageDirection:function(t){this.$node.attr("dir",t)},selectableRelativeToCursor:function(t){var e,n,i;return n=this.getActiveSelectable(),e=this._getSelectables(),-1===(i=(i=((i=(n?e.index(n):-1)+t)+1)%(e.length+1)-1)<-1?e.length-1:i)?null:e.eq(i)},setCursor:function(t){this._removeCursor(),(t=t&&t.first())&&(t.addClass(this.classes.cursor),this._ensureVisible(t))},getSelectableData:function(t){return t&&t.length?o.extractData(t):null},getActiveSelectable:function(){var t=this._getSelectables().filter(this.selectors.cursor).first();return t.length?t:null},getTopSelectable:function(){var t=this._getSelectables().first();return t.length?t:null},update:function(t){var n=t!==this.query;return n&&(this.query=t,e.each(this.datasets,(function(e){e.update(t)}))),n},empty:function(){e.each(this.datasets,(function(t){t.clear()})),this.query=null,this.$node.addClass(this.classes.empty)},destroy:function(){this.$node.off(".tt"),this.$node=t("<div>"),e.each(this.datasets,(function(t){t.destroy()}))}}),n}(),c=function(){"use strict";var t=u.prototype;function n(){u.apply(this,[].slice.call(arguments,0))}return e.mixin(n.prototype,u.prototype,{open:function(){return!this._allDatasetsEmpty()&&this._show(),t.open.apply(this,[].slice.call(arguments,0))},close:function(){return this._hide(),t.close.apply(this,[].slice.call(arguments,0))},_onRendered:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),t._onRendered.apply(this,[].slice.call(arguments,0))},_onCleared:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),t._onCleared.apply(this,[].slice.call(arguments,0))},setLanguageDirection:function(e){return this.$node.css("ltr"===e?this.css.ltr:this.css.rtl),t.setLanguageDirection.apply(this,[].slice.call(arguments,0))},_hide:function(){this.$node.hide()},_show:function(){this.$node.css("display","block")}}),n}(),h=function(){"use strict";function n(n,s){var r,a,o,u,c,h,l,d,p,f,g;(n=n||{}).input||t.error("missing input"),n.menu||t.error("missing menu"),n.eventBus||t.error("missing event bus"),s.mixin(this),this.eventBus=n.eventBus,this.minLength=e.isNumber(n.minLength)?n.minLength:1,this.input=n.input,this.menu=n.menu,this.enabled=!0,this.active=!1,this.input.hasFocus()&&this.activate(),this.dir=this.input.getLangDir(),this._hacks(),this.menu.bind().onSync("selectableClicked",this._onSelectableClicked,this).onSync("asyncRequested",this._onAsyncRequested,this).onSync("asyncCanceled",this._onAsyncCanceled,this).onSync("asyncReceived",this._onAsyncReceived,this).onSync("datasetRendered",this._onDatasetRendered,this).onSync("datasetCleared",this._onDatasetCleared,this),r=i(this,"activate","open","_onFocused"),a=i(this,"deactivate","_onBlurred"),o=i(this,"isActive","isOpen","_onEnterKeyed"),u=i(this,"isActive","isOpen","_onTabKeyed"),c=i(this,"isActive","_onEscKeyed"),h=i(this,"isActive","open","_onUpKeyed"),l=i(this,"isActive","open","_onDownKeyed"),d=i(this,"isActive","isOpen","_onLeftKeyed"),p=i(this,"isActive","isOpen","_onRightKeyed"),f=i(this,"_openIfActive","_onQueryChanged"),g=i(this,"_openIfActive","_onWhitespaceChanged"),this.input.bind().onSync("focused",r,this).onSync("blurred",a,this).onSync("enterKeyed",o,this).onSync("tabKeyed",u,this).onSync("escKeyed",c,this).onSync("upKeyed",h,this).onSync("downKeyed",l,this).onSync("leftKeyed",d,this).onSync("rightKeyed",p,this).onSync("queryChanged",f,this).onSync("whitespaceChanged",g,this).onSync("langDirChanged",this._onLangDirChanged,this)}return e.mixin(n.prototype,{_hacks:function(){var n,i;n=this.input.$input||t("<div>"),i=this.menu.$node||t("<div>"),n.on("blur.tt",(function(t){var s,r,a;s=document.activeElement,r=i.is(s),a=i.has(s).length>0,e.isMsie()&&(r||a)&&(t.preventDefault(),t.stopImmediatePropagation(),e.defer((function(){n.focus()})))})),i.on("mousedown.tt",(function(t){t.preventDefault()}))},_onSelectableClicked:function(t,e){this.select(e)},_onDatasetCleared:function(){this._updateHint()},_onDatasetRendered:function(t,e,n,i){this._updateHint(),this.eventBus.trigger("render",n,i,e)},_onAsyncRequested:function(t,e,n){this.eventBus.trigger("asyncrequest",n,e)},_onAsyncCanceled:function(t,e,n){this.eventBus.trigger("asynccancel",n,e)},_onAsyncReceived:function(t,e,n){this.eventBus.trigger("asyncreceive",n,e)},_onFocused:function(){this._minLengthMet()&&this.menu.update(this.input.getQuery())},_onBlurred:function(){this.input.hasQueryChangedSinceLastFocus()&&this.eventBus.trigger("change",this.input.getQuery())},_onEnterKeyed:function(t,e){var n;(n=this.menu.getActiveSelectable())&&this.select(n)&&e.preventDefault()},_onTabKeyed:function(t,e){var n;(n=this.menu.getActiveSelectable())?this.select(n)&&e.preventDefault():(n=this.menu.getTopSelectable())&&this.autocomplete(n)&&e.preventDefault()},_onEscKeyed:function(){this.close()},_onUpKeyed:function(){this.moveCursor(-1)},_onDownKeyed:function(){this.moveCursor(1)},_onLeftKeyed:function(){"rtl"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onRightKeyed:function(){"ltr"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onQueryChanged:function(t,e){this._minLengthMet(e)?this.menu.update(e):this.menu.empty()},_onWhitespaceChanged:function(){this._updateHint()},_onLangDirChanged:function(t,e){this.dir!==e&&(this.dir=e,this.menu.setLanguageDirection(e))},_openIfActive:function(){this.isActive()&&this.open()},_minLengthMet:function(t){return(t=e.isString(t)?t:this.input.getQuery()||"").length>=this.minLength},_updateHint:function(){var t,n,i,s,r,o;t=this.menu.getTopSelectable(),n=this.menu.getSelectableData(t),i=this.input.getInputValue(),!n||e.isBlankString(i)||this.input.hasOverflow()?this.input.clearHint():(s=a.normalizeQuery(i),r=e.escapeRegExChars(s),(o=new RegExp("^(?:"+r+")(.+$)","i").exec(n.val))&&this.input.setHint(i+o[1]))},isEnabled:function(){return this.enabled},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},isActive:function(){return this.active},activate:function(){return!!this.isActive()||!(!this.isEnabled()||this.eventBus.before("active"))&&(this.active=!0,this.eventBus.trigger("active"),!0)},deactivate:function(){return!this.isActive()||!this.eventBus.before("idle")&&(this.active=!1,this.close(),this.eventBus.trigger("idle"),!0)},isOpen:function(){return this.menu.isOpen()},open:function(){return this.isOpen()||this.eventBus.before("open")||(this.menu.open(),this._updateHint(),this.eventBus.trigger("open")),this.isOpen()},close:function(){return this.isOpen()&&!this.eventBus.before("close")&&(this.menu.close(),this.input.clearHint(),this.input.resetInputValue(),this.eventBus.trigger("close")),!this.isOpen()},setVal:function(t){this.input.setQuery(e.toStr(t))},getVal:function(){return this.input.getQuery()},select:function(t){var e=this.menu.getSelectableData(t);return!(!e||this.eventBus.before("select",e.obj))&&(this.input.setQuery(e.val,!0),this.eventBus.trigger("select",e.obj),this.close(),!0)},autocomplete:function(t){var e,n;return e=this.input.getQuery(),!(!((n=this.menu.getSelectableData(t))&&e!==n.val)||this.eventBus.before("autocomplete",n.obj))&&(this.input.setQuery(n.val),this.eventBus.trigger("autocomplete",n.obj),!0)},moveCursor:function(t){var e,n,i,s;return e=this.input.getQuery(),n=this.menu.selectableRelativeToCursor(t),s=(i=this.menu.getSelectableData(n))?i.obj:null,!(this._minLengthMet()&&this.menu.update(e))&&!this.eventBus.before("cursorchange",s)&&(this.menu.setCursor(n),i?this.input.setInputValue(i.val):(this.input.resetInputValue(),this._updateHint()),this.eventBus.trigger("cursorchange",s),!0)},destroy:function(){this.input.destroy(),this.menu.destroy()}}),n;function i(t){var n=[].slice.call(arguments,1);return function(){var i=[].slice.call(arguments);e.each(n,(function(e){return t[e].apply(t,i)}))}}}();!function(){"use strict";var s,r,o;function l(e,n){e.each((function(){var e,i=t(this);(e=i.data(r.typeahead))&&n(e,i)}))}function d(t,e){return t.clone().addClass(e.classes.hint).removeData().css(e.css.hint).css((n=t,{backgroundAttachment:n.css("background-attachment"),backgroundClip:n.css("background-clip"),backgroundColor:n.css("background-color"),backgroundImage:n.css("background-image"),backgroundOrigin:n.css("background-origin"),backgroundPosition:n.css("background-position"),backgroundRepeat:n.css("background-repeat"),backgroundSize:n.css("background-size")})).prop("readonly",!0).removeAttr("id name placeholder required").attr({autocomplete:"off",spellcheck:"false",tabindex:-1});var n}function p(t,e){t.data(r.attrs,{dir:t.attr("dir"),autocomplete:t.attr("autocomplete"),spellcheck:t.attr("spellcheck"),style:t.attr("style")}),t.addClass(e.classes.input).attr({autocomplete:"off",spellcheck:!1});try{!t.attr("dir")&&t.attr("dir","auto")}catch(t){}return t}function f(n){var i;return(i=e.isJQuery(n)||e.isElement(n)?t(n).first():[]).length?i:null}s=t.fn.typeahead,r={www:"tt-www",attrs:"tt-attrs",typeahead:"tt-typeahead"},o={initialize:function(s,o){var l;return o=e.isArray(o)?o:[].slice.call(arguments,1),l=n((s=s||{}).classNames),this.each(g);function g(){var n,g,y,m,v,_,b,S,w,A,C;e.each(o,(function(t){t.highlight=!!s.highlight})),n=t(this),g=t(l.html.wrapper),y=f(s.hint),m=f(s.menu),v=!1!==s.hint&&!y,_=!1!==s.menu&&!m,v&&(y=d(n,l)),_&&(m=t(l.html.menu).css(l.css.menu)),y&&y.val(""),n=p(n,l),(v||_)&&(g.css(l.css.wrapper),n.css(v?l.css.input:l.css.inputWithNoHint),n.wrap(g).parent().prepend(v?y:null).append(_?m:null)),C=_?c:u,b=new i({el:n}),S=new a({hint:y,input:n},l),w=new C({node:m,datasets:o},l),A=new h({input:S,menu:w,eventBus:b,minLength:s.minLength},l),n.data(r.www,l),n.data(r.typeahead,A)}},isEnabled:function(){var t;return l(this.first(),(function(e){t=e.isEnabled()})),t},enable:function(){return l(this,(function(t){t.enable()})),this},disable:function(){return l(this,(function(t){t.disable()})),this},isActive:function(){var t;return l(this.first(),(function(e){t=e.isActive()})),t},activate:function(){return l(this,(function(t){t.activate()})),this},deactivate:function(){return l(this,(function(t){t.deactivate()})),this},isOpen:function(){var t;return l(this.first(),(function(e){t=e.isOpen()})),t},open:function(){return l(this,(function(t){t.open()})),this},close:function(){return l(this,(function(t){t.close()})),this},select:function(e){var n=!1,i=t(e);return l(this.first(),(function(t){n=t.select(i)})),n},autocomplete:function(e){var n=!1,i=t(e);return l(this.first(),(function(t){n=t.autocomplete(i)})),n},moveCursor:function(t){var e=!1;return l(this.first(),(function(n){e=n.moveCursor(t)})),e},val:function(t){var e;return arguments.length?(l(this,(function(e){e.setVal(t)})),this):(l(this.first(),(function(t){e=t.getVal()})),e)},destroy:function(){return l(this,(function(t,n){!function(t){var n,i;n=t.data(r.www),i=t.parent().filter(n.selectors.wrapper),e.each(t.data(r.attrs),(function(n,i){e.isUndefined(n)?t.removeAttr(i):t.attr(i,n)})),t.removeData(r.typeahead).removeData(r.www).removeData(r.attr).removeClass(n.classes.input),i.length&&(t.detach().insertAfter(i),i.remove())}(n),t.destroy()})),this}},t.fn.typeahead=function(t){return o[t]?o[t].apply(this,[].slice.call(arguments,1)):o.initialize.apply(this,arguments)},t.fn.typeahead.noConflict=function(){return t.fn.typeahead=s,this}}()}));
//# sourceMappingURL=typeahead.jquery.min.js.map