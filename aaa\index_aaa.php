<?php
include("functions.php");
include("header.php");
?>
<link rel="stylesheet" href="css/litepicker.css"/>

		<!-- Page Header -->

<div id="rev_slider_490_1_wrapper" class="rev_slider_wrapper fullwidthbanner-container" data-alias="image-hero39" data-source="gallery" style="margin:0px auto;background-color:transparent;padding:0px;margin-top:0px;margin-bottom:0px;">
<!-- START REVOLUTION SLIDER 5.4.1 fullwidth mode -->
	<div id="rev_slider_490_1" class="rev_slider fullwidthabanner" style="display:none;" data-version="5.4.1">
<ul>	

<!-- SLIDE 1  -->
	<li data-index="rs-1691" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-11.jpg"  alt=""  data-bgposition="center 50%" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>

		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-1691-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500-white.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title   tp-resizeme" 
			 id="slide-1691-layer-1" 
			 data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			 data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;">Zone di sosta per Camper</div>

		<!-- LAYER 3 -->
		<div class="tp-caption NotGeneric-SubTitle   tp-resizeme" 
			id="slide-1691-layer-4" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['60','60','60','60']" 
			data-fontsize="['30','30','20','20']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 8; white-space: nowrap;text-transform:left;">Formula "all inclusive"</div>
	</li>


<!-- SLIDE 2  -->
	<li data-index="rs-1622" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-7.jpg"  alt=""  data-bgposition="center 50%" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>

		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-1691-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title   tp-resizeme" 
			id="slide-1691-layer-1" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;color:#333">Zone di sosta per Camper</div>

		<!-- LAYER 3 -->
		<div class="tp-caption NotGeneric-SubTitle   tp-resizeme" 
			id="slide-1691-layer-4" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['60','60','60','60']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 8; white-space: nowrap;text-transform:left;color:#333">Pochi chilometri da Venezia</div>
	</li>


<!-- SLIDE 1  -->
	<li data-index="rs-1699" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-20.jpg"  alt=""  data-bgposition="center center" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>
		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-1699-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500-white.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title tp-resizeme" 
			 id="slide-1699-layer-1" 
			 data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			 data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;" class="txt-slider">Piazzole per Camper</div>
	</li>


<!-- SLIDE 3  -->
	<li data-index="rs-1693" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-21.jpg"  alt=""  data-bgposition="center 40%" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>

		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-1693-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500-white.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title   tp-resizeme" 
			id="slide-1693-layer-1" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;">Campeggio roulotte e tende</div>
	</li>

<!-- ###########################  -->

<!-- SLIDE 4  -->
	<li data-index="rs-11" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-22.jpg"  alt=""  data-bgposition="center 50%" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>

		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-11-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500-white.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title   tp-resizeme" 
			 id="slide-11-layer-1" 
			 data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			 data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;">Zone di sosta per Camper</div>

		<!-- LAYER 3 -->
		<div class="tp-caption NotGeneric-SubTitle   tp-resizeme" 
			id="slide-11-layer-4" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['60','60','60','60']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 8; white-space: nowrap;text-transform:left;">Pochi chilometri da Venezia</div>
	</li>


<!-- SLIDE 5  -->
	<li data-index="rs-12" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-23.jpg"  alt=""  data-bgposition="center 50%" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>

		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-12-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500-white.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title   tp-resizeme" 
			id="slide-12-layer-1" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;">Zone di sosta per Camper</div>

		<!-- LAYER 3 -->
		<div class="tp-caption NotGeneric-SubTitle   tp-resizeme" 
			id="slide-12-layer-4" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['60','60','60','60']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 8; white-space: nowrap;text-transform:left;">Pochi chilometri da Venezia</div>
	</li>


<!-- SLIDE 6  -->
	<li data-index="rs-13" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-24.jpg"  alt=""  data-bgposition="center center" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>
		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-13-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500-white.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title tp-resizeme" 
			 id="slide-13-layer-1" 
			 data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			 data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;" class="txt-slider">Piazzole per Camper</div>
	</li>


<!-- SLIDE 7  -->
	<li data-index="rs-14" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-25.jpg"  alt=""  data-bgposition="center 40%" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>

		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-14-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500-white.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title   tp-resizeme" 
			id="slide-14-layer-1" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;">Campeggio roulotte e tende</div>
	</li>

<!-- SLIDE 8  -->
	<li data-index="rs-15" data-transition="zoomout" data-slotamount="default" data-hideafterloop="0" data-hideslideonmobile="off"  data-easein="Power4.easeInOut" data-easeout="Power4.easeInOut" data-masterspeed="2000"  data-thumb="img/slider-5.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Intro" data-param1="" data-param2="" data-param3="" data-param4="" data-param5="" data-param6="" data-param7="" data-param8="" data-param9="" data-param10="" data-description="">
		<!-- MAIN IMAGE -->
		<img src="img/slider-26.jpg"  alt=""  data-bgposition="center 40%" data-bgfit="cover" data-bgrepeat="no-repeat" data-bgparallax="10" class="rev-slidebg" data-no-retina>

		<!-- LAYER 1 -->
		<div class="tp-caption NotGeneric-Icon   tp-resizeme" 
			id="slide-15-layer-8" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['-140','-68','-78','-78']" 
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;","mask":"x:0px;y:[100%];s:inherit;e:inherit;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power4.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;s:inherit;e:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[0,0,0,0]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[0,0,0,0]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 6; "><img class="img_slider" src="img/Aladin-Camp-Logo-w500-white.png" style="" alt="logo" />	</div>

		<!-- LAYER 2 -->
		<div class="tp-caption NotGeneric-Title   tp-resizeme" 
			id="slide-15-layer-1" 
			data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']" 
			data-y="['middle','middle','middle','middle']" data-voffset="['0','30','30','30']" 
			data-fontsize="['70','50','40','30']"
			data-lineheight="['70','50','40','30']"
			data-width="none"
			data-height="none"
			data-whitespace="nowrap"
 			data-type="text" 
			data-responsive_offset="on" 
			data-frames='[{"from":"z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;","mask":"x:0px;y:0px;","speed":1500,"to":"o:1;","delay":1000,"ease":"Power3.easeInOut"},{"delay":"wait","speed":1000,"to":"y:[100%];","mask":"x:inherit;y:inherit;","ease":"Power2.easeInOut"}]'
			data-textAlign="['left','left','left','left']"
			data-paddingtop="[10,10,10,10]"
			data-paddingright="[0,0,0,0]"
			data-paddingbottom="[10,10,10,10]"
			data-paddingleft="[0,0,0,0]"
			style="z-index: 7; white-space: nowrap;text-transform:left;">Campeggio roulotte e tende</div>
	</li>


</ul>
<div class="tp-bannertimer tp-bottom" style="visibility: hidden !important;"></div>	</div>
</div><!-- END REVOLUTION SLIDER -->





		</div>

		<!-- Trip Search Bar -->
		<div class="container justify-content-center mt--200 search_bar_wrap">
		
                
               <div class="search-bar__container">
					<form class="form trip-search" method="post" action="checkout.php" id="form">
						<div class="form-container">
							<label style="text-align:center"><h4>Verifica<br>disponibilità:</h4>
                            <?/*<p style="font-size:0.8rem">Nel calendario selezionare entrambe le date<br/>
                            Prima la data di arrivo<br/> e senza chiudere il calendario<br/> selezionate la data di partenza
                            </p>*/?>
                            </label>
						</div>

						<div class="form-container ">
							<label class="search-bar__title decorated">Seleziona arrivo e partenza</label>
							<div class="input-group">
                                <input type="text" class="input-field" autocomplete="off" id="data_arrivo" name="data_arrivo" value="" placeholder="Arrivo" required>
								<div class="input-group-addon ">
								</div>
                                <input type="text" class="input-field" autocomplete="off" id="data_partenza" name="data_partenza" value=""  placeholder="Partenza" required>
							</div>
                            
						</div>
	
						<div class="form-container">
							<label class="search-bar__title decorated">Numero persone</label>
							<select class="input__field selectpicker" id="carType" data-size="6" data-style="custom-select" autocomplete="off" name="persone">
								<option value="1">1</option>
								<option value="2">2</option>
								<option value="3">3</option>
								<option value="4">4</option>
								<option value="5">5</option>
								<option value="6">6</option>
								<option value="7">7</option>
								<option value="8">8</option>
							</select>
						</div>
						<button type="submit" class="btn btn-primary accent">Calcola</button>
					</form>
				</div>
                
            
		</div>

	</header>

	<main>



					<div class="tab-content">
						<!-- Tab #1 -->
						<div class="tab-pane active" id="home" 	role="tabpanel">
							<div class="container">

								<div class="row justify-content-center">
									<div class="col-12 col-md-8 mt-60">
										<h4 class="text-center">Caratteristiche e servizi:</h4>
									</div>
								</div>
								<!-- Features -->
								<div class="row mt-30">
									<div class="col-12 d-flex justify-content-between feature-icon-block-container">

										<div class="" style="text-align:center;">
											<img src="img/videocamera.png" style="text-align:center;width:75px;" /><p class="feature-text">Zona<br>video<br>sorvegliata</p>
										</div>

										<div class="" style="text-align:center;">
											<img src="img/piazzola.png" style="width:75px;" /><p class="feature-text">Piazzola<br>privacy<br>erba 60m<sup>2</sup></p>
										</div>

										<div class="" style="text-align:center;">
											<img src="img/recinto.png" style="width:75px;" /><p class="feature-text">Zona<br>recintata</p>
										</div>

										<div class="" style="text-align:center;">
											<img src="img/animali.png" style="width:75px;" /><p class="feature-text">Animali<br>ammessi</p>
										</div>

										<div class="" style="text-align:center;">
											<img src="img/scarico.png" style="width:75px;" /><p class="feature-text">Scarico<br>camper</p>
										</div>



										<div class="" style="text-align:center;">
											<img src="img/doccia-calda.png" style="width:75px;" /><p class="feature-text">Doccia<br>calda</p>
										</div>

										<div class="" style="text-align:center;">
											<img src="img/energia.png" style="width:75px;" /><p class="feature-text">Energia<br>elettrica</p>
										</div>

										<div class="" style="text-align:center;">
											<img src="img/barbecue.png" style="width:75px;" /><p class="feature-text">Barbecue</p>
										</div>

										<div class="" style="text-align:center;">
											<img src="img/bath.png" style="width:75px;" /><p class="feature-text">Servizi<br>igienici</p>
										</div>

										<div class="" style="text-align:center;">
											<img src="img/check.png" style="width:75px;" /><p class="feature-text">Check<br>H24</p>
										</div>


									</div>
								</div>


							</div>
						</div><!-- End of Tab #1 --><br><br>


		<!-- About Section -->
		<section class="section about  mb-100">
			<div class="container">
				<div class="row">
					<!-- Left block with images -->
					<div class="col-12 col-lg-6 col-md-12 ">
						<div class="images-container">
							<img src="img/home2.jpg" alt="image" class="image-small">
						</div>
					</div>

					<!-- Left block with text info -->
					<div class="col-12 col-lg-6 col-md-12 mt-40">
						<div class="heading heading-left-align heading-on-light heading-about">
							<h2 class="heading-title  " data--duration="1s" data--delay="0.8s">Benvenuti a Aladin Camp</h2>
							<div class="separator-container left  " data--duration="1s" data--delay="0.8s">
								<div class="separator"></div>
							</div>
							<div class="subtitle extra-small  " data--duration="1s" data--delay="1s">Aladin Camp, la natura diventa magia.</div>
							<p class="heading-text  " data--duration="1s" data--delay="1.1s">
									Gestito da una storica famiglia di agricoltori locali, l’agricampeggio Aladin, a Oriago di Mira (Venezia), è situato in una posizione strategica: a circa 900 m dal casello autostradale, da cui è facilmente raggiungibile Venezia (15 km), Padova (30 km), Treviso (35 km) e a 500 m dalla metropolitana di superficie che permette di raggiungere in 15 minuti Venezia. La dantesca cittadina che ospita la struttura si sviluppa lungo il fiume Brenta, offrendo la possibilità di visitare le storiche ville palladiane a due passi dallo stesso agricampeggio. <br><br>

Il camping, appena inaugurato, è collocato in una zona di autentica campagna, pur continuando ad offrire i comfort cittadini e attrazioni culturali entusiasmanti essendo alle porte della Repubblica Serenissima. Inoltre, per gli appassionati di storia, l’agricampeggio Aladin gode della vicinanza ai famosissimi forti, costruiti all’inizio del secolo scorso a difesa della città di Venezia da attacchi provenienti da terra. A pochi passi dall’agricampeggio, ma pur sempre a Oriago, è ancora possibile vedere il termine, eretto in mattoni dopo il 1375 per marcare il confine tra le belligeranti Padova e Venezia.<br><br>

Goditi qualche giorno immerso nella natura, con tutti i servizi di cui hai bisogno, in un’area di sosta completamente nuova, ideale per qualsiasi tipo di camper.
							</p>
							<div class="heading-btn-container  " data--duration="1s" data--delay="1.2s">
								<a href="contatti.php" class="btn border--double">CONTATTACI</a>										<a target="_blank" href="img/carta-dei-servizi .pdf" class="btn btn-primary btn-lg send" data--duration="1s" data--delay="1.1s">CARTA DEI SERVIZI</a><br><br>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
        
        

      
        	<!-- Section Vehicles -->
		<section class="section light-bg vehicles" style="background-color:#f7f7f7;margin-top:-60px">
			<div class="container inner-content">
				<!-- Header -->
				<div class="row justify-content-center">
					<div class="col-6 col-heading">
						<div class="heading heading-on-light">
							<div class="icon-with-text">
								<span class="  " data--duration="1s" data--delay="0.7s">Turismo</span>
							</div>
							<h2 class="heading-title  " data--duration="1s" data--delay="0.9s">Località turistiche nei dintorni</h2>
							<div class="separator-container  " data--duration="1s" data--delay="1.1s">
								<div class="separator"></div>
							</div>
						</div>
					</div>
				</div> <!-- End of Header -->

				<!-- Vehicle Carousel -->
				<div class="row owl-carousel triple-carousel">

						<!-- Vehicle Item #2 -->
					<div class="card-container">
							<div class="card-preview">
								<img src="img/venezia-1.jpg" alt="car image" class="card-preview__image">
							</div>
							<div class="card-info">
								<div class="card-info__price">10 km</div>
								<h4 class="card-info__title">Venezia</h4>
								<ul class="card-info__description">
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>Piazza San Marco</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-bed.png"></div>Palazzo Ducale</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Canal Grande</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Il Ponte di Rialto</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Le Gallerie dell'Accademia</li>
								</ul>
							</div>
					</div>
					<!-- Vehicle Item #1 -->
					<div class="card-container">
							<div class="card-preview">
								<img src="img/padova-1.jpg" alt="car image" class="card-preview__image">
							</div>
							<div class="card-info">
								<div class="card-info__price">20 km</div>
								<h4 class="card-info__title">Padova</h4>
								<ul class="card-info__description">
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>La Cappella degli Scrovegni</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>Basilica di Sant'Antonio</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>Piazza delle Erbe e della Frutta</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>Palazzo della Ragione</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>Prato della Valle</li>

								</ul>
							</div>
					</div>

						<!-- Vehicle Item #3 -->
					<div class="card-container">
							<div class="card-preview">
								<img src="img/treviso-1.jpg" alt="car image" class="card-preview__image">
							</div>
							<div class="card-info">
								<div class="card-info__price">25 km</div>
								<h4 class="card-info__title">Treviso</h4>
								<ul class="card-info__description">
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>Piazza dei Signori</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-bed.png"></div>Il Palazzo dei Trecento</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Palazzo del Podestà e Torre Civica</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>La Chiesa di San Nicolò</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Isola della Pescheria</li>
								</ul>
							</div>
					</div>

						<!-- Vehicle Item #4 -->
					<div class="card-container">
							<div class="card-preview">
								<img src="img/dolomiti-1.jpg" alt="car image" class="card-preview__image">
							</div>
							<div class="card-info">
								<div class="card-info__price">230 km</div>
								<h4 class="card-info__title">Le Dolomiti</h4>
								<ul class="card-info__description">
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>Cortina d'Ampezzo</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-bed.png"></div>Marmolada</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>La Val di Fassa</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Madonna di Campiglio</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Il lago di Braies</li>
								</ul>
							</div>
					</div>

						<!-- Vehicle Item #3 -->
					<div class="card-container">
							<div class="card-preview">
								<img src="img/jesolo-1.jpg" alt="car image" class="card-preview__image">
							</div>
							<div class="card-info">
								<div class="card-info__price">40 km</div>
								<h4 class="card-info__title">Jesolo</h4>
								<ul class="card-info__description">
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>La spiaggia di Lido di Jesolo</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-bed.png"></div>Aqualandia </li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Sea Life Jesolo</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Piazza Mazzini</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Tropicarium Park</li>
								</ul>
							</div>
					</div>

						<!-- Vehicle Item #3 -->
					<div class="card-container">
							<div class="card-preview">
								<img src="img/riviera-del-brenta-1.jpg" alt="car image" class="card-preview__image">
							</div>
							<div class="card-info">
								<div class="card-info__price">5 km</div>
								<h4 class="card-info__title">Riviera del Brenta - Le ville</h4>
								<ul class="card-info__description">
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-gas.png"></div>Villa Foscari</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-bed.png"></div>Villa Pisani</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Villa Widmann</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Villa Grimani</li>
									<li class="card-info__item"><div class="card-icon bg-img" data-img="img/_src/card-icon-layer.png"></div>Villa Contarini dei Leoni</li>
								</ul>
							</div>
					</div>
	

				</div> <!-- End of Vehicle Carousel -->

		
			</div>
		</section>
        
        
        
        
        	<!-- Section RV Types -->
		<section class="section types">
			<!-- Heading -->
			<div class="container">
				<div class="row mt-60">
					<div class="col-6 offset-3 col-heading">
						<div class="heading heading-on-light">
							<div class="icon-with-text">
								<img src="img/Aladin-Camp-Logo-w300.png" alt="logo icon" class="" style="max-width:200px;margin-bottom:40px" data--duration="1s" data--delay="0.5s">
							</div>
							<h2 class="heading-title  " data--duration="1s" data--delay="0.9s">Veicoli che sono accolti nel nostro camping</h2>
							<div class="separator-container left  " data--duration="1s" data--delay="1.1s">
								<div class="separator"></div>
							</div>
						</div>
					</div>
				</div>
			</div><!-- End of Heading -->

			<!-- Tabs -->
			<div class="container mb-20">

					<ul class="nav nav-pills  nav-justified cr-tabs">
						<li class="nav-item">
							<a class="nav-link active" href="#home" data-toggle="tab">
								<div class="type-container">
									<div class="type-preview">
										<svg display="none">
											<symbol id="rv-2" viewBox="0 0 152 71">
												<g>
													<path d="M28.085,0.664c2.58-.805,5.341-0.245,8-0.4,3.721,0.152,7.491-.385,11.161.4-0.017,1.09-.022,2.176-0.011,3.266-3.687.727-7.452,0.241-11.178,0.375-2.651-.123-5.385.358-7.97-0.38a8.255,8.255,0,0,1,0-3.267h0Zm66.688-.089c3.186-.525,6.421-0.257,9.629-0.3,3.18,0.061,6.388-.263,9.535.347-0.011,1.118-.011,2.243,0,3.366-3.148.559-6.344,0.268-9.514,0.319a70.326,70.326,0,0,1-9.662-.28c0.005-1.152.011-2.3,0.017-3.451h0ZM1.016,8.187A2.3,2.3,0,0,1,3.535,5.569q69.185,0,138.369.017c1.544-.152,2.2,1.526,2.492,2.774,2.359,9.949,4.823,19.871,7.055,29.843a25.424,25.424,0,0,1,.474,6.1q-21.588-.017-43.163,0c-0.083-8.892.027-17.785-.045-26.671a2.009,2.009,0,0,0-2.127-2.165c-4.029-.224-8.075-0.017-12.11-0.1-1.24.107-3.136-.139-3.616,1.381a28.739,28.739,0,0,0-.177,4.822q0.025,11.358-.005,22.711-44.836.06-89.678,0.022C1.016,32.264.983,20.222,1.016,8.187m8.279,8.752c-1.224.448-.987,1.936-1.058,2.981,0.039,4.3-.005,8.591.022,12.88-0.039.929,0.138,2.36,1.323,2.388,4.421,0.179,8.852,0,13.278.084,1.014,0.033,2.607-.006,2.629-1.4,0.182-4.648-.006-9.307.088-13.96-0.033-1.074.16-2.853-1.284-3.037-2.679-.291-5.38-0.046-8.07-0.112a48.5,48.5,0,0,0-6.928.173m30.844-.05c-1.169.122-1.207,1.51-1.19,2.421,0.033,4.682-.028,9.363.022,14.044a1.708,1.708,0,0,0,2.039,1.9c11.592-.006,23.183.016,34.774-0.012a1.676,1.676,0,0,0,1.957-1.885q0.041-7.273,0-14.553a1.653,1.653,0,0,0-1.488-1.951c-3.461-.235-6.939-0.018-10.412-0.09-8.565.1-17.147-.179-25.7,0.128m81.653,0.05c-1.241.431-1,1.907-1.07,2.959,0.055,6.354-.033,12.706.039,19.055a1.7,1.7,0,0,0,1.8,2.046c4.4-.016,8.819.134,13.212-0.078,1.234-.117,1.052-1.61,1.1-2.511-0.077-6.723.056-13.451-.06-20.168,0.038-1.711-2.017-1.426-3.147-1.5-3.952.146-7.938-.234-11.867,0.2M140.1,17c-1.075.526-.761,1.907-0.871,2.886,0.039,6.174.011,12.354,0.011,18.529,0.05,0.895-.055,2.366,1.136,2.511,3.505,0.251,7.033.028,10.549,0.067-1.725-8.1-3.737-16.135-5.6-24.2A21.174,21.174,0,0,0,140.1,17h0Zm-48.069-.09c5.115-.509,10.313-0.162,15.461-0.151,0.1,9.178.022,18.361,0.044,27.539q-7.83,0-15.67.005,0-11.082,0-22.158a31.946,31.946,0,0,1,.165-5.235m3.7,3.73c-1,.443-0.838,1.728-0.893,2.628,0.022,2.612-.066,5.231.039,7.841a1.568,1.568,0,0,0,1.284,1.79,41.8,41.8,0,0,0,7.144,0,1.526,1.526,0,0,0,1.251-1.773c0.061-2.993.077-5.989,0-8.982a1.625,1.625,0,0,0-1.759-1.7,31.762,31.762,0,0,0-7.061.2h0ZM57.3,17.929c0.524,0,1.582.006,2.106,0.006q-0.016,8.07,0,16.129l-2.111.023q0.017-8.08.005-16.158h0ZM1.1,50.474A33.366,33.366,0,0,1,1.044,45.5q44.82,0.058,89.645.016c0.1,3.748-.16,7.512.116,11.247,0.138,1.214,1.51,1.622,2.541,1.555,4.437-.005,8.885.062,13.322-0.033a1.919,1.919,0,0,0,2.034-1.964c0.127-3.6-.022-7.2.039-10.794,14.391-.028,28.777-0.005,43.168-0.011,0,2.971.006,5.94,0,8.915-4.222-.016-8.444,0-12.666-0.011,0.017,2.467.017,4.928,0.022,7.388-0.463.017-1.383,0.039-1.841,0.05a11.512,11.512,0,0,0-3.329-7.164,9.89,9.89,0,0,0-12.991-.515,11.276,11.276,0,0,0-3.93,7.713c-12.474-.123-24.941-0.029-37.409-0.056a11.131,11.131,0,0,0-3.439-7.248,9.862,9.862,0,0,0-13.3-.028c-2.133,1.824-3.092,4.592-3.511,7.31l-1.973-.05a10.779,10.779,0,0,0-4.933-8.383,9.883,9.883,0,0,0-12.534,1.885,11.679,11.679,0,0,0-2.75,6.533c-2.469-.107-5.016.3-7.425-0.4-9.105-3.1-18.156-6.364-27.289-9.379a1.9,1.9,0,0,1-1.51-1.6h0Zm90.769-4.961q7.838,0,15.681.006c-0.055,3.853.094,7.712-.11,11.566-5.143.045-10.291,0.056-15.433-.011-0.264-3.848-.083-7.707-0.138-11.561h0ZM45.138,54.378a8,8,0,0,1,9.949,5.447c1.565,4.62-2.056,10.061-6.862,10.313A7.962,7.962,0,0,1,39.544,63a8.126,8.126,0,0,1,5.594-8.624m0.992,4.278a3.774,3.774,0,0,0-.97,6.336c1.924,1.813,5.286.548,5.859-1.963,0.838-2.746-2.3-5.587-4.889-4.374h0Zm22.058-4.468a8.006,8.006,0,0,1,9.227,6.029c1.268,4.62-2.409,9.8-7.149,9.939a7.961,7.961,0,0,1-8.444-6.634,8.081,8.081,0,0,1,6.366-9.334M68.217,58.7a3.794,3.794,0,0,0-2.139,4.2,3.68,3.68,0,0,0,4.663,2.773,3.72,3.72,0,0,0,2.348-4.843A3.652,3.652,0,0,0,68.217,58.7h0Zm56.811-4.34a7.987,7.987,0,0,1,10.158,6.9c0.667,4.48-3.13,8.965-7.6,8.909a7.942,7.942,0,0,1-8.208-7.718,8.075,8.075,0,0,1,5.65-8.087m0.992,4.273c-2.364.874-3.285,4.313-1.312,6.07a3.626,3.626,0,0,0,6.025-1.287c1.273-2.769-2-6.012-4.713-4.783h0Zm14.425,3.256c-0.028-2.1-.022-4.205.044-6.3,2.706,0.039,5.407.056,8.113,0a6.86,6.86,0,0,0,.336,6.331c-2.827-.135-5.66-0.1-8.493-0.028h0Zm10.351-6.32,1.108,0.067c0,2.176.468,4.631-1.185,6.354a10.006,10.006,0,0,0,.077-6.421h0Zm-1.174,5.408c-0.3-1.449-.987-3.328.331-4.474a8.3,8.3,0,0,1-.331,4.474h0Z"/>
												</g>
											</symbol>
										</svg>
										<svg class="type-preview__image grey">
											<use xlink:href="#rv-2"></use>
										</svg>
									</div>
									<div class="type-info">
										<div class="type__title">Caravan</div>
									</div>
								</div>
							</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#settings" data-toggle="tab">
								<div class="type-container">
									<div class="type-preview">
										<img src="img/tenda.png" style="max-width:144px" />
									</div>
									<div class="type-info">
										<div class="type__title">Tenda</div>
									</div>
								</div>
							</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#messages" data-toggle="tab">
								<div class="type-container">
									<div class="type-preview">
										<svg display="none">
											<symbol id="rv-4" viewBox="0 0 128 71">
												<g>
													<path d="M39.548,0.65c3.256-.518,6.561-0.19,9.844-0.268,3.108,0.062,6.233-.228,9.325.268-0.06,1.125-.093,2.249-0.252,3.368-2.068.556-4.235,0.228-6.342,0.295-4.148-.1-8.312.217-12.449-0.211C39.635,2.949,39.586,1.8,39.548.65h0ZM1.572,4.758A3.171,3.171,0,0,1,5.057,2.436c7.551-.022,15.1.006,22.649-0.005,0,1.03,0,2.06.006,3.09,14.889-.04,29.779,0,44.668-0.018a13.135,13.135,0,0,1,6.7,1.931A63.748,63.748,0,0,1,88.654,13.8a13.843,13.843,0,0,1,3.688,4.069,19.171,19.171,0,0,1,.252,4.008c-8.285-.117-16.564-0.022-24.848-0.05-0.027,5.655.06,11.316-.044,16.977-15.7-.1-31.41.011-47.109-0.056,0,1.331.006,2.666,0.017,4q-8.077.007-16.153,0,0-1.829.016-3.657c-0.668,0-1.335-.006-2,0C2.5,41.839,2.491,44.6,2.491,47.36c0.651-.011,1.313-0.022,1.975-0.028C4.461,46.214,4.45,45.1,4.456,43.982c5.379-.006,10.764.006,16.148-0.006-0.006,3.59,0,7.175-.006,10.765-4.772-.117-9.56.167-14.32-0.122-2.293-1.092-1.773-4.13-1.833-6.24-1,.018-2,0.029-2.993.039,0.005-2.96-.022-5.927.033-8.888l-0.925-.986,0.908-.79q-0.033-3.531.005-7.074C1.243,30.5.789,30.122,0.559,29.933c0.23-.245.684-0.734,0.914-0.98C1.44,26.727,1.446,24.5,1.468,22.269c-0.246-.3-0.487-0.6-0.717-0.891,1.423-2.372.416-5.327,0.717-7.949C1.232,13.219.762,12.806,0.532,12.6c0.235-.217.711-0.651,0.952-0.869a59.162,59.162,0,0,1,.088-6.968M2.557,5.342c-0.181,2.243-.033,4.5-0.071,6.746H4.466c0.066-1.8-.312-3.723.323-5.449,1.16-1.714,3.513-.974,5.242-1.158,0-.495-0.005-1.486-0.005-1.981-1.833,0-3.666-.106-5.488.033A2.023,2.023,0,0,0,2.557,5.342m8.6-1.854C11.148,4,11.121,5,11.11,5.5q3.513,0.017,7.026.005l-0.044-2.01q-3.464,0-6.933-.005m8.049,0C19.208,4,19.2,5,19.2,5.51h7.48C26.678,5,26.667,3.989,26.656,3.484q-3.727.015-7.448,0m47.525,6.624c-0.17,2.227-.881,4.82.241,6.846,3.59,0.5,7.245.078,10.862,0.212,2.95-.139,6.014.462,8.859-0.6-2.238-2.995-5.746-4.609-8.941-6.295-3.65-.429-7.354-0.05-11.021-0.162M2.5,13.168q-0.008,3.807-.005,7.609l1.97-.016q-0.008-3.783,0-7.565C3.8,13.185,3.148,13.174,2.5,13.168M15.438,18.74a71.9,71.9,0,0,0-.175,7.682,12.6,12.6,0,0,0,.268,3.824c0.394,1.152,1.74.713,2.659,0.852,6.933-.039,13.866-0.006,20.8-0.023l0.958-.885c-0.033-3.645.115-7.3-.038-10.943,0.033-1.052-1.127-1.509-1.992-1.408-6.572-.029-13.149.027-19.721-0.022-0.974.084-2.254-.173-2.758,0.924m29.254-.283a1.7,1.7,0,0,0-.979,1.836,28.4,28.4,0,0,0,.186,6.1c0.837,0.941,2.2.624,3.316,0.724a47.441,47.441,0,0,0,6.468-.2c0.17-.151.5-0.446,0.662-0.591a26.188,26.188,0,0,0,.153-6.545,1.515,1.515,0,0,0-1.784-1.414c-2.676-.006-5.363-0.15-8.022.09M2.486,21.818c0,2.538-.011,5.071.055,7.6,0.476-.022,1.434-0.072,1.91-0.1,0.005-2.5.005-5,.016-7.5H2.486M2.491,30.4c-0.006,2.538,0,5.076,0,7.614,0.651,0,1.308-.016,1.97-0.021,0-2.522-.005-5.048,0-7.576L2.491,30.4h0ZM27.022,19.069l1.368,0.043c-0.115,3.5.12,7-.1,10.493-0.306.128-.914,0.389-1.22,0.517-0.088-3.684-.022-7.369-0.049-11.053h0Zm41.949,4c6.977-.006,13.954.016,20.931-0.023a7.746,7.746,0,0,1,2.933.389,14.675,14.675,0,0,1,2.205,2.694c2.692,3.952,5.477,7.837,8.175,11.784,7.743,1.631,15.5,3.167,23.251,4.748-0.777,1.63-2.49,2.95-2.293,4.926-0.011,1.6,1.248,2.661,2.3,3.646-0.783.078-1.554,0.162-2.32,0.245q-0.09,3.682.011,7.358-2.094.008-4.17,0c-1.609-3.718-5.187-6.7-9.313-6.6-4.2-.194-7.858,2.823-9.511,6.585-16.208.055-32.416,0.022-48.619,0.016C50.864,55,47.1,51.975,42.82,52.231c-4.1-.005-7.5,2.995-9.16,6.618-3.945,0-7.885.006-11.825-.005-0.022-6.284-.011-12.573-0.011-18.864,15.721,0.039,31.442.011,47.164,0.016-0.027-5.644-.005-11.288-0.016-16.931m3.02,3.439A69.534,69.534,0,0,0,72.211,37.5c7.956-.117,15.918.073,23.88-0.1-1.964-3.461-4.69-6.373-7-9.579-0.832-.963-1.538-2.51-3.031-2.311-4.017-.016-8.033.018-12.044-0.011-0.739.045-2.063-.111-2.019,1.008m20.061-1.029c2.244,2.878,4.41,5.822,6.534,8.794,0.952,1.225,1.253,3.357,3.228,3.156-2.643-4.063-5.472-7.993-8.257-11.956-0.378,0-1.127.006-1.5,0.006M73.683,41.766c0.082,0.385.252,1.152,0.334,1.537a53.189,53.189,0,0,0,5.806.089c0.005-.412.016-1.23,0.022-1.642-2.057-.016-4.11-0.022-6.162.016h0Zm52.307,6.808c-0.739-.285-0.728-2.31.011-2.571a3.079,3.079,0,0,1-.011,2.571h0Zm-1.259,3.406c0.372,0,1.111-.011,1.483-0.011q0.009,3.055,0,6.112h-1.472c-0.016-2.032-.022-4.069-0.011-6.1h0ZM40.856,54.607a8.049,8.049,0,1,1-5.62,8.027,8.012,8.012,0,0,1,5.62-8.027m0.98,4.258a3.741,3.741,0,0,0-.98,6.329,3.6,3.6,0,0,0,5.653-1.575c1.253-2.75-1.97-6.023-4.673-4.754h0Zm67.334-4.448a7.906,7.906,0,0,1,9.182,6.48c1.012,4.669-2.955,9.6-7.672,9.44a8.035,8.035,0,0,1-1.51-15.92m0.049,4.476a3.748,3.748,0,0,0-1,6.235c1.915,1.848,5.341.628,5.91-1.932,0.788-2.766-2.337-5.549-4.914-4.3h0Zm-87.985,1.23c4.055-.155,8.11-0.044,12.164-0.077-0.071.662-.142,1.325-0.208,1.987-3.19-.084-6.381.15-9.565-0.022-1.144.011-1.68-1.185-2.391-1.887h0Zm31.607-.072c16.022,0.005,32.05-.028,48.078.017-0.115.656-.213,1.319-0.312,1.981Q76.818,62,53.036,62.038q-0.107-.993-0.2-1.986h0Zm67.471-.005q3.6,0.008,7.223.011c-0.016.663-.033,1.325-0.038,1.987-2.342-.011-4.684-0.011-7.015-0.016-0.044-.5-0.132-1.486-0.17-1.982h0Z"/>
												</g>
											</symbol>
										</svg>
		
										<svg class="type-preview__image grey">
											<use xlink:href="#rv-4"></use>
										</svg>
									</div>
									<div class="type-info">
										<div class="type__title">Van</div>
									</div>
								</div>
							</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#settings" data-toggle="tab">
								<div class="type-container">
									<div class="type-preview">
										<img src="img/roulotte.png" style="max-width:144px" />
									</div>
									<div class="type-info">
										<div class="type__title">Roulotte</div>
									</div>
								</div>
							</a>
						</li>
					</ul>


			</div> <!-- End of Tabs -->
		</section>
        
		<!-- About Section -->
		<section class="section about  mb-100">
			<div class="container">
				<div class="row">
					<!-- Left block with images -->
					<div class="col-12 col-lg-6 col-md-12 mt-40" style="padding:25px">
						<div class="images-container">
							<img src="img/venezia-unica-logo.jpg" alt="image" class="image-small">
<div class="subtitle extra-small" style="margin-top:50px" data--duration="1s" data--delay="1s">Informazione e accoglienza turistica della città di Venezia</div><br>
<p>Tutte le informazioni utili per organizzare il tuo soggiorno.<br>Acquista online il trasporto pubblico, l’ingresso ai principali luoghi di interesse e molti altri servizi.</p>

							<div class="heading-btn-container  " data--duration="1s" data--delay="1.2s">
								<a target="_blank" href="https://www.veneziaunica.it/" class="btn border--double">Vai al sito</a>	
							</div>

						</div>
					</div>

					<!-- Left block with text info -->
					<div class="col-12 col-lg-6 col-md-12 mt-40" style="padding:25px">
						<div class="images-container">
							<img src="img/920x400_APP_AVM_1.png" alt="image" class="image-small">
<div class="subtitle extra-small" style="margin-top:50px" data--duration="1s" data--delay="1s">AVM Venezia official App</div><br>
<p>AVM Venezia Official App è lo strumento ufficiale @muoversivenezia che permette l’acquisto e la convalida dei titoli di viaggio del trasporto pubblico locale AVM/Actv ed il pagamento della sosta (strisce blu) del comune di Venezia, la consultazione delle News relative ai servizi di mobilità pubblica erogati ,consultazione e la pianificazione di orari e percorsi mediante le funzioni “Calcola il percorso” (Trip Planner) e “Ricerca Orari alle Fermate</p><br><br>
						<a href="https://play.google.com/store/apps/details?id=net.pluservice.Actv&hl=it" title="" alt=""><img src="img/google-app-download.png" style="max-width:200px" alt="" /></a> <a href="https://itunes.apple.com/it/app/avm-venezia-official-app/id1051672947?mt=8" title="" alt=""><img src="img/apple-app-download.png" style="max-width:200px"  alt="" /></a>
						</div>
					</div>
				</div>
			</div>
		</section>
        
        
	</main>
<script src="js/date.format.js"></script>
<script src="js/moment.min.js"></script>
<?/*<script src="js/litepicker.js"></script>*/?>
<script src="https://cdn.jsdelivr.net/npm/litepicker/dist/bundle.js"></script>

<?php $colonne=(isMobile())?1:2; ?>

<script>
new Litepicker({
    element: document.getElementById('data_arrivo'),
    elementEnd: document.getElementById('data_partenza'),
    singleMode: false,
    allowRepick: true,

    lang: "it-IT",
    format: "DD/MM/YYYY",
    highlightedDays : [ <?=lockDays()?> ],
    lockDays: [ <?=lockDays()?> ],
    minDate: '<?=date("Y-m-d")?>',
    disallowLockDaysInRange: true,
    selectForward: false,
    numberOfColumns: <?=$colonne?>,
    numberOfMonths: <?=$colonne?>,
    singleMode: false,
    tooltipText: {
        one: 'giorno',
        other: 'giorni'
    }
})
</script>

<?php include("footer.php"); ?>