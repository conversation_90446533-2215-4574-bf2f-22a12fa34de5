<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'YaLinqo\\' => array($vendorDir . '/athari/yalinqo/YaLinqo'),
    'Svg\\' => array($vendorDir . '/phenx/php-svg-lib/src/Svg'),
    'Soundasleep\\' => array($vendorDir . '/soundasleep/html2text/src'),
    'Slim\\Views\\' => array($vendorDir . '/slim/php-view/src'),
    'Slim\\Http\\' => array($vendorDir . '/slim/http/src'),
    'Slim\\HttpCache\\' => array($vendorDir . '/slim/http-cache/src'),
    'Slim\\Flash\\' => array($vendorDir . '/slim/flash/src'),
    'Slim\\Csrf\\' => array($vendorDir . '/slim/csrf/src'),
    'Slim\\' => array($vendorDir . '/slim/slim/Slim'),
    'Selective\\SameSiteCookie\\' => array($vendorDir . '/selective/samesite-cookie/src'),
    'Sabberworm\\CSS\\' => array($vendorDir . '/sabberworm/php-css-parser/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Psr\\Http\\Server\\' => array($vendorDir . '/psr/http-server-handler/src', $vendorDir . '/psr/http-server-middleware/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-message/src', $vendorDir . '/psr/http-factory/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'PhpDocReader\\' => array($vendorDir . '/php-di/phpdoc-reader/src/PhpDocReader'),
    'PHPMaker2021\\aladin2022\\' => array($baseDir . '/src', $baseDir . '/models', $baseDir . '/controllers'),
    'PHPMailer\\PHPMailer\\' => array($vendorDir . '/phpmailer/phpmailer/src'),
    'Opis\\Closure\\' => array($vendorDir . '/opis/closure/src'),
    'Nyholm\\Psr7\\' => array($vendorDir . '/nyholm/psr7/src'),
    'Nyholm\\Psr7Server\\' => array($vendorDir . '/nyholm/psr7-server/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Invoker\\' => array($vendorDir . '/php-di/invoker/src'),
    'Http\\Message\\' => array($vendorDir . '/php-http/message-factory/src'),
    'FontLib\\' => array($vendorDir . '/phenx/php-font-lib/src/FontLib'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'FastRoute\\' => array($vendorDir . '/nikic/fast-route/src'),
    'Dompdf\\' => array($vendorDir . '/hkvstore/dompdf/src'),
    'Doctrine\\DBAL\\' => array($vendorDir . '/doctrine/dbal/lib/Doctrine/DBAL'),
    'Doctrine\\Common\\Cache\\' => array($vendorDir . '/doctrine/cache/lib/Doctrine/Common/Cache'),
    'Doctrine\\Common\\' => array($vendorDir . '/doctrine/event-manager/lib/Doctrine/Common'),
    'Dflydev\\DotAccessData\\' => array($vendorDir . '/dflydev/dot-access-data/src'),
    'Delight\\Http\\' => array($vendorDir . '/delight-im/http/src'),
    'Delight\\Cookie\\' => array($vendorDir . '/delight-im/cookie/src'),
    'Defuse\\Crypto\\' => array($vendorDir . '/defuse/php-encryption/src'),
    'DI\\' => array($vendorDir . '/php-di/php-di/src'),
);
