<?php

namespace PHPMaker2021\aladin2022;

use Doctrine\DBAL\ParameterType;

/**
 * Page class
 */
class OrdiniList extends Ordini
{
    use MessagesTrait;

    // Page ID
    public $PageID = "list";

    // Project ID
    public $ProjectID = PROJECT_ID;

    // Table name
    public $TableName = 'ordini';

    // Page object name
    public $PageObjName = "OrdiniList";

    // Rendering View
    public $RenderingView = false;

    // Grid form hidden field names
    public $FormName = "fordinilist";
    public $FormActionName = "k_action";
    public $FormBlankRowName = "k_blankrow";
    public $FormKeyCountName = "key_count";

    // Page URLs
    public $AddUrl;
    public $EditUrl;
    public $CopyUrl;
    public $DeleteUrl;
    public $ViewUrl;
    public $ListUrl;

    // Export URLs
    public $ExportPrintUrl;
    public $ExportHtmlUrl;
    public $ExportExcelUrl;
    public $ExportWordUrl;
    public $ExportXmlUrl;
    public $ExportCsvUrl;
    public $ExportPdfUrl;

    // Custom export
    public $ExportExcelCustom = false;
    public $ExportWordCustom = false;
    public $ExportPdfCustom = false;
    public $ExportEmailCustom = false;

    // Update URLs
    public $InlineAddUrl;
    public $InlineCopyUrl;
    public $InlineEditUrl;
    public $GridAddUrl;
    public $GridEditUrl;
    public $MultiDeleteUrl;
    public $MultiUpdateUrl;

    // Page headings
    public $Heading = "";
    public $Subheading = "";
    public $PageHeader;
    public $PageFooter;

    // Page terminated
    private $terminated = false;

    // Page heading
    public function pageHeading()
    {
        global $Language;
        if ($this->Heading != "") {
            return $this->Heading;
        }
        if (method_exists($this, "tableCaption")) {
            return $this->tableCaption();
        }
        return "";
    }

    // Page subheading
    public function pageSubheading()
    {
        global $Language;
        if ($this->Subheading != "") {
            return $this->Subheading;
        }
        if ($this->TableName) {
            return $Language->phrase($this->PageID);
        }
        return "";
    }

    // Page name
    public function pageName()
    {
        return CurrentPageName();
    }

    // Page URL
    public function pageUrl()
    {
        $url = ScriptName() . "?";
        if ($this->UseTokenInUrl) {
            $url .= "t=" . $this->TableVar . "&"; // Add page token
        }
        return $url;
    }

    // Show Page Header
    public function showPageHeader()
    {
        $header = $this->PageHeader;
        $this->pageDataRendering($header);
        if ($header != "") { // Header exists, display
            echo '<p id="ew-page-header">' . $header . '</p>';
        }
    }

    // Show Page Footer
    public function showPageFooter()
    {
        $footer = $this->PageFooter;
        $this->pageDataRendered($footer);
        if ($footer != "") { // Footer exists, display
            echo '<p id="ew-page-footer">' . $footer . '</p>';
        }
    }

    // Validate page request
    protected function isPageRequest()
    {
        global $CurrentForm;
        if ($this->UseTokenInUrl) {
            if ($CurrentForm) {
                return ($this->TableVar == $CurrentForm->getValue("t"));
            }
            if (Get("t") !== null) {
                return ($this->TableVar == Get("t"));
            }
        }
        return true;
    }

    // Constructor
    public function __construct()
    {
        global $Language, $DashboardReport, $DebugTimer;

        // Initialize
        $GLOBALS["Page"] = &$this;

        // Language object
        $Language = Container("language");

        // Parent constuctor
        parent::__construct();

        // Table object (ordini)
        if (!isset($GLOBALS["ordini"]) || get_class($GLOBALS["ordini"]) == PROJECT_NAMESPACE . "ordini") {
            $GLOBALS["ordini"] = &$this;
        }

        // Page URL
        $pageUrl = $this->pageUrl();

        // Initialize URLs
        $this->ExportPrintUrl = $pageUrl . "export=print";
        $this->ExportExcelUrl = $pageUrl . "export=excel";
        $this->ExportWordUrl = $pageUrl . "export=word";
        $this->ExportPdfUrl = $pageUrl . "export=pdf";
        $this->ExportHtmlUrl = $pageUrl . "export=html";
        $this->ExportXmlUrl = $pageUrl . "export=xml";
        $this->ExportCsvUrl = $pageUrl . "export=csv";
        $this->AddUrl = "OrdiniAdd";
        $this->InlineAddUrl = $pageUrl . "action=add";
        $this->GridAddUrl = $pageUrl . "action=gridadd";
        $this->GridEditUrl = $pageUrl . "action=gridedit";
        $this->MultiDeleteUrl = "OrdiniDelete";
        $this->MultiUpdateUrl = "OrdiniUpdate";

        // Table name (for backward compatibility only)
        if (!defined(PROJECT_NAMESPACE . "TABLE_NAME")) {
            define(PROJECT_NAMESPACE . "TABLE_NAME", 'ordini');
        }

        // Start timer
        $DebugTimer = Container("timer");

        // Debug message
        LoadDebugMessage();

        // Open connection
        $GLOBALS["Conn"] = $GLOBALS["Conn"] ?? $this->getConnection();

        // List options
        $this->ListOptions = new ListOptions();
        $this->ListOptions->TableVar = $this->TableVar;

        // Export options
        $this->ExportOptions = new ListOptions("div");
        $this->ExportOptions->TagClassName = "ew-export-option";

        // Import options
        $this->ImportOptions = new ListOptions("div");
        $this->ImportOptions->TagClassName = "ew-import-option";

        // Other options
        if (!$this->OtherOptions) {
            $this->OtherOptions = new ListOptionsArray();
        }
        $this->OtherOptions["addedit"] = new ListOptions("div");
        $this->OtherOptions["addedit"]->TagClassName = "ew-add-edit-option";
        $this->OtherOptions["detail"] = new ListOptions("div");
        $this->OtherOptions["detail"]->TagClassName = "ew-detail-option";
        $this->OtherOptions["action"] = new ListOptions("div");
        $this->OtherOptions["action"]->TagClassName = "ew-action-option";

        // Filter options
        $this->FilterOptions = new ListOptions("div");
        $this->FilterOptions->TagClassName = "ew-filter-option fordinilistsrch";

        // List actions
        $this->ListActions = new ListActions();
    }

    // Get content from stream
    public function getContents($stream = null): string
    {
        global $Response;
        return is_object($Response) ? $Response->getBody() : ob_get_clean();
    }

    // Is lookup
    public function isLookup()
    {
        return SameText(Route(0), Config("API_LOOKUP_ACTION"));
    }

    // Is AutoFill
    public function isAutoFill()
    {
        return $this->isLookup() && SameText(Post("ajax"), "autofill");
    }

    // Is AutoSuggest
    public function isAutoSuggest()
    {
        return $this->isLookup() && SameText(Post("ajax"), "autosuggest");
    }

    // Is modal lookup
    public function isModalLookup()
    {
        return $this->isLookup() && SameText(Post("ajax"), "modal");
    }

    // Is terminated
    public function isTerminated()
    {
        return $this->terminated;
    }

    /**
     * Terminate page
     *
     * @param string $url URL for direction
     * @return void
     */
    public function terminate($url = "")
    {
        if ($this->terminated) {
            return;
        }
        global $ExportFileName, $TempImages, $DashboardReport, $Response;

        // Page is terminated
        $this->terminated = true;

         // Page Unload event
        if (method_exists($this, "pageUnload")) {
            $this->pageUnload();
        }

        // Global Page Unloaded event (in userfn*.php)
        Page_Unloaded();

        // Export
        if ($this->CustomExport && $this->CustomExport == $this->Export && array_key_exists($this->CustomExport, Config("EXPORT_CLASSES"))) {
            $content = $this->getContents();
            if ($ExportFileName == "") {
                $ExportFileName = $this->TableVar;
            }
            $class = PROJECT_NAMESPACE . Config("EXPORT_CLASSES." . $this->CustomExport);
            if (class_exists($class)) {
                $doc = new $class(Container("ordini"));
                $doc->Text = @$content;
                if ($this->isExport("email")) {
                    echo $this->exportEmail($doc->Text);
                } else {
                    $doc->export();
                }
                DeleteTempImages(); // Delete temp images
                return;
            }
        }
        if (!IsApi() && method_exists($this, "pageRedirecting")) {
            $this->pageRedirecting($url);
        }

        // Close connection
        CloseConnections();

        // Return for API
        if (IsApi()) {
            $res = $url === true;
            if (!$res) { // Show error
                WriteJson(array_merge(["success" => false], $this->getMessages()));
            }
            return;
        } else { // Check if response is JSON
            if (StartsString("application/json", $Response->getHeaderLine("Content-type")) && $Response->getBody()->getSize()) { // With JSON response
                $this->clearMessages();
                return;
            }
        }

        // Go to URL if specified
        if ($url != "") {
            if (!Config("DEBUG") && ob_get_length()) {
                ob_end_clean();
            }
            SaveDebugMessage();
            Redirect(GetUrl($url));
        }
        return; // Return to controller
    }

    // Get records from recordset
    protected function getRecordsFromRecordset($rs, $current = false)
    {
        $rows = [];
        if (is_object($rs)) { // Recordset
            while ($rs && !$rs->EOF) {
                $this->loadRowValues($rs); // Set up DbValue/CurrentValue
                $row = $this->getRecordFromArray($rs->fields);
                if ($current) {
                    return $row;
                } else {
                    $rows[] = $row;
                }
                $rs->moveNext();
            }
        } elseif (is_array($rs)) {
            foreach ($rs as $ar) {
                $row = $this->getRecordFromArray($ar);
                if ($current) {
                    return $row;
                } else {
                    $rows[] = $row;
                }
            }
        }
        return $rows;
    }

    // Get record from array
    protected function getRecordFromArray($ar)
    {
        $row = [];
        if (is_array($ar)) {
            foreach ($ar as $fldname => $val) {
                if (array_key_exists($fldname, $this->Fields) && ($this->Fields[$fldname]->Visible || $this->Fields[$fldname]->IsPrimaryKey)) { // Primary key or Visible
                    $fld = &$this->Fields[$fldname];
                    if ($fld->HtmlTag == "FILE") { // Upload field
                        if (EmptyValue($val)) {
                            $row[$fldname] = null;
                        } else {
                            if ($fld->DataType == DATATYPE_BLOB) {
                                $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                    "/" . $fld->TableVar . "/" . $fld->Param . "/" . rawurlencode($this->getRecordKeyValue($ar))));
                                $row[$fldname] = ["type" => ContentType($val), "url" => $url, "name" => $fld->Param . ContentExtension($val)];
                            } elseif (!$fld->UploadMultiple || !ContainsString($val, Config("MULTIPLE_UPLOAD_SEPARATOR"))) { // Single file
                                $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                    "/" . $fld->TableVar . "/" . Encrypt($fld->physicalUploadPath() . $val)));
                                $row[$fldname] = ["type" => MimeContentType($val), "url" => $url, "name" => $val];
                            } else { // Multiple files
                                $files = explode(Config("MULTIPLE_UPLOAD_SEPARATOR"), $val);
                                $ar = [];
                                foreach ($files as $file) {
                                    $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                        "/" . $fld->TableVar . "/" . Encrypt($fld->physicalUploadPath() . $file)));
                                    if (!EmptyValue($file)) {
                                        $ar[] = ["type" => MimeContentType($file), "url" => $url, "name" => $file];
                                    }
                                }
                                $row[$fldname] = $ar;
                            }
                        }
                    } else {
                        if ($fld->DataType == DATATYPE_MEMO && $fld->MemoMaxLength > 0) {
                            $val = TruncateMemo($val, $fld->MemoMaxLength, $fld->TruncateMemoRemoveHtml);
                        }
                        $row[$fldname] = $val;
                    }
                }
            }
        }
        return $row;
    }

    // Get record key value from array
    protected function getRecordKeyValue($ar)
    {
        $key = "";
        if (is_array($ar)) {
            $key .= @$ar['id'];
        }
        return $key;
    }

    /**
     * Hide fields for add/edit
     *
     * @return void
     */
    protected function hideFieldsForAddEdit()
    {
        if ($this->isAdd() || $this->isCopy() || $this->isGridAdd()) {
            $this->id->Visible = false;
        }
    }

    // Lookup data
    public function lookup()
    {
        global $Language, $Security;

        // Get lookup object
        $fieldName = Post("field");
        $lookup = $this->Fields[$fieldName]->Lookup;

        // Get lookup parameters
        $lookupType = Post("ajax", "unknown");
        $pageSize = -1;
        $offset = -1;
        $searchValue = "";
        if (SameText($lookupType, "modal")) {
            $searchValue = Post("sv", "");
            $pageSize = Post("recperpage", 10);
            $offset = Post("start", 0);
        } elseif (SameText($lookupType, "autosuggest")) {
            $searchValue = Param("q", "");
            $pageSize = Param("n", -1);
            $pageSize = is_numeric($pageSize) ? (int)$pageSize : -1;
            if ($pageSize <= 0) {
                $pageSize = Config("AUTO_SUGGEST_MAX_ENTRIES");
            }
            $start = Param("start", -1);
            $start = is_numeric($start) ? (int)$start : -1;
            $page = Param("page", -1);
            $page = is_numeric($page) ? (int)$page : -1;
            $offset = $start >= 0 ? $start : ($page > 0 && $pageSize > 0 ? ($page - 1) * $pageSize : 0);
        }
        $userSelect = Decrypt(Post("s", ""));
        $userFilter = Decrypt(Post("f", ""));
        $userOrderBy = Decrypt(Post("o", ""));
        $keys = Post("keys");
        $lookup->LookupType = $lookupType; // Lookup type
        if ($keys !== null) { // Selected records from modal
            if (is_array($keys)) {
                $keys = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $keys);
            }
            $lookup->FilterFields = []; // Skip parent fields if any
            $lookup->FilterValues[] = $keys; // Lookup values
            $pageSize = -1; // Show all records
        } else { // Lookup values
            $lookup->FilterValues[] = Post("v0", Post("lookupValue", ""));
        }
        $cnt = is_array($lookup->FilterFields) ? count($lookup->FilterFields) : 0;
        for ($i = 1; $i <= $cnt; $i++) {
            $lookup->FilterValues[] = Post("v" . $i, "");
        }
        $lookup->SearchValue = $searchValue;
        $lookup->PageSize = $pageSize;
        $lookup->Offset = $offset;
        if ($userSelect != "") {
            $lookup->UserSelect = $userSelect;
        }
        if ($userFilter != "") {
            $lookup->UserFilter = $userFilter;
        }
        if ($userOrderBy != "") {
            $lookup->UserOrderBy = $userOrderBy;
        }
        $lookup->toJson($this); // Use settings from current page
    }

    // Class variables
    public $ListOptions; // List options
    public $ExportOptions; // Export options
    public $SearchOptions; // Search options
    public $OtherOptions; // Other options
    public $FilterOptions; // Filter options
    public $ImportOptions; // Import options
    public $ListActions; // List actions
    public $SelectedCount = 0;
    public $SelectedIndex = 0;
    public $DisplayRecords = 20;
    public $StartRecord;
    public $StopRecord;
    public $TotalRecords = 0;
    public $RecordRange = 10;
    public $PageSizes = "10,20,50,-1"; // Page sizes (comma separated)
    public $DefaultSearchWhere = ""; // Default search WHERE clause
    public $SearchWhere = ""; // Search WHERE clause
    public $SearchPanelClass = "ew-search-panel collapse show"; // Search Panel class
    public $SearchRowCount = 0; // For extended search
    public $SearchColumnCount = 0; // For extended search
    public $SearchFieldsPerRow = 1; // For extended search
    public $RecordCount = 0; // Record count
    public $EditRowCount;
    public $StartRowCount = 1;
    public $RowCount = 0;
    public $Attrs = []; // Row attributes and cell attributes
    public $RowIndex = 0; // Row index
    public $KeyCount = 0; // Key count
    public $RowAction = ""; // Row action
    public $MultiColumnClass = "col-sm";
    public $MultiColumnEditClass = "w-100";
    public $DbMasterFilter = ""; // Master filter
    public $DbDetailFilter = ""; // Detail filter
    public $MasterRecordExists;
    public $MultiSelectKey;
    public $Command;
    public $RestoreSearch = false;
    public $HashValue; // Hash value
    public $DetailPages;
    public $OldRecordset;

    /**
     * Page run
     *
     * @return void
     */
    public function run()
    {
        global $ExportType, $CustomExportType, $ExportFileName, $UserProfile, $Language, $Security, $CurrentForm;

        // Get export parameters
        $custom = "";
        if (Param("export") !== null) {
            $this->Export = Param("export");
            $custom = Param("custom", "");
        } elseif (IsPost()) {
            if (Post("exporttype") !== null) {
                $this->Export = Post("exporttype");
            }
            $custom = Post("custom", "");
        } elseif (Get("cmd") == "json") {
            $this->Export = Get("cmd");
        } else {
            $this->setExportReturnUrl(CurrentUrl());
        }
        $ExportFileName = $this->TableVar; // Get export file, used in header

        // Get custom export parameters
        if ($this->isExport() && $custom != "") {
            $this->CustomExport = $this->Export;
            $this->Export = "print";
        }
        $CustomExportType = $this->CustomExport;
        $ExportType = $this->Export; // Get export parameter, used in header

        // Update Export URLs
        if (Config("USE_PHPEXCEL")) {
            $this->ExportExcelCustom = false;
        }
        if (Config("USE_PHPWORD")) {
            $this->ExportWordCustom = false;
        }
        if ($this->ExportExcelCustom) {
            $this->ExportExcelUrl .= "&amp;custom=1";
        }
        if ($this->ExportWordCustom) {
            $this->ExportWordUrl .= "&amp;custom=1";
        }
        if ($this->ExportPdfCustom) {
            $this->ExportPdfUrl .= "&amp;custom=1";
        }
        $this->CurrentAction = Param("action"); // Set up current action

        // Get grid add count
        $gridaddcnt = Get(Config("TABLE_GRID_ADD_ROW_COUNT"), "");
        if (is_numeric($gridaddcnt) && $gridaddcnt > 0) {
            $this->GridAddRowCount = $gridaddcnt;
        }

        // Set up list options
        $this->setupListOptions();

        // Setup export options
        $this->setupExportOptions();
        $this->id->setVisibility();
        $this->_token->Visible = false;
        $this->data->setVisibility();
        $this->status->Visible = false;
        $this->progress->setVisibility();
        $this->persone->setVisibility();
        $this->data_arrivo->setVisibility();
        $this->data_partenza->setVisibility();
        $this->piazzola_id->setVisibility();
        $this->piazzola->setVisibility();
        $this->nome->setVisibility();
        $this->cognome->setVisibility();
        $this->importo->setVisibility();
        $this->_email->setVisibility();
        $this->prefisso->setVisibility();
        $this->telefono->setVisibility();
        $this->codice_fiscale->setVisibility();
        $this->targa->setVisibility();
        $this->country->setVisibility();
        $this->invio_whatsapp->setVisibility();
        $this->presenza_disabili->setVisibility();
        $this->note->Visible = false;
        $this->wa_inviato->setVisibility();
        $this->debug->Visible = false;
        $this->hideFieldsForAddEdit();

        // Global Page Loading event (in userfn*.php)
        Page_Loading();

        // Page Load event
        if (method_exists($this, "pageLoad")) {
            $this->pageLoad();
        }

        // Setup other options
        $this->setupOtherOptions();

        // Set up custom action (compatible with old version)
        foreach ($this->CustomActions as $name => $action) {
            $this->ListActions->add($name, $action);
        }

        // Show checkbox column if multiple action
        foreach ($this->ListActions->Items as $listaction) {
            if ($listaction->Select == ACTION_MULTIPLE && $listaction->Allow) {
                $this->ListOptions["checkbox"]->Visible = true;
                break;
            }
        }

        // Set up lookup cache
        $this->setupLookupOptions($this->piazzola_id);

        // Search filters
        $srchAdvanced = ""; // Advanced search filter
        $srchBasic = ""; // Basic search filter
        $filter = "";

        // Get command
        $this->Command = strtolower(Get("cmd"));
        if ($this->isPageRequest()) {
            // Process list action first
            if ($this->processListAction()) { // Ajax request
                $this->terminate();
                return;
            }

            // Set up records per page
            $this->setupDisplayRecords();

            // Handle reset command
            $this->resetCmd();

            // Set up Breadcrumb
            if (!$this->isExport()) {
                $this->setupBreadcrumb();
            }

            // Hide list options
            if ($this->isExport()) {
                $this->ListOptions->hideAllOptions(["sequence"]);
                $this->ListOptions->UseDropDownButton = false; // Disable drop down button
                $this->ListOptions->UseButtonGroup = false; // Disable button group
            } elseif ($this->isGridAdd() || $this->isGridEdit()) {
                $this->ListOptions->hideAllOptions();
                $this->ListOptions->UseDropDownButton = false; // Disable drop down button
                $this->ListOptions->UseButtonGroup = false; // Disable button group
            }

            // Hide options
            if ($this->isExport() || $this->CurrentAction) {
                $this->ExportOptions->hideAllOptions();
                $this->FilterOptions->hideAllOptions();
                $this->ImportOptions->hideAllOptions();
            }

            // Hide other options
            if ($this->isExport()) {
                $this->OtherOptions->hideAllOptions();
            }

            // Get default search criteria
            AddFilter($this->DefaultSearchWhere, $this->basicSearchWhere(true));
            AddFilter($this->DefaultSearchWhere, $this->advancedSearchWhere(true));

            // Get basic search values
            $this->loadBasicSearchValues();

            // Get and validate search values for advanced search
            $this->loadSearchValues(); // Get search values

            // Process filter list
            if ($this->processFilterList()) {
                $this->terminate();
                return;
            }
            if (!$this->validateSearch()) {
                // Nothing to do
            }

            // Restore search parms from Session if not searching / reset / export
            if (($this->isExport() || $this->Command != "search" && $this->Command != "reset" && $this->Command != "resetall") && $this->Command != "json" && $this->checkSearchParms()) {
                $this->restoreSearchParms();
            }

            // Call Recordset SearchValidated event
            $this->recordsetSearchValidated();

            // Set up sorting order
            $this->setupSortOrder();

            // Get basic search criteria
            if (!$this->hasInvalidFields()) {
                $srchBasic = $this->basicSearchWhere();
            }

            // Get search criteria for advanced search
            if (!$this->hasInvalidFields()) {
                $srchAdvanced = $this->advancedSearchWhere();
            }
        }

        // Restore display records
        if ($this->Command != "json" && $this->getRecordsPerPage() != "") {
            $this->DisplayRecords = $this->getRecordsPerPage(); // Restore from Session
        } else {
            $this->DisplayRecords = 20; // Load default
            $this->setRecordsPerPage($this->DisplayRecords); // Save default to Session
        }

        // Load Sorting Order
        if ($this->Command != "json") {
            $this->loadSortOrder();
        }

        // Load search default if no existing search criteria
        if (!$this->checkSearchParms()) {
            // Load basic search from default
            $this->BasicSearch->loadDefault();
            if ($this->BasicSearch->Keyword != "") {
                $srchBasic = $this->basicSearchWhere();
            }

            // Load advanced search from default
            if ($this->loadAdvancedSearchDefault()) {
                $srchAdvanced = $this->advancedSearchWhere();
            }
        }

        // Restore search settings from Session
        if (!$this->hasInvalidFields()) {
            $this->loadAdvancedSearch();
        }

        // Build search criteria
        AddFilter($this->SearchWhere, $srchAdvanced);
        AddFilter($this->SearchWhere, $srchBasic);

        // Call Recordset_Searching event
        $this->recordsetSearching($this->SearchWhere);

        // Save search criteria
        if ($this->Command == "search" && !$this->RestoreSearch) {
            $this->setSearchWhere($this->SearchWhere); // Save to Session
            $this->StartRecord = 1; // Reset start record counter
            $this->setStartRecordNumber($this->StartRecord);
        } elseif ($this->Command != "json") {
            $this->SearchWhere = $this->getSearchWhere();
        }

        // Build filter
        $filter = "";
        AddFilter($filter, $this->DbDetailFilter);
        AddFilter($filter, $this->SearchWhere);

        // Set up filter
        if ($this->Command == "json") {
            $this->UseSessionForListSql = false; // Do not use session for ListSQL
            $this->CurrentFilter = $filter;
        } else {
            $this->setSessionWhere($filter);
            $this->CurrentFilter = "";
        }

        // Export data only
        if (!$this->CustomExport && in_array($this->Export, array_keys(Config("EXPORT_CLASSES")))) {
            $this->exportData();
            $this->terminate();
            return;
        }
        if ($this->isGridAdd()) {
            $this->CurrentFilter = "0=1";
            $this->StartRecord = 1;
            $this->DisplayRecords = $this->GridAddRowCount;
            $this->TotalRecords = $this->DisplayRecords;
            $this->StopRecord = $this->DisplayRecords;
        } else {
            $this->TotalRecords = $this->listRecordCount();
            $this->StartRecord = 1;
            if ($this->DisplayRecords <= 0 || ($this->isExport() && $this->ExportAll)) { // Display all records
                $this->DisplayRecords = $this->TotalRecords;
            }
            if (!($this->isExport() && $this->ExportAll)) { // Set up start record position
                $this->setupStartRecord();
            }
            $this->Recordset = $this->loadRecordset($this->StartRecord - 1, $this->DisplayRecords);

            // Set no record found message
            if (!$this->CurrentAction && $this->TotalRecords == 0) {
                if ($this->SearchWhere == "0=101") {
                    $this->setWarningMessage($Language->phrase("EnterSearchCriteria"));
                } else {
                    $this->setWarningMessage($Language->phrase("NoRecord"));
                }
            }
        }

        // Search options
        $this->setupSearchOptions();

        // Set up search panel class
        if ($this->SearchWhere != "") {
            AppendClass($this->SearchPanelClass, "show");
        }

        // Normal return
        if (IsApi()) {
            $rows = $this->getRecordsFromRecordset($this->Recordset);
            $this->Recordset->close();
            WriteJson(["success" => true, $this->TableVar => $rows, "totalRecordCount" => $this->TotalRecords]);
            $this->terminate(true);
            return;
        }

        // Set up pager
        $this->Pager = new PrevNextPager($this->StartRecord, $this->getRecordsPerPage(), $this->TotalRecords, $this->PageSizes, $this->RecordRange, $this->AutoHidePager, $this->AutoHidePageSizeSelector);

        // Set LoginStatus / Page_Rendering / Page_Render
        if (!IsApi() && !$this->isTerminated()) {
            // Pass table and field properties to client side
            $this->toClientVar(["tableCaption"], ["caption", "Visible", "Required", "IsInvalid", "Raw"]);

            // Setup login status
            SetupLoginStatus();

            // Pass login status to client side
            SetClientVar("login", LoginStatus());

            // Global Page Rendering event (in userfn*.php)
            Page_Rendering();

            // Page Render event
            if (method_exists($this, "pageRender")) {
                $this->pageRender();
            }
        }
    }

    // Set up number of records displayed per page
    protected function setupDisplayRecords()
    {
        $wrk = Get(Config("TABLE_REC_PER_PAGE"), "");
        if ($wrk != "") {
            if (is_numeric($wrk)) {
                $this->DisplayRecords = (int)$wrk;
            } else {
                if (SameText($wrk, "all")) { // Display all records
                    $this->DisplayRecords = -1;
                } else {
                    $this->DisplayRecords = 20; // Non-numeric, load default
                }
            }
            $this->setRecordsPerPage($this->DisplayRecords); // Save to Session
            // Reset start position
            $this->StartRecord = 1;
            $this->setStartRecordNumber($this->StartRecord);
        }
    }

    // Build filter for all keys
    protected function buildKeyFilter()
    {
        global $CurrentForm;
        $wrkFilter = "";

        // Update row index and get row key
        $rowindex = 1;
        $CurrentForm->Index = $rowindex;
        $thisKey = strval($CurrentForm->getValue($this->OldKeyName));
        while ($thisKey != "") {
            $this->setKey($thisKey);
            if ($this->OldKey != "") {
                $filter = $this->getRecordFilter();
                if ($wrkFilter != "") {
                    $wrkFilter .= " OR ";
                }
                $wrkFilter .= $filter;
            } else {
                $wrkFilter = "0=1";
                break;
            }

            // Update row index and get row key
            $rowindex++; // Next row
            $CurrentForm->Index = $rowindex;
            $thisKey = strval($CurrentForm->getValue($this->OldKeyName));
        }
        return $wrkFilter;
    }

    // Get list of filters
    public function getFilterList()
    {
        global $UserProfile;

        // Initialize
        $filterList = "";
        $savedFilterList = "";
        $filterList = Concat($filterList, $this->id->AdvancedSearch->toJson(), ","); // Field id
        $filterList = Concat($filterList, $this->data->AdvancedSearch->toJson(), ","); // Field data
        $filterList = Concat($filterList, $this->progress->AdvancedSearch->toJson(), ","); // Field progress
        $filterList = Concat($filterList, $this->persone->AdvancedSearch->toJson(), ","); // Field persone
        $filterList = Concat($filterList, $this->data_arrivo->AdvancedSearch->toJson(), ","); // Field data_arrivo
        $filterList = Concat($filterList, $this->data_partenza->AdvancedSearch->toJson(), ","); // Field data_partenza
        $filterList = Concat($filterList, $this->piazzola_id->AdvancedSearch->toJson(), ","); // Field piazzola_id
        $filterList = Concat($filterList, $this->piazzola->AdvancedSearch->toJson(), ","); // Field piazzola
        $filterList = Concat($filterList, $this->nome->AdvancedSearch->toJson(), ","); // Field nome
        $filterList = Concat($filterList, $this->cognome->AdvancedSearch->toJson(), ","); // Field cognome
        $filterList = Concat($filterList, $this->importo->AdvancedSearch->toJson(), ","); // Field importo
        $filterList = Concat($filterList, $this->_email->AdvancedSearch->toJson(), ","); // Field email
        $filterList = Concat($filterList, $this->prefisso->AdvancedSearch->toJson(), ","); // Field prefisso
        $filterList = Concat($filterList, $this->telefono->AdvancedSearch->toJson(), ","); // Field telefono
        $filterList = Concat($filterList, $this->codice_fiscale->AdvancedSearch->toJson(), ","); // Field codice_fiscale
        $filterList = Concat($filterList, $this->targa->AdvancedSearch->toJson(), ","); // Field targa
        $filterList = Concat($filterList, $this->country->AdvancedSearch->toJson(), ","); // Field country
        $filterList = Concat($filterList, $this->invio_whatsapp->AdvancedSearch->toJson(), ","); // Field invio_whatsapp
        $filterList = Concat($filterList, $this->presenza_disabili->AdvancedSearch->toJson(), ","); // Field presenza_disabili
        $filterList = Concat($filterList, $this->note->AdvancedSearch->toJson(), ","); // Field note
        $filterList = Concat($filterList, $this->wa_inviato->AdvancedSearch->toJson(), ","); // Field wa_inviato
        if ($this->BasicSearch->Keyword != "") {
            $wrk = "\"" . Config("TABLE_BASIC_SEARCH") . "\":\"" . JsEncode($this->BasicSearch->Keyword) . "\",\"" . Config("TABLE_BASIC_SEARCH_TYPE") . "\":\"" . JsEncode($this->BasicSearch->Type) . "\"";
            $filterList = Concat($filterList, $wrk, ",");
        }

        // Return filter list in JSON
        if ($filterList != "") {
            $filterList = "\"data\":{" . $filterList . "}";
        }
        if ($savedFilterList != "") {
            $filterList = Concat($filterList, "\"filters\":" . $savedFilterList, ",");
        }
        return ($filterList != "") ? "{" . $filterList . "}" : "null";
    }

    // Process filter list
    protected function processFilterList()
    {
        global $UserProfile;
        if (Post("ajax") == "savefilters") { // Save filter request (Ajax)
            $filters = Post("filters");
            $UserProfile->setSearchFilters(CurrentUserName(), "fordinilistsrch", $filters);
            WriteJson([["success" => true]]); // Success
            return true;
        } elseif (Post("cmd") == "resetfilter") {
            $this->restoreFilterList();
        }
        return false;
    }

    // Restore list of filters
    protected function restoreFilterList()
    {
        // Return if not reset filter
        if (Post("cmd") !== "resetfilter") {
            return false;
        }
        $filter = json_decode(Post("filter"), true);
        $this->Command = "search";

        // Field id
        $this->id->AdvancedSearch->SearchValue = @$filter["x_id"];
        $this->id->AdvancedSearch->SearchOperator = @$filter["z_id"];
        $this->id->AdvancedSearch->SearchCondition = @$filter["v_id"];
        $this->id->AdvancedSearch->SearchValue2 = @$filter["y_id"];
        $this->id->AdvancedSearch->SearchOperator2 = @$filter["w_id"];
        $this->id->AdvancedSearch->save();

        // Field data
        $this->data->AdvancedSearch->SearchValue = @$filter["x_data"];
        $this->data->AdvancedSearch->SearchOperator = @$filter["z_data"];
        $this->data->AdvancedSearch->SearchCondition = @$filter["v_data"];
        $this->data->AdvancedSearch->SearchValue2 = @$filter["y_data"];
        $this->data->AdvancedSearch->SearchOperator2 = @$filter["w_data"];
        $this->data->AdvancedSearch->save();

        // Field progress
        $this->progress->AdvancedSearch->SearchValue = @$filter["x_progress"];
        $this->progress->AdvancedSearch->SearchOperator = @$filter["z_progress"];
        $this->progress->AdvancedSearch->SearchCondition = @$filter["v_progress"];
        $this->progress->AdvancedSearch->SearchValue2 = @$filter["y_progress"];
        $this->progress->AdvancedSearch->SearchOperator2 = @$filter["w_progress"];
        $this->progress->AdvancedSearch->save();

        // Field persone
        $this->persone->AdvancedSearch->SearchValue = @$filter["x_persone"];
        $this->persone->AdvancedSearch->SearchOperator = @$filter["z_persone"];
        $this->persone->AdvancedSearch->SearchCondition = @$filter["v_persone"];
        $this->persone->AdvancedSearch->SearchValue2 = @$filter["y_persone"];
        $this->persone->AdvancedSearch->SearchOperator2 = @$filter["w_persone"];
        $this->persone->AdvancedSearch->save();

        // Field data_arrivo
        $this->data_arrivo->AdvancedSearch->SearchValue = @$filter["x_data_arrivo"];
        $this->data_arrivo->AdvancedSearch->SearchOperator = @$filter["z_data_arrivo"];
        $this->data_arrivo->AdvancedSearch->SearchCondition = @$filter["v_data_arrivo"];
        $this->data_arrivo->AdvancedSearch->SearchValue2 = @$filter["y_data_arrivo"];
        $this->data_arrivo->AdvancedSearch->SearchOperator2 = @$filter["w_data_arrivo"];
        $this->data_arrivo->AdvancedSearch->save();

        // Field data_partenza
        $this->data_partenza->AdvancedSearch->SearchValue = @$filter["x_data_partenza"];
        $this->data_partenza->AdvancedSearch->SearchOperator = @$filter["z_data_partenza"];
        $this->data_partenza->AdvancedSearch->SearchCondition = @$filter["v_data_partenza"];
        $this->data_partenza->AdvancedSearch->SearchValue2 = @$filter["y_data_partenza"];
        $this->data_partenza->AdvancedSearch->SearchOperator2 = @$filter["w_data_partenza"];
        $this->data_partenza->AdvancedSearch->save();

        // Field piazzola_id
        $this->piazzola_id->AdvancedSearch->SearchValue = @$filter["x_piazzola_id"];
        $this->piazzola_id->AdvancedSearch->SearchOperator = @$filter["z_piazzola_id"];
        $this->piazzola_id->AdvancedSearch->SearchCondition = @$filter["v_piazzola_id"];
        $this->piazzola_id->AdvancedSearch->SearchValue2 = @$filter["y_piazzola_id"];
        $this->piazzola_id->AdvancedSearch->SearchOperator2 = @$filter["w_piazzola_id"];
        $this->piazzola_id->AdvancedSearch->save();

        // Field piazzola
        $this->piazzola->AdvancedSearch->SearchValue = @$filter["x_piazzola"];
        $this->piazzola->AdvancedSearch->SearchOperator = @$filter["z_piazzola"];
        $this->piazzola->AdvancedSearch->SearchCondition = @$filter["v_piazzola"];
        $this->piazzola->AdvancedSearch->SearchValue2 = @$filter["y_piazzola"];
        $this->piazzola->AdvancedSearch->SearchOperator2 = @$filter["w_piazzola"];
        $this->piazzola->AdvancedSearch->save();

        // Field nome
        $this->nome->AdvancedSearch->SearchValue = @$filter["x_nome"];
        $this->nome->AdvancedSearch->SearchOperator = @$filter["z_nome"];
        $this->nome->AdvancedSearch->SearchCondition = @$filter["v_nome"];
        $this->nome->AdvancedSearch->SearchValue2 = @$filter["y_nome"];
        $this->nome->AdvancedSearch->SearchOperator2 = @$filter["w_nome"];
        $this->nome->AdvancedSearch->save();

        // Field cognome
        $this->cognome->AdvancedSearch->SearchValue = @$filter["x_cognome"];
        $this->cognome->AdvancedSearch->SearchOperator = @$filter["z_cognome"];
        $this->cognome->AdvancedSearch->SearchCondition = @$filter["v_cognome"];
        $this->cognome->AdvancedSearch->SearchValue2 = @$filter["y_cognome"];
        $this->cognome->AdvancedSearch->SearchOperator2 = @$filter["w_cognome"];
        $this->cognome->AdvancedSearch->save();

        // Field importo
        $this->importo->AdvancedSearch->SearchValue = @$filter["x_importo"];
        $this->importo->AdvancedSearch->SearchOperator = @$filter["z_importo"];
        $this->importo->AdvancedSearch->SearchCondition = @$filter["v_importo"];
        $this->importo->AdvancedSearch->SearchValue2 = @$filter["y_importo"];
        $this->importo->AdvancedSearch->SearchOperator2 = @$filter["w_importo"];
        $this->importo->AdvancedSearch->save();

        // Field email
        $this->_email->AdvancedSearch->SearchValue = @$filter["x__email"];
        $this->_email->AdvancedSearch->SearchOperator = @$filter["z__email"];
        $this->_email->AdvancedSearch->SearchCondition = @$filter["v__email"];
        $this->_email->AdvancedSearch->SearchValue2 = @$filter["y__email"];
        $this->_email->AdvancedSearch->SearchOperator2 = @$filter["w__email"];
        $this->_email->AdvancedSearch->save();

        // Field prefisso
        $this->prefisso->AdvancedSearch->SearchValue = @$filter["x_prefisso"];
        $this->prefisso->AdvancedSearch->SearchOperator = @$filter["z_prefisso"];
        $this->prefisso->AdvancedSearch->SearchCondition = @$filter["v_prefisso"];
        $this->prefisso->AdvancedSearch->SearchValue2 = @$filter["y_prefisso"];
        $this->prefisso->AdvancedSearch->SearchOperator2 = @$filter["w_prefisso"];
        $this->prefisso->AdvancedSearch->save();

        // Field telefono
        $this->telefono->AdvancedSearch->SearchValue = @$filter["x_telefono"];
        $this->telefono->AdvancedSearch->SearchOperator = @$filter["z_telefono"];
        $this->telefono->AdvancedSearch->SearchCondition = @$filter["v_telefono"];
        $this->telefono->AdvancedSearch->SearchValue2 = @$filter["y_telefono"];
        $this->telefono->AdvancedSearch->SearchOperator2 = @$filter["w_telefono"];
        $this->telefono->AdvancedSearch->save();

        // Field codice_fiscale
        $this->codice_fiscale->AdvancedSearch->SearchValue = @$filter["x_codice_fiscale"];
        $this->codice_fiscale->AdvancedSearch->SearchOperator = @$filter["z_codice_fiscale"];
        $this->codice_fiscale->AdvancedSearch->SearchCondition = @$filter["v_codice_fiscale"];
        $this->codice_fiscale->AdvancedSearch->SearchValue2 = @$filter["y_codice_fiscale"];
        $this->codice_fiscale->AdvancedSearch->SearchOperator2 = @$filter["w_codice_fiscale"];
        $this->codice_fiscale->AdvancedSearch->save();

        // Field targa
        $this->targa->AdvancedSearch->SearchValue = @$filter["x_targa"];
        $this->targa->AdvancedSearch->SearchOperator = @$filter["z_targa"];
        $this->targa->AdvancedSearch->SearchCondition = @$filter["v_targa"];
        $this->targa->AdvancedSearch->SearchValue2 = @$filter["y_targa"];
        $this->targa->AdvancedSearch->SearchOperator2 = @$filter["w_targa"];
        $this->targa->AdvancedSearch->save();

        // Field country
        $this->country->AdvancedSearch->SearchValue = @$filter["x_country"];
        $this->country->AdvancedSearch->SearchOperator = @$filter["z_country"];
        $this->country->AdvancedSearch->SearchCondition = @$filter["v_country"];
        $this->country->AdvancedSearch->SearchValue2 = @$filter["y_country"];
        $this->country->AdvancedSearch->SearchOperator2 = @$filter["w_country"];
        $this->country->AdvancedSearch->save();

        // Field invio_whatsapp
        $this->invio_whatsapp->AdvancedSearch->SearchValue = @$filter["x_invio_whatsapp"];
        $this->invio_whatsapp->AdvancedSearch->SearchOperator = @$filter["z_invio_whatsapp"];
        $this->invio_whatsapp->AdvancedSearch->SearchCondition = @$filter["v_invio_whatsapp"];
        $this->invio_whatsapp->AdvancedSearch->SearchValue2 = @$filter["y_invio_whatsapp"];
        $this->invio_whatsapp->AdvancedSearch->SearchOperator2 = @$filter["w_invio_whatsapp"];
        $this->invio_whatsapp->AdvancedSearch->save();

        // Field presenza_disabili
        $this->presenza_disabili->AdvancedSearch->SearchValue = @$filter["x_presenza_disabili"];
        $this->presenza_disabili->AdvancedSearch->SearchOperator = @$filter["z_presenza_disabili"];
        $this->presenza_disabili->AdvancedSearch->SearchCondition = @$filter["v_presenza_disabili"];
        $this->presenza_disabili->AdvancedSearch->SearchValue2 = @$filter["y_presenza_disabili"];
        $this->presenza_disabili->AdvancedSearch->SearchOperator2 = @$filter["w_presenza_disabili"];
        $this->presenza_disabili->AdvancedSearch->save();

        // Field note
        $this->note->AdvancedSearch->SearchValue = @$filter["x_note"];
        $this->note->AdvancedSearch->SearchOperator = @$filter["z_note"];
        $this->note->AdvancedSearch->SearchCondition = @$filter["v_note"];
        $this->note->AdvancedSearch->SearchValue2 = @$filter["y_note"];
        $this->note->AdvancedSearch->SearchOperator2 = @$filter["w_note"];
        $this->note->AdvancedSearch->save();

        // Field wa_inviato
        $this->wa_inviato->AdvancedSearch->SearchValue = @$filter["x_wa_inviato"];
        $this->wa_inviato->AdvancedSearch->SearchOperator = @$filter["z_wa_inviato"];
        $this->wa_inviato->AdvancedSearch->SearchCondition = @$filter["v_wa_inviato"];
        $this->wa_inviato->AdvancedSearch->SearchValue2 = @$filter["y_wa_inviato"];
        $this->wa_inviato->AdvancedSearch->SearchOperator2 = @$filter["w_wa_inviato"];
        $this->wa_inviato->AdvancedSearch->save();
        $this->BasicSearch->setKeyword(@$filter[Config("TABLE_BASIC_SEARCH")]);
        $this->BasicSearch->setType(@$filter[Config("TABLE_BASIC_SEARCH_TYPE")]);
    }

    // Advanced search WHERE clause based on QueryString
    protected function advancedSearchWhere($default = false)
    {
        global $Security;
        $where = "";
        $this->buildSearchSql($where, $this->id, $default, false); // id
        $this->buildSearchSql($where, $this->data, $default, false); // data
        $this->buildSearchSql($where, $this->progress, $default, false); // progress
        $this->buildSearchSql($where, $this->persone, $default, false); // persone
        $this->buildSearchSql($where, $this->data_arrivo, $default, false); // data_arrivo
        $this->buildSearchSql($where, $this->data_partenza, $default, false); // data_partenza
        $this->buildSearchSql($where, $this->piazzola_id, $default, false); // piazzola_id
        $this->buildSearchSql($where, $this->piazzola, $default, false); // piazzola
        $this->buildSearchSql($where, $this->nome, $default, false); // nome
        $this->buildSearchSql($where, $this->cognome, $default, false); // cognome
        $this->buildSearchSql($where, $this->importo, $default, false); // importo
        $this->buildSearchSql($where, $this->_email, $default, false); // email
        $this->buildSearchSql($where, $this->prefisso, $default, false); // prefisso
        $this->buildSearchSql($where, $this->telefono, $default, false); // telefono
        $this->buildSearchSql($where, $this->codice_fiscale, $default, false); // codice_fiscale
        $this->buildSearchSql($where, $this->targa, $default, false); // targa
        $this->buildSearchSql($where, $this->country, $default, false); // country
        $this->buildSearchSql($where, $this->invio_whatsapp, $default, false); // invio_whatsapp
        $this->buildSearchSql($where, $this->presenza_disabili, $default, false); // presenza_disabili
        $this->buildSearchSql($where, $this->note, $default, false); // note
        $this->buildSearchSql($where, $this->wa_inviato, $default, false); // wa_inviato

        // Set up search parm
        if (!$default && $where != "" && in_array($this->Command, ["", "reset", "resetall"])) {
            $this->Command = "search";
        }
        if (!$default && $this->Command == "search") {
            $this->id->AdvancedSearch->save(); // id
            $this->data->AdvancedSearch->save(); // data
            $this->progress->AdvancedSearch->save(); // progress
            $this->persone->AdvancedSearch->save(); // persone
            $this->data_arrivo->AdvancedSearch->save(); // data_arrivo
            $this->data_partenza->AdvancedSearch->save(); // data_partenza
            $this->piazzola_id->AdvancedSearch->save(); // piazzola_id
            $this->piazzola->AdvancedSearch->save(); // piazzola
            $this->nome->AdvancedSearch->save(); // nome
            $this->cognome->AdvancedSearch->save(); // cognome
            $this->importo->AdvancedSearch->save(); // importo
            $this->_email->AdvancedSearch->save(); // email
            $this->prefisso->AdvancedSearch->save(); // prefisso
            $this->telefono->AdvancedSearch->save(); // telefono
            $this->codice_fiscale->AdvancedSearch->save(); // codice_fiscale
            $this->targa->AdvancedSearch->save(); // targa
            $this->country->AdvancedSearch->save(); // country
            $this->invio_whatsapp->AdvancedSearch->save(); // invio_whatsapp
            $this->presenza_disabili->AdvancedSearch->save(); // presenza_disabili
            $this->note->AdvancedSearch->save(); // note
            $this->wa_inviato->AdvancedSearch->save(); // wa_inviato
        }
        return $where;
    }

    // Build search SQL
    protected function buildSearchSql(&$where, &$fld, $default, $multiValue)
    {
        $fldParm = $fld->Param;
        $fldVal = ($default) ? $fld->AdvancedSearch->SearchValueDefault : $fld->AdvancedSearch->SearchValue;
        $fldOpr = ($default) ? $fld->AdvancedSearch->SearchOperatorDefault : $fld->AdvancedSearch->SearchOperator;
        $fldCond = ($default) ? $fld->AdvancedSearch->SearchConditionDefault : $fld->AdvancedSearch->SearchCondition;
        $fldVal2 = ($default) ? $fld->AdvancedSearch->SearchValue2Default : $fld->AdvancedSearch->SearchValue2;
        $fldOpr2 = ($default) ? $fld->AdvancedSearch->SearchOperator2Default : $fld->AdvancedSearch->SearchOperator2;
        $wrk = "";
        if (is_array($fldVal)) {
            $fldVal = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $fldVal);
        }
        if (is_array($fldVal2)) {
            $fldVal2 = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $fldVal2);
        }
        $fldOpr = strtoupper(trim($fldOpr));
        if ($fldOpr == "") {
            $fldOpr = "=";
        }
        $fldOpr2 = strtoupper(trim($fldOpr2));
        if ($fldOpr2 == "") {
            $fldOpr2 = "=";
        }
        if (Config("SEARCH_MULTI_VALUE_OPTION") == 1 || !IsMultiSearchOperator($fldOpr)) {
            $multiValue = false;
        }
        if ($multiValue) {
            $wrk1 = ($fldVal != "") ? GetMultiSearchSql($fld, $fldOpr, $fldVal, $this->Dbid) : ""; // Field value 1
            $wrk2 = ($fldVal2 != "") ? GetMultiSearchSql($fld, $fldOpr2, $fldVal2, $this->Dbid) : ""; // Field value 2
            $wrk = $wrk1; // Build final SQL
            if ($wrk2 != "") {
                $wrk = ($wrk != "") ? "($wrk) $fldCond ($wrk2)" : $wrk2;
            }
        } else {
            $fldVal = $this->convertSearchValue($fld, $fldVal);
            $fldVal2 = $this->convertSearchValue($fld, $fldVal2);
            $wrk = GetSearchSql($fld, $fldVal, $fldOpr, $fldCond, $fldVal2, $fldOpr2, $this->Dbid);
        }
        AddFilter($where, $wrk);
    }

    // Convert search value
    protected function convertSearchValue(&$fld, $fldVal)
    {
        if ($fldVal == Config("NULL_VALUE") || $fldVal == Config("NOT_NULL_VALUE")) {
            return $fldVal;
        }
        $value = $fldVal;
        if ($fld->isBoolean()) {
            if ($fldVal != "") {
                $value = (SameText($fldVal, "1") || SameText($fldVal, "y") || SameText($fldVal, "t")) ? $fld->TrueValue : $fld->FalseValue;
            }
        } elseif ($fld->DataType == DATATYPE_DATE || $fld->DataType == DATATYPE_TIME) {
            if ($fldVal != "") {
                $value = UnFormatDateTime($fldVal, $fld->DateTimeFormat);
            }
        }
        return $value;
    }

    // Return basic search SQL
    protected function basicSearchSql($arKeywords, $type)
    {
        $where = "";
        $this->buildBasicSearchSql($where, $this->piazzola_id, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->piazzola, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->nome, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->cognome, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->_email, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->prefisso, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->telefono, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->codice_fiscale, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->targa, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->country, $arKeywords, $type);
        $this->buildBasicSearchSql($where, $this->note, $arKeywords, $type);
        return $where;
    }

    // Build basic search SQL
    protected function buildBasicSearchSql(&$where, &$fld, $arKeywords, $type)
    {
        $defCond = ($type == "OR") ? "OR" : "AND";
        $arSql = []; // Array for SQL parts
        $arCond = []; // Array for search conditions
        $cnt = count($arKeywords);
        $j = 0; // Number of SQL parts
        for ($i = 0; $i < $cnt; $i++) {
            $keyword = $arKeywords[$i];
            $keyword = trim($keyword);
            if (Config("BASIC_SEARCH_IGNORE_PATTERN") != "") {
                $keyword = preg_replace(Config("BASIC_SEARCH_IGNORE_PATTERN"), "\\", $keyword);
                $ar = explode("\\", $keyword);
            } else {
                $ar = [$keyword];
            }
            foreach ($ar as $keyword) {
                if ($keyword != "") {
                    $wrk = "";
                    if ($keyword == "OR" && $type == "") {
                        if ($j > 0) {
                            $arCond[$j - 1] = "OR";
                        }
                    } elseif ($keyword == Config("NULL_VALUE")) {
                        $wrk = $fld->Expression . " IS NULL";
                    } elseif ($keyword == Config("NOT_NULL_VALUE")) {
                        $wrk = $fld->Expression . " IS NOT NULL";
                    } elseif ($fld->IsVirtual && $fld->Visible) {
                        $wrk = $fld->VirtualExpression . Like(QuotedValue("%" . $keyword . "%", DATATYPE_STRING, $this->Dbid), $this->Dbid);
                    } elseif ($fld->DataType != DATATYPE_NUMBER || is_numeric($keyword)) {
                        $wrk = $fld->BasicSearchExpression . Like(QuotedValue("%" . $keyword . "%", DATATYPE_STRING, $this->Dbid), $this->Dbid);
                    }
                    if ($wrk != "") {
                        $arSql[$j] = $wrk;
                        $arCond[$j] = $defCond;
                        $j += 1;
                    }
                }
            }
        }
        $cnt = count($arSql);
        $quoted = false;
        $sql = "";
        if ($cnt > 0) {
            for ($i = 0; $i < $cnt - 1; $i++) {
                if ($arCond[$i] == "OR") {
                    if (!$quoted) {
                        $sql .= "(";
                    }
                    $quoted = true;
                }
                $sql .= $arSql[$i];
                if ($quoted && $arCond[$i] != "OR") {
                    $sql .= ")";
                    $quoted = false;
                }
                $sql .= " " . $arCond[$i] . " ";
            }
            $sql .= $arSql[$cnt - 1];
            if ($quoted) {
                $sql .= ")";
            }
        }
        if ($sql != "") {
            if ($where != "") {
                $where .= " OR ";
            }
            $where .= "(" . $sql . ")";
        }
    }

    // Return basic search WHERE clause based on search keyword and type
    protected function basicSearchWhere($default = false)
    {
        global $Security;
        $searchStr = "";
        $searchKeyword = ($default) ? $this->BasicSearch->KeywordDefault : $this->BasicSearch->Keyword;
        $searchType = ($default) ? $this->BasicSearch->TypeDefault : $this->BasicSearch->Type;

        // Get search SQL
        if ($searchKeyword != "") {
            $ar = $this->BasicSearch->keywordList($default);
            // Search keyword in any fields
            if (($searchType == "OR" || $searchType == "AND") && $this->BasicSearch->BasicSearchAnyFields) {
                foreach ($ar as $keyword) {
                    if ($keyword != "") {
                        if ($searchStr != "") {
                            $searchStr .= " " . $searchType . " ";
                        }
                        $searchStr .= "(" . $this->basicSearchSql([$keyword], $searchType) . ")";
                    }
                }
            } else {
                $searchStr = $this->basicSearchSql($ar, $searchType);
            }
            if (!$default && in_array($this->Command, ["", "reset", "resetall"])) {
                $this->Command = "search";
            }
        }
        if (!$default && $this->Command == "search") {
            $this->BasicSearch->setKeyword($searchKeyword);
            $this->BasicSearch->setType($searchType);
        }
        return $searchStr;
    }

    // Check if search parm exists
    protected function checkSearchParms()
    {
        // Check basic search
        if ($this->BasicSearch->issetSession()) {
            return true;
        }
        if ($this->id->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->data->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->progress->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->persone->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->data_arrivo->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->data_partenza->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->piazzola_id->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->piazzola->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->nome->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->cognome->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->importo->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->_email->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->prefisso->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->telefono->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->codice_fiscale->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->targa->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->country->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->invio_whatsapp->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->presenza_disabili->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->note->AdvancedSearch->issetSession()) {
            return true;
        }
        if ($this->wa_inviato->AdvancedSearch->issetSession()) {
            return true;
        }
        return false;
    }

    // Clear all search parameters
    protected function resetSearchParms()
    {
        // Clear search WHERE clause
        $this->SearchWhere = "";
        $this->setSearchWhere($this->SearchWhere);

        // Clear basic search parameters
        $this->resetBasicSearchParms();

        // Clear advanced search parameters
        $this->resetAdvancedSearchParms();
    }

    // Load advanced search default values
    protected function loadAdvancedSearchDefault()
    {
        return false;
    }

    // Clear all basic search parameters
    protected function resetBasicSearchParms()
    {
        $this->BasicSearch->unsetSession();
    }

    // Clear all advanced search parameters
    protected function resetAdvancedSearchParms()
    {
                $this->id->AdvancedSearch->unsetSession();
                $this->data->AdvancedSearch->unsetSession();
                $this->progress->AdvancedSearch->unsetSession();
                $this->persone->AdvancedSearch->unsetSession();
                $this->data_arrivo->AdvancedSearch->unsetSession();
                $this->data_partenza->AdvancedSearch->unsetSession();
                $this->piazzola_id->AdvancedSearch->unsetSession();
                $this->piazzola->AdvancedSearch->unsetSession();
                $this->nome->AdvancedSearch->unsetSession();
                $this->cognome->AdvancedSearch->unsetSession();
                $this->importo->AdvancedSearch->unsetSession();
                $this->_email->AdvancedSearch->unsetSession();
                $this->prefisso->AdvancedSearch->unsetSession();
                $this->telefono->AdvancedSearch->unsetSession();
                $this->codice_fiscale->AdvancedSearch->unsetSession();
                $this->targa->AdvancedSearch->unsetSession();
                $this->country->AdvancedSearch->unsetSession();
                $this->invio_whatsapp->AdvancedSearch->unsetSession();
                $this->presenza_disabili->AdvancedSearch->unsetSession();
                $this->note->AdvancedSearch->unsetSession();
                $this->wa_inviato->AdvancedSearch->unsetSession();
    }

    // Restore all search parameters
    protected function restoreSearchParms()
    {
        $this->RestoreSearch = true;

        // Restore basic search values
        $this->BasicSearch->load();

        // Restore advanced search values
                $this->id->AdvancedSearch->load();
                $this->data->AdvancedSearch->load();
                $this->progress->AdvancedSearch->load();
                $this->persone->AdvancedSearch->load();
                $this->data_arrivo->AdvancedSearch->load();
                $this->data_partenza->AdvancedSearch->load();
                $this->piazzola_id->AdvancedSearch->load();
                $this->piazzola->AdvancedSearch->load();
                $this->nome->AdvancedSearch->load();
                $this->cognome->AdvancedSearch->load();
                $this->importo->AdvancedSearch->load();
                $this->_email->AdvancedSearch->load();
                $this->prefisso->AdvancedSearch->load();
                $this->telefono->AdvancedSearch->load();
                $this->codice_fiscale->AdvancedSearch->load();
                $this->targa->AdvancedSearch->load();
                $this->country->AdvancedSearch->load();
                $this->invio_whatsapp->AdvancedSearch->load();
                $this->presenza_disabili->AdvancedSearch->load();
                $this->note->AdvancedSearch->load();
                $this->wa_inviato->AdvancedSearch->load();
    }

    // Set up sort parameters
    protected function setupSortOrder()
    {
        // Check for "order" parameter
        if (Get("order") !== null) {
            $this->CurrentOrder = Get("order");
            $this->CurrentOrderType = Get("ordertype", "");
            $this->updateSort($this->id); // id
            $this->updateSort($this->data); // data
            $this->updateSort($this->progress); // progress
            $this->updateSort($this->persone); // persone
            $this->updateSort($this->data_arrivo); // data_arrivo
            $this->updateSort($this->data_partenza); // data_partenza
            $this->updateSort($this->piazzola_id); // piazzola_id
            $this->updateSort($this->piazzola); // piazzola
            $this->updateSort($this->nome); // nome
            $this->updateSort($this->cognome); // cognome
            $this->updateSort($this->importo); // importo
            $this->updateSort($this->_email); // email
            $this->updateSort($this->prefisso); // prefisso
            $this->updateSort($this->telefono); // telefono
            $this->updateSort($this->codice_fiscale); // codice_fiscale
            $this->updateSort($this->targa); // targa
            $this->updateSort($this->country); // country
            $this->updateSort($this->invio_whatsapp); // invio_whatsapp
            $this->updateSort($this->presenza_disabili); // presenza_disabili
            $this->updateSort($this->wa_inviato); // wa_inviato
            $this->setStartRecordNumber(1); // Reset start position
        }
    }

    // Load sort order parameters
    protected function loadSortOrder()
    {
        $orderBy = $this->getSessionOrderBy(); // Get ORDER BY from Session
        if ($orderBy == "") {
            $this->DefaultSort = "`data` DESC";
            if ($this->getSqlOrderBy() != "") {
                $useDefaultSort = true;
                if ($this->data->getSort() != "") {
                    $useDefaultSort = false;
                }
                if ($useDefaultSort) {
                    $this->data->setSort("DESC");
                    $orderBy = $this->getSqlOrderBy();
                    $this->setSessionOrderBy($orderBy);
                } else {
                    $this->setSessionOrderBy("");
                }
            }
        }
    }

    // Reset command
    // - cmd=reset (Reset search parameters)
    // - cmd=resetall (Reset search and master/detail parameters)
    // - cmd=resetsort (Reset sort parameters)
    protected function resetCmd()
    {
        // Check if reset command
        if (StartsString("reset", $this->Command)) {
            // Reset search criteria
            if ($this->Command == "reset" || $this->Command == "resetall") {
                $this->resetSearchParms();
            }

            // Reset (clear) sorting order
            if ($this->Command == "resetsort") {
                $orderBy = "";
                $this->setSessionOrderBy($orderBy);
                $this->id->setSort("");
                $this->_token->setSort("");
                $this->data->setSort("");
                $this->status->setSort("");
                $this->progress->setSort("");
                $this->persone->setSort("");
                $this->data_arrivo->setSort("");
                $this->data_partenza->setSort("");
                $this->piazzola_id->setSort("");
                $this->piazzola->setSort("");
                $this->nome->setSort("");
                $this->cognome->setSort("");
                $this->importo->setSort("");
                $this->_email->setSort("");
                $this->prefisso->setSort("");
                $this->telefono->setSort("");
                $this->codice_fiscale->setSort("");
                $this->targa->setSort("");
                $this->country->setSort("");
                $this->invio_whatsapp->setSort("");
                $this->presenza_disabili->setSort("");
                $this->note->setSort("");
                $this->wa_inviato->setSort("");
                $this->debug->setSort("");
            }

            // Reset start position
            $this->StartRecord = 1;
            $this->setStartRecordNumber($this->StartRecord);
        }
    }

    // Set up list options
    protected function setupListOptions()
    {
        global $Security, $Language;

        // Add group option item
        $item = &$this->ListOptions->add($this->ListOptions->GroupOptionName);
        $item->Body = "";
        $item->OnLeft = false;
        $item->Visible = false;

        // "edit"
        $item = &$this->ListOptions->add("edit");
        $item->CssClass = "text-nowrap";
        $item->Visible = $Security->canEdit();
        $item->OnLeft = false;

        // "delete"
        $item = &$this->ListOptions->add("delete");
        $item->CssClass = "text-nowrap";
        $item->Visible = $Security->canDelete();
        $item->OnLeft = false;

        // List actions
        $item = &$this->ListOptions->add("listactions");
        $item->CssClass = "text-nowrap";
        $item->OnLeft = false;
        $item->Visible = false;
        $item->ShowInButtonGroup = false;
        $item->ShowInDropDown = false;

        // "checkbox"
        $item = &$this->ListOptions->add("checkbox");
        $item->Visible = false;
        $item->OnLeft = false;
        $item->Header = "<div class=\"custom-control custom-checkbox d-inline-block\"><input type=\"checkbox\" name=\"key\" id=\"key\" class=\"custom-control-input\" onclick=\"ew.selectAllKey(this);\"><label class=\"custom-control-label\" for=\"key\"></label></div>";
        $item->ShowInDropDown = false;
        $item->ShowInButtonGroup = false;

        // Drop down button for ListOptions
        $this->ListOptions->UseDropDownButton = false;
        $this->ListOptions->DropDownButtonPhrase = $Language->phrase("ButtonListOptions");
        $this->ListOptions->UseButtonGroup = false;
        if ($this->ListOptions->UseButtonGroup && IsMobile()) {
            $this->ListOptions->UseDropDownButton = true;
        }

        //$this->ListOptions->ButtonClass = ""; // Class for button group

        // Call ListOptions_Load event
        $this->listOptionsLoad();
        $this->setupListOptionsExt();
        $item = $this->ListOptions[$this->ListOptions->GroupOptionName];
        $item->Visible = $this->ListOptions->groupOptionVisible();
    }

    // Render list options
    public function renderListOptions()
    {
        global $Security, $Language, $CurrentForm;
        $this->ListOptions->loadDefault();

        // Call ListOptions_Rendering event
        $this->listOptionsRendering();
        $pageUrl = $this->pageUrl();
        if ($this->CurrentMode == "view") {
            // "edit"
            $opt = $this->ListOptions["edit"];
            $editcaption = HtmlTitle($Language->phrase("EditLink"));
            if ($Security->canEdit()) {
                $opt->Body = "<a class=\"ew-row-link ew-edit\" title=\"" . HtmlTitle($Language->phrase("EditLink")) . "\" data-caption=\"" . HtmlTitle($Language->phrase("EditLink")) . "\" href=\"" . HtmlEncode(GetUrl($this->EditUrl)) . "\">" . $Language->phrase("EditLink") . "</a>";
            } else {
                $opt->Body = "";
            }

            // "delete"
            $opt = $this->ListOptions["delete"];
            if ($Security->canDelete()) {
            $opt->Body = "<a class=\"ew-row-link ew-delete\"" . "" . " title=\"" . HtmlTitle($Language->phrase("DeleteLink")) . "\" data-caption=\"" . HtmlTitle($Language->phrase("DeleteLink")) . "\" href=\"" . HtmlEncode(GetUrl($this->DeleteUrl)) . "\">" . $Language->phrase("DeleteLink") . "</a>";
            } else {
                $opt->Body = "";
            }
        } // End View mode

        // Set up list action buttons
        $opt = $this->ListOptions["listactions"];
        if ($opt && !$this->isExport() && !$this->CurrentAction) {
            $body = "";
            $links = [];
            foreach ($this->ListActions->Items as $listaction) {
                if ($listaction->Select == ACTION_SINGLE && $listaction->Allow) {
                    $action = $listaction->Action;
                    $caption = $listaction->Caption;
                    $icon = ($listaction->Icon != "") ? "<i class=\"" . HtmlEncode(str_replace(" ew-icon", "", $listaction->Icon)) . "\" data-caption=\"" . HtmlTitle($caption) . "\"></i> " : "";
                    $links[] = "<li><a class=\"dropdown-item ew-action ew-list-action\" data-action=\"" . HtmlEncode($action) . "\" data-caption=\"" . HtmlTitle($caption) . "\" href=\"#\" onclick=\"return ew.submitAction(event,jQuery.extend({key:" . $this->keyToJson(true) . "}," . $listaction->toJson(true) . "));\">" . $icon . $listaction->Caption . "</a></li>";
                    if (count($links) == 1) { // Single button
                        $body = "<a class=\"ew-action ew-list-action\" data-action=\"" . HtmlEncode($action) . "\" title=\"" . HtmlTitle($caption) . "\" data-caption=\"" . HtmlTitle($caption) . "\" href=\"#\" onclick=\"return ew.submitAction(event,jQuery.extend({key:" . $this->keyToJson(true) . "}," . $listaction->toJson(true) . "));\">" . $icon . $listaction->Caption . "</a>";
                    }
                }
            }
            if (count($links) > 1) { // More than one buttons, use dropdown
                $body = "<button class=\"dropdown-toggle btn btn-default ew-actions\" title=\"" . HtmlTitle($Language->phrase("ListActionButton")) . "\" data-toggle=\"dropdown\">" . $Language->phrase("ListActionButton") . "</button>";
                $content = "";
                foreach ($links as $link) {
                    $content .= "<li>" . $link . "</li>";
                }
                $body .= "<ul class=\"dropdown-menu" . ($opt->OnLeft ? "" : " dropdown-menu-right") . "\">" . $content . "</ul>";
                $body = "<div class=\"btn-group btn-group-sm\">" . $body . "</div>";
            }
            if (count($links) > 0) {
                $opt->Body = $body;
                $opt->Visible = true;
            }
        }

        // "checkbox"
        $opt = $this->ListOptions["checkbox"];
        $opt->Body = "<div class=\"custom-control custom-checkbox d-inline-block\"><input type=\"checkbox\" id=\"key_m_" . $this->RowCount . "\" name=\"key_m[]\" class=\"custom-control-input ew-multi-select\" value=\"" . HtmlEncode($this->id->CurrentValue) . "\" onclick=\"ew.clickMultiCheckbox(event);\"><label class=\"custom-control-label\" for=\"key_m_" . $this->RowCount . "\"></label></div>";
        $this->renderListOptionsExt();

        // Call ListOptions_Rendered event
        $this->listOptionsRendered();
    }

    // Set up other options
    protected function setupOtherOptions()
    {
        global $Language, $Security;
        $options = &$this->OtherOptions;
        $option = $options["addedit"];

        // Add
        $item = &$option->add("add");
        $addcaption = HtmlTitle($Language->phrase("AddLink"));
        $item->Body = "<a class=\"ew-add-edit ew-add\" title=\"" . $addcaption . "\" data-caption=\"" . $addcaption . "\" href=\"" . HtmlEncode(GetUrl($this->AddUrl)) . "\">" . $Language->phrase("AddLink") . "</a>";
        $item->Visible = $this->AddUrl != "" && $Security->canAdd();
        $option = $options["action"];

        // Set up options default
        foreach ($options as $option) {
            $option->UseDropDownButton = false;
            $option->UseButtonGroup = true;
            //$option->ButtonClass = ""; // Class for button group
            $item = &$option->add($option->GroupOptionName);
            $item->Body = "";
            $item->Visible = false;
        }
        $options["addedit"]->DropDownButtonPhrase = $Language->phrase("ButtonAddEdit");
        $options["detail"]->DropDownButtonPhrase = $Language->phrase("ButtonDetails");
        $options["action"]->DropDownButtonPhrase = $Language->phrase("ButtonActions");

        // Filter button
        $item = &$this->FilterOptions->add("savecurrentfilter");
        $item->Body = "<a class=\"ew-save-filter\" data-form=\"fordinilistsrch\" href=\"#\" onclick=\"return false;\">" . $Language->phrase("SaveCurrentFilter") . "</a>";
        $item->Visible = true;
        $item = &$this->FilterOptions->add("deletefilter");
        $item->Body = "<a class=\"ew-delete-filter\" data-form=\"fordinilistsrch\" href=\"#\" onclick=\"return false;\">" . $Language->phrase("DeleteFilter") . "</a>";
        $item->Visible = true;
        $this->FilterOptions->UseDropDownButton = true;
        $this->FilterOptions->UseButtonGroup = !$this->FilterOptions->UseDropDownButton;
        $this->FilterOptions->DropDownButtonPhrase = $Language->phrase("Filters");

        // Add group option item
        $item = &$this->FilterOptions->add($this->FilterOptions->GroupOptionName);
        $item->Body = "";
        $item->Visible = false;
    }

    // Render other options
    public function renderOtherOptions()
    {
        global $Language, $Security;
        $options = &$this->OtherOptions;
        $option = $options["action"];
        // Set up list action buttons
        foreach ($this->ListActions->Items as $listaction) {
            if ($listaction->Select == ACTION_MULTIPLE) {
                $item = &$option->add("custom_" . $listaction->Action);
                $caption = $listaction->Caption;
                $icon = ($listaction->Icon != "") ? '<i class="' . HtmlEncode($listaction->Icon) . '" data-caption="' . HtmlEncode($caption) . '"></i>' . $caption : $caption;
                $item->Body = '<a class="ew-action ew-list-action" title="' . HtmlEncode($caption) . '" data-caption="' . HtmlEncode($caption) . '" href="#" onclick="return ew.submitAction(event,jQuery.extend({f:document.fordinilist},' . $listaction->toJson(true) . '));">' . $icon . '</a>';
                $item->Visible = $listaction->Allow;
            }
        }

        // Hide grid edit and other options
        if ($this->TotalRecords <= 0) {
            $option = $options["addedit"];
            $item = $option["gridedit"];
            if ($item) {
                $item->Visible = false;
            }
            $option = $options["action"];
            $option->hideAllOptions();
        }
    }

    // Process list action
    protected function processListAction()
    {
        global $Language, $Security;
        $userlist = "";
        $user = "";
        $filter = $this->getFilterFromRecordKeys();
        $userAction = Post("useraction", "");
        if ($filter != "" && $userAction != "") {
            // Check permission first
            $actionCaption = $userAction;
            if (array_key_exists($userAction, $this->ListActions->Items)) {
                $actionCaption = $this->ListActions[$userAction]->Caption;
                if (!$this->ListActions[$userAction]->Allow) {
                    $errmsg = str_replace('%s', $actionCaption, $Language->phrase("CustomActionNotAllowed"));
                    if (Post("ajax") == $userAction) { // Ajax
                        echo "<p class=\"text-danger\">" . $errmsg . "</p>";
                        return true;
                    } else {
                        $this->setFailureMessage($errmsg);
                        return false;
                    }
                }
            }
            $this->CurrentFilter = $filter;
            $sql = $this->getCurrentSql();
            $conn = $this->getConnection();
            $rs = LoadRecordset($sql, $conn, \PDO::FETCH_ASSOC);
            $this->CurrentAction = $userAction;

            // Call row action event
            if ($rs) {
                $conn->beginTransaction();
                $this->SelectedCount = $rs->recordCount();
                $this->SelectedIndex = 0;
                while (!$rs->EOF) {
                    $this->SelectedIndex++;
                    $row = $rs->fields;
                    $processed = $this->rowCustomAction($userAction, $row);
                    if (!$processed) {
                        break;
                    }
                    $rs->moveNext();
                }
                if ($processed) {
                    $conn->commit(); // Commit the changes
                    if ($this->getSuccessMessage() == "" && !ob_get_length()) { // No output
                        $this->setSuccessMessage(str_replace('%s', $actionCaption, $Language->phrase("CustomActionCompleted"))); // Set up success message
                    }
                } else {
                    $conn->rollback(); // Rollback changes

                    // Set up error message
                    if ($this->getSuccessMessage() != "" || $this->getFailureMessage() != "") {
                        // Use the message, do nothing
                    } elseif ($this->CancelMessage != "") {
                        $this->setFailureMessage($this->CancelMessage);
                        $this->CancelMessage = "";
                    } else {
                        $this->setFailureMessage(str_replace('%s', $actionCaption, $Language->phrase("CustomActionFailed")));
                    }
                }
            }
            if ($rs) {
                $rs->close();
            }
            $this->CurrentAction = ""; // Clear action
            if (Post("ajax") == $userAction) { // Ajax
                if ($this->getSuccessMessage() != "") {
                    echo "<p class=\"text-success\">" . $this->getSuccessMessage() . "</p>";
                    $this->clearSuccessMessage(); // Clear message
                }
                if ($this->getFailureMessage() != "") {
                    echo "<p class=\"text-danger\">" . $this->getFailureMessage() . "</p>";
                    $this->clearFailureMessage(); // Clear message
                }
                return true;
            }
        }
        return false; // Not ajax request
    }

    // Set up list options (extended codes)
    protected function setupListOptionsExt()
    {
    }

    // Render list options (extended codes)
    protected function renderListOptionsExt()
    {
    }

    // Load basic search values
    protected function loadBasicSearchValues()
    {
        $this->BasicSearch->setKeyword(Get(Config("TABLE_BASIC_SEARCH"), ""), false);
        if ($this->BasicSearch->Keyword != "" && $this->Command == "") {
            $this->Command = "search";
        }
        $this->BasicSearch->setType(Get(Config("TABLE_BASIC_SEARCH_TYPE"), ""), false);
    }

    // Load search values for validation
    protected function loadSearchValues()
    {
        // Load search values
        $hasValue = false;

        // id
        if (!$this->isAddOrEdit() && $this->id->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->id->AdvancedSearch->SearchValue != "" || $this->id->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // data
        if (!$this->isAddOrEdit() && $this->data->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->data->AdvancedSearch->SearchValue != "" || $this->data->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // progress
        if (!$this->isAddOrEdit() && $this->progress->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->progress->AdvancedSearch->SearchValue != "" || $this->progress->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // persone
        if (!$this->isAddOrEdit() && $this->persone->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->persone->AdvancedSearch->SearchValue != "" || $this->persone->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // data_arrivo
        if (!$this->isAddOrEdit() && $this->data_arrivo->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->data_arrivo->AdvancedSearch->SearchValue != "" || $this->data_arrivo->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // data_partenza
        if (!$this->isAddOrEdit() && $this->data_partenza->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->data_partenza->AdvancedSearch->SearchValue != "" || $this->data_partenza->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // piazzola_id
        if (!$this->isAddOrEdit() && $this->piazzola_id->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->piazzola_id->AdvancedSearch->SearchValue != "" || $this->piazzola_id->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // piazzola
        if (!$this->isAddOrEdit() && $this->piazzola->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->piazzola->AdvancedSearch->SearchValue != "" || $this->piazzola->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // nome
        if (!$this->isAddOrEdit() && $this->nome->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->nome->AdvancedSearch->SearchValue != "" || $this->nome->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // cognome
        if (!$this->isAddOrEdit() && $this->cognome->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->cognome->AdvancedSearch->SearchValue != "" || $this->cognome->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // importo
        if (!$this->isAddOrEdit() && $this->importo->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->importo->AdvancedSearch->SearchValue != "" || $this->importo->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // email
        if (!$this->isAddOrEdit() && $this->_email->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->_email->AdvancedSearch->SearchValue != "" || $this->_email->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // prefisso
        if (!$this->isAddOrEdit() && $this->prefisso->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->prefisso->AdvancedSearch->SearchValue != "" || $this->prefisso->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // telefono
        if (!$this->isAddOrEdit() && $this->telefono->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->telefono->AdvancedSearch->SearchValue != "" || $this->telefono->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // codice_fiscale
        if (!$this->isAddOrEdit() && $this->codice_fiscale->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->codice_fiscale->AdvancedSearch->SearchValue != "" || $this->codice_fiscale->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // targa
        if (!$this->isAddOrEdit() && $this->targa->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->targa->AdvancedSearch->SearchValue != "" || $this->targa->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // country
        if (!$this->isAddOrEdit() && $this->country->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->country->AdvancedSearch->SearchValue != "" || $this->country->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // invio_whatsapp
        if (!$this->isAddOrEdit() && $this->invio_whatsapp->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->invio_whatsapp->AdvancedSearch->SearchValue != "" || $this->invio_whatsapp->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }
        if (is_array($this->invio_whatsapp->AdvancedSearch->SearchValue)) {
            $this->invio_whatsapp->AdvancedSearch->SearchValue = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->invio_whatsapp->AdvancedSearch->SearchValue);
        }
        if (is_array($this->invio_whatsapp->AdvancedSearch->SearchValue2)) {
            $this->invio_whatsapp->AdvancedSearch->SearchValue2 = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->invio_whatsapp->AdvancedSearch->SearchValue2);
        }

        // presenza_disabili
        if (!$this->isAddOrEdit() && $this->presenza_disabili->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->presenza_disabili->AdvancedSearch->SearchValue != "" || $this->presenza_disabili->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }
        if (is_array($this->presenza_disabili->AdvancedSearch->SearchValue)) {
            $this->presenza_disabili->AdvancedSearch->SearchValue = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->presenza_disabili->AdvancedSearch->SearchValue);
        }
        if (is_array($this->presenza_disabili->AdvancedSearch->SearchValue2)) {
            $this->presenza_disabili->AdvancedSearch->SearchValue2 = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->presenza_disabili->AdvancedSearch->SearchValue2);
        }

        // note
        if (!$this->isAddOrEdit() && $this->note->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->note->AdvancedSearch->SearchValue != "" || $this->note->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }

        // wa_inviato
        if (!$this->isAddOrEdit() && $this->wa_inviato->AdvancedSearch->get()) {
            $hasValue = true;
            if (($this->wa_inviato->AdvancedSearch->SearchValue != "" || $this->wa_inviato->AdvancedSearch->SearchValue2 != "") && $this->Command == "") {
                $this->Command = "search";
            }
        }
        if (is_array($this->wa_inviato->AdvancedSearch->SearchValue)) {
            $this->wa_inviato->AdvancedSearch->SearchValue = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->wa_inviato->AdvancedSearch->SearchValue);
        }
        if (is_array($this->wa_inviato->AdvancedSearch->SearchValue2)) {
            $this->wa_inviato->AdvancedSearch->SearchValue2 = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->wa_inviato->AdvancedSearch->SearchValue2);
        }
        return $hasValue;
    }

    // Load recordset
    public function loadRecordset($offset = -1, $rowcnt = -1)
    {
        // Load List page SQL (QueryBuilder)
        $sql = $this->getListSql();

        // Load recordset
        if ($offset > -1) {
            $sql->setFirstResult($offset);
        }
        if ($rowcnt > 0) {
            $sql->setMaxResults($rowcnt);
        }
        $stmt = $sql->execute();
        $rs = new Recordset($stmt, $sql);

        // Call Recordset Selected event
        $this->recordsetSelected($rs);
        return $rs;
    }

    /**
     * Load row based on key values
     *
     * @return void
     */
    public function loadRow()
    {
        global $Security, $Language;
        $filter = $this->getRecordFilter();

        // Call Row Selecting event
        $this->rowSelecting($filter);

        // Load SQL based on filter
        $this->CurrentFilter = $filter;
        $sql = $this->getCurrentSql();
        $conn = $this->getConnection();
        $res = false;
        $row = $conn->fetchAssoc($sql);
        if ($row) {
            $res = true;
            $this->loadRowValues($row); // Load row values
        }
        return $res;
    }

    /**
     * Load row values from recordset or record
     *
     * @param Recordset|array $rs Record
     * @return void
     */
    public function loadRowValues($rs = null)
    {
        if (is_array($rs)) {
            $row = $rs;
        } elseif ($rs && property_exists($rs, "fields")) { // Recordset
            $row = $rs->fields;
        } else {
            $row = $this->newRow();
        }

        // Call Row Selected event
        $this->rowSelected($row);
        if (!$rs) {
            return;
        }
        $this->id->setDbValue($row['id']);
        $this->_token->setDbValue($row['token']);
        $this->data->setDbValue($row['data']);
        $this->status->setDbValue($row['status']);
        $this->progress->setDbValue($row['progress']);
        $this->persone->setDbValue($row['persone']);
        $this->data_arrivo->setDbValue($row['data_arrivo']);
        $this->data_partenza->setDbValue($row['data_partenza']);
        $this->piazzola_id->setDbValue($row['piazzola_id']);
        $this->piazzola->setDbValue($row['piazzola']);
        $this->nome->setDbValue($row['nome']);
        $this->cognome->setDbValue($row['cognome']);
        $this->importo->setDbValue($row['importo']);
        $this->_email->setDbValue($row['email']);
        $this->prefisso->setDbValue($row['prefisso']);
        $this->telefono->setDbValue($row['telefono']);
        $this->codice_fiscale->setDbValue($row['codice_fiscale']);
        $this->targa->setDbValue($row['targa']);
        $this->country->setDbValue($row['country']);
        $this->invio_whatsapp->setDbValue($row['invio_whatsapp']);
        $this->presenza_disabili->setDbValue($row['presenza_disabili']);
        $this->note->setDbValue($row['note']);
        $this->wa_inviato->setDbValue($row['wa_inviato']);
        $this->debug->setDbValue($row['debug']);
    }

    // Return a row with default values
    protected function newRow()
    {
        $row = [];
        $row['id'] = null;
        $row['token'] = null;
        $row['data'] = null;
        $row['status'] = null;
        $row['progress'] = null;
        $row['persone'] = null;
        $row['data_arrivo'] = null;
        $row['data_partenza'] = null;
        $row['piazzola_id'] = null;
        $row['piazzola'] = null;
        $row['nome'] = null;
        $row['cognome'] = null;
        $row['importo'] = null;
        $row['email'] = null;
        $row['prefisso'] = null;
        $row['telefono'] = null;
        $row['codice_fiscale'] = null;
        $row['targa'] = null;
        $row['country'] = null;
        $row['invio_whatsapp'] = null;
        $row['presenza_disabili'] = null;
        $row['note'] = null;
        $row['wa_inviato'] = null;
        $row['debug'] = null;
        return $row;
    }

    // Load old record
    protected function loadOldRecord()
    {
        // Load old record
        $this->OldRecordset = null;
        $validKey = $this->OldKey != "";
        if ($validKey) {
            $this->CurrentFilter = $this->getRecordFilter();
            $sql = $this->getCurrentSql();
            $conn = $this->getConnection();
            $this->OldRecordset = LoadRecordset($sql, $conn);
        }
        $this->loadRowValues($this->OldRecordset); // Load row values
        return $validKey;
    }

    // Render row values based on field settings
    public function renderRow()
    {
        global $Security, $Language, $CurrentLanguage;

        // Initialize URLs
        $this->ViewUrl = $this->getViewUrl();
        $this->EditUrl = $this->getEditUrl();
        $this->InlineEditUrl = $this->getInlineEditUrl();
        $this->CopyUrl = $this->getCopyUrl();
        $this->InlineCopyUrl = $this->getInlineCopyUrl();
        $this->DeleteUrl = $this->getDeleteUrl();

        // Convert decimal values if posted back
        if ($this->importo->FormValue == $this->importo->CurrentValue && is_numeric(ConvertToFloatString($this->importo->CurrentValue))) {
            $this->importo->CurrentValue = ConvertToFloatString($this->importo->CurrentValue);
        }

        // Call Row_Rendering event
        $this->rowRendering();

        // Common render codes for all row types

        // id

        // token
        $this->_token->CellCssStyle = "white-space: nowrap;";

        // data

        // status
        $this->status->CellCssStyle = "white-space: nowrap;";

        // progress

        // persone

        // data_arrivo

        // data_partenza

        // piazzola_id

        // piazzola

        // nome

        // cognome

        // importo

        // email

        // prefisso

        // telefono

        // codice_fiscale

        // targa

        // country

        // invio_whatsapp

        // presenza_disabili

        // note

        // wa_inviato

        // debug
        $this->debug->CellCssStyle = "white-space: nowrap;";
        if ($this->RowType == ROWTYPE_VIEW) {
            // id
            $this->id->ViewValue = $this->id->CurrentValue;
            $this->id->ViewCustomAttributes = "";

            // data
            $this->data->ViewValue = $this->data->CurrentValue;
            $this->data->ViewValue = FormatDateTime($this->data->ViewValue, 0);
            $this->data->ViewCustomAttributes = "";

            // progress
            if (strval($this->progress->CurrentValue) != "") {
                $this->progress->ViewValue = $this->progress->optionCaption($this->progress->CurrentValue);
            } else {
                $this->progress->ViewValue = null;
            }
            $this->progress->ViewCustomAttributes = "";

            // persone
            $this->persone->ViewValue = $this->persone->CurrentValue;
            $this->persone->ViewValue = FormatNumber($this->persone->ViewValue, 0, -2, -2, -2);
            $this->persone->ViewCustomAttributes = "";

            // data_arrivo
            $this->data_arrivo->ViewValue = $this->data_arrivo->CurrentValue;
            $this->data_arrivo->ViewValue = FormatDateTime($this->data_arrivo->ViewValue, 1);
            $this->data_arrivo->ViewCustomAttributes = "";

            // data_partenza
            $this->data_partenza->ViewValue = $this->data_partenza->CurrentValue;
            $this->data_partenza->ViewValue = FormatDateTime($this->data_partenza->ViewValue, 1);
            $this->data_partenza->ViewCustomAttributes = "";

            // piazzola_id
            $curVal = trim(strval($this->piazzola_id->CurrentValue));
            if ($curVal != "") {
                $this->piazzola_id->ViewValue = $this->piazzola_id->lookupCacheOption($curVal);
                if ($this->piazzola_id->ViewValue === null) { // Lookup from database
                    $filterWrk = "`id`" . SearchString("=", $curVal, DATATYPE_NUMBER, "");
                    $sqlWrk = $this->piazzola_id->Lookup->getSql(false, $filterWrk, '', $this, true, true);
                    $rswrk = Conn()->executeQuery($sqlWrk)->fetchAll(\PDO::FETCH_BOTH);
                    $ari = count($rswrk);
                    if ($ari > 0) { // Lookup values found
                        $arwrk = $this->piazzola_id->Lookup->renderViewRow($rswrk[0]);
                        $this->piazzola_id->ViewValue = $this->piazzola_id->displayValue($arwrk);
                    } else {
                        $this->piazzola_id->ViewValue = $this->piazzola_id->CurrentValue;
                    }
                }
            } else {
                $this->piazzola_id->ViewValue = null;
            }
            $this->piazzola_id->ViewCustomAttributes = "";

            // piazzola
            $this->piazzola->ViewValue = $this->piazzola->CurrentValue;
            $this->piazzola->ViewCustomAttributes = "";

            // nome
            $this->nome->ViewValue = $this->nome->CurrentValue;
            $this->nome->ViewCustomAttributes = "";

            // cognome
            $this->cognome->ViewValue = $this->cognome->CurrentValue;
            $this->cognome->ViewCustomAttributes = "";

            // importo
            $this->importo->ViewValue = $this->importo->CurrentValue;
            $this->importo->ViewValue = FormatCurrency($this->importo->ViewValue, 2, -1, -2, -2);
            $this->importo->ViewCustomAttributes = "";

            // email
            $this->_email->ViewValue = $this->_email->CurrentValue;
            $this->_email->ViewCustomAttributes = "";

            // prefisso
            $this->prefisso->ViewValue = $this->prefisso->CurrentValue;
            $this->prefisso->ViewCustomAttributes = "";

            // telefono
            $this->telefono->ViewValue = $this->telefono->CurrentValue;
            $this->telefono->ViewCustomAttributes = "";

            // codice_fiscale
            $this->codice_fiscale->ViewValue = $this->codice_fiscale->CurrentValue;
            $this->codice_fiscale->ViewCustomAttributes = "";

            // targa
            $this->targa->ViewValue = $this->targa->CurrentValue;
            $this->targa->ViewCustomAttributes = "";

            // country
            $this->country->ViewValue = $this->country->CurrentValue;
            $this->country->ViewCustomAttributes = "";

            // invio_whatsapp
            if (ConvertToBool($this->invio_whatsapp->CurrentValue)) {
                $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(2) != "" ? $this->invio_whatsapp->tagCaption(2) : "si";
            } else {
                $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(1) != "" ? $this->invio_whatsapp->tagCaption(1) : "no";
            }
            $this->invio_whatsapp->ViewCustomAttributes = "";

            // presenza_disabili
            if (ConvertToBool($this->presenza_disabili->CurrentValue)) {
                $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(2) != "" ? $this->presenza_disabili->tagCaption(2) : "si";
            } else {
                $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(1) != "" ? $this->presenza_disabili->tagCaption(1) : "no";
            }
            $this->presenza_disabili->ViewCustomAttributes = "";

            // wa_inviato
            if (ConvertToBool($this->wa_inviato->CurrentValue)) {
                $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(2) != "" ? $this->wa_inviato->tagCaption(2) : "1";
            } else {
                $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(1) != "" ? $this->wa_inviato->tagCaption(1) : "0";
            }
            $this->wa_inviato->ViewCustomAttributes = "";

            // id
            $this->id->LinkCustomAttributes = "";
            $this->id->HrefValue = "";
            $this->id->TooltipValue = "";

            // data
            $this->data->LinkCustomAttributes = "";
            $this->data->HrefValue = "";
            $this->data->TooltipValue = "";

            // progress
            $this->progress->LinkCustomAttributes = "";
            $this->progress->HrefValue = "";
            $this->progress->TooltipValue = "";

            // persone
            $this->persone->LinkCustomAttributes = "";
            $this->persone->HrefValue = "";
            $this->persone->TooltipValue = "";

            // data_arrivo
            $this->data_arrivo->LinkCustomAttributes = "";
            $this->data_arrivo->HrefValue = "";
            $this->data_arrivo->TooltipValue = "";

            // data_partenza
            $this->data_partenza->LinkCustomAttributes = "";
            $this->data_partenza->HrefValue = "";
            $this->data_partenza->TooltipValue = "";

            // piazzola_id
            $this->piazzola_id->LinkCustomAttributes = "";
            $this->piazzola_id->HrefValue = "";
            $this->piazzola_id->TooltipValue = "";

            // piazzola
            $this->piazzola->LinkCustomAttributes = "";
            $this->piazzola->HrefValue = "";
            $this->piazzola->TooltipValue = "";

            // nome
            $this->nome->LinkCustomAttributes = "";
            $this->nome->HrefValue = "";
            $this->nome->TooltipValue = "";

            // cognome
            $this->cognome->LinkCustomAttributes = "";
            $this->cognome->HrefValue = "";
            $this->cognome->TooltipValue = "";

            // importo
            $this->importo->LinkCustomAttributes = "";
            $this->importo->HrefValue = "";
            $this->importo->TooltipValue = "";

            // email
            $this->_email->LinkCustomAttributes = "";
            $this->_email->HrefValue = "";
            $this->_email->TooltipValue = "";

            // prefisso
            $this->prefisso->LinkCustomAttributes = "";
            $this->prefisso->HrefValue = "";
            $this->prefisso->TooltipValue = "";

            // telefono
            $this->telefono->LinkCustomAttributes = "";
            $this->telefono->HrefValue = "";
            $this->telefono->TooltipValue = "";

            // codice_fiscale
            $this->codice_fiscale->LinkCustomAttributes = "";
            $this->codice_fiscale->HrefValue = "";
            $this->codice_fiscale->TooltipValue = "";

            // targa
            $this->targa->LinkCustomAttributes = "";
            $this->targa->HrefValue = "";
            $this->targa->TooltipValue = "";

            // country
            $this->country->LinkCustomAttributes = "";
            $this->country->HrefValue = "";
            $this->country->TooltipValue = "";

            // invio_whatsapp
            $this->invio_whatsapp->LinkCustomAttributes = "";
            $this->invio_whatsapp->HrefValue = "";
            $this->invio_whatsapp->TooltipValue = "";

            // presenza_disabili
            $this->presenza_disabili->LinkCustomAttributes = "";
            $this->presenza_disabili->HrefValue = "";
            $this->presenza_disabili->TooltipValue = "";

            // wa_inviato
            $this->wa_inviato->LinkCustomAttributes = "";
            $this->wa_inviato->HrefValue = "";
            $this->wa_inviato->TooltipValue = "";
        }

        // Call Row Rendered event
        if ($this->RowType != ROWTYPE_AGGREGATEINIT) {
            $this->rowRendered();
        }
    }

    // Validate search
    protected function validateSearch()
    {
        // Check if validation required
        if (!Config("SERVER_VALIDATE")) {
            return true;
        }

        // Return validate result
        $validateSearch = !$this->hasInvalidFields();

        // Call Form_CustomValidate event
        $formCustomError = "";
        $validateSearch = $validateSearch && $this->formCustomValidate($formCustomError);
        if ($formCustomError != "") {
            $this->setFailureMessage($formCustomError);
        }
        return $validateSearch;
    }

    // Load advanced search
    public function loadAdvancedSearch()
    {
        $this->id->AdvancedSearch->load();
        $this->data->AdvancedSearch->load();
        $this->progress->AdvancedSearch->load();
        $this->persone->AdvancedSearch->load();
        $this->data_arrivo->AdvancedSearch->load();
        $this->data_partenza->AdvancedSearch->load();
        $this->piazzola_id->AdvancedSearch->load();
        $this->piazzola->AdvancedSearch->load();
        $this->nome->AdvancedSearch->load();
        $this->cognome->AdvancedSearch->load();
        $this->importo->AdvancedSearch->load();
        $this->_email->AdvancedSearch->load();
        $this->prefisso->AdvancedSearch->load();
        $this->telefono->AdvancedSearch->load();
        $this->codice_fiscale->AdvancedSearch->load();
        $this->targa->AdvancedSearch->load();
        $this->country->AdvancedSearch->load();
        $this->invio_whatsapp->AdvancedSearch->load();
        $this->presenza_disabili->AdvancedSearch->load();
        $this->note->AdvancedSearch->load();
        $this->wa_inviato->AdvancedSearch->load();
    }

    // Get export HTML tag
    protected function getExportTag($type, $custom = false)
    {
        global $Language;
        $pageUrl = $this->pageUrl();
        if (SameText($type, "excel")) {
            if ($custom) {
                return "<a href=\"#\" class=\"ew-export-link ew-excel\" title=\"" . HtmlEncode($Language->phrase("ExportToExcelText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToExcelText")) . "\" onclick=\"return ew.export(document.fordinilist, '" . $this->ExportExcelUrl . "', 'excel', true);\">" . $Language->phrase("ExportToExcel") . "</a>";
            } else {
                return "<a href=\"" . $this->ExportExcelUrl . "\" class=\"ew-export-link ew-excel\" title=\"" . HtmlEncode($Language->phrase("ExportToExcelText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToExcelText")) . "\">" . $Language->phrase("ExportToExcel") . "</a>";
            }
        } elseif (SameText($type, "word")) {
            if ($custom) {
                return "<a href=\"#\" class=\"ew-export-link ew-word\" title=\"" . HtmlEncode($Language->phrase("ExportToWordText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToWordText")) . "\" onclick=\"return ew.export(document.fordinilist, '" . $this->ExportWordUrl . "', 'word', true);\">" . $Language->phrase("ExportToWord") . "</a>";
            } else {
                return "<a href=\"" . $this->ExportWordUrl . "\" class=\"ew-export-link ew-word\" title=\"" . HtmlEncode($Language->phrase("ExportToWordText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToWordText")) . "\">" . $Language->phrase("ExportToWord") . "</a>";
            }
        } elseif (SameText($type, "pdf")) {
            if ($custom) {
                return "<a href=\"#\" class=\"ew-export-link ew-pdf\" title=\"" . HtmlEncode($Language->phrase("ExportToPDFText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToPDFText")) . "\" onclick=\"return ew.export(document.fordinilist, '" . $this->ExportPdfUrl . "', 'pdf', true);\">" . $Language->phrase("ExportToPDF") . "</a>";
            } else {
                return "<a href=\"" . $this->ExportPdfUrl . "\" class=\"ew-export-link ew-pdf\" title=\"" . HtmlEncode($Language->phrase("ExportToPDFText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToPDFText")) . "\">" . $Language->phrase("ExportToPDF") . "</a>";
            }
        } elseif (SameText($type, "html")) {
            return "<a href=\"" . $this->ExportHtmlUrl . "\" class=\"ew-export-link ew-html\" title=\"" . HtmlEncode($Language->phrase("ExportToHtmlText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToHtmlText")) . "\">" . $Language->phrase("ExportToHtml") . "</a>";
        } elseif (SameText($type, "xml")) {
            return "<a href=\"" . $this->ExportXmlUrl . "\" class=\"ew-export-link ew-xml\" title=\"" . HtmlEncode($Language->phrase("ExportToXmlText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToXmlText")) . "\">" . $Language->phrase("ExportToXml") . "</a>";
        } elseif (SameText($type, "csv")) {
            return "<a href=\"" . $this->ExportCsvUrl . "\" class=\"ew-export-link ew-csv\" title=\"" . HtmlEncode($Language->phrase("ExportToCsvText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("ExportToCsvText")) . "\">" . $Language->phrase("ExportToCsv") . "</a>";
        } elseif (SameText($type, "email")) {
            $url = $custom ? ",url:'" . $pageUrl . "export=email&amp;custom=1'" : "";
            return '<button id="emf_ordini" class="ew-export-link ew-email" title="' . $Language->phrase("ExportToEmailText") . '" data-caption="' . $Language->phrase("ExportToEmailText") . '" onclick="ew.emailDialogShow({lnk:\'emf_ordini\', hdr:ew.language.phrase(\'ExportToEmailText\'), f:document.fordinilist, sel:false' . $url . '});">' . $Language->phrase("ExportToEmail") . '</button>';
        } elseif (SameText($type, "print")) {
            return "<a href=\"" . $this->ExportPrintUrl . "\" class=\"ew-export-link ew-print\" title=\"" . HtmlEncode($Language->phrase("PrinterFriendlyText")) . "\" data-caption=\"" . HtmlEncode($Language->phrase("PrinterFriendlyText")) . "\">" . $Language->phrase("PrinterFriendly") . "</a>";
        }
    }

    // Set up export options
    protected function setupExportOptions()
    {
        global $Language;

        // Printer friendly
        $item = &$this->ExportOptions->add("print");
        $item->Body = $this->getExportTag("print");
        $item->Visible = false;

        // Export to Excel
        $item = &$this->ExportOptions->add("excel");
        $item->Body = $this->getExportTag("excel");
        $item->Visible = false;

        // Export to Word
        $item = &$this->ExportOptions->add("word");
        $item->Body = $this->getExportTag("word");
        $item->Visible = false;

        // Export to Html
        $item = &$this->ExportOptions->add("html");
        $item->Body = $this->getExportTag("html");
        $item->Visible = false;

        // Export to Xml
        $item = &$this->ExportOptions->add("xml");
        $item->Body = $this->getExportTag("xml");
        $item->Visible = false;

        // Export to Csv
        $item = &$this->ExportOptions->add("csv");
        $item->Body = $this->getExportTag("csv");
        $item->Visible = true;

        // Export to Pdf
        $item = &$this->ExportOptions->add("pdf");
        $item->Body = $this->getExportTag("pdf");
        $item->Visible = false;

        // Export to Email
        $item = &$this->ExportOptions->add("email");
        $item->Body = $this->getExportTag("email");
        $item->Visible = false;

        // Drop down button for export
        $this->ExportOptions->UseButtonGroup = true;
        $this->ExportOptions->UseDropDownButton = false;
        if ($this->ExportOptions->UseButtonGroup && IsMobile()) {
            $this->ExportOptions->UseDropDownButton = true;
        }
        $this->ExportOptions->DropDownButtonPhrase = $Language->phrase("ButtonExport");

        // Add group option item
        $item = &$this->ExportOptions->add($this->ExportOptions->GroupOptionName);
        $item->Body = "";
        $item->Visible = false;
    }

    // Set up search options
    protected function setupSearchOptions()
    {
        global $Language, $Security;
        $pageUrl = $this->pageUrl();
        $this->SearchOptions = new ListOptions("div");
        $this->SearchOptions->TagClassName = "ew-search-option";

        // Search button
        $item = &$this->SearchOptions->add("searchtoggle");
        $searchToggleClass = ($this->SearchWhere != "") ? " active" : " active";
        $item->Body = "<a class=\"btn btn-default ew-search-toggle" . $searchToggleClass . "\" href=\"#\" role=\"button\" title=\"" . $Language->phrase("SearchPanel") . "\" data-caption=\"" . $Language->phrase("SearchPanel") . "\" data-toggle=\"button\" data-form=\"fordinilistsrch\" aria-pressed=\"" . ($searchToggleClass == " active" ? "true" : "false") . "\">" . $Language->phrase("SearchLink") . "</a>";
        $item->Visible = true;

        // Show all button
        $item = &$this->SearchOptions->add("showall");
        $item->Body = "<a class=\"btn btn-default ew-show-all\" title=\"" . $Language->phrase("ShowAll") . "\" data-caption=\"" . $Language->phrase("ShowAll") . "\" href=\"" . $pageUrl . "cmd=reset\">" . $Language->phrase("ShowAllBtn") . "</a>";
        $item->Visible = ($this->SearchWhere != $this->DefaultSearchWhere && $this->SearchWhere != "0=101");

        // Advanced search button
        $item = &$this->SearchOptions->add("advancedsearch");
        $item->Body = "<a class=\"btn btn-default ew-advanced-search\" title=\"" . $Language->phrase("AdvancedSearch") . "\" data-caption=\"" . $Language->phrase("AdvancedSearch") . "\" href=\"OrdiniSearch\">" . $Language->phrase("AdvancedSearchBtn") . "</a>";
        $item->Visible = true;

        // Button group for search
        $this->SearchOptions->UseDropDownButton = false;
        $this->SearchOptions->UseButtonGroup = true;
        $this->SearchOptions->DropDownButtonPhrase = $Language->phrase("ButtonSearch");

        // Add group option item
        $item = &$this->SearchOptions->add($this->SearchOptions->GroupOptionName);
        $item->Body = "";
        $item->Visible = false;

        // Hide search options
        if ($this->isExport() || $this->CurrentAction) {
            $this->SearchOptions->hideAllOptions();
        }
    }

    /**
    * Export data in HTML/CSV/Word/Excel/XML/Email/PDF format
    *
    * @param bool $return Return the data rather than output it
    * @return mixed
    */
    public function exportData($return = false)
    {
        global $Language;
        $utf8 = SameText(Config("PROJECT_CHARSET"), "utf-8");

        // Load recordset
        $this->TotalRecords = $this->listRecordCount();
        $this->StartRecord = 1;

        // Export all
        if ($this->ExportAll) {
            if (Config("EXPORT_ALL_TIME_LIMIT") >= 0) {
                @set_time_limit(Config("EXPORT_ALL_TIME_LIMIT"));
            }
            $this->DisplayRecords = $this->TotalRecords;
            $this->StopRecord = $this->TotalRecords;
        } else { // Export one page only
            $this->setupStartRecord(); // Set up start record position
            // Set the last record to display
            if ($this->DisplayRecords <= 0) {
                $this->StopRecord = $this->TotalRecords;
            } else {
                $this->StopRecord = $this->StartRecord + $this->DisplayRecords - 1;
            }
        }
        $rs = $this->loadRecordset($this->StartRecord - 1, $this->DisplayRecords <= 0 ? $this->TotalRecords : $this->DisplayRecords);
        $this->ExportDoc = GetExportDocument($this, "h");
        $doc = &$this->ExportDoc;
        if (!$doc) {
            $this->setFailureMessage($Language->phrase("ExportClassNotFound")); // Export class not found
        }
        if (!$rs || !$doc) {
            RemoveHeader("Content-Type"); // Remove header
            RemoveHeader("Content-Disposition");
            $this->showMessage();
            return;
        }
        $this->StartRecord = 1;
        $this->StopRecord = $this->DisplayRecords <= 0 ? $this->TotalRecords : $this->DisplayRecords;

        // Call Page Exporting server event
        $this->ExportDoc->ExportCustom = !$this->pageExporting();
        $header = $this->PageHeader;
        $this->pageDataRendering($header);
        $doc->Text .= $header;
        $this->exportDocument($doc, $rs, $this->StartRecord, $this->StopRecord, "");
        $footer = $this->PageFooter;
        $this->pageDataRendered($footer);
        $doc->Text .= $footer;

        // Close recordset
        $rs->close();

        // Call Page Exported server event
        $this->pageExported();

        // Export header and footer
        $doc->exportHeaderAndFooter();

        // Clean output buffer (without destroying output buffer)
        $buffer = ob_get_contents(); // Save the output buffer
        if (!Config("DEBUG") && $buffer) {
            ob_clean();
        }

        // Write debug message if enabled
        if (Config("DEBUG") && !$this->isExport("pdf")) {
            echo GetDebugMessage();
        }

        // Output data
        if ($this->isExport("email")) {
            // Export-to-email disabled
        } else {
            $doc->export();
            if ($return) {
                RemoveHeader("Content-Type"); // Remove header
                RemoveHeader("Content-Disposition");
                $content = ob_get_contents();
                if ($content) {
                    ob_clean();
                }
                if ($buffer) {
                    echo $buffer; // Resume the output buffer
                }
                return $content;
            }
        }
    }

    // Set up Breadcrumb
    protected function setupBreadcrumb()
    {
        global $Breadcrumb, $Language;
        $Breadcrumb = new Breadcrumb("index");
        $url = CurrentUrl();
        $url = preg_replace('/\?cmd=reset(all){0,1}$/i', '', $url); // Remove cmd=reset / cmd=resetall
        $Breadcrumb->add("list", $this->TableVar, $url, "", $this->TableVar, true);
    }

    // Setup lookup options
    public function setupLookupOptions($fld)
    {
        if ($fld->Lookup !== null && $fld->Lookup->Options === null) {
            // Get default connection and filter
            $conn = $this->getConnection();
            $lookupFilter = "";

            // No need to check any more
            $fld->Lookup->Options = [];

            // Set up lookup SQL and connection
            switch ($fld->FieldVar) {
                case "x_status":
                    break;
                case "x_progress":
                    break;
                case "x_piazzola_id":
                    break;
                case "x_invio_whatsapp":
                    break;
                case "x_presenza_disabili":
                    break;
                case "x_wa_inviato":
                    break;
                default:
                    $lookupFilter = "";
                    break;
            }

            // Always call to Lookup->getSql so that user can setup Lookup->Options in Lookup_Selecting server event
            $sql = $fld->Lookup->getSql(false, "", $lookupFilter, $this);

            // Set up lookup cache
            if ($fld->UseLookupCache && $sql != "" && count($fld->Lookup->Options) == 0) {
                $totalCnt = $this->getRecordCount($sql, $conn);
                if ($totalCnt > $fld->LookupCacheCount) { // Total count > cache count, do not cache
                    return;
                }
                $rows = $conn->executeQuery($sql)->fetchAll(\PDO::FETCH_BOTH);
                $ar = [];
                foreach ($rows as $row) {
                    $row = $fld->Lookup->renderViewRow($row);
                    $ar[strval($row[0])] = $row;
                }
                $fld->Lookup->Options = $ar;
            }
        }
    }

    // Set up starting record parameters
    public function setupStartRecord()
    {
        if ($this->DisplayRecords == 0) {
            return;
        }
        if ($this->isPageRequest()) { // Validate request
            $startRec = Get(Config("TABLE_START_REC"));
            $pageNo = Get(Config("TABLE_PAGE_NO"));
            if ($pageNo !== null) { // Check for "pageno" parameter first
                if (is_numeric($pageNo)) {
                    $this->StartRecord = ($pageNo - 1) * $this->DisplayRecords + 1;
                    if ($this->StartRecord <= 0) {
                        $this->StartRecord = 1;
                    } elseif ($this->StartRecord >= (int)(($this->TotalRecords - 1) / $this->DisplayRecords) * $this->DisplayRecords + 1) {
                        $this->StartRecord = (int)(($this->TotalRecords - 1) / $this->DisplayRecords) * $this->DisplayRecords + 1;
                    }
                    $this->setStartRecordNumber($this->StartRecord);
                }
            } elseif ($startRec !== null) { // Check for "start" parameter
                $this->StartRecord = $startRec;
                $this->setStartRecordNumber($this->StartRecord);
            }
        }
        $this->StartRecord = $this->getStartRecordNumber();

        // Check if correct start record counter
        if (!is_numeric($this->StartRecord) || $this->StartRecord == "") { // Avoid invalid start record counter
            $this->StartRecord = 1; // Reset start record counter
            $this->setStartRecordNumber($this->StartRecord);
        } elseif ($this->StartRecord > $this->TotalRecords) { // Avoid starting record > total records
            $this->StartRecord = (int)(($this->TotalRecords - 1) / $this->DisplayRecords) * $this->DisplayRecords + 1; // Point to last page first record
            $this->setStartRecordNumber($this->StartRecord);
        } elseif (($this->StartRecord - 1) % $this->DisplayRecords != 0) {
            $this->StartRecord = (int)(($this->StartRecord - 1) / $this->DisplayRecords) * $this->DisplayRecords + 1; // Point to page boundary
            $this->setStartRecordNumber($this->StartRecord);
        }
    }

    // Page Load event
    public function pageLoad()
    {
        //Log("Page Load");
    }

    // Page Unload event
    public function pageUnload()
    {
        //Log("Page Unload");
    }

    // Page Redirecting event
    public function pageRedirecting(&$url)
    {
        // Example:
        //$url = "your URL";
    }

    // Message Showing event
    // $type = ''|'success'|'failure'|'warning'
    public function messageShowing(&$msg, $type)
    {
        if ($type == 'success') {
            //$msg = "your success message";
        } elseif ($type == 'failure') {
            //$msg = "your failure message";
        } elseif ($type == 'warning') {
            //$msg = "your warning message";
        } else {
            //$msg = "your message";
        }
    }

    // Page Render event
    public function pageRender()
    {
        //Log("Page Render");
    }

    // Page Data Rendering event
    public function pageDataRendering(&$header)
    {
        // Example:
        //$header = "your header";
    }

    // Page Data Rendered event
    public function pageDataRendered(&$footer)
    {
        // Example:
        //$footer = "your footer";
    }

    // Form Custom Validate event
    public function formCustomValidate(&$customError)
    {
        // Return error message in CustomError
        return true;
    }

    // ListOptions Load event
    public function listOptionsLoad()
    {
        // Example:
        //$opt = &$this->ListOptions->Add("new");
        //$opt->Header = "xxx";
        //$opt->OnLeft = true; // Link on left
        //$opt->MoveTo(0); // Move to first column
    }

    // ListOptions Rendering event
    public function listOptionsRendering()
    {
        //Container("DetailTableGrid")->DetailAdd = (...condition...); // Set to true or false conditionally
        //Container("DetailTableGrid")->DetailEdit = (...condition...); // Set to true or false conditionally
        //Container("DetailTableGrid")->DetailView = (...condition...); // Set to true or false conditionally
    }

    // ListOptions Rendered event
    public function listOptionsRendered()
    {
        // Example:
        //$this->ListOptions["new"]->Body = "xxx";
    }

    // Row Custom Action event
    public function rowCustomAction($action, $row)
    {
        // Return false to abort
        return true;
    }

    // Page Exporting event
    // $this->ExportDoc = export document object
    public function pageExporting()
    {
        //$this->ExportDoc->Text = "my header"; // Export header
        //return false; // Return false to skip default export and use Row_Export event
        return true; // Return true to use default export and skip Row_Export event
    }

    // Row Export event
    // $this->ExportDoc = export document object
    public function rowExport($rs)
    {
        //$this->ExportDoc->Text .= "my content"; // Build HTML with field value: $rs["MyField"] or $this->MyField->ViewValue
    }

    // Page Exported event
    // $this->ExportDoc = export document object
    public function pageExported()
    {
        //$this->ExportDoc->Text .= "my footer"; // Export footer
        //Log($this->ExportDoc->Text);
    }

    // Page Importing event
    public function pageImporting($reader, &$options)
    {
        //var_dump($reader); // Import data reader
        //var_dump($options); // Show all options for importing
        //return false; // Return false to skip import
        return true;
    }

    // Row Import event
    public function rowImport(&$row, $cnt)
    {
        //Log($cnt); // Import record count
        //var_dump($row); // Import row
        //return false; // Return false to skip import
        return true;
    }

    // Page Imported event
    public function pageImported($reader, $results)
    {
        //var_dump($reader); // Import data reader
        //var_dump($results); // Import results
    }
}
