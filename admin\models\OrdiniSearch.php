<?php

namespace PHPMaker2021\aladin2022;

use Doctrine\DBAL\ParameterType;

/**
 * Page class
 */
class OrdiniSearch extends Ordini
{
    use MessagesTrait;

    // Page ID
    public $PageID = "search";

    // Project ID
    public $ProjectID = PROJECT_ID;

    // Table name
    public $TableName = 'ordini';

    // Page object name
    public $PageObjName = "OrdiniSearch";

    // Rendering View
    public $RenderingView = false;

    // Page headings
    public $Heading = "";
    public $Subheading = "";
    public $PageHeader;
    public $PageFooter;

    // Page terminated
    private $terminated = false;

    // Page heading
    public function pageHeading()
    {
        global $Language;
        if ($this->Heading != "") {
            return $this->Heading;
        }
        if (method_exists($this, "tableCaption")) {
            return $this->tableCaption();
        }
        return "";
    }

    // Page subheading
    public function pageSubheading()
    {
        global $Language;
        if ($this->Subheading != "") {
            return $this->Subheading;
        }
        if ($this->TableName) {
            return $Language->phrase($this->PageID);
        }
        return "";
    }

    // Page name
    public function pageName()
    {
        return CurrentPageName();
    }

    // Page URL
    public function pageUrl()
    {
        $url = ScriptName() . "?";
        if ($this->UseTokenInUrl) {
            $url .= "t=" . $this->TableVar . "&"; // Add page token
        }
        return $url;
    }

    // Show Page Header
    public function showPageHeader()
    {
        $header = $this->PageHeader;
        $this->pageDataRendering($header);
        if ($header != "") { // Header exists, display
            echo '<p id="ew-page-header">' . $header . '</p>';
        }
    }

    // Show Page Footer
    public function showPageFooter()
    {
        $footer = $this->PageFooter;
        $this->pageDataRendered($footer);
        if ($footer != "") { // Footer exists, display
            echo '<p id="ew-page-footer">' . $footer . '</p>';
        }
    }

    // Validate page request
    protected function isPageRequest()
    {
        global $CurrentForm;
        if ($this->UseTokenInUrl) {
            if ($CurrentForm) {
                return ($this->TableVar == $CurrentForm->getValue("t"));
            }
            if (Get("t") !== null) {
                return ($this->TableVar == Get("t"));
            }
        }
        return true;
    }

    // Constructor
    public function __construct()
    {
        global $Language, $DashboardReport, $DebugTimer;

        // Initialize
        $GLOBALS["Page"] = &$this;

        // Language object
        $Language = Container("language");

        // Parent constuctor
        parent::__construct();

        // Table object (ordini)
        if (!isset($GLOBALS["ordini"]) || get_class($GLOBALS["ordini"]) == PROJECT_NAMESPACE . "ordini") {
            $GLOBALS["ordini"] = &$this;
        }

        // Page URL
        $pageUrl = $this->pageUrl();

        // Table name (for backward compatibility only)
        if (!defined(PROJECT_NAMESPACE . "TABLE_NAME")) {
            define(PROJECT_NAMESPACE . "TABLE_NAME", 'ordini');
        }

        // Start timer
        $DebugTimer = Container("timer");

        // Debug message
        LoadDebugMessage();

        // Open connection
        $GLOBALS["Conn"] = $GLOBALS["Conn"] ?? $this->getConnection();
    }

    // Get content from stream
    public function getContents($stream = null): string
    {
        global $Response;
        return is_object($Response) ? $Response->getBody() : ob_get_clean();
    }

    // Is lookup
    public function isLookup()
    {
        return SameText(Route(0), Config("API_LOOKUP_ACTION"));
    }

    // Is AutoFill
    public function isAutoFill()
    {
        return $this->isLookup() && SameText(Post("ajax"), "autofill");
    }

    // Is AutoSuggest
    public function isAutoSuggest()
    {
        return $this->isLookup() && SameText(Post("ajax"), "autosuggest");
    }

    // Is modal lookup
    public function isModalLookup()
    {
        return $this->isLookup() && SameText(Post("ajax"), "modal");
    }

    // Is terminated
    public function isTerminated()
    {
        return $this->terminated;
    }

    /**
     * Terminate page
     *
     * @param string $url URL for direction
     * @return void
     */
    public function terminate($url = "")
    {
        if ($this->terminated) {
            return;
        }
        global $ExportFileName, $TempImages, $DashboardReport, $Response;

        // Page is terminated
        $this->terminated = true;

         // Page Unload event
        if (method_exists($this, "pageUnload")) {
            $this->pageUnload();
        }

        // Global Page Unloaded event (in userfn*.php)
        Page_Unloaded();

        // Export
        if ($this->CustomExport && $this->CustomExport == $this->Export && array_key_exists($this->CustomExport, Config("EXPORT_CLASSES"))) {
            $content = $this->getContents();
            if ($ExportFileName == "") {
                $ExportFileName = $this->TableVar;
            }
            $class = PROJECT_NAMESPACE . Config("EXPORT_CLASSES." . $this->CustomExport);
            if (class_exists($class)) {
                $doc = new $class(Container("ordini"));
                $doc->Text = @$content;
                if ($this->isExport("email")) {
                    echo $this->exportEmail($doc->Text);
                } else {
                    $doc->export();
                }
                DeleteTempImages(); // Delete temp images
                return;
            }
        }
        if (!IsApi() && method_exists($this, "pageRedirecting")) {
            $this->pageRedirecting($url);
        }

        // Close connection
        CloseConnections();

        // Return for API
        if (IsApi()) {
            $res = $url === true;
            if (!$res) { // Show error
                WriteJson(array_merge(["success" => false], $this->getMessages()));
            }
            return;
        } else { // Check if response is JSON
            if (StartsString("application/json", $Response->getHeaderLine("Content-type")) && $Response->getBody()->getSize()) { // With JSON response
                $this->clearMessages();
                return;
            }
        }

        // Go to URL if specified
        if ($url != "") {
            if (!Config("DEBUG") && ob_get_length()) {
                ob_end_clean();
            }

            // Handle modal response
            if ($this->IsModal) { // Show as modal
                $row = ["url" => GetUrl($url), "modal" => "1"];
                $pageName = GetPageName($url);
                if ($pageName != $this->getListUrl()) { // Not List page
                    $row["caption"] = $this->getModalCaption($pageName);
                    if ($pageName == "OrdiniView") {
                        $row["view"] = "1";
                    }
                } else { // List page should not be shown as modal => error
                    $row["error"] = $this->getFailureMessage();
                    $this->clearFailureMessage();
                }
                WriteJson($row);
            } else {
                SaveDebugMessage();
                Redirect(GetUrl($url));
            }
        }
        return; // Return to controller
    }

    // Get records from recordset
    protected function getRecordsFromRecordset($rs, $current = false)
    {
        $rows = [];
        if (is_object($rs)) { // Recordset
            while ($rs && !$rs->EOF) {
                $this->loadRowValues($rs); // Set up DbValue/CurrentValue
                $row = $this->getRecordFromArray($rs->fields);
                if ($current) {
                    return $row;
                } else {
                    $rows[] = $row;
                }
                $rs->moveNext();
            }
        } elseif (is_array($rs)) {
            foreach ($rs as $ar) {
                $row = $this->getRecordFromArray($ar);
                if ($current) {
                    return $row;
                } else {
                    $rows[] = $row;
                }
            }
        }
        return $rows;
    }

    // Get record from array
    protected function getRecordFromArray($ar)
    {
        $row = [];
        if (is_array($ar)) {
            foreach ($ar as $fldname => $val) {
                if (array_key_exists($fldname, $this->Fields) && ($this->Fields[$fldname]->Visible || $this->Fields[$fldname]->IsPrimaryKey)) { // Primary key or Visible
                    $fld = &$this->Fields[$fldname];
                    if ($fld->HtmlTag == "FILE") { // Upload field
                        if (EmptyValue($val)) {
                            $row[$fldname] = null;
                        } else {
                            if ($fld->DataType == DATATYPE_BLOB) {
                                $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                    "/" . $fld->TableVar . "/" . $fld->Param . "/" . rawurlencode($this->getRecordKeyValue($ar))));
                                $row[$fldname] = ["type" => ContentType($val), "url" => $url, "name" => $fld->Param . ContentExtension($val)];
                            } elseif (!$fld->UploadMultiple || !ContainsString($val, Config("MULTIPLE_UPLOAD_SEPARATOR"))) { // Single file
                                $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                    "/" . $fld->TableVar . "/" . Encrypt($fld->physicalUploadPath() . $val)));
                                $row[$fldname] = ["type" => MimeContentType($val), "url" => $url, "name" => $val];
                            } else { // Multiple files
                                $files = explode(Config("MULTIPLE_UPLOAD_SEPARATOR"), $val);
                                $ar = [];
                                foreach ($files as $file) {
                                    $url = FullUrl(GetApiUrl(Config("API_FILE_ACTION") .
                                        "/" . $fld->TableVar . "/" . Encrypt($fld->physicalUploadPath() . $file)));
                                    if (!EmptyValue($file)) {
                                        $ar[] = ["type" => MimeContentType($file), "url" => $url, "name" => $file];
                                    }
                                }
                                $row[$fldname] = $ar;
                            }
                        }
                    } else {
                        $row[$fldname] = $val;
                    }
                }
            }
        }
        return $row;
    }

    // Get record key value from array
    protected function getRecordKeyValue($ar)
    {
        $key = "";
        if (is_array($ar)) {
            $key .= @$ar['id'];
        }
        return $key;
    }

    /**
     * Hide fields for add/edit
     *
     * @return void
     */
    protected function hideFieldsForAddEdit()
    {
        if ($this->isAdd() || $this->isCopy() || $this->isGridAdd()) {
            $this->id->Visible = false;
        }
    }

    // Lookup data
    public function lookup()
    {
        global $Language, $Security;

        // Get lookup object
        $fieldName = Post("field");
        $lookup = $this->Fields[$fieldName]->Lookup;

        // Get lookup parameters
        $lookupType = Post("ajax", "unknown");
        $pageSize = -1;
        $offset = -1;
        $searchValue = "";
        if (SameText($lookupType, "modal")) {
            $searchValue = Post("sv", "");
            $pageSize = Post("recperpage", 10);
            $offset = Post("start", 0);
        } elseif (SameText($lookupType, "autosuggest")) {
            $searchValue = Param("q", "");
            $pageSize = Param("n", -1);
            $pageSize = is_numeric($pageSize) ? (int)$pageSize : -1;
            if ($pageSize <= 0) {
                $pageSize = Config("AUTO_SUGGEST_MAX_ENTRIES");
            }
            $start = Param("start", -1);
            $start = is_numeric($start) ? (int)$start : -1;
            $page = Param("page", -1);
            $page = is_numeric($page) ? (int)$page : -1;
            $offset = $start >= 0 ? $start : ($page > 0 && $pageSize > 0 ? ($page - 1) * $pageSize : 0);
        }
        $userSelect = Decrypt(Post("s", ""));
        $userFilter = Decrypt(Post("f", ""));
        $userOrderBy = Decrypt(Post("o", ""));
        $keys = Post("keys");
        $lookup->LookupType = $lookupType; // Lookup type
        if ($keys !== null) { // Selected records from modal
            if (is_array($keys)) {
                $keys = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $keys);
            }
            $lookup->FilterFields = []; // Skip parent fields if any
            $lookup->FilterValues[] = $keys; // Lookup values
            $pageSize = -1; // Show all records
        } else { // Lookup values
            $lookup->FilterValues[] = Post("v0", Post("lookupValue", ""));
        }
        $cnt = is_array($lookup->FilterFields) ? count($lookup->FilterFields) : 0;
        for ($i = 1; $i <= $cnt; $i++) {
            $lookup->FilterValues[] = Post("v" . $i, "");
        }
        $lookup->SearchValue = $searchValue;
        $lookup->PageSize = $pageSize;
        $lookup->Offset = $offset;
        if ($userSelect != "") {
            $lookup->UserSelect = $userSelect;
        }
        if ($userFilter != "") {
            $lookup->UserFilter = $userFilter;
        }
        if ($userOrderBy != "") {
            $lookup->UserOrderBy = $userOrderBy;
        }
        $lookup->toJson($this); // Use settings from current page
    }
    public $FormClassName = "ew-horizontal ew-form ew-search-form";
    public $IsModal = false;
    public $IsMobileOrModal = false;

    /**
     * Page run
     *
     * @return void
     */
    public function run()
    {
        global $ExportType, $CustomExportType, $ExportFileName, $UserProfile, $Language, $Security, $CurrentForm,
            $SkipHeaderFooter;

        // Is modal
        $this->IsModal = Param("modal") == "1";

        // Create form object
        $CurrentForm = new HttpForm();
        $this->CurrentAction = Param("action"); // Set up current action
        $this->id->setVisibility();
        $this->_token->Visible = false;
        $this->data->setVisibility();
        $this->status->Visible = false;
        $this->progress->setVisibility();
        $this->persone->setVisibility();
        $this->data_arrivo->setVisibility();
        $this->data_partenza->setVisibility();
        $this->piazzola_id->setVisibility();
        $this->piazzola->setVisibility();
        $this->nome->setVisibility();
        $this->cognome->setVisibility();
        $this->importo->setVisibility();
        $this->_email->setVisibility();
        $this->prefisso->setVisibility();
        $this->telefono->setVisibility();
        $this->codice_fiscale->setVisibility();
        $this->targa->setVisibility();
        $this->country->setVisibility();
        $this->invio_whatsapp->setVisibility();
        $this->presenza_disabili->setVisibility();
        $this->note->setVisibility();
        $this->wa_inviato->setVisibility();
        $this->debug->Visible = false;
        $this->hideFieldsForAddEdit();

        // Do not use lookup cache
        $this->setUseLookupCache(false);

        // Global Page Loading event (in userfn*.php)
        Page_Loading();

        // Page Load event
        if (method_exists($this, "pageLoad")) {
            $this->pageLoad();
        }

        // Set up lookup cache
        $this->setupLookupOptions($this->piazzola_id);

        // Set up Breadcrumb
        $this->setupBreadcrumb();

        // Check modal
        if ($this->IsModal) {
            $SkipHeaderFooter = true;
        }
        $this->IsMobileOrModal = IsMobile() || $this->IsModal;
        if ($this->isPageRequest()) {
            // Get action
            $this->CurrentAction = Post("action");
            if ($this->isSearch()) {
                // Build search string for advanced search, remove blank field
                $this->loadSearchValues(); // Get search values
                if ($this->validateSearch()) {
                    $srchStr = $this->buildAdvancedSearch();
                } else {
                    $srchStr = "";
                }
                if ($srchStr != "") {
                    $srchStr = $this->getUrlParm($srchStr);
                    $srchStr = "OrdiniList" . "?" . $srchStr;
                    $this->terminate($srchStr); // Go to list page
                    return;
                }
            }
        }

        // Restore search settings from Session
        if (!$this->hasInvalidFields()) {
            $this->loadAdvancedSearch();
        }

        // Render row for search
        $this->RowType = ROWTYPE_SEARCH;
        $this->resetAttributes();
        $this->renderRow();

        // Set LoginStatus / Page_Rendering / Page_Render
        if (!IsApi() && !$this->isTerminated()) {
            // Pass table and field properties to client side
            $this->toClientVar(["tableCaption"], ["caption", "Visible", "Required", "IsInvalid", "Raw"]);

            // Setup login status
            SetupLoginStatus();

            // Pass login status to client side
            SetClientVar("login", LoginStatus());

            // Global Page Rendering event (in userfn*.php)
            Page_Rendering();

            // Page Render event
            if (method_exists($this, "pageRender")) {
                $this->pageRender();
            }
        }
    }

    // Build advanced search
    protected function buildAdvancedSearch()
    {
        $srchUrl = "";
        $this->buildSearchUrl($srchUrl, $this->id); // id
        $this->buildSearchUrl($srchUrl, $this->data); // data
        $this->buildSearchUrl($srchUrl, $this->progress); // progress
        $this->buildSearchUrl($srchUrl, $this->persone); // persone
        $this->buildSearchUrl($srchUrl, $this->data_arrivo); // data_arrivo
        $this->buildSearchUrl($srchUrl, $this->data_partenza); // data_partenza
        $this->buildSearchUrl($srchUrl, $this->piazzola_id); // piazzola_id
        $this->buildSearchUrl($srchUrl, $this->piazzola); // piazzola
        $this->buildSearchUrl($srchUrl, $this->nome); // nome
        $this->buildSearchUrl($srchUrl, $this->cognome); // cognome
        $this->buildSearchUrl($srchUrl, $this->importo); // importo
        $this->buildSearchUrl($srchUrl, $this->_email); // email
        $this->buildSearchUrl($srchUrl, $this->prefisso); // prefisso
        $this->buildSearchUrl($srchUrl, $this->telefono); // telefono
        $this->buildSearchUrl($srchUrl, $this->codice_fiscale); // codice_fiscale
        $this->buildSearchUrl($srchUrl, $this->targa); // targa
        $this->buildSearchUrl($srchUrl, $this->country); // country
        $this->buildSearchUrl($srchUrl, $this->invio_whatsapp, true); // invio_whatsapp
        $this->buildSearchUrl($srchUrl, $this->presenza_disabili, true); // presenza_disabili
        $this->buildSearchUrl($srchUrl, $this->note); // note
        $this->buildSearchUrl($srchUrl, $this->wa_inviato, true); // wa_inviato
        if ($srchUrl != "") {
            $srchUrl .= "&";
        }
        $srchUrl .= "cmd=search";
        return $srchUrl;
    }

    // Build search URL
    protected function buildSearchUrl(&$url, &$fld, $oprOnly = false)
    {
        global $CurrentForm;
        $wrk = "";
        $fldParm = $fld->Param;
        $fldVal = $CurrentForm->getValue("x_$fldParm");
        $fldOpr = $CurrentForm->getValue("z_$fldParm");
        $fldCond = $CurrentForm->getValue("v_$fldParm");
        $fldVal2 = $CurrentForm->getValue("y_$fldParm");
        $fldOpr2 = $CurrentForm->getValue("w_$fldParm");
        if (is_array($fldVal)) {
            $fldVal = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $fldVal);
        }
        if (is_array($fldVal2)) {
            $fldVal2 = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $fldVal2);
        }
        $fldOpr = strtoupper(trim($fldOpr));
        $fldDataType = ($fld->IsVirtual) ? DATATYPE_STRING : $fld->DataType;
        if ($fldOpr == "BETWEEN") {
            $isValidValue = ($fldDataType != DATATYPE_NUMBER) ||
                ($fldDataType == DATATYPE_NUMBER && $this->searchValueIsNumeric($fld, $fldVal) && $this->searchValueIsNumeric($fld, $fldVal2));
            if ($fldVal != "" && $fldVal2 != "" && $isValidValue) {
                $wrk = "x_" . $fldParm . "=" . urlencode($fldVal) .
                    "&y_" . $fldParm . "=" . urlencode($fldVal2) .
                    "&z_" . $fldParm . "=" . urlencode($fldOpr);
            }
        } else {
            $isValidValue = ($fldDataType != DATATYPE_NUMBER) ||
                ($fldDataType == DATATYPE_NUMBER && $this->searchValueIsNumeric($fld, $fldVal));
            if ($fldVal != "" && $isValidValue && IsValidOperator($fldOpr, $fldDataType)) {
                $wrk = "x_" . $fldParm . "=" . urlencode($fldVal) .
                    "&z_" . $fldParm . "=" . urlencode($fldOpr);
            } elseif ($fldOpr == "IS NULL" || $fldOpr == "IS NOT NULL" || ($fldOpr != "" && $oprOnly && IsValidOperator($fldOpr, $fldDataType))) {
                $wrk = "z_" . $fldParm . "=" . urlencode($fldOpr);
            }
            $isValidValue = ($fldDataType != DATATYPE_NUMBER) ||
                ($fldDataType == DATATYPE_NUMBER && $this->searchValueIsNumeric($fld, $fldVal2));
            if ($fldVal2 != "" && $isValidValue && IsValidOperator($fldOpr2, $fldDataType)) {
                if ($wrk != "") {
                    $wrk .= "&v_" . $fldParm . "=" . urlencode($fldCond) . "&";
                }
                $wrk .= "y_" . $fldParm . "=" . urlencode($fldVal2) .
                    "&w_" . $fldParm . "=" . urlencode($fldOpr2);
            } elseif ($fldOpr2 == "IS NULL" || $fldOpr2 == "IS NOT NULL" || ($fldOpr2 != "" && $oprOnly && IsValidOperator($fldOpr2, $fldDataType))) {
                if ($wrk != "") {
                    $wrk .= "&v_" . $fldParm . "=" . urlencode($fldCond) . "&";
                }
                $wrk .= "w_" . $fldParm . "=" . urlencode($fldOpr2);
            }
        }
        if ($wrk != "") {
            if ($url != "") {
                $url .= "&";
            }
            $url .= $wrk;
        }
    }

    // Check if search value is numeric
    protected function searchValueIsNumeric($fld, $value)
    {
        if (IsFloatFormat($fld->Type)) {
            $value = ConvertToFloatString($value);
        }
        return is_numeric($value);
    }

    // Load search values for validation
    protected function loadSearchValues()
    {
        // Load search values
        $hasValue = false;
        if ($this->id->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->data->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->progress->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->persone->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->data_arrivo->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->data_partenza->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->piazzola_id->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->piazzola->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->nome->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->cognome->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->importo->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->_email->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->prefisso->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->telefono->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->codice_fiscale->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->targa->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->country->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->invio_whatsapp->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if (is_array($this->invio_whatsapp->AdvancedSearch->SearchValue)) {
            $this->invio_whatsapp->AdvancedSearch->SearchValue = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->invio_whatsapp->AdvancedSearch->SearchValue);
        }
        if (is_array($this->invio_whatsapp->AdvancedSearch->SearchValue2)) {
            $this->invio_whatsapp->AdvancedSearch->SearchValue2 = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->invio_whatsapp->AdvancedSearch->SearchValue2);
        }
        if ($this->presenza_disabili->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if (is_array($this->presenza_disabili->AdvancedSearch->SearchValue)) {
            $this->presenza_disabili->AdvancedSearch->SearchValue = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->presenza_disabili->AdvancedSearch->SearchValue);
        }
        if (is_array($this->presenza_disabili->AdvancedSearch->SearchValue2)) {
            $this->presenza_disabili->AdvancedSearch->SearchValue2 = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->presenza_disabili->AdvancedSearch->SearchValue2);
        }
        if ($this->note->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if ($this->wa_inviato->AdvancedSearch->post()) {
            $hasValue = true;
        }
        if (is_array($this->wa_inviato->AdvancedSearch->SearchValue)) {
            $this->wa_inviato->AdvancedSearch->SearchValue = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->wa_inviato->AdvancedSearch->SearchValue);
        }
        if (is_array($this->wa_inviato->AdvancedSearch->SearchValue2)) {
            $this->wa_inviato->AdvancedSearch->SearchValue2 = implode(Config("MULTIPLE_OPTION_SEPARATOR"), $this->wa_inviato->AdvancedSearch->SearchValue2);
        }
        return $hasValue;
    }

    // Render row values based on field settings
    public function renderRow()
    {
        global $Security, $Language, $CurrentLanguage;

        // Initialize URLs

        // Convert decimal values if posted back
        if ($this->importo->FormValue == $this->importo->CurrentValue && is_numeric(ConvertToFloatString($this->importo->CurrentValue))) {
            $this->importo->CurrentValue = ConvertToFloatString($this->importo->CurrentValue);
        }

        // Call Row_Rendering event
        $this->rowRendering();

        // Common render codes for all row types

        // id

        // token

        // data

        // status

        // progress

        // persone

        // data_arrivo

        // data_partenza

        // piazzola_id

        // piazzola

        // nome

        // cognome

        // importo

        // email

        // prefisso

        // telefono

        // codice_fiscale

        // targa

        // country

        // invio_whatsapp

        // presenza_disabili

        // note

        // wa_inviato

        // debug
        if ($this->RowType == ROWTYPE_VIEW) {
            // id
            $this->id->ViewValue = $this->id->CurrentValue;
            $this->id->ViewCustomAttributes = "";

            // data
            $this->data->ViewValue = $this->data->CurrentValue;
            $this->data->ViewValue = FormatDateTime($this->data->ViewValue, 0);
            $this->data->ViewCustomAttributes = "";

            // progress
            if (strval($this->progress->CurrentValue) != "") {
                $this->progress->ViewValue = $this->progress->optionCaption($this->progress->CurrentValue);
            } else {
                $this->progress->ViewValue = null;
            }
            $this->progress->ViewCustomAttributes = "";

            // persone
            $this->persone->ViewValue = $this->persone->CurrentValue;
            $this->persone->ViewValue = FormatNumber($this->persone->ViewValue, 0, -2, -2, -2);
            $this->persone->ViewCustomAttributes = "";

            // data_arrivo
            $this->data_arrivo->ViewValue = $this->data_arrivo->CurrentValue;
            $this->data_arrivo->ViewValue = FormatDateTime($this->data_arrivo->ViewValue, 1);
            $this->data_arrivo->ViewCustomAttributes = "";

            // data_partenza
            $this->data_partenza->ViewValue = $this->data_partenza->CurrentValue;
            $this->data_partenza->ViewValue = FormatDateTime($this->data_partenza->ViewValue, 1);
            $this->data_partenza->ViewCustomAttributes = "";

            // piazzola_id
            $curVal = trim(strval($this->piazzola_id->CurrentValue));
            if ($curVal != "") {
                $this->piazzola_id->ViewValue = $this->piazzola_id->lookupCacheOption($curVal);
                if ($this->piazzola_id->ViewValue === null) { // Lookup from database
                    $filterWrk = "`id`" . SearchString("=", $curVal, DATATYPE_NUMBER, "");
                    $sqlWrk = $this->piazzola_id->Lookup->getSql(false, $filterWrk, '', $this, true, true);
                    $rswrk = Conn()->executeQuery($sqlWrk)->fetchAll(\PDO::FETCH_BOTH);
                    $ari = count($rswrk);
                    if ($ari > 0) { // Lookup values found
                        $arwrk = $this->piazzola_id->Lookup->renderViewRow($rswrk[0]);
                        $this->piazzola_id->ViewValue = $this->piazzola_id->displayValue($arwrk);
                    } else {
                        $this->piazzola_id->ViewValue = $this->piazzola_id->CurrentValue;
                    }
                }
            } else {
                $this->piazzola_id->ViewValue = null;
            }
            $this->piazzola_id->ViewCustomAttributes = "";

            // piazzola
            $this->piazzola->ViewValue = $this->piazzola->CurrentValue;
            $this->piazzola->ViewCustomAttributes = "";

            // nome
            $this->nome->ViewValue = $this->nome->CurrentValue;
            $this->nome->ViewCustomAttributes = "";

            // cognome
            $this->cognome->ViewValue = $this->cognome->CurrentValue;
            $this->cognome->ViewCustomAttributes = "";

            // importo
            $this->importo->ViewValue = $this->importo->CurrentValue;
            $this->importo->ViewValue = FormatCurrency($this->importo->ViewValue, 2, -1, -2, -2);
            $this->importo->ViewCustomAttributes = "";

            // email
            $this->_email->ViewValue = $this->_email->CurrentValue;
            $this->_email->ViewCustomAttributes = "";

            // prefisso
            $this->prefisso->ViewValue = $this->prefisso->CurrentValue;
            $this->prefisso->ViewCustomAttributes = "";

            // telefono
            $this->telefono->ViewValue = $this->telefono->CurrentValue;
            $this->telefono->ViewCustomAttributes = "";

            // codice_fiscale
            $this->codice_fiscale->ViewValue = $this->codice_fiscale->CurrentValue;
            $this->codice_fiscale->ViewCustomAttributes = "";

            // targa
            $this->targa->ViewValue = $this->targa->CurrentValue;
            $this->targa->ViewCustomAttributes = "";

            // country
            $this->country->ViewValue = $this->country->CurrentValue;
            $this->country->ViewCustomAttributes = "";

            // invio_whatsapp
            if (ConvertToBool($this->invio_whatsapp->CurrentValue)) {
                $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(2) != "" ? $this->invio_whatsapp->tagCaption(2) : "si";
            } else {
                $this->invio_whatsapp->ViewValue = $this->invio_whatsapp->tagCaption(1) != "" ? $this->invio_whatsapp->tagCaption(1) : "no";
            }
            $this->invio_whatsapp->ViewCustomAttributes = "";

            // presenza_disabili
            if (ConvertToBool($this->presenza_disabili->CurrentValue)) {
                $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(2) != "" ? $this->presenza_disabili->tagCaption(2) : "si";
            } else {
                $this->presenza_disabili->ViewValue = $this->presenza_disabili->tagCaption(1) != "" ? $this->presenza_disabili->tagCaption(1) : "no";
            }
            $this->presenza_disabili->ViewCustomAttributes = "";

            // note
            $this->note->ViewValue = $this->note->CurrentValue;
            $this->note->ViewCustomAttributes = "";

            // wa_inviato
            if (ConvertToBool($this->wa_inviato->CurrentValue)) {
                $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(2) != "" ? $this->wa_inviato->tagCaption(2) : "1";
            } else {
                $this->wa_inviato->ViewValue = $this->wa_inviato->tagCaption(1) != "" ? $this->wa_inviato->tagCaption(1) : "0";
            }
            $this->wa_inviato->ViewCustomAttributes = "";

            // id
            $this->id->LinkCustomAttributes = "";
            $this->id->HrefValue = "";
            $this->id->TooltipValue = "";

            // data
            $this->data->LinkCustomAttributes = "";
            $this->data->HrefValue = "";
            $this->data->TooltipValue = "";

            // progress
            $this->progress->LinkCustomAttributes = "";
            $this->progress->HrefValue = "";
            $this->progress->TooltipValue = "";

            // persone
            $this->persone->LinkCustomAttributes = "";
            $this->persone->HrefValue = "";
            $this->persone->TooltipValue = "";

            // data_arrivo
            $this->data_arrivo->LinkCustomAttributes = "";
            $this->data_arrivo->HrefValue = "";
            $this->data_arrivo->TooltipValue = "";

            // data_partenza
            $this->data_partenza->LinkCustomAttributes = "";
            $this->data_partenza->HrefValue = "";
            $this->data_partenza->TooltipValue = "";

            // piazzola_id
            $this->piazzola_id->LinkCustomAttributes = "";
            $this->piazzola_id->HrefValue = "";
            $this->piazzola_id->TooltipValue = "";

            // piazzola
            $this->piazzola->LinkCustomAttributes = "";
            $this->piazzola->HrefValue = "";
            $this->piazzola->TooltipValue = "";

            // nome
            $this->nome->LinkCustomAttributes = "";
            $this->nome->HrefValue = "";
            $this->nome->TooltipValue = "";

            // cognome
            $this->cognome->LinkCustomAttributes = "";
            $this->cognome->HrefValue = "";
            $this->cognome->TooltipValue = "";

            // importo
            $this->importo->LinkCustomAttributes = "";
            $this->importo->HrefValue = "";
            $this->importo->TooltipValue = "";

            // email
            $this->_email->LinkCustomAttributes = "";
            $this->_email->HrefValue = "";
            $this->_email->TooltipValue = "";

            // prefisso
            $this->prefisso->LinkCustomAttributes = "";
            $this->prefisso->HrefValue = "";
            $this->prefisso->TooltipValue = "";

            // telefono
            $this->telefono->LinkCustomAttributes = "";
            $this->telefono->HrefValue = "";
            $this->telefono->TooltipValue = "";

            // codice_fiscale
            $this->codice_fiscale->LinkCustomAttributes = "";
            $this->codice_fiscale->HrefValue = "";
            $this->codice_fiscale->TooltipValue = "";

            // targa
            $this->targa->LinkCustomAttributes = "";
            $this->targa->HrefValue = "";
            $this->targa->TooltipValue = "";

            // country
            $this->country->LinkCustomAttributes = "";
            $this->country->HrefValue = "";
            $this->country->TooltipValue = "";

            // invio_whatsapp
            $this->invio_whatsapp->LinkCustomAttributes = "";
            $this->invio_whatsapp->HrefValue = "";
            $this->invio_whatsapp->TooltipValue = "";

            // presenza_disabili
            $this->presenza_disabili->LinkCustomAttributes = "";
            $this->presenza_disabili->HrefValue = "";
            $this->presenza_disabili->TooltipValue = "";

            // note
            $this->note->LinkCustomAttributes = "";
            $this->note->HrefValue = "";
            $this->note->TooltipValue = "";

            // wa_inviato
            $this->wa_inviato->LinkCustomAttributes = "";
            $this->wa_inviato->HrefValue = "";
            $this->wa_inviato->TooltipValue = "";
        } elseif ($this->RowType == ROWTYPE_SEARCH) {
            // id
            $this->id->EditAttrs["class"] = "form-control";
            $this->id->EditCustomAttributes = "";
            $this->id->EditValue = HtmlEncode($this->id->AdvancedSearch->SearchValue);
            $this->id->PlaceHolder = RemoveHtml($this->id->caption());

            // data
            $this->data->EditAttrs["class"] = "form-control";
            $this->data->EditCustomAttributes = "";
            $this->data->EditValue = HtmlEncode(FormatDateTime(UnFormatDateTime($this->data->AdvancedSearch->SearchValue, 0), 8));
            $this->data->PlaceHolder = RemoveHtml($this->data->caption());

            // progress
            $this->progress->EditCustomAttributes = "";
            $this->progress->EditValue = $this->progress->options(false);
            $this->progress->PlaceHolder = RemoveHtml($this->progress->caption());

            // persone
            $this->persone->EditAttrs["class"] = "form-control";
            $this->persone->EditCustomAttributes = "";
            $this->persone->EditValue = HtmlEncode($this->persone->AdvancedSearch->SearchValue);
            $this->persone->PlaceHolder = RemoveHtml($this->persone->caption());

            // data_arrivo
            $this->data_arrivo->EditAttrs["class"] = "form-control";
            $this->data_arrivo->EditCustomAttributes = "";
            $this->data_arrivo->EditValue = HtmlEncode(FormatDateTime(UnFormatDateTime($this->data_arrivo->AdvancedSearch->SearchValue, 1), 8));
            $this->data_arrivo->PlaceHolder = RemoveHtml($this->data_arrivo->caption());

            // data_partenza
            $this->data_partenza->EditAttrs["class"] = "form-control";
            $this->data_partenza->EditCustomAttributes = "";
            $this->data_partenza->EditValue = HtmlEncode(FormatDateTime(UnFormatDateTime($this->data_partenza->AdvancedSearch->SearchValue, 1), 8));
            $this->data_partenza->PlaceHolder = RemoveHtml($this->data_partenza->caption());

            // piazzola_id
            $this->piazzola_id->EditAttrs["class"] = "form-control";
            $this->piazzola_id->EditCustomAttributes = "";
            $curVal = trim(strval($this->piazzola_id->AdvancedSearch->SearchValue));
            if ($curVal != "") {
                $this->piazzola_id->AdvancedSearch->ViewValue = $this->piazzola_id->lookupCacheOption($curVal);
            } else {
                $this->piazzola_id->AdvancedSearch->ViewValue = $this->piazzola_id->Lookup !== null && is_array($this->piazzola_id->Lookup->Options) ? $curVal : null;
            }
            if ($this->piazzola_id->AdvancedSearch->ViewValue !== null) { // Load from cache
                $this->piazzola_id->EditValue = array_values($this->piazzola_id->Lookup->Options);
            } else { // Lookup from database
                if ($curVal == "") {
                    $filterWrk = "0=1";
                } else {
                    $filterWrk = "`id`" . SearchString("=", $this->piazzola_id->AdvancedSearch->SearchValue, DATATYPE_NUMBER, "");
                }
                $sqlWrk = $this->piazzola_id->Lookup->getSql(true, $filterWrk, '', $this, false, true);
                $rswrk = Conn()->executeQuery($sqlWrk)->fetchAll(\PDO::FETCH_BOTH);
                $ari = count($rswrk);
                $arwrk = $rswrk;
                $this->piazzola_id->EditValue = $arwrk;
            }
            $this->piazzola_id->PlaceHolder = RemoveHtml($this->piazzola_id->caption());

            // piazzola
            $this->piazzola->EditAttrs["class"] = "form-control";
            $this->piazzola->EditCustomAttributes = "";
            if (!$this->piazzola->Raw) {
                $this->piazzola->AdvancedSearch->SearchValue = HtmlDecode($this->piazzola->AdvancedSearch->SearchValue);
            }
            $this->piazzola->EditValue = HtmlEncode($this->piazzola->AdvancedSearch->SearchValue);
            $this->piazzola->PlaceHolder = RemoveHtml($this->piazzola->caption());

            // nome
            $this->nome->EditAttrs["class"] = "form-control";
            $this->nome->EditCustomAttributes = "";
            if (!$this->nome->Raw) {
                $this->nome->AdvancedSearch->SearchValue = HtmlDecode($this->nome->AdvancedSearch->SearchValue);
            }
            $this->nome->EditValue = HtmlEncode($this->nome->AdvancedSearch->SearchValue);
            $this->nome->PlaceHolder = RemoveHtml($this->nome->caption());

            // cognome
            $this->cognome->EditAttrs["class"] = "form-control";
            $this->cognome->EditCustomAttributes = "";
            if (!$this->cognome->Raw) {
                $this->cognome->AdvancedSearch->SearchValue = HtmlDecode($this->cognome->AdvancedSearch->SearchValue);
            }
            $this->cognome->EditValue = HtmlEncode($this->cognome->AdvancedSearch->SearchValue);
            $this->cognome->PlaceHolder = RemoveHtml($this->cognome->caption());

            // importo
            $this->importo->EditAttrs["class"] = "form-control";
            $this->importo->EditCustomAttributes = "";
            $this->importo->EditValue = HtmlEncode($this->importo->AdvancedSearch->SearchValue);
            $this->importo->PlaceHolder = RemoveHtml($this->importo->caption());

            // email
            $this->_email->EditAttrs["class"] = "form-control";
            $this->_email->EditCustomAttributes = "";
            if (!$this->_email->Raw) {
                $this->_email->AdvancedSearch->SearchValue = HtmlDecode($this->_email->AdvancedSearch->SearchValue);
            }
            $this->_email->EditValue = HtmlEncode($this->_email->AdvancedSearch->SearchValue);
            $this->_email->PlaceHolder = RemoveHtml($this->_email->caption());

            // prefisso
            $this->prefisso->EditAttrs["class"] = "form-control";
            $this->prefisso->EditCustomAttributes = "";
            if (!$this->prefisso->Raw) {
                $this->prefisso->AdvancedSearch->SearchValue = HtmlDecode($this->prefisso->AdvancedSearch->SearchValue);
            }
            $this->prefisso->EditValue = HtmlEncode($this->prefisso->AdvancedSearch->SearchValue);
            $this->prefisso->PlaceHolder = RemoveHtml($this->prefisso->caption());

            // telefono
            $this->telefono->EditAttrs["class"] = "form-control";
            $this->telefono->EditCustomAttributes = "";
            if (!$this->telefono->Raw) {
                $this->telefono->AdvancedSearch->SearchValue = HtmlDecode($this->telefono->AdvancedSearch->SearchValue);
            }
            $this->telefono->EditValue = HtmlEncode($this->telefono->AdvancedSearch->SearchValue);
            $this->telefono->PlaceHolder = RemoveHtml($this->telefono->caption());

            // codice_fiscale
            $this->codice_fiscale->EditAttrs["class"] = "form-control";
            $this->codice_fiscale->EditCustomAttributes = "";
            if (!$this->codice_fiscale->Raw) {
                $this->codice_fiscale->AdvancedSearch->SearchValue = HtmlDecode($this->codice_fiscale->AdvancedSearch->SearchValue);
            }
            $this->codice_fiscale->EditValue = HtmlEncode($this->codice_fiscale->AdvancedSearch->SearchValue);
            $this->codice_fiscale->PlaceHolder = RemoveHtml($this->codice_fiscale->caption());

            // targa
            $this->targa->EditAttrs["class"] = "form-control";
            $this->targa->EditCustomAttributes = "";
            if (!$this->targa->Raw) {
                $this->targa->AdvancedSearch->SearchValue = HtmlDecode($this->targa->AdvancedSearch->SearchValue);
            }
            $this->targa->EditValue = HtmlEncode($this->targa->AdvancedSearch->SearchValue);
            $this->targa->PlaceHolder = RemoveHtml($this->targa->caption());

            // country
            $this->country->EditAttrs["class"] = "form-control";
            $this->country->EditCustomAttributes = "";
            if (!$this->country->Raw) {
                $this->country->AdvancedSearch->SearchValue = HtmlDecode($this->country->AdvancedSearch->SearchValue);
            }
            $this->country->EditValue = HtmlEncode($this->country->AdvancedSearch->SearchValue);
            $this->country->PlaceHolder = RemoveHtml($this->country->caption());

            // invio_whatsapp
            $this->invio_whatsapp->EditCustomAttributes = "";
            $this->invio_whatsapp->EditValue = $this->invio_whatsapp->options(false);
            $this->invio_whatsapp->PlaceHolder = RemoveHtml($this->invio_whatsapp->caption());

            // presenza_disabili
            $this->presenza_disabili->EditCustomAttributes = "";
            $this->presenza_disabili->EditValue = $this->presenza_disabili->options(false);
            $this->presenza_disabili->PlaceHolder = RemoveHtml($this->presenza_disabili->caption());

            // note
            $this->note->EditAttrs["class"] = "form-control";
            $this->note->EditCustomAttributes = "";
            $this->note->EditValue = HtmlEncode($this->note->AdvancedSearch->SearchValue);
            $this->note->PlaceHolder = RemoveHtml($this->note->caption());

            // wa_inviato
            $this->wa_inviato->EditCustomAttributes = "";
            $this->wa_inviato->EditValue = $this->wa_inviato->options(false);
            $this->wa_inviato->PlaceHolder = RemoveHtml($this->wa_inviato->caption());
        }
        if ($this->RowType == ROWTYPE_ADD || $this->RowType == ROWTYPE_EDIT || $this->RowType == ROWTYPE_SEARCH) { // Add/Edit/Search row
            $this->setupFieldTitles();
        }

        // Call Row Rendered event
        if ($this->RowType != ROWTYPE_AGGREGATEINIT) {
            $this->rowRendered();
        }
    }

    // Validate search
    protected function validateSearch()
    {
        // Check if validation required
        if (!Config("SERVER_VALIDATE")) {
            return true;
        }
        if (!CheckInteger($this->id->AdvancedSearch->SearchValue)) {
            $this->id->addErrorMessage($this->id->getErrorMessage(false));
        }
        if (!CheckDate($this->data->AdvancedSearch->SearchValue)) {
            $this->data->addErrorMessage($this->data->getErrorMessage(false));
        }
        if (!CheckInteger($this->persone->AdvancedSearch->SearchValue)) {
            $this->persone->addErrorMessage($this->persone->getErrorMessage(false));
        }
        if (!CheckNumber($this->importo->AdvancedSearch->SearchValue)) {
            $this->importo->addErrorMessage($this->importo->getErrorMessage(false));
        }

        // Return validate result
        $validateSearch = !$this->hasInvalidFields();

        // Call Form_CustomValidate event
        $formCustomError = "";
        $validateSearch = $validateSearch && $this->formCustomValidate($formCustomError);
        if ($formCustomError != "") {
            $this->setFailureMessage($formCustomError);
        }
        return $validateSearch;
    }

    // Load advanced search
    public function loadAdvancedSearch()
    {
        $this->id->AdvancedSearch->load();
        $this->data->AdvancedSearch->load();
        $this->progress->AdvancedSearch->load();
        $this->persone->AdvancedSearch->load();
        $this->data_arrivo->AdvancedSearch->load();
        $this->data_partenza->AdvancedSearch->load();
        $this->piazzola_id->AdvancedSearch->load();
        $this->piazzola->AdvancedSearch->load();
        $this->nome->AdvancedSearch->load();
        $this->cognome->AdvancedSearch->load();
        $this->importo->AdvancedSearch->load();
        $this->_email->AdvancedSearch->load();
        $this->prefisso->AdvancedSearch->load();
        $this->telefono->AdvancedSearch->load();
        $this->codice_fiscale->AdvancedSearch->load();
        $this->targa->AdvancedSearch->load();
        $this->country->AdvancedSearch->load();
        $this->invio_whatsapp->AdvancedSearch->load();
        $this->presenza_disabili->AdvancedSearch->load();
        $this->note->AdvancedSearch->load();
        $this->wa_inviato->AdvancedSearch->load();
    }

    // Set up Breadcrumb
    protected function setupBreadcrumb()
    {
        global $Breadcrumb, $Language;
        $Breadcrumb = new Breadcrumb("index");
        $url = CurrentUrl();
        $Breadcrumb->add("list", $this->TableVar, $this->addMasterUrl("OrdiniList"), "", $this->TableVar, true);
        $pageId = "search";
        $Breadcrumb->add("search", $pageId, $url);
    }

    // Setup lookup options
    public function setupLookupOptions($fld)
    {
        if ($fld->Lookup !== null && $fld->Lookup->Options === null) {
            // Get default connection and filter
            $conn = $this->getConnection();
            $lookupFilter = "";

            // No need to check any more
            $fld->Lookup->Options = [];

            // Set up lookup SQL and connection
            switch ($fld->FieldVar) {
                case "x_status":
                    break;
                case "x_progress":
                    break;
                case "x_piazzola_id":
                    break;
                case "x_invio_whatsapp":
                    break;
                case "x_presenza_disabili":
                    break;
                case "x_wa_inviato":
                    break;
                default:
                    $lookupFilter = "";
                    break;
            }

            // Always call to Lookup->getSql so that user can setup Lookup->Options in Lookup_Selecting server event
            $sql = $fld->Lookup->getSql(false, "", $lookupFilter, $this);

            // Set up lookup cache
            if ($fld->UseLookupCache && $sql != "" && count($fld->Lookup->Options) == 0) {
                $totalCnt = $this->getRecordCount($sql, $conn);
                if ($totalCnt > $fld->LookupCacheCount) { // Total count > cache count, do not cache
                    return;
                }
                $rows = $conn->executeQuery($sql)->fetchAll(\PDO::FETCH_BOTH);
                $ar = [];
                foreach ($rows as $row) {
                    $row = $fld->Lookup->renderViewRow($row);
                    $ar[strval($row[0])] = $row;
                }
                $fld->Lookup->Options = $ar;
            }
        }
    }

    // Page Load event
    public function pageLoad()
    {
        //Log("Page Load");
    }

    // Page Unload event
    public function pageUnload()
    {
        //Log("Page Unload");
    }

    // Page Redirecting event
    public function pageRedirecting(&$url)
    {
        // Example:
        //$url = "your URL";
    }

    // Message Showing event
    // $type = ''|'success'|'failure'|'warning'
    public function messageShowing(&$msg, $type)
    {
        if ($type == 'success') {
            //$msg = "your success message";
        } elseif ($type == 'failure') {
            //$msg = "your failure message";
        } elseif ($type == 'warning') {
            //$msg = "your warning message";
        } else {
            //$msg = "your message";
        }
    }

    // Page Render event
    public function pageRender()
    {
        //Log("Page Render");
    }

    // Page Data Rendering event
    public function pageDataRendering(&$header)
    {
        // Example:
        //$header = "your header";
    }

    // Page Data Rendered event
    public function pageDataRendered(&$footer)
    {
        // Example:
        //$footer = "your footer";
    }

    // Form Custom Validate event
    public function formCustomValidate(&$customError)
    {
        // Return error message in CustomError
        return true;
    }
}
