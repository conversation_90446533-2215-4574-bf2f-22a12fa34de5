{"version": 3, "sources": ["pStrength.jquery.js"], "names": ["$", "settings", "numbers_array", "Array", "upper_letters_array", "lower_letters_array", "special_chars_array", "pStrengthElementsDefaultStyle", "methods", "init", "options", "callbacks", "extend", "bind", "changeBackground", "backgrounds", "passwordValidFrom", "onValidatePassword", "percentage", "onPasswordStrengthChanged", "passwordStrength", "i", "push", "this", "each", "proxy", "idx", "pStrengthElement", "background", "css", "color", "calculatePasswordStrength", "call", "data", "undefined", "background-color", "resetStyle", "ord", "string", "str", "code", "charCodeAt", "hi", "length", "numbers_found", "upper_letters_found", "lower_letters_found", "special_chars_found", "text", "val", "trim", "Math", "floor", "inArray", "char<PERSON>t", "behaviour", "strengthPercentage", "ceil", "fn", "pStrength", "method", "apply", "prototype", "slice", "arguments", "error", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;CASA,SAAUA,GACN,IAKIC,EALAC,EAAgB,IAAIC,MACpBC,EAAsB,IAAID,MAC1BE,EAAsB,IAAIF,MAC1BG,EAAsB,IAAIH,MAC1BI,EAAgC,IAAIJ,MAEpCK,EAAU,CACNC,KAAO,SAAUC,EAASC,GAEtBV,EAAWD,EAAEY,OAAO,CAChBC,KAAQ,eACRC,kBAAoB,EACpBC,YAAe,CACX,CAAC,UAAW,QAAS,CAAC,UAAW,QAAS,CAAC,UAAW,QAAS,CAAC,UAAW,QAC3E,CAAC,UAAW,QAAS,CAAC,UAAW,QAAS,CAAC,UAAW,QAAS,CAAC,UAAW,QAC3E,CAAC,UAAW,QAAS,CAAC,UAAW,QAAS,CAAC,UAAW,QAAS,CAAC,UAAW,QAAS,CAAC,UAAW,SAEpGC,kBAAqB,GACrBC,mBAAsB,SAASC,KAC/BC,0BAA6B,SAASC,EAAkBF,MACzDR,GAEH,IAAI,IAAIW,EAAI,GAAIA,EAAI,GAAIA,IACpBnB,EAAcoB,KAAKD,GACvB,IAAIA,EAAI,GAAIA,EAAI,GAAIA,IAChBjB,EAAoBkB,KAAKD,GAC7B,IAAIA,EAAI,GAAIA,EAAI,IAAKA,IACjBhB,EAAoBiB,KAAKD,GAC7B,IAAIA,EAAI,GAAIA,EAAI,GAAIA,IAChBf,EAAoBgB,KAAKD,GAC7B,IAAIA,EAAI,GAAIA,EAAI,GAAIA,IAChBf,EAAoBgB,KAAKD,GAC7B,IAAIA,EAAI,GAAIA,EAAI,GAAIA,IAChBf,EAAoBgB,KAAKD,GAC7B,IAAIA,EAAI,IAAKA,EAAI,IAAKA,IAClBf,EAAoBgB,KAAKD,GAE7B,OAAOE,KAAKC,KAAKxB,EAAEyB,OAAM,SAAUC,EAAKC,GAEpCpB,EAA8BP,EAAE2B,IAAqB,CACjDC,WAAc5B,EAAE2B,GAAkBE,IAAI,cACtCC,MAAS9B,EAAE2B,GAAkBE,IAAI,UAGrCE,EAA0BC,KAAKL,GAE/B3B,EAAE2B,GAAkBM,KAAK,aAAa,GAAMpB,KAAKZ,EAASY,MAAM,WAC5DkB,EAA0BC,KAAKT,KAAMI,QAG1CJ,QAGPT,iBAAkB,SAASa,EAAkBP,QAChBc,IAArBd,IACAA,EAAmBO,EACnBA,EAAmB3B,EAAEuB,OAEzBH,EAAmBA,EAAmB,GAAK,GAAKA,EAEhDpB,EAAE2B,GAAkBE,IAAI,CACpBM,mBAAoBlC,EAASc,YAAYK,GAAkB,GAC3DU,MAAS7B,EAASc,YAAYK,GAAkB,MAIxDgB,WAAY,SAAST,GACjB3B,EAAE2B,GAAkBE,IAAItB,EAA8BP,EAAE2B,OAIhEU,EAAM,SAASC,GACf,IAAIC,EAAMD,EAAS,GACfE,EAAOD,EAAIE,WAAW,GAC1B,GAAI,OAAUD,GAAQA,GAAQ,MAAQ,CAClC,IAAIE,EAAKF,EACT,OAAmB,IAAfD,EAAII,OACCH,EAGe,MAAfE,EAAK,QADJH,EAAIE,WAAW,GACe,OAAU,MAGtD,OACWD,GAKXT,EAA4B,WAC5B,IAAIX,EAAsB,EACtBwB,EAAsB,EACtBC,EAAsB,EACtBC,EAAsB,EACtBC,EAAsB,EACtBC,EAAOhD,EAAEuB,MAAM0B,MAAMC,OAEzB9B,GAAoB,EAAI+B,KAAKC,MAAMJ,EAAKL,OAAS,GAEjD,IAAI,IAAItB,EAAI,EAAGA,EAAI2B,EAAKL,OAAQtB,KACyB,GAAlDrB,EAAEqD,QAAQhB,EAAIW,EAAKM,OAAOjC,IAAKnB,IAAwB0C,EAAgB,GACtExB,IACAwB,MAGuD,GAAxD5C,EAAEqD,QAAQhB,EAAIW,EAAKM,OAAOjC,IAAKjB,IAA8ByC,EAAsB,GAClFzB,IACAyB,MAGuD,GAAxD7C,EAAEqD,QAAQhB,EAAIW,EAAKM,OAAOjC,IAAKhB,IAA8ByC,EAAsB,GAClF1B,IACA0B,MAGuD,GAAxD9C,EAAEqD,QAAQhB,EAAIW,EAAKM,OAAOjC,IAAKf,IAA8ByC,EAAsB,IAClF3B,IACA2B,KAOR,OAFAQ,EAAUvB,KAAKhC,EAAEuB,MAAOH,GAEjBA,GAGNmC,EAAY,SAASnC,GACtB,IAAIoC,EAAqBL,KAAKM,KAAwB,IAAnBrC,EAAyB,IACxDoC,EAAqBA,EAAqB,IAAM,IAAMA,EAE1DvD,EAASkB,0BAA0Ba,KAAKhC,EAAEuB,MAAOH,EAAkBoC,GAC/DA,GAAsBvD,EAASe,mBAC/Bf,EAASgB,mBAAmBe,KAAKhC,EAAEuB,MAAOiC,GAG1CvD,EAASa,kBACTN,EAAQM,iBAAiBkB,KAAKhC,EAAEuB,MAAOH,IAI/CpB,EAAE0D,GAAGC,UAAY,SAASC,GACtB,OAAKpD,EAAQoD,GACApD,EAAQoD,GAAQC,MAAOtC,KAAMpB,MAAM2D,UAAUC,MAAM/B,KAAMgC,UAAW,IACnD,iBAAXJ,GAAyBA,OAGtC5D,EAAEiE,MAAO,UAAaL,EAAS,wCAFxBpD,EAAQC,KAAKoD,MAAOtC,KAAMyC,YAlJ/C,CAuJGE", "sourcesContent": ["/*!\r\n * pStrength jQuery Plugin v1.0.6\r\n * http://accountspassword.com/pstrength-jquery-plugin\r\n *\r\n * Created by AccountsPassword.com\r\n * Released under the MIT License (Feel free to copy, modify or redistribute this plugin.)\r\n *\r\n */\r\n \r\n(function($){\r\n    var numbers_array = new Array(),\r\n        upper_letters_array = new Array(),\r\n        lower_letters_array = new Array(),\r\n        special_chars_array = new Array(),\r\n        pStrengthElementsDefaultStyle = new Array(),\r\n        settings,\r\n        methods = {\r\n            init : function( options, callbacks) {\r\n            \r\n                settings = $.extend({\r\n                    'bind': 'keyup change',\r\n                    'changeBackground': true,\r\n                    'backgrounds': [\r\n                        ['#cc0000', '#FFF'], ['#cc3333', '#FFF'], ['#cc6666', '#FFF'], ['#ff9999', '#FFF'],\r\n                        ['#e0941c', '#FFF'], ['#e8a53a', '#FFF'], ['#eab259', '#FFF'], ['#efd09e', '#FFF'],\r\n                        ['#ccffcc', '#FFF'], ['#66cc66', '#FFF'], ['#339933', '#FFF'], ['#006600', '#FFF'], ['#105610', '#FFF']\r\n                    ],\r\n                    'passwordValidFrom': 60, // 60%\r\n                    'onValidatePassword': function(percentage) { },\r\n                    'onPasswordStrengthChanged': function(passwordStrength, percentage) { }\r\n                }, options);\r\n                         \r\n                for(var i = 48; i < 58; i++)\r\n                    numbers_array.push(i);\r\n                for(i = 65; i < 91; i++)\r\n                    upper_letters_array.push(i);\r\n                for(i = 97; i < 123; i++)\r\n                    lower_letters_array.push(i);\r\n                for(i = 32; i < 48; i++)\r\n                    special_chars_array.push(i);\r\n                for(i = 58; i < 65; i++)\r\n                    special_chars_array.push(i);\r\n                for(i = 91; i < 97; i++)\r\n                    special_chars_array.push(i);\r\n                for(i = 123; i < 127; i++)\r\n                    special_chars_array.push(i);\r\n                \r\n                return this.each($.proxy(function (idx, pStrengthElement) {\r\n\r\n                    pStrengthElementsDefaultStyle[$(pStrengthElement)] = {\r\n                        'background': $(pStrengthElement).css('background'),\r\n                        'color': $(pStrengthElement).css('color')\r\n                    }\r\n                    \r\n                    calculatePasswordStrength.call(pStrengthElement);\r\n                    \r\n                    $(pStrengthElement).data(\"pStrength\", true).bind(settings.bind, function(){\r\n                        calculatePasswordStrength.call(this, pStrengthElement); //***\r\n                    });\r\n                    \r\n                }, this));\r\n            },\r\n            \r\n            changeBackground: function(pStrengthElement, passwordStrength) {\r\n                if (passwordStrength === undefined) {\r\n                    passwordStrength = pStrengthElement;\r\n                    pStrengthElement = $(this);\r\n                }\r\n                passwordStrength = passwordStrength > 12 ? 12 : passwordStrength;\r\n                \r\n                $(pStrengthElement).css({\r\n                    'background-color': settings.backgrounds[passwordStrength][0],\r\n                    'color': settings.backgrounds[passwordStrength][1]\r\n                });\r\n            },\r\n            \r\n            resetStyle: function(pStrengthElement) {\r\n                $(pStrengthElement).css(pStrengthElementsDefaultStyle[$(pStrengthElement)]);\r\n            }\r\n        };\r\n      \r\n    var ord = function(string) {\r\n        var str = string + '',\r\n            code = str.charCodeAt(0);\r\n        if (0xD800 <= code && code <= 0xDBFF) {\r\n            var hi = code;\r\n            if (str.length === 1) {\r\n              return code;\r\n            }\r\n            var low = str.charCodeAt(1);\r\n            return ((hi - 0xD800) * 0x400) + (low - 0xDC00) + 0x10000;\r\n        }\r\n        \r\n        if (0xDC00 <= code && code <= 0xDFFF) {\r\n            return code;\r\n        }\r\n          return code;\r\n    }\r\n    \r\n    var calculatePasswordStrength = function(){\r\n        var passwordStrength    = 0,\r\n            numbers_found       = 0,\r\n            upper_letters_found = 0,\r\n            lower_letters_found = 0,\r\n            special_chars_found = 0,\r\n            text = $(this).val().trim();\r\n                        \r\n        passwordStrength += 2 * Math.floor(text.length / 8);\r\n        \r\n        for(var i = 0; i < text.length; i++) {\r\n            if($.inArray(ord(text.charAt(i)), numbers_array) != -1 && numbers_found < 2) {\r\n                passwordStrength++;\r\n                numbers_found++;\r\n                continue;\r\n            }\r\n            if($.inArray(ord(text.charAt(i)), upper_letters_array) != -1 && upper_letters_found < 2) {\r\n                passwordStrength++;\r\n                upper_letters_found++;\r\n                continue;\r\n            }\r\n            if($.inArray(ord(text.charAt(i)), lower_letters_array) != -1 && lower_letters_found < 2) {\r\n                passwordStrength++;\r\n                lower_letters_found++;\r\n                continue;\r\n            }\r\n            if($.inArray(ord(text.charAt(i)), special_chars_array) != -1 && special_chars_found < 2) {\r\n                passwordStrength++;\r\n                special_chars_found++;\r\n                continue;\r\n            }\r\n        }\r\n        \r\n        behaviour.call($(this), passwordStrength);\r\n        \r\n        return passwordStrength;\r\n     }\r\n     \r\n     var behaviour = function(passwordStrength) {\r\n        var strengthPercentage = Math.ceil(passwordStrength * 100 / 12);\r\n            strengthPercentage = strengthPercentage > 100 ? 100 : strengthPercentage;\r\n            \r\n        settings.onPasswordStrengthChanged.call($(this), passwordStrength, strengthPercentage);\r\n        if (strengthPercentage >= settings.passwordValidFrom) {\r\n            settings.onValidatePassword.call($(this), strengthPercentage);\r\n        }\r\n        \r\n        if (settings.changeBackground) {\r\n            methods.changeBackground.call($(this), passwordStrength);\r\n        }     \r\n    }\r\n\r\n    $.fn.pStrength = function(method) {\r\n        if ( methods[method] ) {\r\n              return methods[method].apply( this, Array.prototype.slice.call( arguments, 1 ));\r\n        } else if ( typeof method === 'object' || ! method ) {\r\n              return methods.init.apply( this, arguments );\r\n        } else {\r\n              $.error( 'Method ' +  method + ' does not exists on jQuery.pStrength' );\r\n        }\r\n      };\r\n})(jQuery);\r\n"]}