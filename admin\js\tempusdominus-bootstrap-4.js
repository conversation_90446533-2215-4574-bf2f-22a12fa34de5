/*@preserve
 * Tempus Dominus Bootstrap4 v5.1.2 (https://tempusdominus.github.io/bootstrap-4/)
 * Copyright 2016-2018 <PERSON>
 * Licensed under MIT (https://github.com/tempusdominus/bootstrap-3/blob/master/LICENSE)
 */
if("undefined"==typeof jQuery)throw new Error("Tempus Dominus Bootstrap4's requires jQuery. jQuery must be included before Tempus Dominus Bootstrap4's JavaScript.");if(function(t){var e=jQuery.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||e[0]>=4)throw new Error("Tempus Dominus Bootstrap4's requires at least jQuery v3.0.0 but less than v4.0.0")}(),"undefined"==typeof moment)throw new Error("Tempus Dominus Bootstrap4's requires moment.js. Moment.js must be included before Tempus Dominus Bootstrap4's JavaScript.");var version=moment.version.split(".");if(version[0]<=2&&version[1]<17||version[0]>=3)throw new Error("Tempus Dominus Bootstrap4's requires at least moment.js v2.17.0 but less than v3.0.0");!function(){var t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e=function(){function t(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(e,i,s){return i&&t(e.prototype,i),s&&t(e,s),e}}();function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var s=function(t,s){var o="datetimepicker",a=".datetimepicker",n={DATA_TOGGLE:'[data-toggle="datetimepicker"]'},r={INPUT:o+"-input"},d={CHANGE:"change"+a,BLUR:"blur"+a,KEYUP:"keyup"+a,KEYDOWN:"keydown"+a,FOCUS:"focus"+a,CLICK_DATA_API:"click"+a+".data-api",UPDATE:"update"+a,ERROR:"error"+a,HIDE:"hide"+a,SHOW:"show"+a},p=[{CLASS_NAME:"days",NAV_FUNCTION:"M",NAV_STEP:1},{CLASS_NAME:"months",NAV_FUNCTION:"y",NAV_STEP:1},{CLASS_NAME:"years",NAV_FUNCTION:"y",NAV_STEP:10},{CLASS_NAME:"decades",NAV_FUNCTION:"y",NAV_STEP:100}],h={up:38,38:"up",down:40,40:"down",left:37,37:"left",right:39,39:"right",tab:9,9:"tab",escape:27,27:"escape",enter:13,13:"enter",pageUp:33,33:"pageUp",pageDown:34,34:"pageDown",shift:16,16:"shift",control:17,17:"control",space:32,32:"space",t:84,84:"t",delete:46,46:"delete"},l=["times","days","months","years","decades"],c={},u={},_={timeZone:"",format:!1,dayViewHeaderFormat:"MMMM YYYY",extraFormats:!1,stepping:1,minDate:!1,maxDate:!1,useCurrent:!0,collapse:!0,locale:s.locale(),defaultDate:!1,disabledDates:!1,enabledDates:!1,icons:{time:"fa fa-clock-o",date:"fa fa-calendar",up:"fa fa-arrow-up",down:"fa fa-arrow-down",previous:"fa fa-chevron-left",next:"fa fa-chevron-right",today:"fa fa-calendar-check-o",clear:"fa fa-delete",close:"fa fa-times"},tooltips:{today:"Go to today",clear:"Clear selection",close:"Close the picker",selectMonth:"Select Month",prevMonth:"Previous Month",nextMonth:"Next Month",selectYear:"Select Year",prevYear:"Previous Year",nextYear:"Next Year",selectDecade:"Select Decade",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevCentury:"Previous Century",nextCentury:"Next Century",pickHour:"Pick Hour",incrementHour:"Increment Hour",decrementHour:"Decrement Hour",pickMinute:"Pick Minute",incrementMinute:"Increment Minute",decrementMinute:"Decrement Minute",pickSecond:"Pick Second",incrementSecond:"Increment Second",decrementSecond:"Decrement Second",togglePeriod:"Toggle Period",selectTime:"Select Time",selectDate:"Select Date"},useStrict:!1,sideBySide:!1,daysOfWeekDisabled:!1,calendarWeeks:!1,viewMode:"days",toolbarPlacement:"default",buttons:{showToday:!1,showClear:!1,showClose:!1},widgetPositioning:{horizontal:"auto",vertical:"auto"},widgetParent:null,ignoreReadonly:!1,keepOpen:!1,focusOnShow:!0,inline:!1,keepInvalid:!1,keyBinds:{up:function(){if(!this.widget)return!1;var t=this._dates[0]||this.getMoment();return this.widget.find(".datepicker").is(":visible")?this.date(t.clone().subtract(7,"d")):this.date(t.clone().add(this.stepping(),"m")),!0},down:function(){if(!this.widget)return this.show(),!1;var t=this._dates[0]||this.getMoment();return this.widget.find(".datepicker").is(":visible")?this.date(t.clone().add(7,"d")):this.date(t.clone().subtract(this.stepping(),"m")),!0},"control up":function(){if(!this.widget)return!1;var t=this._dates[0]||this.getMoment();return this.widget.find(".datepicker").is(":visible")?this.date(t.clone().subtract(1,"y")):this.date(t.clone().add(1,"h")),!0},"control down":function(){if(!this.widget)return!1;var t=this._dates[0]||this.getMoment();return this.widget.find(".datepicker").is(":visible")?this.date(t.clone().add(1,"y")):this.date(t.clone().subtract(1,"h")),!0},left:function(){if(!this.widget)return!1;var t=this._dates[0]||this.getMoment();return this.widget.find(".datepicker").is(":visible")&&this.date(t.clone().subtract(1,"d")),!0},right:function(){if(!this.widget)return!1;var t=this._dates[0]||this.getMoment();return this.widget.find(".datepicker").is(":visible")&&this.date(t.clone().add(1,"d")),!0},pageUp:function(){if(!this.widget)return!1;var t=this._dates[0]||this.getMoment();return this.widget.find(".datepicker").is(":visible")&&this.date(t.clone().subtract(1,"M")),!0},pageDown:function(){if(!this.widget)return!1;var t=this._dates[0]||this.getMoment();return this.widget.find(".datepicker").is(":visible")&&this.date(t.clone().add(1,"M")),!0},enter:function(){return!!this.widget&&(this.hide(),!0)},escape:function(){return!!this.widget&&(this.hide(),!0)},"control space":function(){return!!this.widget&&(this.widget.find(".timepicker").is(":visible")&&this.widget.find('.btn[data-action="togglePeriod"]').click(),!0)},t:function(){return!!this.widget&&(this.date(this.getMoment()),!0)},delete:function(){return!!this.widget&&(this.clear(),!0)}},debug:!1,allowInputToggle:!1,disabledTimeIntervals:!1,disabledHours:!1,enabledHours:!1,viewDate:!1,allowMultidate:!1,multidateSeparator:","};return function(){function f(t,e){i(this,f),this._options=this._getOptions(e),this._element=t,this._dates=[],this._datesFormatted=[],this._viewDate=null,this.unset=!0,this.component=!1,this.widget=!1,this.use24Hours=null,this.actualFormat=null,this.parseFormats=null,this.currentViewMode=null,this.MinViewModeNumber=0,this._int()}return f.prototype._int=function(){var e=this._element.data("target-input");this._element.is("input")?this.input=this._element:void 0!==e&&(this.input="nearest"===e?this._element.find("input"):t(e)),this._dates=[],this._dates[0]=this.getMoment(),this._viewDate=this.getMoment().clone(),t.extend(!0,this._options,this._dataToOptions()),this.options(this._options),this._initFormatting(),void 0!==this.input&&this.input.is("input")&&0!==this.input.val().trim().length?this._setValue(this._parseInputDate(this.input.val().trim()),0):this._options.defaultDate&&void 0!==this.input&&void 0===this.input.attr("placeholder")&&this._setValue(this._options.defaultDate,0),this._options.inline&&this.show()},f.prototype._update=function(){this.widget&&(this._fillDate(),this._fillTime())},f.prototype._setValue=function(t,e){var i=this.unset?null:this._dates[e],s="";if(!t)return this._options.allowMultidate&&1!==this._dates.length?(s=(s=this._element.data("date")+",").replace(i.format(this.actualFormat)+",","").replace(",,","").replace(/,\s*$/,""),this._dates.splice(e,1),this._datesFormatted.splice(e,1)):(this.unset=!0,this._dates=[],this._datesFormatted=[]),void 0!==this.input&&(this.input.val(s),this.input.trigger("input")),this._element.data("date",s),this._notifyEvent({type:f.Event.CHANGE,date:!1,oldDate:i}),void this._update();if(t=t.clone().locale(this._options.locale),this._hasTimeZone()&&t.tz(this._options.timeZone),1!==this._options.stepping&&t.minutes(Math.round(t.minutes()/this._options.stepping)*this._options.stepping).seconds(0),this._isValid(t)){if(this._dates[e]=t,this._datesFormatted[e]=t.format("YYYY-MM-DD"),this._viewDate=t.clone(),this._options.allowMultidate&&this._dates.length>1){for(var o=0;o<this._dates.length;o++)s+=""+this._dates[o].format(this.actualFormat)+this._options.multidateSeparator;s=s.replace(/,\s*$/,"")}else s=this._dates[e].format(this.actualFormat);void 0!==this.input&&(this.input.val(s),this.input.trigger("input")),this._element.data("date",s),this.unset=!1,this._update(),this._notifyEvent({type:f.Event.CHANGE,date:this._dates[e].clone(),oldDate:i})}else this._options.keepInvalid?this._notifyEvent({type:f.Event.CHANGE,date:t,oldDate:i}):void 0!==this.input&&(this.input.val(""+(this.unset?"":this._dates[e].format(this.actualFormat))),this.input.trigger("input")),this._notifyEvent({type:f.Event.ERROR,date:t,oldDate:i})},f.prototype._change=function(e){var i=t(e.target).val().trim(),s=i?this._parseInputDate(i):null;return this._setValue(s),e.stopImmediatePropagation(),!1},f.prototype._getOptions=function(e){return e=t.extend(!0,{},_,e)},f.prototype._hasTimeZone=function(){return void 0!==s.tz&&void 0!==this._options.timeZone&&null!==this._options.timeZone&&""!==this._options.timeZone},f.prototype._isEnabled=function(t){if("string"!=typeof t||t.length>1)throw new TypeError("isEnabled expects a single character string parameter");switch(t){case"y":return-1!==this.actualFormat.indexOf("Y");case"M":return-1!==this.actualFormat.indexOf("M");case"d":return-1!==this.actualFormat.toLowerCase().indexOf("d");case"h":case"H":return-1!==this.actualFormat.toLowerCase().indexOf("h");case"m":return-1!==this.actualFormat.indexOf("m");case"s":return-1!==this.actualFormat.indexOf("s");case"a":case"A":return-1!==this.actualFormat.toLowerCase().indexOf("a");default:return!1}},f.prototype._hasTime=function(){return this._isEnabled("h")||this._isEnabled("m")||this._isEnabled("s")},f.prototype._hasDate=function(){return this._isEnabled("y")||this._isEnabled("M")||this._isEnabled("d")},f.prototype._dataToOptions=function(){var e=this._element.data(),i={};return e.dateOptions&&e.dateOptions instanceof Object&&(i=t.extend(!0,i,e.dateOptions)),t.each(this._options,(function(t){var s="date"+t.charAt(0).toUpperCase()+t.slice(1);void 0!==e[s]?i[t]=e[s]:delete i[t]})),i},f.prototype._notifyEvent=function(t){t.type===f.Event.CHANGE&&t.date&&t.date.isSame(t.oldDate)||!t.date&&!t.oldDate||this._element.trigger(t)},f.prototype._viewUpdate=function(t){"y"===t&&(t="YYYY"),this._notifyEvent({type:f.Event.UPDATE,change:t,viewDate:this._viewDate.clone()})},f.prototype._showMode=function(t){this.widget&&(t&&(this.currentViewMode=Math.max(this.MinViewModeNumber,Math.min(3,this.currentViewMode+t))),this.widget.find(".datepicker > div").hide().filter(".datepicker-"+p[this.currentViewMode].CLASS_NAME).show())},f.prototype._isInDisabledDates=function(t){return!0===this._options.disabledDates[t.format("YYYY-MM-DD")]},f.prototype._isInEnabledDates=function(t){return!0===this._options.enabledDates[t.format("YYYY-MM-DD")]},f.prototype._isInDisabledHours=function(t){return!0===this._options.disabledHours[t.format("H")]},f.prototype._isInEnabledHours=function(t){return!0===this._options.enabledHours[t.format("H")]},f.prototype._isValid=function(e,i){if(!e.isValid())return!1;if(this._options.disabledDates&&"d"===i&&this._isInDisabledDates(e))return!1;if(this._options.enabledDates&&"d"===i&&!this._isInEnabledDates(e))return!1;if(this._options.minDate&&e.isBefore(this._options.minDate,i))return!1;if(this._options.maxDate&&e.isAfter(this._options.maxDate,i))return!1;if(this._options.daysOfWeekDisabled&&"d"===i&&-1!==this._options.daysOfWeekDisabled.indexOf(e.day()))return!1;if(this._options.disabledHours&&("h"===i||"m"===i||"s"===i)&&this._isInDisabledHours(e))return!1;if(this._options.enabledHours&&("h"===i||"m"===i||"s"===i)&&!this._isInEnabledHours(e))return!1;if(this._options.disabledTimeIntervals&&("h"===i||"m"===i||"s"===i)){var s=!1;if(t.each(this._options.disabledTimeIntervals,(function(){if(e.isBetween(this[0],this[1]))return s=!0,!1})),s)return!1}return!0},f.prototype._parseInputDate=function(t){return void 0===this._options.parseInputDate?s.isMoment(t)||(t=this.getMoment(t)):t=this._options.parseInputDate(t),t},f.prototype._keydown=function(t){var e=null,i=void 0,s=void 0,o=void 0,a=void 0,n=[],r={},d=t.which;for(i in c[d]="p",c)c.hasOwnProperty(i)&&"p"===c[i]&&(n.push(i),parseInt(i,10)!==d&&(r[i]=!0));for(i in this._options.keyBinds)if(this._options.keyBinds.hasOwnProperty(i)&&"function"==typeof this._options.keyBinds[i]&&(o=i.split(" ")).length===n.length&&h[d]===o[o.length-1]){for(a=!0,s=o.length-2;s>=0;s--)if(!(h[o[s]]in r)){a=!1;break}if(a){e=this._options.keyBinds[i];break}}e&&e.call(this)&&(t.stopPropagation(),t.preventDefault())},f.prototype._keyup=function(t){c[t.which]="r",u[t.which]&&(u[t.which]=!1,t.stopPropagation(),t.preventDefault())},f.prototype._indexGivenDates=function(e){var i={},s=this;return t.each(e,(function(){var t=s._parseInputDate(this);t.isValid()&&(i[t.format("YYYY-MM-DD")]=!0)})),!!Object.keys(i).length&&i},f.prototype._indexGivenHours=function(e){var i={};return t.each(e,(function(){i[this]=!0})),!!Object.keys(i).length&&i},f.prototype._initFormatting=function(){var t=this._options.format||"L LT",e=this;this.actualFormat=t.replace(/(\[[^\[]*])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,(function(t){return e._dates[0].localeData().longDateFormat(t)||t})),this.parseFormats=this._options.extraFormats?this._options.extraFormats.slice():[],this.parseFormats.indexOf(t)<0&&this.parseFormats.indexOf(this.actualFormat)<0&&this.parseFormats.push(this.actualFormat),this.use24Hours=this.actualFormat.toLowerCase().indexOf("a")<1&&this.actualFormat.replace(/\[.*?]/g,"").indexOf("h")<1,this._isEnabled("y")&&(this.MinViewModeNumber=2),this._isEnabled("M")&&(this.MinViewModeNumber=1),this._isEnabled("d")&&(this.MinViewModeNumber=0),this.currentViewMode=Math.max(this.MinViewModeNumber,this.currentViewMode),this.unset||this._setValue(this._dates[0],0)},f.prototype._getLastPickedDate=function(){return this._dates[this._getLastPickedDateIndex()]},f.prototype._getLastPickedDateIndex=function(){return this._dates.length-1},f.prototype.getMoment=function(t){var e=void 0;return e=null==t?s():this._hasTimeZone()?s.tz(t,this.parseFormats,this._options.locale,this._options.useStrict,this._options.timeZone):s(t,this.parseFormats,this._options.locale,this._options.useStrict),this._hasTimeZone()&&e.tz(this._options.timeZone),e},f.prototype.toggle=function(){return this.widget?this.hide():this.show()},f.prototype.ignoreReadonly=function(t){if(0===arguments.length)return this._options.ignoreReadonly;if("boolean"!=typeof t)throw new TypeError("ignoreReadonly () expects a boolean parameter");this._options.ignoreReadonly=t},f.prototype.options=function(e){if(0===arguments.length)return t.extend(!0,{},this._options);if(!(e instanceof Object))throw new TypeError("options() this.options parameter should be an object");t.extend(!0,this._options,e);var i=this;t.each(this._options,(function(t,e){void 0!==i[t]&&i[t](e)}))},f.prototype.date=function(t,e){if(e=e||0,0===arguments.length)return this.unset?null:this._options.allowMultidate?this._dates.join(this._options.multidateSeparator):this._dates[e].clone();if(!(null===t||"string"==typeof t||s.isMoment(t)||t instanceof Date))throw new TypeError("date() parameter must be one of [null, string, moment or Date]");this._setValue(null===t?null:this._parseInputDate(t),e)},f.prototype.format=function(t){if(0===arguments.length)return this._options.format;if("string"!=typeof t&&("boolean"!=typeof t||!1!==t))throw new TypeError("format() expects a string or boolean:false parameter "+t);this._options.format=t,this.actualFormat&&this._initFormatting()},f.prototype.timeZone=function(t){if(0===arguments.length)return this._options.timeZone;if("string"!=typeof t)throw new TypeError("newZone() expects a string parameter");this._options.timeZone=t},f.prototype.dayViewHeaderFormat=function(t){if(0===arguments.length)return this._options.dayViewHeaderFormat;if("string"!=typeof t)throw new TypeError("dayViewHeaderFormat() expects a string parameter");this._options.dayViewHeaderFormat=t},f.prototype.extraFormats=function(t){if(0===arguments.length)return this._options.extraFormats;if(!1!==t&&!(t instanceof Array))throw new TypeError("extraFormats() expects an array or false parameter");this._options.extraFormats=t,this.parseFormats&&this._initFormatting()},f.prototype.disabledDates=function(e){if(0===arguments.length)return this._options.disabledDates?t.extend({},this._options.disabledDates):this._options.disabledDates;if(!e)return this._options.disabledDates=!1,this._update(),!0;if(!(e instanceof Array))throw new TypeError("disabledDates() expects an array parameter");this._options.disabledDates=this._indexGivenDates(e),this._options.enabledDates=!1,this._update()},f.prototype.enabledDates=function(e){if(0===arguments.length)return this._options.enabledDates?t.extend({},this._options.enabledDates):this._options.enabledDates;if(!e)return this._options.enabledDates=!1,this._update(),!0;if(!(e instanceof Array))throw new TypeError("enabledDates() expects an array parameter");this._options.enabledDates=this._indexGivenDates(e),this._options.disabledDates=!1,this._update()},f.prototype.daysOfWeekDisabled=function(t){if(0===arguments.length)return this._options.daysOfWeekDisabled.splice(0);if("boolean"==typeof t&&!t)return this._options.daysOfWeekDisabled=!1,this._update(),!0;if(!(t instanceof Array))throw new TypeError("daysOfWeekDisabled() expects an array parameter");if(this._options.daysOfWeekDisabled=t.reduce((function(t,e){return(e=parseInt(e,10))>6||e<0||isNaN(e)||-1===t.indexOf(e)&&t.push(e),t}),[]).sort(),this._options.useCurrent&&!this._options.keepInvalid)for(var e=0;e<this._dates.length;e++){for(var i=0;!this._isValid(this._dates[e],"d");){if(this._dates[e].add(1,"d"),31===i)throw"Tried 31 times to find a valid date";i++}this._setValue(this._dates[e],e)}this._update()},f.prototype.maxDate=function(t){if(0===arguments.length)return this._options.maxDate?this._options.maxDate.clone():this._options.maxDate;if("boolean"==typeof t&&!1===t)return this._options.maxDate=!1,this._update(),!0;"string"==typeof t&&("now"!==t&&"moment"!==t||(t=this.getMoment()));var e=this._parseInputDate(t);if(!e.isValid())throw new TypeError("maxDate() Could not parse date parameter: "+t);if(this._options.minDate&&e.isBefore(this._options.minDate))throw new TypeError("maxDate() date parameter is before this.options.minDate: "+e.format(this.actualFormat));this._options.maxDate=e;for(var i=0;i<this._dates.length;i++)this._options.useCurrent&&!this._options.keepInvalid&&this._dates[i].isAfter(t)&&this._setValue(this._options.maxDate,i);this._viewDate.isAfter(e)&&(this._viewDate=e.clone().subtract(this._options.stepping,"m")),this._update()},f.prototype.minDate=function(t){if(0===arguments.length)return this._options.minDate?this._options.minDate.clone():this._options.minDate;if("boolean"==typeof t&&!1===t)return this._options.minDate=!1,this._update(),!0;"string"==typeof t&&("now"!==t&&"moment"!==t||(t=this.getMoment()));var e=this._parseInputDate(t);if(!e.isValid())throw new TypeError("minDate() Could not parse date parameter: "+t);if(this._options.maxDate&&e.isAfter(this._options.maxDate))throw new TypeError("minDate() date parameter is after this.options.maxDate: "+e.format(this.actualFormat));this._options.minDate=e;for(var i=0;i<this._dates.length;i++)this._options.useCurrent&&!this._options.keepInvalid&&this._dates[i].isBefore(t)&&this._setValue(this._options.minDate,i);this._viewDate.isBefore(e)&&(this._viewDate=e.clone().add(this._options.stepping,"m")),this._update()},f.prototype.defaultDate=function(t){if(0===arguments.length)return this._options.defaultDate?this._options.defaultDate.clone():this._options.defaultDate;if(!t)return this._options.defaultDate=!1,!0;"string"==typeof t&&(t="now"===t||"moment"===t?this.getMoment():this.getMoment(t));var e=this._parseInputDate(t);if(!e.isValid())throw new TypeError("defaultDate() Could not parse date parameter: "+t);if(!this._isValid(e))throw new TypeError("defaultDate() date passed is invalid according to component setup validations");this._options.defaultDate=e,(this._options.defaultDate&&this._options.inline||void 0!==this.input&&""===this.input.val().trim())&&this._setValue(this._options.defaultDate,0)},f.prototype.locale=function(t){if(0===arguments.length)return this._options.locale;if(!s.localeData(t))throw new TypeError("locale() locale "+t+" is not loaded from moment locales!");this._options.locale=t;for(var e=0;e<this._dates.length;e++)this._dates[e].locale(this._options.locale);this._viewDate.locale(this._options.locale),this.actualFormat&&this._initFormatting(),this.widget&&(this.hide(),this.show())},f.prototype.stepping=function(t){if(0===arguments.length)return this._options.stepping;t=parseInt(t,10),(isNaN(t)||t<1)&&(t=1),this._options.stepping=t},f.prototype.useCurrent=function(t){var e=["year","month","day","hour","minute"];if(0===arguments.length)return this._options.useCurrent;if("boolean"!=typeof t&&"string"!=typeof t)throw new TypeError("useCurrent() expects a boolean or string parameter");if("string"==typeof t&&-1===e.indexOf(t.toLowerCase()))throw new TypeError("useCurrent() expects a string parameter of "+e.join(", "));this._options.useCurrent=t},f.prototype.collapse=function(t){if(0===arguments.length)return this._options.collapse;if("boolean"!=typeof t)throw new TypeError("collapse() expects a boolean parameter");if(this._options.collapse===t)return!0;this._options.collapse=t,this.widget&&(this.hide(),this.show())},f.prototype.icons=function(e){if(0===arguments.length)return t.extend({},this._options.icons);if(!(e instanceof Object))throw new TypeError("icons() expects parameter to be an Object");t.extend(this._options.icons,e),this.widget&&(this.hide(),this.show())},f.prototype.tooltips=function(e){if(0===arguments.length)return t.extend({},this._options.tooltips);if(!(e instanceof Object))throw new TypeError("tooltips() expects parameter to be an Object");t.extend(this._options.tooltips,e),this.widget&&(this.hide(),this.show())},f.prototype.useStrict=function(t){if(0===arguments.length)return this._options.useStrict;if("boolean"!=typeof t)throw new TypeError("useStrict() expects a boolean parameter");this._options.useStrict=t},f.prototype.sideBySide=function(t){if(0===arguments.length)return this._options.sideBySide;if("boolean"!=typeof t)throw new TypeError("sideBySide() expects a boolean parameter");this._options.sideBySide=t,this.widget&&(this.hide(),this.show())},f.prototype.viewMode=function(t){if(0===arguments.length)return this._options.viewMode;if("string"!=typeof t)throw new TypeError("viewMode() expects a string parameter");if(-1===f.ViewModes.indexOf(t))throw new TypeError("viewMode() parameter must be one of ("+f.ViewModes.join(", ")+") value");this._options.viewMode=t,this.currentViewMode=Math.max(f.ViewModes.indexOf(t)-1,this.MinViewModeNumber),this._showMode()},f.prototype.calendarWeeks=function(t){if(0===arguments.length)return this._options.calendarWeeks;if("boolean"!=typeof t)throw new TypeError("calendarWeeks() expects parameter to be a boolean value");this._options.calendarWeeks=t,this._update()},f.prototype.buttons=function(e){if(0===arguments.length)return t.extend({},this._options.buttons);if(!(e instanceof Object))throw new TypeError("buttons() expects parameter to be an Object");if(t.extend(this._options.buttons,e),"boolean"!=typeof this._options.buttons.showToday)throw new TypeError("buttons.showToday expects a boolean parameter");if("boolean"!=typeof this._options.buttons.showClear)throw new TypeError("buttons.showClear expects a boolean parameter");if("boolean"!=typeof this._options.buttons.showClose)throw new TypeError("buttons.showClose expects a boolean parameter");this.widget&&(this.hide(),this.show())},f.prototype.keepOpen=function(t){if(0===arguments.length)return this._options.keepOpen;if("boolean"!=typeof t)throw new TypeError("keepOpen() expects a boolean parameter");this._options.keepOpen=t},f.prototype.focusOnShow=function(t){if(0===arguments.length)return this._options.focusOnShow;if("boolean"!=typeof t)throw new TypeError("focusOnShow() expects a boolean parameter");this._options.focusOnShow=t},f.prototype.inline=function(t){if(0===arguments.length)return this._options.inline;if("boolean"!=typeof t)throw new TypeError("inline() expects a boolean parameter");this._options.inline=t},f.prototype.clear=function(){this._setValue(null)},f.prototype.keyBinds=function(t){if(0===arguments.length)return this._options.keyBinds;this._options.keyBinds=t},f.prototype.debug=function(t){if("boolean"!=typeof t)throw new TypeError("debug() expects a boolean parameter");this._options.debug=t},f.prototype.allowInputToggle=function(t){if(0===arguments.length)return this._options.allowInputToggle;if("boolean"!=typeof t)throw new TypeError("allowInputToggle() expects a boolean parameter");this._options.allowInputToggle=t},f.prototype.keepInvalid=function(t){if(0===arguments.length)return this._options.keepInvalid;if("boolean"!=typeof t)throw new TypeError("keepInvalid() expects a boolean parameter");this._options.keepInvalid=t},f.prototype.datepickerInput=function(t){if(0===arguments.length)return this._options.datepickerInput;if("string"!=typeof t)throw new TypeError("datepickerInput() expects a string parameter");this._options.datepickerInput=t},f.prototype.parseInputDate=function(t){if(0===arguments.length)return this._options.parseInputDate;if("function"!=typeof t)throw new TypeError("parseInputDate() should be as function");this._options.parseInputDate=t},f.prototype.disabledTimeIntervals=function(e){if(0===arguments.length)return this._options.disabledTimeIntervals?t.extend({},this._options.disabledTimeIntervals):this._options.disabledTimeIntervals;if(!e)return this._options.disabledTimeIntervals=!1,this._update(),!0;if(!(e instanceof Array))throw new TypeError("disabledTimeIntervals() expects an array parameter");this._options.disabledTimeIntervals=e,this._update()},f.prototype.disabledHours=function(e){if(0===arguments.length)return this._options.disabledHours?t.extend({},this._options.disabledHours):this._options.disabledHours;if(!e)return this._options.disabledHours=!1,this._update(),!0;if(!(e instanceof Array))throw new TypeError("disabledHours() expects an array parameter");if(this._options.disabledHours=this._indexGivenHours(e),this._options.enabledHours=!1,this._options.useCurrent&&!this._options.keepInvalid)for(var i=0;i<this._dates.length;i++){for(var s=0;!this._isValid(this._dates[i],"h");){if(this._dates[i].add(1,"h"),24===s)throw"Tried 24 times to find a valid date";s++}this._setValue(this._dates[i],i)}this._update()},f.prototype.enabledHours=function(e){if(0===arguments.length)return this._options.enabledHours?t.extend({},this._options.enabledHours):this._options.enabledHours;if(!e)return this._options.enabledHours=!1,this._update(),!0;if(!(e instanceof Array))throw new TypeError("enabledHours() expects an array parameter");if(this._options.enabledHours=this._indexGivenHours(e),this._options.disabledHours=!1,this._options.useCurrent&&!this._options.keepInvalid)for(var i=0;i<this._dates.length;i++){for(var s=0;!this._isValid(this._dates[i],"h");){if(this._dates[i].add(1,"h"),24===s)throw"Tried 24 times to find a valid date";s++}this._setValue(this._dates[i],i)}this._update()},f.prototype.viewDate=function(t){if(0===arguments.length)return this._viewDate.clone();if(!t)return this._viewDate=(this._dates[0]||this.getMoment()).clone(),!0;if(!("string"==typeof t||s.isMoment(t)||t instanceof Date))throw new TypeError("viewDate() parameter must be one of [string, moment or Date]");this._viewDate=this._parseInputDate(t),this._viewUpdate()},f.prototype.allowMultidate=function(t){if("boolean"!=typeof t)throw new TypeError("allowMultidate() expects a boolean parameter");this._options.allowMultidate=t},f.prototype.multidateSeparator=function(t){if(0===arguments.length)return this._options.multidateSeparator;if("string"!=typeof t||t.length>1)throw new TypeError("multidateSeparator expects a single character string parameter");this._options.multidateSeparator=t},e(f,null,[{key:"NAME",get:function(){return o}},{key:"DATA_KEY",get:function(){return"datetimepicker"}},{key:"EVENT_KEY",get:function(){return a}},{key:"DATA_API_KEY",get:function(){return".data-api"}},{key:"DatePickerModes",get:function(){return p}},{key:"ViewModes",get:function(){return l}},{key:"Event",get:function(){return d}},{key:"Selector",get:function(){return n}},{key:"Default",get:function(){return _},set:function(t){_=t}},{key:"ClassName",get:function(){return r}}]),f}()}(jQuery,moment);!function(e){var o=e.fn[s.NAME],a=["top","bottom","auto"],n=["left","right","auto"],r=["default","top","bottom"],d=function(t){var i=t.data("target"),o=void 0;return i||(i=t.attr("href")||"",i=/^#[a-z]/i.test(i)?i:null),0===(o=e(i)).length||o.data(s.DATA_KEY)||e.extend({},o.data(),e(this).data()),o},p=function(o){function d(t,e){i(this,d);var s=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,o.call(this,t,e));return s._init(),s}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(d,o),d.prototype._init=function(){if(this._element.hasClass("input-group")){var t=this._element.find(".datepickerbutton");0===t.length?this.component=this._element.find('[data-toggle="datetimepicker"]'):this.component=t}},d.prototype._getDatePickerTemplate=function(){var t=e("<thead>").append(e("<tr>").append(e("<th>").addClass("prev").attr("data-action","previous").append(e("<span>").addClass(this._options.icons.previous))).append(e("<th>").addClass("picker-switch").attr("data-action","pickerSwitch").attr("colspan",this._options.calendarWeeks?"6":"5")).append(e("<th>").addClass("next").attr("data-action","next").append(e("<span>").addClass(this._options.icons.next)))),i=e("<tbody>").append(e("<tr>").append(e("<td>").attr("colspan",this._options.calendarWeeks?"8":"7")));return[e("<div>").addClass("datepicker-days").append(e("<table>").addClass("table table-sm").append(t).append(e("<tbody>"))),e("<div>").addClass("datepicker-months").append(e("<table>").addClass("table-condensed").append(t.clone()).append(i.clone())),e("<div>").addClass("datepicker-years").append(e("<table>").addClass("table-condensed").append(t.clone()).append(i.clone())),e("<div>").addClass("datepicker-decades").append(e("<table>").addClass("table-condensed").append(t.clone()).append(i.clone()))]},d.prototype._getTimePickerMainTemplate=function(){var t=e("<tr>"),i=e("<tr>"),s=e("<tr>");return this._isEnabled("h")&&(t.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:this._options.tooltips.incrementHour}).addClass("btn").attr("data-action","incrementHours").append(e("<span>").addClass(this._options.icons.up)))),i.append(e("<td>").append(e("<span>").addClass("timepicker-hour").attr({"data-time-component":"hours",title:this._options.tooltips.pickHour}).attr("data-action","showHours"))),s.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:this._options.tooltips.decrementHour}).addClass("btn").attr("data-action","decrementHours").append(e("<span>").addClass(this._options.icons.down))))),this._isEnabled("m")&&(this._isEnabled("h")&&(t.append(e("<td>").addClass("separator")),i.append(e("<td>").addClass("separator").html(":")),s.append(e("<td>").addClass("separator"))),t.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:this._options.tooltips.incrementMinute}).addClass("btn").attr("data-action","incrementMinutes").append(e("<span>").addClass(this._options.icons.up)))),i.append(e("<td>").append(e("<span>").addClass("timepicker-minute").attr({"data-time-component":"minutes",title:this._options.tooltips.pickMinute}).attr("data-action","showMinutes"))),s.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:this._options.tooltips.decrementMinute}).addClass("btn").attr("data-action","decrementMinutes").append(e("<span>").addClass(this._options.icons.down))))),this._isEnabled("s")&&(this._isEnabled("m")&&(t.append(e("<td>").addClass("separator")),i.append(e("<td>").addClass("separator").html(":")),s.append(e("<td>").addClass("separator"))),t.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:this._options.tooltips.incrementSecond}).addClass("btn").attr("data-action","incrementSeconds").append(e("<span>").addClass(this._options.icons.up)))),i.append(e("<td>").append(e("<span>").addClass("timepicker-second").attr({"data-time-component":"seconds",title:this._options.tooltips.pickSecond}).attr("data-action","showSeconds"))),s.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:this._options.tooltips.decrementSecond}).addClass("btn").attr("data-action","decrementSeconds").append(e("<span>").addClass(this._options.icons.down))))),this.use24Hours||(t.append(e("<td>").addClass("separator")),i.append(e("<td>").append(e("<button>").addClass("btn btn-primary").attr({"data-action":"togglePeriod",tabindex:"-1",title:this._options.tooltips.togglePeriod}))),s.append(e("<td>").addClass("separator"))),e("<div>").addClass("timepicker-picker").append(e("<table>").addClass("table-condensed").append([t,i,s]))},d.prototype._getTimePickerTemplate=function(){var t=e("<div>").addClass("timepicker-hours").append(e("<table>").addClass("table-condensed")),i=e("<div>").addClass("timepicker-minutes").append(e("<table>").addClass("table-condensed")),s=e("<div>").addClass("timepicker-seconds").append(e("<table>").addClass("table-condensed")),o=[this._getTimePickerMainTemplate()];return this._isEnabled("h")&&o.push(t),this._isEnabled("m")&&o.push(i),this._isEnabled("s")&&o.push(s),o},d.prototype._getToolbar=function(){var t=[];if(this._options.buttons.showToday&&t.push(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1","data-action":"today",title:this._options.tooltips.today}).append(e("<span>").addClass(this._options.icons.today)))),!this._options.sideBySide&&this._hasDate()&&this._hasTime()){var i=void 0,s=void 0;"times"===this._options.viewMode?(i=this._options.tooltips.selectDate,s=this._options.icons.date):(i=this._options.tooltips.selectTime,s=this._options.icons.time),t.push(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1","data-action":"togglePicker",title:i}).append(e("<span>").addClass(s))))}return this._options.buttons.showClear&&t.push(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1","data-action":"clear",title:this._options.tooltips.clear}).append(e("<span>").addClass(this._options.icons.clear)))),this._options.buttons.showClose&&t.push(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1","data-action":"close",title:this._options.tooltips.close}).append(e("<span>").addClass(this._options.icons.close)))),0===t.length?"":e("<table>").addClass("table-condensed").append(e("<tbody>").append(e("<tr>").append(t)))},d.prototype._getTemplate=function(){var t=e("<div>").addClass("bootstrap-datetimepicker-widget dropdown-menu"),i=e("<div>").addClass("datepicker").append(this._getDatePickerTemplate()),s=e("<div>").addClass("timepicker").append(this._getTimePickerTemplate()),o=e("<ul>").addClass("list-unstyled"),a=e("<li>").addClass("picker-switch"+(this._options.collapse?" accordion-toggle":"")).append(this._getToolbar());return this._options.inline&&t.removeClass("dropdown-menu"),this.use24Hours&&t.addClass("usetwentyfour"),this._isEnabled("s")&&!this.use24Hours&&t.addClass("wider"),this._options.sideBySide&&this._hasDate()&&this._hasTime()?(t.addClass("timepicker-sbs"),"top"===this._options.toolbarPlacement&&t.append(a),t.append(e("<div>").addClass("row").append(i.addClass("col-md-6")).append(s.addClass("col-md-6"))),"bottom"!==this._options.toolbarPlacement&&"default"!==this._options.toolbarPlacement||t.append(a),t):("top"===this._options.toolbarPlacement&&o.append(a),this._hasDate()&&o.append(e("<li>").addClass(this._options.collapse&&this._hasTime()?"collapse":"").addClass(this._options.collapse&&this._hasTime()&&"times"===this._options.viewMode?"":"show").append(i)),"default"===this._options.toolbarPlacement&&o.append(a),this._hasTime()&&o.append(e("<li>").addClass(this._options.collapse&&this._hasDate()?"collapse":"").addClass(this._options.collapse&&this._hasDate()&&"times"===this._options.viewMode?"show":"").append(s)),"bottom"===this._options.toolbarPlacement&&o.append(a),t.append(o))},d.prototype._place=function(t){var i=t&&t.data&&t.data.picker||this,s=i._options.widgetPositioning.vertical,o=i._options.widgetPositioning.horizontal,a=void 0,n=(i.component&&i.component.length?i.component:i._element).position(),r=(i.component&&i.component.length?i.component:i._element).offset();if(i._options.widgetParent)a=i._options.widgetParent.append(i.widget);else if(i._element.is("input"))a=i._element.after(i.widget).parent();else{if(i._options.inline)return void(a=i._element.append(i.widget));a=i._element,i._element.children().first().after(i.widget)}if("auto"===s&&(s=r.top+1.5*i.widget.height()>=e(window).height()+e(window).scrollTop()&&i.widget.height()+i._element.outerHeight()<r.top?"top":"bottom"),"auto"===o&&(o=a.width()<r.left+i.widget.outerWidth()/2&&r.left+i.widget.outerWidth()>e(window).width()?"right":"left"),"top"===s?i.widget.addClass("top").removeClass("bottom"):i.widget.addClass("bottom").removeClass("top"),"right"===o?i.widget.addClass("float-right"):i.widget.removeClass("float-right"),"relative"!==a.css("position")&&(a=a.parents().filter((function(){return"relative"===e(this).css("position")})).first()),0===a.length)throw new Error("datetimepicker component should be placed within a relative positioned container");i.widget.css({top:"top"===s?"auto":n.top+i._element.outerHeight()+"px",bottom:"top"===s?a.outerHeight()-(a===i._element?0:n.top)+"px":"auto",left:"left"===o?(a===i._element?0:n.left)+"px":"auto",right:"left"===o?"auto":a.outerWidth()-i._element.outerWidth()-(a===i._element?0:n.left)+"px"})},d.prototype._fillDow=function(){var t=e("<tr>"),i=this._viewDate.clone().startOf("w").startOf("d");for(!0===this._options.calendarWeeks&&t.append(e("<th>").addClass("cw").text("#"));i.isBefore(this._viewDate.clone().endOf("w"));)t.append(e("<th>").addClass("dow").text(i.format("dd"))),i.add(1,"d");this.widget.find(".datepicker-days thead").append(t)},d.prototype._fillMonths=function(){for(var t=[],i=this._viewDate.clone().startOf("y").startOf("d");i.isSame(this._viewDate,"y");)t.push(e("<span>").attr("data-action","selectMonth").addClass("month").text(i.format("MMM"))),i.add(1,"M");this.widget.find(".datepicker-months td").empty().append(t)},d.prototype._updateMonths=function(){var t=this.widget.find(".datepicker-months"),i=t.find("th"),s=t.find("tbody").find("span"),o=this;i.eq(0).find("span").attr("title",this._options.tooltips.prevYear),i.eq(1).attr("title",this._options.tooltips.selectYear),i.eq(2).find("span").attr("title",this._options.tooltips.nextYear),t.find(".disabled").removeClass("disabled"),this._isValid(this._viewDate.clone().subtract(1,"y"),"y")||i.eq(0).addClass("disabled"),i.eq(1).text(this._viewDate.year()),this._isValid(this._viewDate.clone().add(1,"y"),"y")||i.eq(2).addClass("disabled"),s.removeClass("active"),this._getLastPickedDate().isSame(this._viewDate,"y")&&!this.unset&&s.eq(this._getLastPickedDate().month()).addClass("active"),s.each((function(t){o._isValid(o._viewDate.clone().month(t),"M")||e(this).addClass("disabled")}))},d.prototype._getStartEndYear=function(t,e){var i=t/10,s=Math.floor(e/t)*t;return[s,s+9*i,Math.floor(e/i)*i]},d.prototype._updateYears=function(){var t=this.widget.find(".datepicker-years"),e=t.find("th"),i=this._getStartEndYear(10,this._viewDate.year()),s=this._viewDate.clone().year(i[0]),o=this._viewDate.clone().year(i[1]),a="";for(e.eq(0).find("span").attr("title",this._options.tooltips.prevDecade),e.eq(1).attr("title",this._options.tooltips.selectDecade),e.eq(2).find("span").attr("title",this._options.tooltips.nextDecade),t.find(".disabled").removeClass("disabled"),this._options.minDate&&this._options.minDate.isAfter(s,"y")&&e.eq(0).addClass("disabled"),e.eq(1).text(s.year()+"-"+o.year()),this._options.maxDate&&this._options.maxDate.isBefore(o,"y")&&e.eq(2).addClass("disabled"),a+='<span data-action="selectYear" class="year old'+(this._isValid(s,"y")?"":" disabled")+'">'+(s.year()-1)+"</span>";!s.isAfter(o,"y");)a+='<span data-action="selectYear" class="year'+(s.isSame(this._getLastPickedDate(),"y")&&!this.unset?" active":"")+(this._isValid(s,"y")?"":" disabled")+'">'+s.year()+"</span>",s.add(1,"y");a+='<span data-action="selectYear" class="year old'+(this._isValid(s,"y")?"":" disabled")+'">'+s.year()+"</span>",t.find("td").html(a)},d.prototype._updateDecades=function(){var t=this.widget.find(".datepicker-decades"),e=t.find("th"),i=this._getStartEndYear(100,this._viewDate.year()),s=this._viewDate.clone().year(i[0]),o=this._viewDate.clone().year(i[1]),a=!1,n=!1,r=void 0,d="";for(e.eq(0).find("span").attr("title",this._options.tooltips.prevCentury),e.eq(2).find("span").attr("title",this._options.tooltips.nextCentury),t.find(".disabled").removeClass("disabled"),(0===s.year()||this._options.minDate&&this._options.minDate.isAfter(s,"y"))&&e.eq(0).addClass("disabled"),e.eq(1).text(s.year()+"-"+o.year()),this._options.maxDate&&this._options.maxDate.isBefore(o,"y")&&e.eq(2).addClass("disabled"),s.year()-10<0?d+="<span>&nbsp;</span>":d+='<span data-action="selectDecade" class="decade old" data-selection="'+(s.year()+6)+'">'+(s.year()-10)+"</span>";!s.isAfter(o,"y");)r=s.year()+11,a=this._options.minDate&&this._options.minDate.isAfter(s,"y")&&this._options.minDate.year()<=r,n=this._options.maxDate&&this._options.maxDate.isAfter(s,"y")&&this._options.maxDate.year()<=r,d+='<span data-action="selectDecade" class="decade'+(this._getLastPickedDate().isAfter(s)&&this._getLastPickedDate().year()<=r?" active":"")+(this._isValid(s,"y")||a||n?"":" disabled")+'" data-selection="'+(s.year()+6)+'">'+s.year()+"</span>",s.add(10,"y");d+='<span data-action="selectDecade" class="decade old" data-selection="'+(s.year()+6)+'">'+s.year()+"</span>",t.find("td").html(d)},d.prototype._fillDate=function(){var t=this.widget.find(".datepicker-days"),i=t.find("th"),s=[],o=void 0,a=void 0,n=void 0,r=void 0;if(this._hasDate()){for(i.eq(0).find("span").attr("title",this._options.tooltips.prevMonth),i.eq(1).attr("title",this._options.tooltips.selectMonth),i.eq(2).find("span").attr("title",this._options.tooltips.nextMonth),t.find(".disabled").removeClass("disabled"),i.eq(1).text(this._viewDate.format(this._options.dayViewHeaderFormat)),this._isValid(this._viewDate.clone().subtract(1,"M"),"M")||i.eq(0).addClass("disabled"),this._isValid(this._viewDate.clone().add(1,"M"),"M")||i.eq(2).addClass("disabled"),o=this._viewDate.clone().startOf("M").startOf("w").startOf("d"),r=0;r<42;r++){if(0===o.weekday()&&(a=e("<tr>"),this._options.calendarWeeks&&a.append('<td class="cw">'+o.week()+"</td>"),s.push(a)),n="",o.isBefore(this._viewDate,"M")&&(n+=" old"),o.isAfter(this._viewDate,"M")&&(n+=" new"),this._options.allowMultidate){var d=this._datesFormatted.indexOf(o.format("YYYY-MM-DD"));-1!==d&&o.isSame(this._datesFormatted[d],"d")&&!this.unset&&(n+=" active")}else o.isSame(this._getLastPickedDate(),"d")&&!this.unset&&(n+=" active");this._isValid(o,"d")||(n+=" disabled"),o.isSame(this.getMoment(),"d")&&(n+=" today"),0!==o.day()&&6!==o.day()||(n+=" weekend"),a.append('<td data-action="selectDay" data-day="'+o.format("L")+'" class="day'+n+'">'+o.date()+"</td>"),o.add(1,"d")}t.find("tbody").empty().append(s),this._updateMonths(),this._updateYears(),this._updateDecades()}},d.prototype._fillHours=function(){var t=this.widget.find(".timepicker-hours table"),i=this._viewDate.clone().startOf("d"),s=[],o=e("<tr>");for(this._viewDate.hour()>11&&!this.use24Hours&&i.hour(12);i.isSame(this._viewDate,"d")&&(this.use24Hours||this._viewDate.hour()<12&&i.hour()<12||this._viewDate.hour()>11);)i.hour()%4==0&&(o=e("<tr>"),s.push(o)),o.append('<td data-action="selectHour" class="hour'+(this._isValid(i,"h")?"":" disabled")+'">'+i.format(this.use24Hours?"HH":"hh")+"</td>"),i.add(1,"h");t.empty().append(s)},d.prototype._fillMinutes=function(){for(var t=this.widget.find(".timepicker-minutes table"),i=this._viewDate.clone().startOf("h"),s=[],o=1===this._options.stepping?5:this._options.stepping,a=e("<tr>");this._viewDate.isSame(i,"h");)i.minute()%(4*o)==0&&(a=e("<tr>"),s.push(a)),a.append('<td data-action="selectMinute" class="minute'+(this._isValid(i,"m")?"":" disabled")+'">'+i.format("mm")+"</td>"),i.add(o,"m");t.empty().append(s)},d.prototype._fillSeconds=function(){for(var t=this.widget.find(".timepicker-seconds table"),i=this._viewDate.clone().startOf("m"),s=[],o=e("<tr>");this._viewDate.isSame(i,"m");)i.second()%20==0&&(o=e("<tr>"),s.push(o)),o.append('<td data-action="selectSecond" class="second'+(this._isValid(i,"s")?"":" disabled")+'">'+i.format("ss")+"</td>"),i.add(5,"s");t.empty().append(s)},d.prototype._fillTime=function(){var t=void 0,e=void 0,i=this.widget.find(".timepicker span[data-time-component]");this.use24Hours||(t=this.widget.find(".timepicker [data-action=togglePeriod]"),e=this._getLastPickedDate().clone().add(this._getLastPickedDate().hours()>=12?-12:12,"h"),t.text(this._getLastPickedDate().format("A")),this._isValid(e,"h")?t.removeClass("disabled"):t.addClass("disabled")),i.filter("[data-time-component=hours]").text(this._getLastPickedDate().format(this.use24Hours?"HH":"hh")),i.filter("[data-time-component=minutes]").text(this._getLastPickedDate().format("mm")),i.filter("[data-time-component=seconds]").text(this._getLastPickedDate().format("ss")),this._fillHours(),this._fillMinutes(),this._fillSeconds()},d.prototype._doAction=function(t,i){var o=this._getLastPickedDate();if(e(t.currentTarget).is(".disabled"))return!1;switch(i=i||e(t.currentTarget).data("action")){case"next":var a=s.DatePickerModes[this.currentViewMode].NAV_FUNCTION;this._viewDate.add(s.DatePickerModes[this.currentViewMode].NAV_STEP,a),this._fillDate(),this._viewUpdate(a);break;case"previous":var n=s.DatePickerModes[this.currentViewMode].NAV_FUNCTION;this._viewDate.subtract(s.DatePickerModes[this.currentViewMode].NAV_STEP,n),this._fillDate(),this._viewUpdate(n);break;case"pickerSwitch":this._showMode(1);break;case"selectMonth":var r=e(t.target).closest("tbody").find("span").index(e(t.target));this._viewDate.month(r),this.currentViewMode===this.MinViewModeNumber?(this._setValue(o.clone().year(this._viewDate.year()).month(this._viewDate.month()),this._getLastPickedDateIndex()),this._options.inline||this.hide()):(this._showMode(-1),this._fillDate()),this._viewUpdate("M");break;case"selectYear":var d=parseInt(e(t.target).text(),10)||0;this._viewDate.year(d),this.currentViewMode===this.MinViewModeNumber?(this._setValue(o.clone().year(this._viewDate.year()),this._getLastPickedDateIndex()),this._options.inline||this.hide()):(this._showMode(-1),this._fillDate()),this._viewUpdate("YYYY");break;case"selectDecade":var p=parseInt(e(t.target).data("selection"),10)||0;this._viewDate.year(p),this.currentViewMode===this.MinViewModeNumber?(this._setValue(o.clone().year(this._viewDate.year()),this._getLastPickedDateIndex()),this._options.inline||this.hide()):(this._showMode(-1),this._fillDate()),this._viewUpdate("YYYY");break;case"selectDay":var h=this._viewDate.clone();e(t.target).is(".old")&&h.subtract(1,"M"),e(t.target).is(".new")&&h.add(1,"M");var l=h.date(parseInt(e(t.target).text(),10)),c=0;this._options.allowMultidate?-1!==(c=this._datesFormatted.indexOf(l.format("YYYY-MM-DD")))?this._setValue(null,c):this._setValue(l,this._getLastPickedDateIndex()+1):this._setValue(l,this._getLastPickedDateIndex()),this._hasTime()||this._options.keepOpen||this._options.inline||this._options.allowMultidate||this.hide();break;case"incrementHours":var u=o.clone().add(1,"h");this._isValid(u,"h")&&this._setValue(u,this._getLastPickedDateIndex());break;case"incrementMinutes":var _=o.clone().add(this._options.stepping,"m");this._isValid(_,"m")&&this._setValue(_,this._getLastPickedDateIndex());break;case"incrementSeconds":var f=o.clone().add(1,"s");this._isValid(f,"s")&&this._setValue(f,this._getLastPickedDateIndex());break;case"decrementHours":var m=o.clone().subtract(1,"h");this._isValid(m,"h")&&this._setValue(m,this._getLastPickedDateIndex());break;case"decrementMinutes":var w=o.clone().subtract(this._options.stepping,"m");this._isValid(w,"m")&&this._setValue(w,this._getLastPickedDateIndex());break;case"decrementSeconds":var y=o.clone().subtract(1,"s");this._isValid(y,"s")&&this._setValue(y,this._getLastPickedDateIndex());break;case"togglePeriod":this._setValue(o.clone().add(o.hours()>=12?-12:12,"h"),this._getLastPickedDateIndex());break;case"togglePicker":var g=e(t.target),b=g.closest("a"),v=g.closest("ul"),D=v.find(".show"),k=v.find(".collapse:not(.show)"),M=g.is("span")?g:g.find("span"),x=void 0;if(D&&D.length){if((x=D.data("collapse"))&&x.transitioning)return!0;D.collapse?(D.collapse("hide"),k.collapse("show")):(D.removeClass("show"),k.addClass("show")),M.toggleClass(this._options.icons.time+" "+this._options.icons.date),M.hasClass(this._options.icons.date)?b.attr("title",this._options.tooltips.selectDate):b.attr("title",this._options.tooltips.selectTime)}break;case"showPicker":this.widget.find(".timepicker > div:not(.timepicker-picker)").hide(),this.widget.find(".timepicker .timepicker-picker").show();break;case"showHours":this.widget.find(".timepicker .timepicker-picker").hide(),this.widget.find(".timepicker .timepicker-hours").show();break;case"showMinutes":this.widget.find(".timepicker .timepicker-picker").hide(),this.widget.find(".timepicker .timepicker-minutes").show();break;case"showSeconds":this.widget.find(".timepicker .timepicker-picker").hide(),this.widget.find(".timepicker .timepicker-seconds").show();break;case"selectHour":var C=parseInt(e(t.target).text(),10);this.use24Hours||(o.hours()>=12?12!==C&&(C+=12):12===C&&(C=0)),this._setValue(o.clone().hours(C),this._getLastPickedDateIndex()),this._isEnabled("a")||this._isEnabled("m")||this._options.keepOpen||this._options.inline?this._doAction(t,"showPicker"):this.hide();break;case"selectMinute":this._setValue(o.clone().minutes(parseInt(e(t.target).text(),10)),this._getLastPickedDateIndex()),this._isEnabled("a")||this._isEnabled("s")||this._options.keepOpen||this._options.inline?this._doAction(t,"showPicker"):this.hide();break;case"selectSecond":this._setValue(o.clone().seconds(parseInt(e(t.target).text(),10)),this._getLastPickedDateIndex()),this._isEnabled("a")||this._options.keepOpen||this._options.inline?this._doAction(t,"showPicker"):this.hide();break;case"clear":this.clear();break;case"close":this.hide();break;case"today":var E=this.getMoment();this._isValid(E,"d")&&this._setValue(E,this._getLastPickedDateIndex())}return!1},d.prototype.hide=function(){var t=!1;this.widget&&(this.widget.find(".collapse").each((function(){var i=e(this).data("collapse");return!i||!i.transitioning||(t=!0,!1)})),t||(this.component&&this.component.hasClass("btn")&&this.component.toggleClass("active"),this.widget.hide(),e(window).off("resize",this._place()),this.widget.off("click","[data-action]"),this.widget.off("mousedown",!1),this.widget.remove(),this.widget=!1,this._notifyEvent({type:s.Event.HIDE,date:this._getLastPickedDate().clone()}),void 0!==this.input&&this.input.blur(),this._viewDate=this._getLastPickedDate().clone()))},d.prototype.show=function(){var t=void 0,i={year:function(t){return t.month(0).date(1).hours(0).seconds(0).minutes(0)},month:function(t){return t.date(1).hours(0).seconds(0).minutes(0)},day:function(t){return t.hours(0).seconds(0).minutes(0)},hour:function(t){return t.seconds(0).minutes(0)},minute:function(t){return t.seconds(0)}};if(void 0!==this.input){if(this.input.prop("disabled")||!this._options.ignoreReadonly&&this.input.prop("readonly")||this.widget)return;void 0!==this.input.val()&&0!==this.input.val().trim().length?this._setValue(this._parseInputDate(this.input.val().trim()),0):this.unset&&this._options.useCurrent&&(t=this.getMoment(),"string"==typeof this._options.useCurrent&&(t=i[this._options.useCurrent](t)),this._setValue(t,0))}else this.unset&&this._options.useCurrent&&(t=this.getMoment(),"string"==typeof this._options.useCurrent&&(t=i[this._options.useCurrent](t)),this._setValue(t,0));this.widget=this._getTemplate(),this._fillDow(),this._fillMonths(),this.widget.find(".timepicker-hours").hide(),this.widget.find(".timepicker-minutes").hide(),this.widget.find(".timepicker-seconds").hide(),this._update(),this._showMode(),e(window).on("resize",{picker:this},this._place),this.widget.on("click","[data-action]",e.proxy(this._doAction,this)),this.widget.on("mousedown",!1),this.component&&this.component.hasClass("btn")&&this.component.toggleClass("active"),this._place(),this.widget.show(),void 0!==this.input&&this._options.focusOnShow&&!this.input.is(":focus")&&this.input.focus(),this._notifyEvent({type:s.Event.SHOW})},d.prototype.destroy=function(){this.hide(),this._element.removeData(s.DATA_KEY),this._element.removeData("date")},d.prototype.disable=function(){this.hide(),this.component&&this.component.hasClass("btn")&&this.component.addClass("disabled"),void 0!==this.input&&this.input.prop("disabled",!0)},d.prototype.enable=function(){this.component&&this.component.hasClass("btn")&&this.component.removeClass("disabled"),void 0!==this.input&&this.input.prop("disabled",!1)},d.prototype.toolbarPlacement=function(t){if(0===arguments.length)return this._options.toolbarPlacement;if("string"!=typeof t)throw new TypeError("toolbarPlacement() expects a string parameter");if(-1===r.indexOf(t))throw new TypeError("toolbarPlacement() parameter must be one of ("+r.join(", ")+") value");this._options.toolbarPlacement=t,this.widget&&(this.hide(),this.show())},d.prototype.widgetPositioning=function(t){if(0===arguments.length)return e.extend({},this._options.widgetPositioning);if("[object Object]"!=={}.toString.call(t))throw new TypeError("widgetPositioning() expects an object variable");if(t.horizontal){if("string"!=typeof t.horizontal)throw new TypeError("widgetPositioning() horizontal variable must be a string");if(t.horizontal=t.horizontal.toLowerCase(),-1===n.indexOf(t.horizontal))throw new TypeError("widgetPositioning() expects horizontal parameter to be one of ("+n.join(", ")+")");this._options.widgetPositioning.horizontal=t.horizontal}if(t.vertical){if("string"!=typeof t.vertical)throw new TypeError("widgetPositioning() vertical variable must be a string");if(t.vertical=t.vertical.toLowerCase(),-1===a.indexOf(t.vertical))throw new TypeError("widgetPositioning() expects vertical parameter to be one of ("+a.join(", ")+")");this._options.widgetPositioning.vertical=t.vertical}this._update()},d.prototype.widgetParent=function(t){if(0===arguments.length)return this._options.widgetParent;if("string"==typeof t&&(t=e(t)),null!==t&&"string"!=typeof t&&!(t instanceof e))throw new TypeError("widgetParent() expects a string or a jQuery object parameter");this._options.widgetParent=t,this.widget&&(this.hide(),this.show())},d._jQueryHandleThis=function(i,o,a){var n=e(i).data(s.DATA_KEY);if("object"===(void 0===o?"undefined":t(o))&&e.extend({},s.Default,o),n||(n=new d(e(i),o),e(i).data(s.DATA_KEY,n)),"string"==typeof o){if(void 0===n[o])throw new Error('No method named "'+o+'"');return void 0===a?n[o]():n[o](a)}},d._jQueryInterface=function(t,e){return 1===this.length?d._jQueryHandleThis(this[0],t,e):this.each((function(){d._jQueryHandleThis(this,t,e)}))},d}(s);e(document).on(s.Event.CLICK_DATA_API,s.Selector.DATA_TOGGLE,(function(){var t=d(e(this));0!==t.length&&p._jQueryInterface.call(t,"toggle")})).on(s.Event.CHANGE,"."+s.ClassName.INPUT,(function(t){var i=d(e(this));0!==i.length&&p._jQueryInterface.call(i,"_change",t)})).on(s.Event.BLUR,"."+s.ClassName.INPUT,(function(t){var i=d(e(this)),o=i.data(s.DATA_KEY);0!==i.length&&(o._options.debug||window.debug||p._jQueryInterface.call(i,"hide",t))})).on(s.Event.KEYDOWN,"."+s.ClassName.INPUT,(function(t){var i=d(e(this));0!==i.length&&p._jQueryInterface.call(i,"_keydown",t)})).on(s.Event.KEYUP,"."+s.ClassName.INPUT,(function(t){var i=d(e(this));0!==i.length&&p._jQueryInterface.call(i,"_keyup",t)})).on(s.Event.FOCUS,"."+s.ClassName.INPUT,(function(t){var i=d(e(this)),o=i.data(s.DATA_KEY);0!==i.length&&o._options.allowInputToggle&&p._jQueryInterface.call(i,"show",t)})),e.fn[s.NAME]=p._jQueryInterface,e.fn[s.NAME].Constructor=p,e.fn[s.NAME].noConflict=function(){return e.fn[s.NAME]=o,p._jQueryInterface}}(jQuery)}();