/*!
 * pGenerator jQuery Plugin v1.0.5
 * https://github.com/M1Sh0u/pGenerator
 *
 * Created by <PERSON><PERSON> <<EMAIL>>
 * Released under the MIT License (Feel free to copy, modify or redistribute this plugin.)
 */
!function(e){var r=[],n=[],a=[],t=[],o={init:function(s,l){for(var i=e.extend({bind:"click",passwordElement:null,displayElement:null,passwordLength:16,uppercase:!0,lowercase:!0,numbers:!0,specialChars:!0,additionalSpecialChars:[],onPasswordGenerated:function(e){}},s),p=48;p<58;p++)r.push(p);for(p=65;p<91;p++)n.push(p);for(p=97;p<123;p++)a.push(p);return t=[33,35,64,36,42,91,93,123,125,92,47,63,58,59,95,45].concat(i.additionalSpecialChars),this.each((function(){e(this).bind(i.bind,(function(e){e.preventDefault(),o.generatePassword.call(this,i)}))}))},generatePassword:function(o){var l=new Array,i=o.uppercase+o.lowercase+o.numbers+o.specialChars,p=0,h=new Array,c=Math.floor(o.passwordLength/i);if(o.uppercase){for(var d=0;d<c;d++)l.push(String.fromCharCode(n[s(0,n.length-1)]));h=h.concat(n),p++}if(o.numbers){for(d=0;d<c;d++)l.push(String.fromCharCode(r[s(0,r.length-1)]));h=h.concat(r),p++}if(o.specialChars){for(d=0;d<c;d++)l.push(String.fromCharCode(t[s(0,t.length-1)]));h=h.concat(t),p++}var u=o.passwordLength-p*c;if(o.lowercase)for(d=0;d<u;d++)l.push(String.fromCharCode(a[s(0,a.length-1)]));else for(d=0;d<u;d++)l.push(String.fromCharCode(h[s(0,h.length-1)]));l=function(e){for(var r,n,a=e.length;a;r=parseInt(Math.random()*a),n=e[--a],e[a]=e[r],e[r]=n);return e}(l).join(""),null!==o.passwordElement&&e(o.passwordElement).val(l),null!==o.displayElement&&(e(o.displayElement).is("input")?e(o.displayElement).val(l):e(o.displayElement).text(l)),o.onPasswordGenerated.call(this,l)}};function s(e,r){return Math.floor(Math.random()*(r-e+1)+e)}e.fn.pGenerator=function(r){return o[r]?o[r].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof r&&r?void e.error("Method "+r+" does not exist on jQuery.pGenerator"):o.init.apply(this,arguments)}}(jQuery);
//# sourceMappingURL=pGenerator.jquery.min.js.map