{"version": 3, "file": "purify.min.js", "sources": ["../src/utils.js", "../src/tags.js", "../src/attrs.js", "../src/regexp.js", "../src/purify.js"], "sourcesContent": ["const { hasOwnProperty, setPrototypeOf, isFrozen, keys: objectKeys } = Object;\n\nlet { freeze, seal } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayJoin = unapply(Array.prototype.join);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\nconst regExpCreate = unconstruct(RegExp);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array) {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = stringToLowerCase(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = {};\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property])) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayJoin,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  hasOwnProperty,\n  isFrozen,\n  objectKeys,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpCreate,\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n};\n", "import { freeze } from './utils';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'audio',\n  'canvas',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'video',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\s\\S]*|[\\s\\S]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\s\\S]*|[\\s\\S]*%>/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205f\\u3000]/g // eslint-disable-line no-control-regex\n);\n", "import * as TAGS from './tags';\nimport * as ATTRS from './attrs';\nimport * as EXPRESSIONS from './regexp';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  objectKeys,\n  arrayForEach,\n  arrayIndexOf,\n  arrayJoin,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  regExpCreate,\n  typeErrorCreate,\n} from './utils';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n  let removeTitle = false;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    Text,\n    Comment,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML =\n    trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML('')\n      : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    getElementsByTagName,\n    createDocumentFragment,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    implementation &&\n    typeof implementation.createHTMLDocument !== 'undefined' &&\n    document.documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Output should be safe for jQuery's $() factory? */\n  let SAFE_FOR_JQUERY = false;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* If `RETURN_DOM` or `RETURN_DOM_FRAGMENT` is enabled, decide if the returned DOM\n   * `Node` is imported into the current `Document`. If this flag is not enabled the\n   * `Node` will belong (its ownerDocument) to a fresh `HTMLDocument`, created by\n   * DOMPurify. */\n  let RETURN_DOM_IMPORT = false;\n\n  /* Try to return a Trusted Type object instead of a string, retrun a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks? */\n  let SANITIZE_DOM = true;\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  const FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR)\n        : DEFAULT_ALLOWED_ATTR;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), cfg.ADD_URI_SAFE_ATTR)\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(clone(DEFAULT_DATA_URI_TAGS), cfg.ADD_DATA_URI_TAGS)\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS) : {};\n    FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR) : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    SAFE_FOR_JQUERY = cfg.SAFE_FOR_JQUERY || false; // Default false\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_DOM_IMPORT = cfg.RETURN_DOM_IMPORT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      node.outerHTML = emptyHTML;\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /* Use the DOMParser API by default, fallback later if needs be */\n    try {\n      doc = new DOMParser().parseFromString(dirtyPayload, 'text/html');\n    } catch (_) {}\n\n    /* Remove title to fix a mXSS bug in older MS Edge */\n    if (removeTitle) {\n      addToSet(FORBID_TAGS, ['title']);\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createHTMLDocument('');\n      const { body } = doc;\n      body.parentNode.removeChild(body.parentNode.firstElementChild);\n      body.outerHTML = dirtyPayload;\n    }\n\n    if (dirty && leadingWhitespace) {\n      doc.body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        doc.body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n  };\n\n  /* Here we test for a broken feature in Edge that might cause mXSS */\n  if (DOMPurify.isSupported) {\n    (function () {\n      try {\n        const doc = _initDocument('<x/><title>&lt;/title&gt;&lt;img&gt;');\n        if (regExpTest(/<\\/title/, doc.querySelector('title').innerHTML)) {\n          removeTitle = true;\n        }\n      } catch (_) {}\n    })();\n  }\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT,\n      () => {\n        return NodeFilter.FILTER_ACCEPT;\n      },\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    if (elm instanceof Text || elm instanceof Comment) {\n      return false;\n    }\n\n    if (\n      typeof elm.nodeName !== 'string' ||\n      typeof elm.textContent !== 'string' ||\n      typeof elm.removeChild !== 'function' ||\n      !(elm.attributes instanceof NamedNodeMap) ||\n      typeof elm.removeAttribute !== 'function' ||\n      typeof elm.setAttribute !== 'function' ||\n      typeof elm.namespaceURI !== 'string'\n    ) {\n      return true;\n    }\n\n    return false;\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  // eslint-disable-next-line complexity\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = stringToLowerCase(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Take care of an mXSS pattern using p, br inside svg, math */\n    if (\n      (tagName === 'svg' || tagName === 'math') &&\n      currentNode.querySelectorAll('p, br').length !== 0\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Keep content except for bad-listed elements */\n      if (\n        KEEP_CONTENT &&\n        !FORBID_CONTENTS[tagName] &&\n        typeof currentNode.insertAdjacentHTML === 'function'\n      ) {\n        try {\n          const htmlToInsert = currentNode.innerHTML;\n          currentNode.insertAdjacentHTML(\n            'AfterEnd',\n            trustedTypesPolicy\n              ? trustedTypesPolicy.createHTML(htmlToInsert)\n              : htmlToInsert\n          );\n        } catch (_) {}\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove in case a noscript/noembed XSS is suspected */\n    if (\n      tagName === 'noscript' &&\n      regExpTest(/<\\/noscript/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    if (\n      tagName === 'noembed' &&\n      regExpTest(/<\\/noembed/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Convert markup to cover jQuery behavior */\n    if (\n      SAFE_FOR_JQUERY &&\n      !currentNode.firstElementChild &&\n      (!currentNode.content || !currentNode.content.firstElementChild) &&\n      regExpTest(/</g, currentNode.textContent)\n    ) {\n      arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n      if (currentNode.innerHTML) {\n        currentNode.innerHTML = stringReplace(\n          currentNode.innerHTML,\n          /</g,\n          '&lt;'\n        );\n      } else {\n        currentNode.innerHTML = stringReplace(\n          currentNode.textContent,\n          /</g,\n          '&lt;'\n        );\n      }\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && regExpTest(DATA_ATTR, lcName)) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      return false;\n\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n      // eslint-disable-next-line no-negated-condition\n    } else if (!value) {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    } else {\n      return false;\n    }\n\n    return true;\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  // eslint-disable-next-line complexity\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let idAttr;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    let { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = stringTrim(attr.value);\n      lcName = stringToLowerCase(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      // Safari (iOS + Mac), last tested v8.0.5, crashes if you try to\n      // remove a \"name\" attribute from an <img> tag that has an \"id\"\n      // attribute at the time.\n      if (\n        lcName === 'name' &&\n        currentNode.nodeName === 'IMG' &&\n        attributes.id\n      ) {\n        idAttr = attributes.id;\n        attributes = arraySlice(attributes, []);\n        _removeAttribute('id', currentNode);\n        _removeAttribute(name, currentNode);\n        if (arrayIndexOf(attributes, idAttr) > l) {\n          currentNode.setAttribute('id', idAttr.value);\n        }\n      } else if (\n        // This works around a bug in Safari, where input[type=file]\n        // cannot be dynamically set after type has been removed\n        currentNode.nodeName === 'INPUT' &&\n        lcName === 'type' &&\n        value === 'file' &&\n        hookEvent.keepAttr &&\n        (ALLOWED_ATTR[lcName] || !FORBID_ATTR[lcName])\n      ) {\n        continue;\n      } else {\n        // This avoids a crash in Safari v9.0 with double-ids.\n        // The trick is to first set the id to be empty and then to\n        // remove the attribute\n        if (name === 'id') {\n          currentNode.setAttribute(name, '');\n        }\n\n        _removeAttribute(name, currentNode);\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (SAFE_FOR_JQUERY && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Take care of an mXSS pattern using namespace switches */\n      if (\n        regExpTest(/svg|math/i, currentNode.namespaceURI) &&\n        regExpTest(\n          regExpCreate(\n            '</(' + arrayJoin(objectKeys(FORBID_CONTENTS), '|') + ')',\n            'i'\n          ),\n          value\n        )\n      ) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = currentNode.nodeName.toLowerCase();\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        arrayPop(DOMPurify.removed);\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    if (!dirty) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      // eslint-disable-next-line no-negated-condition\n      if (typeof dirty.toString !== 'function') {\n        throw typeErrorCreate('toString is not a function');\n      } else {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* No special handling necessary for in-place sanitization */\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!-->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : emptyHTML;\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (RETURN_DOM_IMPORT) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = stringToLowerCase(tag);\n    const lcName = stringToLowerCase(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["hasOwnProperty", "Object", "setPrototypeOf", "isFrozen", "objectKeys", "keys", "freeze", "seal", "Reflect", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayIndexOf", "indexOf", "arrayJoin", "join", "arrayPop", "pop", "arrayPush", "push", "arraySlice", "slice", "stringToLowerCase", "String", "toLowerCase", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "regExpCreate", "unconstruct", "typeErrorCreate", "TypeError", "func", "thisArg", "addToSet", "set", "array", "l", "length", "element", "lcElement", "clone", "object", "newObject", "property", "html", "svg", "svgFilters", "mathMl", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "createPolicy", "suffix", "currentScript", "hasAttribute", "getAttribute", "policyName", "_", "warn", "createDOMPurify", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "removeTitle", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "Text", "Comment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "RETURN_TRUSTED_TYPE", "createHTML", "implementation", "createNodeIterator", "getElementsByTagName", "createDocumentFragment", "importNode", "hooks", "createHTMLDocument", "documentMode", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "SAFE_FOR_JQUERY", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_DOM_IMPORT", "SANITIZE_DOM", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "CONFIG", "formElement", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "_removeAttribute", "name", "getAttributeNode", "removeAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "body", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "insertBefore", "createTextNode", "childNodes", "call", "querySelector", "innerHTML", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "FILTER_ACCEPT", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "setAttribute", "namespaceURI", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "tagName", "querySelectorAll", "insertAdjacentHTML", "htmlToInsert", "cloneNode", "_isValidAttribute", "lcTag", "lcName", "value", "_sanitizeAttributes", "attr", "idAttr", "hookEvent", "attrName", "attrValue", "keepAttr", "forceKeepAttr", "undefined", "id", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toString", "_typeof", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "serializedHTML", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";oMAAQA,EAA+DC,OAA/DD,eAAgBE,EAA+CD,OAA/CC,eAAgBC,EAA+BF,OAA/BE,SAAgBC,EAAeH,OAArBI,KAE5CC,EAAiBL,OAAjBK,OAAQC,EAASN,OAATM,OACgC,oBAAZC,SAA2BA,QAAvDC,IAAAA,MAAOC,IAAAA,UAERD,MACK,SAAUE,EAAKC,EAAWC,UACzBF,EAAIF,MAAMG,EAAWC,KAI3BP,MACM,SAAUQ,UACVA,IAINP,MACI,SAAUO,UACRA,IAINJ,MACS,SAAUK,EAAMF,4CACfE,uIAAQF,QAIvB,IAAMG,EAAeC,EAAQC,MAAMC,UAAUC,SACvCC,EAAeJ,EAAQC,MAAMC,UAAUG,SACvCC,EAAYN,EAAQC,MAAMC,UAAUK,MACpCC,EAAWR,EAAQC,MAAMC,UAAUO,KACnCC,EAAYV,EAAQC,MAAMC,UAAUS,MACpCC,EAAaZ,EAAQC,MAAMC,UAAUW,OAErCC,EAAoBd,EAAQe,OAAOb,UAAUc,aAC7CC,EAAcjB,EAAQe,OAAOb,UAAUgB,OACvCC,EAAgBnB,EAAQe,OAAOb,UAAUkB,SACzCC,EAAgBrB,EAAQe,OAAOb,UAAUG,SACzCiB,EAAatB,EAAQe,OAAOb,UAAUqB,MAEtCC,EAAaxB,EAAQyB,OAAOvB,UAAUwB,MACtCC,EAAeC,EAAYH,QAE3BI,EAAkBD,EAAYE,WAE7B,SAAS9B,EAAQ+B,UACf,SAACC,8BAAYpC,0DAASJ,EAAMuC,EAAMC,EAASpC,IAG7C,SAASgC,EAAYG,UACnB,sCAAInC,gDAASH,EAAUsC,EAAMnC,IAI/B,SAASqC,EAASC,EAAKC,GACxBlD,KAIaiD,EAAK,cAGlBE,EAAID,EAAME,OACPD,KAAK,KACNE,EAAUH,EAAMC,MACG,iBAAZE,EAAsB,KACzBC,EAAYzB,EAAkBwB,GAChCC,IAAcD,IAEXpD,EAASiD,OACNC,GAAKG,KAGHA,KAIVD,IAAW,SAGVJ,EAIF,SAASM,EAAMC,OACdC,EAAY,GAEdC,aACCA,KAAYF,EACXjD,EAAMT,EAAgB0D,EAAQ,CAACE,QACvBA,GAAYF,EAAOE,WAI1BD,EC9FF,IAAME,EAAOvD,EAAO,CACzB,IACA,OACA,UACA,UACA,OACA,UACA,QACA,QACA,IACA,MACA,MACA,MACA,QACA,aACA,OACA,KACA,SACA,SACA,UACA,SACA,OACA,OACA,MACA,WACA,UACA,OACA,WACA,KACA,YACA,MACA,UACA,MACA,MACA,MACA,KACA,KACA,UACA,KACA,WACA,aACA,SACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,MACA,OACA,UACA,OACA,WACA,QACA,MACA,OACA,KACA,WACA,SACA,SACA,IACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,UACA,SACA,SACA,QACA,SACA,SACA,OACA,SACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,WACA,WACA,QACA,KACA,QACA,OACA,KACA,QACA,KACA,IACA,KACA,MACA,QACA,QAIWwD,EAAMxD,EAAO,CACxB,MACA,IACA,WACA,cACA,eACA,eACA,gBACA,mBACA,QACA,SACA,SACA,WACA,OACA,OACA,UACA,SACA,OACA,IACA,QACA,WACA,QACA,QACA,OACA,iBACA,SACA,OACA,WACA,QACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,QACA,SACA,SACA,OACA,WACA,QACA,OACA,QACA,QACA,OACA,UAGWyD,EAAazD,EAAO,CAC/B,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,UACA,UACA,UACA,UACA,UACA,iBACA,UACA,cACA,eACA,WACA,eACA,qBACA,cACA,SACA,iBAGW0D,EAAS1D,EAAO,CAC3B,OACA,WACA,SACA,UACA,QACA,SACA,KACA,aACA,gBACA,KACA,KACA,QACA,UACA,WACA,QACA,OACA,KACA,SACA,QACA,SACA,OACA,OACA,UACA,SACA,MACA,QACA,MACA,SACA,eAGW2D,EAAO3D,EAAO,CAAC,UCnOfuD,EAAOvD,EAAO,CACzB,SACA,SACA,QACA,MACA,iBACA,eACA,uBACA,WACA,aACA,UACA,SACA,UACA,cACA,cACA,UACA,OACA,QACA,QACA,QACA,OACA,UACA,WACA,eACA,SACA,cACA,WACA,WACA,UACA,MACA,WACA,0BACA,wBACA,WACA,YACA,UACA,eACA,OACA,MACA,UACA,SACA,SACA,OACA,OACA,WACA,KACA,YACA,YACA,QACA,OACA,QACA,OACA,OACA,UACA,OACA,MACA,MACA,YACA,QACA,SACA,MACA,YACA,WACA,QACA,OACA,UACA,aACA,SACA,OACA,UACA,UACA,cACA,cACA,SACA,UACA,UACA,aACA,WACA,MACA,WACA,MACA,WACA,OACA,OACA,UACA,aACA,QACA,WACA,QACA,OACA,QACA,OACA,UACA,QACA,MACA,SACA,OACA,QACA,UACA,WACA,QACA,YACA,OACA,SACA,SACA,QACA,QACA,UAGWwD,EAAMxD,EAAO,CACxB,gBACA,aACA,WACA,qBACA,SACA,gBACA,gBACA,UACA,gBACA,iBACA,QACA,OACA,KACA,QACA,OACA,YACA,YACA,QACA,sBACA,8BACA,gBACA,kBACA,KACA,KACA,IACA,KACA,KACA,kBACA,YACA,UACA,UACA,MACA,WACA,YACA,MACA,OACA,eACA,YACA,SACA,cACA,cACA,gBACA,cACA,YACA,mBACA,eACA,aACA,eACA,cACA,KACA,KACA,KACA,KACA,aACA,WACA,gBACA,oBACA,SACA,OACA,KACA,kBACA,KACA,MACA,IACA,KACA,KACA,KACA,KACA,UACA,YACA,aACA,WACA,OACA,eACA,iBACA,eACA,mBACA,iBACA,QACA,aACA,aACA,eACA,eACA,cACA,cACA,mBACA,YACA,MACA,OACA,QACA,SACA,OACA,MACA,OACA,aACA,SACA,WACA,UACA,QACA,SACA,cACA,SACA,WACA,cACA,OACA,aACA,sBACA,mBACA,eACA,SACA,gBACA,sBACA,iBACA,IACA,KACA,KACA,SACA,OACA,OACA,cACA,YACA,UACA,SACA,SACA,QACA,OACA,kBACA,mBACA,mBACA,eACA,cACA,eACA,cACA,aACA,eACA,mBACA,oBACA,iBACA,kBACA,oBACA,iBACA,SACA,eACA,QACA,eACA,WACA,UACA,UACA,YACA,cACA,kBACA,iBACA,aACA,OACA,KACA,KACA,UACA,SACA,UACA,aACA,UACA,aACA,gBACA,gBACA,QACA,eACA,OACA,eACA,mBACA,mBACA,IACA,KACA,KACA,QACA,IACA,KACA,KACA,IACA,eAGW0D,EAAS1D,EAAO,CAC3B,SACA,cACA,QACA,WACA,QACA,eACA,cACA,aACA,aACA,QACA,MACA,UACA,eACA,WACA,QACA,QACA,SACA,OACA,KACA,UACA,SACA,gBACA,SACA,SACA,iBACA,YACA,WACA,cACA,UACA,UACA,gBACA,WACA,WACA,OACA,WACA,WACA,aACA,UACA,SACA,SACA,cACA,gBACA,uBACA,YACA,YACA,aACA,WACA,iBACA,iBACA,YACA,UACA,QACA,UAGW4D,EAAM5D,EAAO,CACxB,aACA,SACA,cACA,YACA,gBChWW6D,EAAgB5D,EAAK,6BACrB6D,EAAW7D,EAAK,yBAChB8D,EAAY9D,EAAK,8BACjB+D,EAAY/D,EAAK,kBACjBgE,EAAiBhE,EAC5B,yFAEWiE,EAAoBjE,EAAK,yBACzBkE,EAAkBlE,EAC7B,mYCYF,IAAMmE,EAAY,iBAAyB,oBAAXC,OAAyB,KAAOA,QAU1DC,EAA4B,SAAUC,EAAcC,MAE9B,qBAAjBD,gBAAAA,KAC8B,mBAA9BA,EAAaE,oBAEb,SAMLC,EAAS,KAGXF,EAASG,eACTH,EAASG,cAAcC,aAHP,6BAKPJ,EAASG,cAAcE,aALhB,8BAQZC,EAAa,aAAeJ,EAAS,IAAMA,EAAS,eAGjDH,EAAaE,aAAaK,EAAY,qBAChCvB,UACFA,KAGX,MAAOwB,kBAICC,KACN,uBAAyBF,EAAa,0BAEjC,cAIX,SAASG,QAAgBZ,yDAASD,IAC1Bc,EAAY,SAACC,UAASF,EAAgBE,SAMlCC,QAAUC,WAMVC,QAAU,IAEfjB,IAAWA,EAAOG,UAAyC,IAA7BH,EAAOG,SAASe,kBAGvCC,aAAc,EAEjBN,MAGHO,EAAmBpB,EAAOG,SAC5BkB,GAAc,EAEZlB,EAAaH,EAAbG,SAEJmB,EASEtB,EATFsB,iBACAC,EAQEvB,EARFuB,oBACAC,EAOExB,EAPFwB,KACAC,EAMEzB,EANFyB,aAMEzB,EALF0B,aAAAA,aAAe1B,EAAO0B,cAAgB1B,EAAO2B,kBAC7CC,EAIE5B,EAJF4B,KACAC,EAGE7B,EAHF6B,QACAC,EAEE9B,EAFF8B,UACA5B,EACEF,EADFE,gBASiC,mBAAxBqB,EAAoC,KACvCQ,GAAW5B,EAAS6B,cAAc,YACpCD,GAASE,SAAWF,GAASE,QAAQC,kBAC5BH,GAASE,QAAQC,mBAI1BC,GAAqBlC,EACzBC,EACAkB,GAEIgB,GACJD,IAAsBE,GAClBF,GAAmBG,WAAW,IAC9B,MAOFnC,EAJFoC,MAAAA,eACAC,MAAAA,mBACAC,MAAAA,qBACAC,MAAAA,uBAEMC,GAAevB,EAAfuB,WAEJC,GAAQ,KAKFzB,YACRoB,SAC6C,IAAtCA,GAAeM,oBACI,IAA1B1C,EAAS2C,iBAGTtD,GAMEuD,EALFtD,GAKEsD,EAJFrD,GAIEqD,EAHFpD,GAGEoD,EAFFlD,GAEEkD,EADFjD,GACEiD,EAEEnD,GAAmBmD,EAQrBC,GAAe,KACbC,GAAuB1E,EAAS,eACjC2E,KACAA,KACAA,KACAA,KACAA,KAIDC,GAAe,KACbC,GAAuB7E,EAAS,eACjC8E,KACAA,KACAA,KACAA,KAIDC,GAAc,KAGdC,GAAc,KAGdC,IAAkB,EAGlBC,IAAkB,EAGlBC,IAA0B,EAG1BC,IAAkB,EAKlBC,IAAqB,EAGrBC,IAAiB,EAGjBC,IAAa,EAIbC,IAAa,EAMbC,IAAa,EAIbC,IAAsB,EAMtBC,IAAoB,EAIpB7B,IAAsB,EAGtB8B,IAAe,EAGfC,IAAe,EAIfC,IAAW,EAGXC,GAAe,GAGbC,GAAkBhG,EAAS,GAAI,CACnC,iBACA,QACA,WACA,OACA,gBACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,QACA,UACA,WACA,YACA,SACA,QACA,MACA,WACA,QACA,QACA,QACA,QAIEiG,GAAgB,KACdC,GAAwBlG,EAAS,GAAI,CACzC,QACA,QACA,MACA,SACA,QACA,UAIEmG,GAAsB,KACpBC,GAA8BpG,EAAS,GAAI,CAC/C,MACA,QACA,MACA,KACA,QACA,OACA,UACA,cACA,UACA,QACA,QACA,QACA,UAIEqG,GAAS,KAKPC,GAAc1E,EAAS6B,cAAc,QAQrC8C,GAAe,SAAUC,GACzBH,IAAUA,KAAWG,IAKpBA,GAAsB,qBAARA,gBAAAA,QACX,OAKN,iBAAkBA,EACdxG,EAAS,GAAIwG,EAAI/B,cACjBC,MAEJ,iBAAkB8B,EACdxG,EAAS,GAAIwG,EAAI5B,cACjBC,MAEJ,sBAAuB2B,EACnBxG,EAASO,EAAM6F,IAA8BI,EAAIC,mBACjDL,MAEJ,sBAAuBI,EACnBxG,EAASO,EAAM2F,IAAwBM,EAAIE,mBAC3CR,MACQ,gBAAiBM,EAAMxG,EAAS,GAAIwG,EAAIzB,aAAe,MACvD,gBAAiByB,EAAMxG,EAAS,GAAIwG,EAAIxB,aAAe,MACtD,iBAAkBwB,GAAMA,EAAIT,iBACD,IAAxBS,EAAIvB,oBACoB,IAAxBuB,EAAItB,mBACIsB,EAAIrB,0BAA2B,KACvCqB,EAAIpB,kBAAmB,KACpBoB,EAAInB,qBAAsB,KAC9BmB,EAAIlB,iBAAkB,KAC1BkB,EAAIf,aAAc,KACTe,EAAId,sBAAuB,KAC7Bc,EAAIb,oBAAqB,KACvBa,EAAI1C,sBAAuB,KACpC0C,EAAIhB,aAAc,MACK,IAArBgB,EAAIZ,iBACiB,IAArBY,EAAIX,gBACRW,EAAIV,WAAY,KACVU,EAAIG,oBAAsBtF,GACvCgE,SACgB,GAGhBK,SACW,GAIXK,QACa/F,EAAS,eAAQ2E,QACjB,IACW,IAAtBoB,GAAapF,SACN8D,GAAcE,KACdC,GAAcE,KAGA,IAArBiB,GAAanF,QACN6D,GAAcE,KACdC,GAAcE,KACdF,GAAcE,KAGO,IAA5BiB,GAAalF,eACN4D,GAAcE,KACdC,GAAcE,KACdF,GAAcE,KAGG,IAAxBiB,GAAajF,WACN2D,GAAcE,KACdC,GAAcE,KACdF,GAAcE,KAKvB0B,EAAII,WACFnC,KAAiBC,QACJnE,EAAMkE,OAGdA,GAAc+B,EAAII,WAGzBJ,EAAIK,WACFjC,KAAiBC,QACJtE,EAAMqE,OAGdA,GAAc4B,EAAIK,WAGzBL,EAAIC,qBACGN,GAAqBK,EAAIC,mBAIhCZ,QACW,UAAW,GAItBP,MACOb,GAAc,CAAC,OAAQ,OAAQ,SAItCA,GAAaqC,UACNrC,GAAc,CAAC,iBACjBM,GAAYgC,OAKjB3J,KACKoJ,MAGAA,IAQLQ,GAAe,SAAUC,KACnB3E,EAAUI,QAAS,CAAErC,QAAS4G,UAGjCC,WAAWC,YAAYF,GAC5B,MAAO9E,KACFiF,UAAYvD,KAUfwD,GAAmB,SAAUC,EAAML,SAE3B3E,EAAUI,QAAS,WAChBuE,EAAKM,iBAAiBD,QAC3BL,IAER,MAAO9E,KACGG,EAAUI,QAAS,WAChB,UACLuE,MAILO,gBAAgBF,IASjBG,GAAgB,SAAUC,OAE1BC,SACAC,YAEApC,KACM,oBAAsBkC,MACzB,KAECG,EAAU7I,EAAY0I,EAAO,iBACfG,GAAWA,EAAQ,OAGnCC,EAAelE,GACjBA,GAAmBG,WAAW2D,GAC9BA,SAGI,IAAInE,GAAYwE,gBAAgBD,EAAc,aACpD,MAAO3F,OAGLW,KACOiC,GAAa,CAAC,WAIpB4C,IAAQA,EAAIK,gBAAiB,KAExBC,KADFjE,GAAeM,mBAAmB,KAChC2D,OACHf,WAAWC,YAAYc,EAAKf,WAAWgB,qBACvCd,UAAYU,SAGfJ,GAASE,KACPK,KAAKE,aACPvG,EAASwG,eAAeR,GACxBD,EAAIM,KAAKI,WAAW,IAAM,MAKvBnE,GAAqBoE,KAAKX,EAAKrC,GAAiB,OAAS,QAAQ,IAItEhD,EAAUM,gCAGF+E,EAAMF,GAAc,wCACtBlI,EAAW,WAAYoI,EAAIY,cAAc,SAASC,gBACtC,GAEhB,MAAOrG,YAUPsG,GAAkB,SAAUlG,UACzB0B,GAAmBqE,KACxB/F,EAAKoB,eAAiBpB,EACtBA,EACAW,EAAWwF,aAAexF,EAAWyF,aAAezF,EAAW0F,WAC/D,kBACS1F,EAAW2F,iBAEpB,IAUEC,GAAe,SAAUC,WACzBA,aAAe1F,GAAQ0F,aAAezF,MAKhB,iBAAjByF,EAAIC,UACgB,iBAApBD,EAAIE,aACgB,mBAApBF,EAAI5B,aACT4B,EAAIG,sBAAsB/F,GACG,mBAAxB4F,EAAIvB,iBACiB,mBAArBuB,EAAII,cACiB,iBAArBJ,EAAIK,eAcTC,GAAU,SAAU7I,SACD,qBAATyC,gBAAAA,IACVzC,aAAkByC,EAClBzC,GACoB,qBAAXA,gBAAAA,KACoB,iBAApBA,EAAOmC,UACa,iBAApBnC,EAAOwI,UAWhBM,GAAe,SAAUC,EAAYC,EAAaC,GACjDpF,GAAMkF,MAIElF,GAAMkF,IAAa,SAACG,KAC1BpB,KAAKhG,EAAWkH,EAAaC,EAAMpD,QAetCsD,GAAoB,SAAUH,OAC9B9F,eAGS,yBAA0B8F,EAAa,MAGhDV,GAAaU,aACFA,IACN,MAIHI,EAAU/K,EAAkB2K,EAAYR,gBAGjC,sBAAuBQ,EAAa,uBAElC/E,MAKA,QAAZmF,GAAiC,SAAZA,IAC2B,IAAjDJ,EAAYK,iBAAiB,SAASzJ,iBAEzBoJ,IACN,MAIJ/E,GAAamF,IAAY7E,GAAY6E,GAAU,IAGhD/D,KACCG,GAAgB4D,IACyB,mBAAnCJ,EAAYM,2BAGXC,EAAeP,EAAYhB,YACrBsB,mBACV,WACAlG,GACIA,GAAmBG,WAAWgG,GAC9BA,GAEN,MAAO5H,cAGEqH,IACN,QAKK,aAAZI,GACArK,EAAW,eAAgBiK,EAAYhB,YAO3B,YAAZoB,GACArK,EAAW,cAAeiK,EAAYhB,eANzBgB,IACN,KAaPpE,IACCoE,EAAYtB,mBACXsB,EAAY9F,SAAY8F,EAAY9F,QAAQwE,oBAC9C3I,EAAW,KAAMiK,EAAYP,iBAEnB3G,EAAUI,QAAS,CAAErC,QAASmJ,EAAYQ,cAChDR,EAAYhB,YACFA,UAAYtJ,EACtBsK,EAAYhB,UACZ,KACA,UAGUA,UAAYtJ,EACtBsK,EAAYP,YACZ,KACA,SAMF5D,IAA+C,IAAzBmE,EAAY7G,aAE1B6G,EAAYP,cACZ/J,EAAcwE,EAASzC,GAAe,OACtC/B,EAAcwE,EAASxC,GAAU,KACvCsI,EAAYP,cAAgBvF,MACpBpB,EAAUI,QAAS,CAAErC,QAASmJ,EAAYQ,gBACxCf,YAAcvF,OAKjB,wBAAyB8F,EAAa,OAE5C,IAYHS,GAAoB,SAAUC,EAAOC,EAAQC,MAG/CxE,KACY,OAAXuE,GAA8B,SAAXA,KACnBC,KAASxI,GAAYwI,KAAS9D,WAExB,KAOLpB,IAAmB3F,EAAW4B,GAAWgJ,SAEtC,GAAIlF,IAAmB1F,EAAW6B,GAAW+I,QAG7C,CAAA,IAAKvF,GAAauF,IAAWnF,GAAYmF,UACvC,EAGF,GAAIhE,GAAoBgE,SAIxB,GACL5K,EAAW8B,GAAgBnC,EAAckL,EAAO7I,GAAiB,WAK5D,GACO,QAAX4I,GAA+B,eAAXA,GAAsC,SAAXA,GACtC,WAAVD,GACkC,IAAlC9K,EAAcgL,EAAO,WACrBnE,GAAciE,IAMT,GACL/E,KACC5F,EAAW+B,GAAmBpC,EAAckL,EAAO7I,GAAiB,WAKhE,GAAK6I,SAIH,eAGF,GAcHC,GAAsB,SAAUb,OAChCc,SACAF,SACAD,SACAI,SACApK,YAES,2BAA4BqJ,EAAa,UAEhDN,EAAeM,EAAfN,cAGDA,OAICsB,EAAY,UACN,aACC,aACD,oBACS5F,UAEjBsE,EAAW9I,OAGRD,KAAK,SACH+I,EAAW/I,GACVmH,IAAAA,KAAM8B,IAAAA,kBACN/J,EAAWiL,EAAKF,SACfvL,EAAkByI,KAGjBmD,SAAWN,IACXO,UAAYN,IACZO,UAAW,IACXC,mBAAgBC,KACb,wBAAyBrB,EAAagB,KAC3CA,EAAUE,WAEdF,EAAUI,kBASD,SAAXT,GACyB,QAAzBX,EAAYR,UACZE,EAAW4B,KAEF5B,EAAW4B,KACPnM,EAAWuK,EAAY,OACnB,KAAMM,MACNlC,EAAMkC,GACnBrL,EAAa+K,EAAYqB,GAAUpK,KACzBgJ,aAAa,KAAMoB,EAAOH,WAEnC,CAAA,GAGoB,YAAbpB,UACD,SAAXmB,GACU,SAAVC,GACAI,EAAUG,WACT/F,GAAauF,KAAYnF,GAAYmF,aAOzB,OAAT7C,KACU6B,aAAa7B,EAAM,OAGhBA,EAAMkC,MAIpBgB,EAAUG,YAKXvF,IAAmB7F,EAAW,OAAQ6K,MACvB9C,EAAMkC,WAMvBjK,EAAW,YAAaiK,EAAYJ,eACpC7J,EACEG,EACE,MAAQrB,EAAUnB,EAAW8I,IAAkB,KAAO,IACtD,KAEFoE,MAGe9C,EAAMkC,QAKrBnE,OACMnG,EAAckL,EAAOnJ,GAAe,OACpC/B,EAAckL,EAAOlJ,GAAU,UAInCgJ,EAAQV,EAAYR,SAASjK,iBAC9BkL,GAAkBC,EAAOC,EAAQC,OAMhChB,IACU2B,eAAe3B,EAAc9B,EAAM8C,KAGnCjB,aAAa7B,EAAM8C,KAGxB9H,EAAUI,SACnB,MAAOP,UAIE,0BAA2BqH,EAAa,QAQjDwB,GAAqB,SAArBA,EAA+BC,OAC/BC,SACEC,EAAiB1C,GAAgBwC,UAG1B,0BAA2BA,EAAU,MAE1CC,EAAaC,EAAeC,eAErB,yBAA0BF,EAAY,MAG/CvB,GAAkBuB,KAKlBA,EAAWxH,mBAAmBX,KACbmI,EAAWxH,YAIZwH,OAIT,yBAA0BD,EAAU,gBAWzCI,SAAW,SAAU3D,EAAOlB,OAChCyB,SACAqD,SACA9B,SACA+B,SACAC,YAIC9D,MACK,eAIW,iBAAVA,IAAuB2B,GAAQ3B,GAAQ,IAElB,mBAAnBA,EAAM+D,eACT7L,EAAgB,iCAGD,mBADb8H,EAAM+D,kBAEN7L,EAAgB,uCAMvB0C,EAAUM,YAAa,IAEO,WAA/B8I,EAAOjK,EAAOkK,eACiB,mBAAxBlK,EAAOkK,aACd,IACqB,iBAAVjE,SACFjG,EAAOkK,aAAajE,MAGzB2B,GAAQ3B,UACHjG,EAAOkK,aAAajE,EAAMN,kBAI9BM,KAIJnC,OACUiB,KAIL9D,QAAU,GAGC,iBAAVgF,QACE,GAGT5B,SAEG,GAAI4B,aAAiBzE,EAKI,UAFvBwE,GAAc,gBACD9D,cAAcS,WAAWsD,GAAO,IACnC/E,UAA4C,SAA1B2I,EAAatC,UAGX,SAA1BsC,EAAatC,WADfsC,IAKFM,YAAYN,OAEd,KAGF7F,KACAJ,KACAC,KAEuB,MAAlBlH,QAAQ,YAEPwF,IAAsBE,GACzBF,GAAmBG,WAAW2D,GAC9BA,SAICD,GAAcC,WAIZjC,GAAa,KAAO5B,GAK3BoE,GAAQzC,OACGyC,EAAK4D,oBAIdC,EAAerD,GAAgB3C,GAAW4B,EAAQO,GAGhDuB,EAAcsC,EAAaV,YAEJ,IAAzB5B,EAAY7G,UAAkB6G,IAAgB+B,GAK9C5B,GAAkBH,KAKlBA,EAAY9F,mBAAmBX,MACdyG,EAAY9F,YAIb8F,KAEVA,QAGF,KAGN1D,UACK4B,KAILjC,GAAY,IACVC,SACWvB,GAAuBmE,KAAKL,EAAKtE,eAEvCsE,EAAK4D,cAECD,YAAY3D,EAAK4D,mBAGjB5D,SAGXtC,OAQWvB,GAAWkE,KAAKzF,EAAkB2I,GAAY,IAGtDA,MAGLO,EAAiBzG,GAAiB2C,EAAKb,UAAYa,EAAKO,iBAGxDnD,OACenG,EAAc6M,EAAgB9K,GAAe,OAC7C/B,EAAc6M,EAAgB7K,GAAU,MAGpD0C,IAAsBE,GACzBF,GAAmBG,WAAWgI,GAC9BA,KASIC,UAAY,SAAUxF,MACjBA,OACA,KAQLyF,YAAc,cACb,SACI,KAaLC,iBAAmB,SAAUC,EAAK7B,EAAMF,GAE3C/D,OACU,QAGT6D,EAAQrL,EAAkBsN,GAC1BhC,EAAStL,EAAkByL,UAC1BL,GAAkBC,EAAOC,EAAQC,MAUhCgC,QAAU,SAAU7C,EAAY8C,GACZ,mBAAjBA,OAIL9C,GAAclF,GAAMkF,IAAe,KAC/BlF,GAAMkF,GAAa8C,OAUrBC,WAAa,SAAU/C,GAC3BlF,GAAMkF,MACClF,GAAMkF,OAUTgD,YAAc,SAAUhD,GAC5BlF,GAAMkF,QACFA,GAAc,OASdiD,eAAiB,cACjB,IAGHlK,EAGMD"}