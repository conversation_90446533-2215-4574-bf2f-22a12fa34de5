!function(e){"use strict";var t=e.HTMLCanvasElement&&e.HTMLCanvasElement.prototype,i=e.Blob&&function(){try{return Boolean(new Blob)}catch(e){return!1}}(),n=i&&e.Uint8Array&&function(){try{return 100===new Blob([new Uint8Array(100)]).size}catch(e){return!1}}(),r=e.BlobBuilder||e.WebKitBlobBuilder||e.MozBlobBuilder||e.MSBlobBuilder,o=/^data:((.*?)(;charset=.*?)?)(;base64)?,/,a=(i||r)&&e.atob&&e.ArrayBuffer&&e.Uint8Array&&function(e){var t,a,s,l,d,p,u,f,c;if(!(t=e.match(o)))throw new Error("invalid data URI");for(a=t[2]?t[1]:"text/plain"+(t[3]||";charset=US-ASCII"),s=!!t[4],l=e.slice(t[0].length),d=s?atob(l):decodeURIComponent(l),p=new ArrayBuffer(d.length),u=new Uint8Array(p),f=0;f<d.length;f+=1)u[f]=d.charCodeAt(f);return i?new Blob([n?u:p],{type:a}):((c=new r).append(p),c.getBlob(a))};e.HTMLCanvasElement&&!t.toBlob&&(t.mozGetAsFile?t.toBlob=function(e,i,n){var r=this;setTimeout((function(){n&&t.toDataURL&&a?e(a(r.toDataURL(i,n))):e(r.mozGetAsFile("blob",i))}))}:t.toDataURL&&a&&(t.msToBlob?t.toBlob=function(e,i,n){var r=this;setTimeout((function(){(i&&"image/png"!==i||n)&&t.toDataURL&&a?e(a(r.toDataURL(i,n))):e(r.msToBlob(i))}))}:t.toBlob=function(e,t,i){var n=this;setTimeout((function(){e(a(n.toDataURL(t,i)))}))})),"function"==typeof define&&define.amd?define((function(){return a})):"object"==typeof module&&module.exports?module.exports=a:e.dataURLtoBlob=a}(window),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?e(require("jquery")):e(window.jQuery)}((function(e){"use strict";var t=0,i=e,n="parseJSON";"JSON"in window&&"parse"in JSON&&(i=JSON,n="parse"),e.ajaxTransport("iframe",(function(i){if(i.async){var n,r,o,a=i.initialIframeSrc||"javascript:false;";return{send:function(s,l){(n=e('<form style="display:none;"></form>')).attr("accept-charset",i.formAcceptCharset),o=/\?/.test(i.url)?"&":"?","DELETE"===i.type?(i.url=i.url+o+"_method=DELETE",i.type="POST"):"PUT"===i.type?(i.url=i.url+o+"_method=PUT",i.type="POST"):"PATCH"===i.type&&(i.url=i.url+o+"_method=PATCH",i.type="POST"),r=e('<iframe src="'+a+'" name="iframe-transport-'+(t+=1)+'"></iframe>').on("load",(function(){var t,o=e.isArray(i.paramName)?i.paramName:[i.paramName];r.off("load").on("load",(function(){var t;try{if(!(t=r.contents()).length||!t[0].firstChild)throw new Error}catch(e){t=void 0}l(200,"success",{iframe:t}),e('<iframe src="'+a+'"></iframe>').appendTo(n),window.setTimeout((function(){n.remove()}),0)})),n.prop("target",r.prop("name")).prop("action",i.url).prop("method",i.type),i.formData&&e.each(i.formData,(function(t,i){e('<input type="hidden"/>').prop("name",i.name).val(i.value).appendTo(n)})),i.fileInput&&i.fileInput.length&&"POST"===i.type&&(t=i.fileInput.clone(),i.fileInput.after((function(e){return t[e]})),i.paramName&&i.fileInput.each((function(t){e(this).prop("name",o[t]||i.paramName)})),n.append(i.fileInput).prop("enctype","multipart/form-data").prop("encoding","multipart/form-data"),i.fileInput.removeAttr("form")),window.setTimeout((function(){n.submit(),t&&t.length&&i.fileInput.each((function(i,n){var r=e(t[i]);e(n).prop("name",r.prop("name")).attr("form",r.attr("form")),r.replaceWith(n)}))}),0)})),n.append(r).appendTo(document.body)},abort:function(){r&&r.off("load").prop("src",a),n&&n.remove()}}}})),e.ajaxSetup({converters:{"iframe text":function(t){return t&&e(t[0].body).text()},"iframe json":function(t){return t&&i[n](e(t[0].body).text())},"iframe html":function(t){return t&&e(t[0].body).html()},"iframe xml":function(t){var i=t&&t[0];return i&&e.isXMLDoc(i)?i:e.parseXML(i.XMLDocument&&i.XMLDocument.xml||e(i.body).html())},"iframe script":function(t){return t&&e.globalEval(e(t[0].body).text())}}})})),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","jquery-ui/ui/widget"],e):"object"==typeof exports?e(require("jquery"),require("./vendor/jquery.ui.widget")):e(window.jQuery)}((function(e){"use strict";function t(t){var i="dragover"===t;return function(n){n.dataTransfer=n.originalEvent&&n.originalEvent.dataTransfer;var r=n.dataTransfer;r&&-1!==e.inArray("Files",r.types)&&!1!==this._trigger(t,e.Event(t,{delegatedEvent:n}))&&(n.preventDefault(),i&&(r.dropEffect="copy"))}}var i;e.support.fileInput=!(new RegExp("(Android (1\\.[0156]|2\\.[01]))|(Windows Phone (OS 7|8\\.0))|(XBLWP)|(ZuneWP)|(WPDesktop)|(w(eb)?OSBrowser)|(webOS)|(Kindle/(1\\.0|2\\.[05]|3\\.0))").test(window.navigator.userAgent)||e('<input type="file"/>').prop("disabled")),e.support.xhrFileUpload=!(!window.ProgressEvent||!window.FileReader),e.support.xhrFormDataFileUpload=!!window.FormData,e.support.blobSlice=window.Blob&&(Blob.prototype.slice||Blob.prototype.webkitSlice||Blob.prototype.mozSlice),e.widget("blueimp.fileupload",{options:{dropZone:e(document),pasteZone:void 0,fileInput:void 0,replaceFileInput:!0,paramName:void 0,singleFileUploads:!0,limitMultiFileUploads:void 0,limitMultiFileUploadSize:void 0,limitMultiFileUploadSizeOverhead:512,sequentialUploads:!1,limitConcurrentUploads:void 0,forceIframeTransport:!1,redirect:void 0,redirectParamName:void 0,postMessage:void 0,multipart:!0,maxChunkSize:void 0,uploadedBytes:void 0,recalculateProgress:!0,progressInterval:100,bitrateInterval:500,autoUpload:!0,uniqueFilenames:void 0,messages:{uploadedBytes:"Uploaded bytes exceed file size"},i18n:function(t,i){return t=this.messages[t]||t.toString(),i&&e.each(i,(function(e,i){t=t.replace("{"+e+"}",i)})),t},formData:function(e){return e.serializeArray()},add:function(t,i){if(t.isDefaultPrevented())return!1;(i.autoUpload||!1!==i.autoUpload&&e(this).fileupload("option","autoUpload"))&&i.process().done((function(){i.submit()}))},processData:!1,contentType:!1,cache:!1,timeout:0},_promisePipe:(i=e.fn.jquery.split("."),Number(i[0])>1||Number(i[1])>7?"then":"pipe"),_specialOptions:["fileInput","dropZone","pasteZone","multipart","forceIframeTransport"],_blobSlice:e.support.blobSlice&&function(){var e=this.slice||this.webkitSlice||this.mozSlice;return e.apply(this,arguments)},_BitrateTimer:function(){this.timestamp=Date.now?Date.now():(new Date).getTime(),this.loaded=0,this.bitrate=0,this.getBitrate=function(e,t,i){var n=e-this.timestamp;return(!this.bitrate||!i||n>i)&&(this.bitrate=(t-this.loaded)*(1e3/n)*8,this.loaded=t,this.timestamp=e),this.bitrate}},_isXHRUpload:function(t){return!t.forceIframeTransport&&(!t.multipart&&e.support.xhrFileUpload||e.support.xhrFormDataFileUpload)},_getFormData:function(t){var i;return"function"===e.type(t.formData)?t.formData(t.form):e.isArray(t.formData)?t.formData:"object"===e.type(t.formData)?(i=[],e.each(t.formData,(function(e,t){i.push({name:e,value:t})})),i):[]},_getTotal:function(t){var i=0;return e.each(t,(function(e,t){i+=t.size||1})),i},_initProgressObject:function(t){var i={loaded:0,total:0,bitrate:0};t._progress?e.extend(t._progress,i):t._progress=i},_initResponseObject:function(e){var t;if(e._response)for(t in e._response)Object.prototype.hasOwnProperty.call(e._response,t)&&delete e._response[t];else e._response={}},_onProgress:function(t,i){if(t.lengthComputable){var n,r=Date.now?Date.now():(new Date).getTime();if(i._time&&i.progressInterval&&r-i._time<i.progressInterval&&t.loaded!==t.total)return;i._time=r,n=Math.floor(t.loaded/t.total*(i.chunkSize||i._progress.total))+(i.uploadedBytes||0),this._progress.loaded+=n-i._progress.loaded,this._progress.bitrate=this._bitrateTimer.getBitrate(r,this._progress.loaded,i.bitrateInterval),i._progress.loaded=i.loaded=n,i._progress.bitrate=i.bitrate=i._bitrateTimer.getBitrate(r,n,i.bitrateInterval),this._trigger("progress",e.Event("progress",{delegatedEvent:t}),i),this._trigger("progressall",e.Event("progressall",{delegatedEvent:t}),this._progress)}},_initProgressListener:function(t){var i=this,n=t.xhr?t.xhr():e.ajaxSettings.xhr();n.upload&&(e(n.upload).on("progress",(function(e){var n=e.originalEvent;e.lengthComputable=n.lengthComputable,e.loaded=n.loaded,e.total=n.total,i._onProgress(e,t)})),t.xhr=function(){return n})},_deinitProgressListener:function(t){var i=t.xhr?t.xhr():e.ajaxSettings.xhr();i.upload&&e(i.upload).off("progress")},_isInstanceOf:function(e,t){return Object.prototype.toString.call(t)==="[object "+e+"]"},_getUniqueFilename:function(e,t){return t[e=String(e)]?(e=e.replace(/(?: \(([\d]+)\))?(\.[^.]+)?$/,(function(e,t,i){return" ("+(t?Number(t)+1:1)+")"+(i||"")})),this._getUniqueFilename(e,t)):(t[e]=!0,e)},_initXHRData:function(t){var i,n=this,r=t.files[0],o=t.multipart||!e.support.xhrFileUpload,a="array"===e.type(t.paramName)?t.paramName[0]:t.paramName;t.headers=e.extend({},t.headers),t.contentRange&&(t.headers["Content-Range"]=t.contentRange),o&&!t.blob&&this._isInstanceOf("File",r)||(t.headers["Content-Disposition"]='attachment; filename="'+encodeURI(r.uploadName||r.name)+'"'),o?e.support.xhrFormDataFileUpload&&(t.postMessage?(i=this._getFormData(t),t.blob?i.push({name:a,value:t.blob}):e.each(t.files,(function(n,r){i.push({name:"array"===e.type(t.paramName)&&t.paramName[n]||a,value:r})}))):(n._isInstanceOf("FormData",t.formData)?i=t.formData:(i=new FormData,e.each(this._getFormData(t),(function(e,t){i.append(t.name,t.value)}))),t.blob?i.append(a,t.blob,r.uploadName||r.name):e.each(t.files,(function(r,o){if(n._isInstanceOf("File",o)||n._isInstanceOf("Blob",o)){var s=o.uploadName||o.name;t.uniqueFilenames&&(s=n._getUniqueFilename(s,t.uniqueFilenames)),i.append("array"===e.type(t.paramName)&&t.paramName[r]||a,o,s)}}))),t.data=i):(t.contentType=r.type||"application/octet-stream",t.data=t.blob||r),t.blob=null},_initIframeSettings:function(t){var i=e("<a></a>").prop("href",t.url).prop("host");t.dataType="iframe "+(t.dataType||""),t.formData=this._getFormData(t),t.redirect&&i&&i!==location.host&&t.formData.push({name:t.redirectParamName||"redirect",value:t.redirect})},_initDataSettings:function(e){this._isXHRUpload(e)?(this._chunkedUpload(e,!0)||(e.data||this._initXHRData(e),this._initProgressListener(e)),e.postMessage&&(e.dataType="postmessage "+(e.dataType||""))):this._initIframeSettings(e)},_getParamName:function(t){var i=e(t.fileInput),n=t.paramName;return n?e.isArray(n)||(n=[n]):(n=[],i.each((function(){for(var t=e(this),i=t.prop("name")||"files[]",r=(t.prop("files")||[1]).length;r;)n.push(i),r-=1})),n.length||(n=[i.prop("name")||"files[]"])),n},_initFormSettings:function(t){t.form&&t.form.length||(t.form=e(t.fileInput.prop("form")),t.form.length||(t.form=e(this.options.fileInput.prop("form")))),t.paramName=this._getParamName(t),t.url||(t.url=t.form.prop("action")||location.href),t.type=(t.type||"string"===e.type(t.form.prop("method"))&&t.form.prop("method")||"").toUpperCase(),"POST"!==t.type&&"PUT"!==t.type&&"PATCH"!==t.type&&(t.type="POST"),t.formAcceptCharset||(t.formAcceptCharset=t.form.attr("accept-charset"))},_getAJAXSettings:function(t){var i=e.extend({},this.options,t);return this._initFormSettings(i),this._initDataSettings(i),i},_getDeferredState:function(e){return e.state?e.state():e.isResolved()?"resolved":e.isRejected()?"rejected":"pending"},_enhancePromise:function(e){return e.success=e.done,e.error=e.fail,e.complete=e.always,e},_getXHRPromise:function(t,i,n){var r=e.Deferred(),o=r.promise();return i=i||this.options.context||o,!0===t?r.resolveWith(i,n):!1===t&&r.rejectWith(i,n),o.abort=r.promise,this._enhancePromise(o)},_addConvenienceMethods:function(t,i){var n=this,r=function(t){return e.Deferred().resolveWith(n,t).promise()};i.process=function(t,o){return(t||o)&&(i._processQueue=this._processQueue=(this._processQueue||r([this]))[n._promisePipe]((function(){return i.errorThrown?e.Deferred().rejectWith(n,[i]).promise():r(arguments)}))[n._promisePipe](t,o)),this._processQueue||r([this])},i.submit=function(){return"pending"!==this.state()&&(i.jqXHR=this.jqXHR=!1!==n._trigger("submit",e.Event("submit",{delegatedEvent:t}),this)&&n._onSend(t,this)),this.jqXHR||n._getXHRPromise()},i.abort=function(){return this.jqXHR?this.jqXHR.abort():(this.errorThrown="abort",n._trigger("fail",null,this),n._getXHRPromise(!1))},i.state=function(){return this.jqXHR?n._getDeferredState(this.jqXHR):this._processQueue?n._getDeferredState(this._processQueue):void 0},i.processing=function(){return!this.jqXHR&&this._processQueue&&"pending"===n._getDeferredState(this._processQueue)},i.progress=function(){return this._progress},i.response=function(){return this._response}},_getUploadedBytes:function(e){var t=e.getResponseHeader("Range"),i=t&&t.split("-"),n=i&&i.length>1&&parseInt(i[1],10);return n&&n+1},_chunkedUpload:function(t,i){t.uploadedBytes=t.uploadedBytes||0;var n,r,o=this,a=t.files[0],s=a.size,l=t.uploadedBytes,d=t.maxChunkSize||s,p=this._blobSlice,u=e.Deferred(),f=u.promise();return!(!(this._isXHRUpload(t)&&p&&(l||("function"===e.type(d)?d(t):d)<s))||t.data)&&(!!i||(l>=s?(a.error=t.i18n("uploadedBytes"),this._getXHRPromise(!1,t.context,[null,"error",a.error])):(r=function(){var i=e.extend({},t),f=i._progress.loaded;i.blob=p.call(a,l,l+("function"===e.type(d)?d(i):d),a.type),i.chunkSize=i.blob.size,i.contentRange="bytes "+l+"-"+(l+i.chunkSize-1)+"/"+s,o._trigger("chunkbeforesend",null,i),o._initXHRData(i),o._initProgressListener(i),n=(!1!==o._trigger("chunksend",null,i)&&e.ajax(i)||o._getXHRPromise(!1,i.context)).done((function(n,a,d){l=o._getUploadedBytes(d)||l+i.chunkSize,f+i.chunkSize-i._progress.loaded&&o._onProgress(e.Event("progress",{lengthComputable:!0,loaded:l-i.uploadedBytes,total:l-i.uploadedBytes}),i),t.uploadedBytes=i.uploadedBytes=l,i.result=n,i.textStatus=a,i.jqXHR=d,o._trigger("chunkdone",null,i),o._trigger("chunkalways",null,i),l<s?r():u.resolveWith(i.context,[n,a,d])})).fail((function(e,t,n){i.jqXHR=e,i.textStatus=t,i.errorThrown=n,o._trigger("chunkfail",null,i),o._trigger("chunkalways",null,i),u.rejectWith(i.context,[e,t,n])})).always((function(){o._deinitProgressListener(i)}))},this._enhancePromise(f),f.abort=function(){return n.abort()},r(),f)))},_beforeSend:function(e,t){0===this._active&&(this._trigger("start"),this._bitrateTimer=new this._BitrateTimer,this._progress.loaded=this._progress.total=0,this._progress.bitrate=0),this._initResponseObject(t),this._initProgressObject(t),t._progress.loaded=t.loaded=t.uploadedBytes||0,t._progress.total=t.total=this._getTotal(t.files)||1,t._progress.bitrate=t.bitrate=0,this._active+=1,this._progress.loaded+=t.loaded,this._progress.total+=t.total},_onDone:function(t,i,n,r){var o=r._progress.total,a=r._response;r._progress.loaded<o&&this._onProgress(e.Event("progress",{lengthComputable:!0,loaded:o,total:o}),r),a.result=r.result=t,a.textStatus=r.textStatus=i,a.jqXHR=r.jqXHR=n,this._trigger("done",null,r)},_onFail:function(e,t,i,n){var r=n._response;n.recalculateProgress&&(this._progress.loaded-=n._progress.loaded,this._progress.total-=n._progress.total),r.jqXHR=n.jqXHR=e,r.textStatus=n.textStatus=t,r.errorThrown=n.errorThrown=i,this._trigger("fail",null,n)},_onAlways:function(e,t,i,n){this._trigger("always",null,n)},_onSend:function(t,i){i.submit||this._addConvenienceMethods(t,i);var n,r,o,a,s=this,l=s._getAJAXSettings(i),d=function(){return s._sending+=1,l._bitrateTimer=new s._BitrateTimer,n=n||((r||!1===s._trigger("send",e.Event("send",{delegatedEvent:t}),l))&&s._getXHRPromise(!1,l.context,r)||s._chunkedUpload(l)||e.ajax(l)).done((function(e,t,i){s._onDone(e,t,i,l)})).fail((function(e,t,i){s._onFail(e,t,i,l)})).always((function(e,t,i){if(s._deinitProgressListener(l),s._onAlways(e,t,i,l),s._sending-=1,s._active-=1,l.limitConcurrentUploads&&l.limitConcurrentUploads>s._sending)for(var n=s._slots.shift();n;){if("pending"===s._getDeferredState(n)){n.resolve();break}n=s._slots.shift()}0===s._active&&s._trigger("stop")}))};return this._beforeSend(t,l),this.options.sequentialUploads||this.options.limitConcurrentUploads&&this.options.limitConcurrentUploads<=this._sending?(this.options.limitConcurrentUploads>1?(o=e.Deferred(),this._slots.push(o),a=o[s._promisePipe](d)):(this._sequence=this._sequence[s._promisePipe](d,d),a=this._sequence),a.abort=function(){return r=[void 0,"abort","abort"],n?n.abort():(o&&o.rejectWith(l.context,r),d())},this._enhancePromise(a)):d()},_onAdd:function(t,i){var n,r,o,a,s=this,l=!0,d=e.extend({},this.options,i),p=i.files,u=p.length,f=d.limitMultiFileUploads,c=d.limitMultiFileUploadSize,h=d.limitMultiFileUploadSizeOverhead,m=0,g=this._getParamName(d),_=0;if(!u)return!1;if(c&&void 0===p[0].size&&(c=void 0),(d.singleFileUploads||f||c)&&this._isXHRUpload(d))if(d.singleFileUploads||c||!f)if(!d.singleFileUploads&&c)for(o=[],n=[],a=0;a<u;a+=1)m+=p[a].size+h,(a+1===u||m+p[a+1].size+h>c||f&&a+1-_>=f)&&(o.push(p.slice(_,a+1)),(r=g.slice(_,a+1)).length||(r=g),n.push(r),_=a+1,m=0);else n=g;else for(o=[],n=[],a=0;a<u;a+=f)o.push(p.slice(a,a+f)),(r=g.slice(a,a+f)).length||(r=g),n.push(r);else o=[p],n=[g];return i.originalFiles=p,e.each(o||p,(function(r,a){var d=e.extend({},i);return d.files=o?a:[a],d.paramName=n[r],s._initResponseObject(d),s._initProgressObject(d),s._addConvenienceMethods(t,d),l=s._trigger("add",e.Event("add",{delegatedEvent:t}),d)})),l},_replaceFileInput:function(t){var i=t.fileInput,n=i.clone(!0),r=i.is(document.activeElement);t.fileInputClone=n,e("<form></form>").append(n)[0].reset(),i.after(n).detach(),r&&n.trigger("focus"),e.cleanData(i.off("remove")),this.options.fileInput=this.options.fileInput.map((function(e,t){return t===i[0]?n[0]:t})),i[0]===this.element[0]&&(this.element=n)},_handleFileTreeEntry:function(t,i){var n,r=this,o=e.Deferred(),a=[],s=function(e){e&&!e.entry&&(e.entry=t),o.resolve([e])},l=function(){n.readEntries((function(e){e.length?(a=a.concat(e),l()):function(e){r._handleFileTreeEntries(e,i+t.name+"/").done((function(e){o.resolve(e)})).fail(s)}(a)}),s)};return i=i||"",t.isFile?t._file?(t._file.relativePath=i,o.resolve(t._file)):t.file((function(e){e.relativePath=i,o.resolve(e)}),s):t.isDirectory?(n=t.createReader(),l()):o.resolve([]),o.promise()},_handleFileTreeEntries:function(t,i){var n=this;return e.when.apply(e,e.map(t,(function(e){return n._handleFileTreeEntry(e,i)})))[this._promisePipe]((function(){return Array.prototype.concat.apply([],arguments)}))},_getDroppedFiles:function(t){var i=(t=t||{}).items;return i&&i.length&&(i[0].webkitGetAsEntry||i[0].getAsEntry)?this._handleFileTreeEntries(e.map(i,(function(e){var t;return e.webkitGetAsEntry?((t=e.webkitGetAsEntry())&&(t._file=e.getAsFile()),t):e.getAsEntry()}))):e.Deferred().resolve(e.makeArray(t.files)).promise()},_getSingleFileInputFiles:function(t){var i,n,r=(t=e(t)).prop("webkitEntries")||t.prop("entries");if(r&&r.length)return this._handleFileTreeEntries(r);if((i=e.makeArray(t.prop("files"))).length)void 0===i[0].name&&i[0].fileName&&e.each(i,(function(e,t){t.name=t.fileName,t.size=t.fileSize}));else{if(!(n=t.prop("value")))return e.Deferred().resolve([]).promise();i=[{name:n.replace(/^.*\\/,"")}]}return e.Deferred().resolve(i).promise()},_getFileInputFiles:function(t){return t instanceof e&&1!==t.length?e.when.apply(e,e.map(t,this._getSingleFileInputFiles))[this._promisePipe]((function(){return Array.prototype.concat.apply([],arguments)})):this._getSingleFileInputFiles(t)},_onChange:function(t){var i=this,n={fileInput:e(t.target),form:e(t.target.form)};this._getFileInputFiles(n.fileInput).always((function(r){n.files=r,i.options.replaceFileInput&&i._replaceFileInput(n),!1!==i._trigger("change",e.Event("change",{delegatedEvent:t}),n)&&i._onAdd(t,n)}))},_onPaste:function(t){var i=t.originalEvent&&t.originalEvent.clipboardData&&t.originalEvent.clipboardData.items,n={files:[]};i&&i.length&&(e.each(i,(function(e,t){var i=t.getAsFile&&t.getAsFile();i&&n.files.push(i)})),!1!==this._trigger("paste",e.Event("paste",{delegatedEvent:t}),n)&&this._onAdd(t,n))},_onDrop:function(t){t.dataTransfer=t.originalEvent&&t.originalEvent.dataTransfer;var i=this,n=t.dataTransfer,r={};n&&n.files&&n.files.length&&(t.preventDefault(),this._getDroppedFiles(n).always((function(n){r.files=n,!1!==i._trigger("drop",e.Event("drop",{delegatedEvent:t}),r)&&i._onAdd(t,r)})))},_onDragOver:t("dragover"),_onDragEnter:t("dragenter"),_onDragLeave:t("dragleave"),_initEventHandlers:function(){this._isXHRUpload(this.options)&&(this._on(this.options.dropZone,{dragover:this._onDragOver,drop:this._onDrop,dragenter:this._onDragEnter,dragleave:this._onDragLeave}),this._on(this.options.pasteZone,{paste:this._onPaste})),e.support.fileInput&&this._on(this.options.fileInput,{change:this._onChange})},_destroyEventHandlers:function(){this._off(this.options.dropZone,"dragenter dragleave dragover drop"),this._off(this.options.pasteZone,"paste"),this._off(this.options.fileInput,"change")},_destroy:function(){this._destroyEventHandlers()},_setOption:function(t,i){var n=-1!==e.inArray(t,this._specialOptions);n&&this._destroyEventHandlers(),this._super(t,i),n&&(this._initSpecialOptions(),this._initEventHandlers())},_initSpecialOptions:function(){var t=this.options;void 0===t.fileInput?t.fileInput=this.element.is('input[type="file"]')?this.element:this.element.find('input[type="file"]'):t.fileInput instanceof e||(t.fileInput=e(t.fileInput)),t.dropZone instanceof e||(t.dropZone=e(t.dropZone)),t.pasteZone instanceof e||(t.pasteZone=e(t.pasteZone))},_getRegExp:function(e){var t=e.split("/"),i=t.pop();return t.shift(),new RegExp(t.join("/"),i)},_isRegExpOption:function(t,i){return"url"!==t&&"string"===e.type(i)&&/^\/.*\/[igm]{0,3}$/.test(i)},_initDataAttributes:function(){var t=this,i=this.options,n=this.element.data();e.each(this.element[0].attributes,(function(e,r){var o,a=r.name.toLowerCase();/^data-/.test(a)&&(a=a.slice(5).replace(/-[a-z]/g,(function(e){return e.charAt(1).toUpperCase()})),o=n[a],t._isRegExpOption(a,o)&&(o=t._getRegExp(o)),i[a]=o)}))},_create:function(){this._initDataAttributes(),this._initSpecialOptions(),this._slots=[],this._sequence=this._getXHRPromise(!0),this._sending=this._active=0,this._initProgressObject(this),this._initEventHandlers()},active:function(){return this._active},progress:function(){return this._progress},add:function(t){var i=this;t&&!this.options.disabled&&(t.fileInput&&!t.files?this._getFileInputFiles(t.fileInput).always((function(e){t.files=e,i._onAdd(null,t)})):(t.files=e.makeArray(t.files),this._onAdd(null,t)))},send:function(t){if(t&&!this.options.disabled){if(t.fileInput&&!t.files){var i,n,r=this,o=e.Deferred(),a=o.promise();return a.abort=function(){return n=!0,i?i.abort():(o.reject(null,"abort","abort"),a)},this._getFileInputFiles(t.fileInput).always((function(e){n||(e.length?(t.files=e,(i=r._onSend(null,t)).then((function(e,t,i){o.resolve(e,t,i)}),(function(e,t,i){o.reject(e,t,i)}))):o.reject())})),this._enhancePromise(a)}if(t.files=e.makeArray(t.files),t.files.length)return this._onSend(null,t)}return this._getXHRPromise(!1,t&&t.context)}})})),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","./jquery.fileupload"],e):"object"==typeof exports?e(require("jquery"),require("./jquery.fileupload")):e(window.jQuery)}((function(e){"use strict";var t=e.blueimp.fileupload.prototype.options.add;e.widget("blueimp.fileupload",e.blueimp.fileupload,{options:{processQueue:[],add:function(i,n){var r=e(this);n.process((function(){return r.fileupload("process",n)})),t.call(this,i,n)}},processActions:{},_processFile:function(t,i){var n=this,r=e.Deferred().resolveWith(n,[t]).promise();return this._trigger("process",null,t),e.each(t.processQueue,(function(t,o){var a=function(t){return i.errorThrown?e.Deferred().rejectWith(n,[i]).promise():n.processActions[o.action].call(n,t,o)};r=r[n._promisePipe](a,o.always&&a)})),r.done((function(){n._trigger("processdone",null,t),n._trigger("processalways",null,t)})).fail((function(){n._trigger("processfail",null,t),n._trigger("processalways",null,t)})),r},_transformProcessQueue:function(t){var i=[];e.each(t.processQueue,(function(){var n={},r=this.action,o=!0===this.prefix?r:this.prefix;e.each(this,(function(i,r){"string"===e.type(r)&&"@"===r.charAt(0)?n[i]=t[r.slice(1)||(o?o+i.charAt(0).toUpperCase()+i.slice(1):i)]:n[i]=r})),i.push(n)})),t.processQueue=i},processing:function(){return this._processing},process:function(t){var i=this,n=e.extend({},this.options,t);return n.processQueue&&n.processQueue.length&&(this._transformProcessQueue(n),0===this._processing&&this._trigger("processstart"),e.each(t.files,(function(r){var o=r?e.extend({},n):n,a=function(){return t.errorThrown?e.Deferred().rejectWith(i,[t]).promise():i._processFile(o,t)};o.index=r,i._processing+=1,i._processingQueue=i._processingQueue[i._promisePipe](a,a).always((function(){i._processing-=1,0===i._processing&&i._trigger("processstop")}))}))),this._processingQueue},_create:function(){this._super(),this._processing=0,this._processingQueue=e.Deferred().resolveWith(this).promise()}})})),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","load-image","load-image-meta","load-image-scale","load-image-exif","load-image-orientation","canvas-to-blob","./jquery.fileupload-process"],e):"object"==typeof exports?e(require("jquery"),require("blueimp-load-image/js/load-image"),require("blueimp-load-image/js/load-image-meta"),require("blueimp-load-image/js/load-image-scale"),require("blueimp-load-image/js/load-image-exif"),require("blueimp-load-image/js/load-image-orientation"),require("blueimp-canvas-to-blob"),require("./jquery.fileupload-process")):e(window.jQuery,window.loadImage)}((function(e,t){"use strict";e.blueimp.fileupload.prototype.options.processQueue.unshift({action:"loadImageMetaData",maxMetaDataSize:"@",disableImageHead:"@",disableMetaDataParsers:"@",disableExif:"@",disableExifOffsets:"@",includeExifTags:"@",excludeExifTags:"@",disableIptc:"@",disableIptcOffsets:"@",includeIptcTags:"@",excludeIptcTags:"@",disabled:"@disableImageMetaDataLoad"},{action:"loadImage",prefix:!0,fileTypes:"@",maxFileSize:"@",noRevoke:"@",disabled:"@disableImageLoad"},{action:"resizeImage",prefix:"image",maxWidth:"@",maxHeight:"@",minWidth:"@",minHeight:"@",crop:"@",orientation:"@",forceResize:"@",disabled:"@disableImageResize"},{action:"saveImage",quality:"@imageQuality",type:"@imageType",disabled:"@disableImageResize"},{action:"saveImageMetaData",disabled:"@disableImageMetaDataSave"},{action:"resizeImage",prefix:"preview",maxWidth:"@",maxHeight:"@",minWidth:"@",minHeight:"@",crop:"@",orientation:"@",thumbnail:"@",canvas:"@",disabled:"@disableImagePreview"},{action:"setImage",name:"@imagePreviewName",disabled:"@disableImagePreview"},{action:"deleteImageReferences",disabled:"@disableImageReferencesDeletion"}),e.widget("blueimp.fileupload",e.blueimp.fileupload,{options:{loadImageFileTypes:/^image\/(gif|jpeg|png|svg\+xml)$/,loadImageMaxFileSize:1e7,imageMaxWidth:1920,imageMaxHeight:1080,imageOrientation:!0,imageCrop:!1,disableImageResize:!0,previewMaxWidth:80,previewMaxHeight:80,previewOrientation:!0,previewThumbnail:!0,previewCrop:!1,previewCanvas:!0},processActions:{loadImage:function(i,n){if(n.disabled)return i;var r=this,o=i.files[i.index],a=e.Deferred();return"number"===e.type(n.maxFileSize)&&o.size>n.maxFileSize||n.fileTypes&&!n.fileTypes.test(o.type)||!t(o,(function(e){e.src&&(i.img=e),a.resolveWith(r,[i])}),n)?i:a.promise()},resizeImage:function(i,n){if(n.disabled||!i.canvas&&!i.img)return i;n=e.extend({canvas:!0},n);var r,o,a=this,s=e.Deferred(),l=n.canvas&&i.canvas||i.img,d=function(e){e&&(e.width!==l.width||e.height!==l.height||n.forceResize)&&(i[e.getContext?"canvas":"img"]=e),i.preview=e,s.resolveWith(a,[i])};return i.exif&&n.thumbnail&&(o=(r=i.exif.get("Thumbnail"))&&r.get("Blob"))?(n.orientation=i.exif.get("Orientation"),t(o,d,n),s.promise()):(i.orientation?delete n.orientation:i.orientation=n.orientation||t.orientation,l?(d(t.scale(l,n,i)),s.promise()):i)},saveImage:function(t,i){if(!t.canvas||i.disabled)return t;var n=this,r=t.files[t.index],o=e.Deferred();return t.canvas.toBlob?(t.canvas.toBlob((function(e){e.name||(r.type===e.type?e.name=r.name:r.name&&(e.name=r.name.replace(/\.\w+$/,"."+e.type.substr(6)))),r.type!==e.type&&delete t.imageHead,t.files[t.index]=e,o.resolveWith(n,[t])}),i.type||r.type,i.quality),o.promise()):t},loadImageMetaData:function(i,n){if(n.disabled)return i;var r=this,o=e.Deferred();return t.parseMetaData(i.files[i.index],(function(t){e.extend(i,t),o.resolveWith(r,[i])}),n),o.promise()},saveImageMetaData:function(i,n){if(!(i.imageHead&&i.canvas&&i.canvas.toBlob)||n.disabled)return i;var r=this,o=i.files[i.index],a=e.Deferred();return!0===i.orientation&&i.exifOffsets&&t.writeExifData(i.imageHead,i,"Orientation",1),t.replaceHead(o,i.imageHead,(function(e){e.name=o.name,i.files[i.index]=e,a.resolveWith(r,[i])})),a.promise()},setImage:function(e,t){return e.preview&&!t.disabled&&(e.files[e.index][t.name||"preview"]=e.preview),e},deleteImageReferences:function(e,t){return t.disabled||(delete e.img,delete e.canvas,delete e.preview,delete e.imageHead),e}}})})),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","load-image","./jquery.fileupload-process"],e):"object"==typeof exports?e(require("jquery"),require("blueimp-load-image/js/load-image"),require("./jquery.fileupload-process")):e(window.jQuery,window.loadImage)}((function(e,t){"use strict";e.blueimp.fileupload.prototype.options.processQueue.unshift({action:"loadAudio",prefix:!0,fileTypes:"@",maxFileSize:"@",disabled:"@disableAudioPreview"},{action:"setAudio",name:"@audioPreviewName",disabled:"@disableAudioPreview"}),e.widget("blueimp.fileupload",e.blueimp.fileupload,{options:{loadAudioFileTypes:/^audio\/.*$/},_audioElement:document.createElement("audio"),processActions:{loadAudio:function(i,n){if(n.disabled)return i;var r,o,a=i.files[i.index];return this._audioElement.canPlayType&&this._audioElement.canPlayType(a.type)&&("number"!==e.type(n.maxFileSize)||a.size<=n.maxFileSize)&&(!n.fileTypes||n.fileTypes.test(a.type))&&(r=t.createObjectURL(a))?((o=this._audioElement.cloneNode(!1)).src=r,o.controls=!0,i.audio=o,i):i},setAudio:function(e,t){return e.audio&&!t.disabled&&(e.files[e.index][t.name||"preview"]=e.audio),e}}})})),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","load-image","./jquery.fileupload-process"],e):"object"==typeof exports?e(require("jquery"),require("blueimp-load-image/js/load-image"),require("./jquery.fileupload-process")):e(window.jQuery,window.loadImage)}((function(e,t){"use strict";e.blueimp.fileupload.prototype.options.processQueue.unshift({action:"loadVideo",prefix:!0,fileTypes:"@",maxFileSize:"@",disabled:"@disableVideoPreview"},{action:"setVideo",name:"@videoPreviewName",disabled:"@disableVideoPreview"}),e.widget("blueimp.fileupload",e.blueimp.fileupload,{options:{loadVideoFileTypes:/^video\/.*$/},_videoElement:document.createElement("video"),processActions:{loadVideo:function(i,n){if(n.disabled)return i;var r,o,a=i.files[i.index];return this._videoElement.canPlayType&&this._videoElement.canPlayType(a.type)&&("number"!==e.type(n.maxFileSize)||a.size<=n.maxFileSize)&&(!n.fileTypes||n.fileTypes.test(a.type))&&(r=t.createObjectURL(a))?((o=this._videoElement.cloneNode(!1)).src=r,o.controls=!0,i.video=o,i):i},setVideo:function(e,t){return e.video&&!t.disabled&&(e.files[e.index][t.name||"preview"]=e.video),e}}})})),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","./jquery.fileupload-process"],e):"object"==typeof exports?e(require("jquery"),require("./jquery.fileupload-process")):e(window.jQuery)}((function(e){"use strict";e.blueimp.fileupload.prototype.options.processQueue.push({action:"validate",always:!0,acceptFileTypes:"@",maxFileSize:"@",minFileSize:"@",maxNumberOfFiles:"@",disabled:"@disableValidation"}),e.widget("blueimp.fileupload",e.blueimp.fileupload,{options:{getNumberOfFiles:e.noop,messages:{maxNumberOfFiles:"Maximum number of files exceeded",acceptFileTypes:"File type not allowed",maxFileSize:"File is too large",minFileSize:"File is too small"}},processActions:{validate:function(t,i){if(i.disabled)return t;var n,r=e.Deferred(),o=this.options,a=t.files[t.index];return(i.minFileSize||i.maxFileSize)&&(n=a.size),"number"===e.type(i.maxNumberOfFiles)&&(o.getNumberOfFiles()||0)+t.files.length>i.maxNumberOfFiles?a.error=o.i18n("maxNumberOfFiles"):!i.acceptFileTypes||i.acceptFileTypes.test(a.type)||i.acceptFileTypes.test(a.name)?n>i.maxFileSize?a.error=o.i18n("maxFileSize"):"number"===e.type(n)&&n<i.minFileSize?a.error=o.i18n("minFileSize"):delete a.error:a.error=o.i18n("acceptFileTypes"),a.error||t.files.error?(t.files.error=!0,r.rejectWith(this,[t])):r.resolveWith(this,[t]),r.promise()}}})})),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","blueimp-tmpl","./jquery.fileupload-image","./jquery.fileupload-audio","./jquery.fileupload-video","./jquery.fileupload-validate"],e):"object"==typeof exports?e(require("jquery"),require("blueimp-tmpl"),require("./jquery.fileupload-image"),require("./jquery.fileupload-audio"),require("./jquery.fileupload-video"),require("./jquery.fileupload-validate")):e(window.jQuery,window.tmpl)}((function(e,t){"use strict";e.blueimp.fileupload.prototype._specialOptions.push("filesContainer","uploadTemplateId","downloadTemplateId"),e.widget("blueimp.fileupload",e.blueimp.fileupload,{options:{autoUpload:!1,showElementClass:"in",uploadTemplateId:"template-upload",downloadTemplateId:"template-download",filesContainer:void 0,prependFiles:!1,dataType:"json",messages:{unknownError:"Unknown error"},getNumberOfFiles:function(){return this.filesContainer.children().not(".processing").length},getFilesFromResponse:function(t){return t.result&&e.isArray(t.result.files)?t.result.files:[]},add:function(t,i){if(t.isDefaultPrevented())return!1;var n=e(this),r=n.data("blueimp-fileupload")||n.data("fileupload"),o=r.options;i.context=r._renderUpload(i.files).data("data",i).addClass("processing"),o.filesContainer[o.prependFiles?"prepend":"append"](i.context),r._forceReflow(i.context),r._transition(i.context),i.process((function(){return n.fileupload("process",i)})).always((function(){i.context.each((function(t){e(this).find(".size").text(r._formatFileSize(i.files[t].size))})).removeClass("processing"),r._renderPreviews(i)})).done((function(){i.context.find(".edit,.start").prop("disabled",!1),!1!==r._trigger("added",t,i)&&(o.autoUpload||i.autoUpload)&&!1!==i.autoUpload&&i.submit()})).fail((function(){i.files.error&&i.context.each((function(t){var n=i.files[t].error;n&&e(this).find(".error").text(n)}))}))},send:function(t,i){if(t.isDefaultPrevented())return!1;var n=e(this).data("blueimp-fileupload")||e(this).data("fileupload");return i.context&&i.dataType&&"iframe"===i.dataType.substr(0,6)&&i.context.find(".progress").addClass(!e.support.transition&&"progress-animated").attr("aria-valuenow",100).children().first().css("width","100%"),n._trigger("sent",t,i)},done:function(t,i){if(t.isDefaultPrevented())return!1;var n,r,o=e(this).data("blueimp-fileupload")||e(this).data("fileupload"),a=(i.getFilesFromResponse||o.options.getFilesFromResponse)(i);i.context?i.context.each((function(s){var l=a[s]||{error:"Empty file upload result"};r=o._addFinishedDeferreds(),o._transition(e(this)).done((function(){var a=e(this);n=o._renderDownload([l]).replaceAll(a),o._forceReflow(n),o._transition(n).done((function(){i.context=e(this),o._trigger("completed",t,i),o._trigger("finished",t,i),r.resolve()}))}))})):(n=o._renderDownload(a)[o.options.prependFiles?"prependTo":"appendTo"](o.options.filesContainer),o._forceReflow(n),r=o._addFinishedDeferreds(),o._transition(n).done((function(){i.context=e(this),o._trigger("completed",t,i),o._trigger("finished",t,i),r.resolve()})))},fail:function(t,i){if(t.isDefaultPrevented())return!1;var n,r,o=e(this).data("blueimp-fileupload")||e(this).data("fileupload");i.context?i.context.each((function(a){if("abort"!==i.errorThrown){var s=i.files[a];s.error=s.error||i.errorThrown||i.i18n("unknownError"),r=o._addFinishedDeferreds(),o._transition(e(this)).done((function(){var a=e(this);n=o._renderDownload([s]).replaceAll(a),o._forceReflow(n),o._transition(n).done((function(){i.context=e(this),o._trigger("failed",t,i),o._trigger("finished",t,i),r.resolve()}))}))}else r=o._addFinishedDeferreds(),o._transition(e(this)).done((function(){e(this).remove(),o._trigger("failed",t,i),o._trigger("finished",t,i),r.resolve()}))})):"abort"!==i.errorThrown?(i.context=o._renderUpload(i.files)[o.options.prependFiles?"prependTo":"appendTo"](o.options.filesContainer).data("data",i),o._forceReflow(i.context),r=o._addFinishedDeferreds(),o._transition(i.context).done((function(){i.context=e(this),o._trigger("failed",t,i),o._trigger("finished",t,i),r.resolve()}))):(o._trigger("failed",t,i),o._trigger("finished",t,i),o._addFinishedDeferreds().resolve())},progress:function(t,i){if(t.isDefaultPrevented())return!1;var n=Math.floor(i.loaded/i.total*100);i.context&&i.context.each((function(){e(this).find(".progress").attr("aria-valuenow",n).children().first().css("width",n+"%")}))},progressall:function(t,i){if(t.isDefaultPrevented())return!1;var n=e(this),r=Math.floor(i.loaded/i.total*100),o=n.find(".fileupload-progress"),a=o.find(".progress-extended");a.length&&a.html((n.data("blueimp-fileupload")||n.data("fileupload"))._renderExtendedProgress(i)),o.find(".progress").attr("aria-valuenow",r).children().first().css("width",r+"%")},start:function(t){if(t.isDefaultPrevented())return!1;var i=e(this).data("blueimp-fileupload")||e(this).data("fileupload");i._resetFinishedDeferreds(),i._transition(e(this).find(".fileupload-progress")).done((function(){i._trigger("started",t)}))},stop:function(t){if(t.isDefaultPrevented())return!1;var i=e(this).data("blueimp-fileupload")||e(this).data("fileupload"),n=i._addFinishedDeferreds();e.when.apply(e,i._getFinishedDeferreds()).done((function(){i._trigger("stopped",t)})),i._transition(e(this).find(".fileupload-progress")).done((function(){e(this).find(".progress").attr("aria-valuenow","0").children().first().css("width","0%"),e(this).find(".progress-extended").html("&nbsp;"),n.resolve()}))},processstart:function(t){if(t.isDefaultPrevented())return!1;e(this).addClass("fileupload-processing")},processstop:function(t){if(t.isDefaultPrevented())return!1;e(this).removeClass("fileupload-processing")},destroy:function(t,i){if(t.isDefaultPrevented())return!1;var n=e(this).data("blueimp-fileupload")||e(this).data("fileupload"),r=function(){n._transition(i.context).done((function(){e(this).remove(),n._trigger("destroyed",t,i)}))};i.url?(i.dataType=i.dataType||n.options.dataType,e.ajax(i).done(r).fail((function(){n._trigger("destroyfailed",t,i)}))):r()}},_resetFinishedDeferreds:function(){this._finishedUploads=[]},_addFinishedDeferreds:function(t){var i=t||e.Deferred();return this._finishedUploads.push(i),i},_getFinishedDeferreds:function(){return this._finishedUploads},_enableDragToDesktop:function(){var t=e(this),i=t.prop("href"),n=t.prop("download"),r="application/octet-stream";t.on("dragstart",(function(e){try{e.originalEvent.dataTransfer.setData("DownloadURL",[r,n,i].join(":"))}catch(e){}}))},_formatFileSize:function(e){return"number"!=typeof e?"":e>=1e9?(e/1e9).toFixed(2)+" GB":e>=1e6?(e/1e6).toFixed(2)+" MB":(e/1e3).toFixed(2)+" KB"},_formatBitrate:function(e){return"number"!=typeof e?"":e>=1e9?(e/1e9).toFixed(2)+" Gbit/s":e>=1e6?(e/1e6).toFixed(2)+" Mbit/s":e>=1e3?(e/1e3).toFixed(2)+" kbit/s":e.toFixed(2)+" bit/s"},_formatTime:function(e){var t=new Date(1e3*e),i=Math.floor(e/86400);return(i=i?i+"d ":"")+("0"+t.getUTCHours()).slice(-2)+":"+("0"+t.getUTCMinutes()).slice(-2)+":"+("0"+t.getUTCSeconds()).slice(-2)},_formatPercentage:function(e){return(100*e).toFixed(2)+" %"},_renderExtendedProgress:function(e){return this._formatBitrate(e.bitrate)+" | "+this._formatTime(8*(e.total-e.loaded)/e.bitrate)+" | "+this._formatPercentage(e.loaded/e.total)+" | "+this._formatFileSize(e.loaded)+" / "+this._formatFileSize(e.total)},_renderTemplate:function(t,i){if(!t)return e();var n=t({files:i,formatFileSize:this._formatFileSize,options:this.options});return n instanceof e?n:e(this.options.templatesContainer).html(n).children()},_renderPreviews:function(t){t.context.find(".preview").each((function(i,n){e(n).empty().append(t.files[i].preview)}))},_renderUpload:function(e){return this._renderTemplate(this.options.uploadTemplate,e)},_renderDownload:function(e){return this._renderTemplate(this.options.downloadTemplate,e).find("a[download]").each(this._enableDragToDesktop).end()},_editHandler:function(t){if(t.preventDefault(),this.options.edit){var i=this,n=e(t.currentTarget),r=n.closest(".template-upload"),o=r.data("data"),a=n.data().index;this.options.edit(o.files[a]).then((function(t){t&&(o.files[a]=t,o.context.addClass("processing"),r.find(".edit,.start").prop("disabled",!0),e(i.element).fileupload("process",o).always((function(){r.find(".size").text(i._formatFileSize(o.files[a].size)),o.context.removeClass("processing"),i._renderPreviews(o)})).done((function(){r.find(".edit,.start").prop("disabled",!1)})).fail((function(){r.find(".edit").prop("disabled",!1);var e=o.files[a].error;e&&r.find(".error").text(e)})))}))}},_startHandler:function(t){t.preventDefault();var i=e(t.currentTarget),n=i.closest(".template-upload").data("data");i.prop("disabled",!0),n&&n.submit&&n.submit()},_cancelHandler:function(t){t.preventDefault();var i=e(t.currentTarget).closest(".template-upload,.template-download"),n=i.data("data")||{};n.context=n.context||i,n.abort?n.abort():(n.errorThrown="abort",this._trigger("fail",t,n))},_deleteHandler:function(t){t.preventDefault();var i=e(t.currentTarget);this._trigger("destroy",t,e.extend({context:i.closest(".template-download"),type:"DELETE"},i.data()))},_forceReflow:function(t){return e.support.transition&&t.length&&t[0].offsetWidth},_transition:function(t){var i=e.Deferred();if(e.support.transition&&t.hasClass("fade")&&t.is(":visible")){var n=function(r){r.target===t[0]&&(t.off(e.support.transition.end,n),i.resolveWith(t))};t.on(e.support.transition.end,n).toggleClass(this.options.showElementClass)}else t.toggleClass(this.options.showElementClass),i.resolveWith(t);return i},_initButtonBarEventHandlers:function(){var t=this.element.find(".fileupload-buttonbar"),i=this.options.filesContainer;this._on(t.find(".start"),{click:function(e){e.preventDefault(),i.find(".start").trigger("click")}}),this._on(t.find(".cancel"),{click:function(e){e.preventDefault(),i.find(".cancel").trigger("click")}}),this._on(t.find(".delete"),{click:function(e){e.preventDefault(),i.find(".toggle:checked").closest(".template-download").find(".delete").trigger("click"),t.find(".toggle").prop("checked",!1)}}),this._on(t.find(".toggle"),{change:function(t){i.find(".toggle").prop("checked",e(t.currentTarget).is(":checked"))}})},_destroyButtonBarEventHandlers:function(){this._off(this.element.find(".fileupload-buttonbar").find(".start, .cancel, .delete"),"click"),this._off(this.element.find(".fileupload-buttonbar .toggle"),"change.")},_initEventHandlers:function(){this._super(),this._on(this.options.filesContainer,{"click .edit":this._editHandler,"click .start":this._startHandler,"click .cancel":this._cancelHandler,"click .delete":this._deleteHandler}),this._initButtonBarEventHandlers()},_destroyEventHandlers:function(){this._destroyButtonBarEventHandlers(),this._off(this.options.filesContainer,"click"),this._super()},_enableFileInputButton:function(){this.element.find(".fileinput-button input").prop("disabled",!1).parent().removeClass("disabled")},_disableFileInputButton:function(){this.element.find(".fileinput-button input").prop("disabled",!0).parent().addClass("disabled")},_initTemplates:function(){var e=this.options;e.templatesContainer=this.document[0].createElement(e.filesContainer.prop("nodeName")),t&&(e.uploadTemplateId&&(e.uploadTemplate=t(e.uploadTemplateId)),e.downloadTemplateId&&(e.downloadTemplate=t(e.downloadTemplateId)))},_initFilesContainer:function(){var t=this.options;void 0===t.filesContainer?t.filesContainer=this.element.find(".files"):t.filesContainer instanceof e||(t.filesContainer=e(t.filesContainer))},_initSpecialOptions:function(){this._super(),this._initFilesContainer(),this._initTemplates()},_create:function(){this._super(),this._resetFinishedDeferreds(),e.support.fileInput||this._disableFileInputButton()},enable:function(){var e=!1;this.options.disabled&&(e=!0),this._super(),e&&(this.element.find("input, button").prop("disabled",!1),this._enableFileInputButton())},disable:function(){this.options.disabled||(this.element.find("input, button").prop("disabled",!0),this._disableFileInputButton()),this._super()}})}));
//# sourceMappingURL=jqueryfileupload.min.js.map