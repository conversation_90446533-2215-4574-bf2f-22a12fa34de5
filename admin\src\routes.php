<?php

namespace PHPMaker2021\aladin2022;

use <PERSON>\App;
use Slim\Routing\RouteCollectorProxy;

// Handle Routes
return function (App $app) {
    // calendario
    $app->any('/CalendarioList[/{id}]', CalendarioController::class . ':list')->add(PermissionMiddleware::class)->setName('CalendarioList-calendario-list'); // list
    $app->any('/CalendarioAdd[/{id}]', CalendarioController::class . ':add')->add(PermissionMiddleware::class)->setName('CalendarioAdd-calendario-add'); // add
    $app->any('/CalendarioEdit[/{id}]', CalendarioController::class . ':edit')->add(PermissionMiddleware::class)->setName('CalendarioEdit-calendario-edit'); // edit
    $app->any('/CalendarioDelete[/{id}]', CalendarioController::class . ':delete')->add(PermissionMiddleware::class)->setName('CalendarioDelete-calendario-delete'); // delete
    $app->group(
        '/calendario',
        function (RouteCollectorProxy $group) {
            $group->any('/' . Config("LIST_ACTION") . '[/{id}]', CalendarioController::class . ':list')->add(PermissionMiddleware::class)->setName('calendario/list-calendario-list-2'); // list
            $group->any('/' . Config("ADD_ACTION") . '[/{id}]', CalendarioController::class . ':add')->add(PermissionMiddleware::class)->setName('calendario/add-calendario-add-2'); // add
            $group->any('/' . Config("EDIT_ACTION") . '[/{id}]', CalendarioController::class . ':edit')->add(PermissionMiddleware::class)->setName('calendario/edit-calendario-edit-2'); // edit
            $group->any('/' . Config("DELETE_ACTION") . '[/{id}]', CalendarioController::class . ':delete')->add(PermissionMiddleware::class)->setName('calendario/delete-calendario-delete-2'); // delete
        }
    );

    // ordini
    $app->any('/OrdiniList[/{id}]', OrdiniController::class . ':list')->add(PermissionMiddleware::class)->setName('OrdiniList-ordini-list'); // list
    $app->any('/OrdiniAdd[/{id}]', OrdiniController::class . ':add')->add(PermissionMiddleware::class)->setName('OrdiniAdd-ordini-add'); // add
    $app->any('/OrdiniEdit[/{id}]', OrdiniController::class . ':edit')->add(PermissionMiddleware::class)->setName('OrdiniEdit-ordini-edit'); // edit
    $app->any('/OrdiniDelete[/{id}]', OrdiniController::class . ':delete')->add(PermissionMiddleware::class)->setName('OrdiniDelete-ordini-delete'); // delete
    $app->any('/OrdiniSearch', OrdiniController::class . ':search')->add(PermissionMiddleware::class)->setName('OrdiniSearch-ordini-search'); // search
    $app->group(
        '/ordini',
        function (RouteCollectorProxy $group) {
            $group->any('/' . Config("LIST_ACTION") . '[/{id}]', OrdiniController::class . ':list')->add(PermissionMiddleware::class)->setName('ordini/list-ordini-list-2'); // list
            $group->any('/' . Config("ADD_ACTION") . '[/{id}]', OrdiniController::class . ':add')->add(PermissionMiddleware::class)->setName('ordini/add-ordini-add-2'); // add
            $group->any('/' . Config("EDIT_ACTION") . '[/{id}]', OrdiniController::class . ':edit')->add(PermissionMiddleware::class)->setName('ordini/edit-ordini-edit-2'); // edit
            $group->any('/' . Config("DELETE_ACTION") . '[/{id}]', OrdiniController::class . ':delete')->add(PermissionMiddleware::class)->setName('ordini/delete-ordini-delete-2'); // delete
            $group->any('/' . Config("SEARCH_ACTION") . '', OrdiniController::class . ':search')->add(PermissionMiddleware::class)->setName('ordini/search-ordini-search-2'); // search
        }
    );

    // configurazione
    $app->any('/ConfigurazioneList[/{id}]', ConfigurazioneController::class . ':list')->add(PermissionMiddleware::class)->setName('ConfigurazioneList-configurazione-list'); // list
    $app->any('/ConfigurazioneEdit[/{id}]', ConfigurazioneController::class . ':edit')->add(PermissionMiddleware::class)->setName('ConfigurazioneEdit-configurazione-edit'); // edit
    $app->group(
        '/configurazione',
        function (RouteCollectorProxy $group) {
            $group->any('/' . Config("LIST_ACTION") . '[/{id}]', ConfigurazioneController::class . ':list')->add(PermissionMiddleware::class)->setName('configurazione/list-configurazione-list-2'); // list
            $group->any('/' . Config("EDIT_ACTION") . '[/{id}]', ConfigurazioneController::class . ':edit')->add(PermissionMiddleware::class)->setName('configurazione/edit-configurazione-edit-2'); // edit
        }
    );

    // piazzole
    $app->any('/PiazzoleList[/{id}]', PiazzoleController::class . ':list')->add(PermissionMiddleware::class)->setName('PiazzoleList-piazzole-list'); // list
    $app->any('/PiazzoleAdd[/{id}]', PiazzoleController::class . ':add')->add(PermissionMiddleware::class)->setName('PiazzoleAdd-piazzole-add'); // add
    $app->any('/PiazzoleEdit[/{id}]', PiazzoleController::class . ':edit')->add(PermissionMiddleware::class)->setName('PiazzoleEdit-piazzole-edit'); // edit
    $app->any('/PiazzoleDelete[/{id}]', PiazzoleController::class . ':delete')->add(PermissionMiddleware::class)->setName('PiazzoleDelete-piazzole-delete'); // delete
    $app->group(
        '/piazzole',
        function (RouteCollectorProxy $group) {
            $group->any('/' . Config("LIST_ACTION") . '[/{id}]', PiazzoleController::class . ':list')->add(PermissionMiddleware::class)->setName('piazzole/list-piazzole-list-2'); // list
            $group->any('/' . Config("ADD_ACTION") . '[/{id}]', PiazzoleController::class . ':add')->add(PermissionMiddleware::class)->setName('piazzole/add-piazzole-add-2'); // add
            $group->any('/' . Config("EDIT_ACTION") . '[/{id}]', PiazzoleController::class . ':edit')->add(PermissionMiddleware::class)->setName('piazzole/edit-piazzole-edit-2'); // edit
            $group->any('/' . Config("DELETE_ACTION") . '[/{id}]', PiazzoleController::class . ':delete')->add(PermissionMiddleware::class)->setName('piazzole/delete-piazzole-delete-2'); // delete
        }
    );

    // error
    $app->any('/error', OthersController::class . ':error')->add(PermissionMiddleware::class)->setName('error');

    // login
    $app->any('/login', OthersController::class . ':login')->add(PermissionMiddleware::class)->setName('login');

    // logout
    $app->any('/logout', OthersController::class . ':logout')->add(PermissionMiddleware::class)->setName('logout');

    // Swagger
    $app->get('/' . Config("SWAGGER_ACTION"), OthersController::class . ':swagger')->setName(Config("SWAGGER_ACTION")); // Swagger

    // Index
    $app->any('/[index]', OthersController::class . ':index')->add(PermissionMiddleware::class)->setName('index');

    // Route Action event
    if (function_exists(PROJECT_NAMESPACE . "Route_Action")) {
        Route_Action($app);
    }

    /**
     * Catch-all route to serve a 404 Not Found page if none of the routes match
     * NOTE: Make sure this route is defined last.
     */
    $app->map(
        ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        '/{routes:.+}',
        function ($request, $response, $params) {
            $error = [
                "statusCode" => "404",
                "error" => [
                    "class" => "text-warning",
                    "type" => Container("language")->phrase("Error"),
                    "description" => str_replace("%p", $params["routes"], Container("language")->phrase("PageNotFound")),
                ],
            ];
            Container("flash")->addMessage("error", $error);
            return $response->withStatus(302)->withHeader("Location", GetUrl("error")); // Redirect to error page
        }
    );
};
